﻿using Lrb.Application.Property.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web.Dtos
{
    public class UpdateLeadStatusDto
    {
        public Guid LeadStatusId { get; set; }
        public string? Notes { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public DateTime? RevertDate { get; set; }
        public string? SoldPrice { get; set; }
        public string? BookedUnderName { get; set; }
        public DateTime? BookedDate { get; set; }
        public string? Rating { get; set; }
        public List<string>? ProjectsList { get; set; }
        public List<string>? PropertiesList { get; set; }
        public DateTime? PostponedDate { get; set; }
        public long? UnmatchedBudget { get; set; }
        public string? PurchasedFrom { get; set; }
        public string? PreferredLocation { get; set; }
        public AppointmentType MeetingOrSiteVisit { get; set; }
        public bool IsDone { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string? ChosenProject { get; set; }
        public double? AgreementValue { get; set; }
        public string? ChosenProperty { get; set; }
        public string? ChoosenUnit { get; set; }
        public AddressDto? Address { get; set; }
        public Guid? AssignTo { get; set; }
        public List<string>? Projects { get; set; }
        public bool? IsFullyCompleted { get; set; }
        public Guid? SecondaryUserId { get; set; }
        public string? SoldPriceCurrency { get; set; } = "INR";
        public List<AddressDto>? Addresses { get; set; }
        public List<Guid>? ProjectIds { get; set; }
        public List<Guid>? PropertyIds { get; set; }
        public bool? IsChoosenProperty { get; set; }
        public string? Currency { get; set; }
        public Guid? UnitTypeId { get; set; }
        public bool? IsBookingCompleted { get; set; }
        public bool IsNotesUpdated { get; set; }

    }
    public class UpdateBulkLeadStatusDto : UpdateLeadStatusDto
    {
        public List<Guid>? LeadIds { get; set; }
        public string? TenantId { get; set; }
        public Guid? CurrentUserId { get; set; }
        public Guid? TrackerId { get; set; }
        public BulkType? BulkCategory { get; set; }
        public List<LeadDocument>? Documents { get; set; }
    }
}
