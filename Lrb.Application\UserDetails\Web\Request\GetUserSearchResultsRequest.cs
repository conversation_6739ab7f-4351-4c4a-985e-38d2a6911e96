﻿using Lrb.Application.Lead.Web.Dtos;
using System.Text.RegularExpressions;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GetUserSearchResultsRequest : IRequest<Response<List<PropertyListsDto>>>
    {
        public ModulePropertiesType ModuleType { get; set; }
    }

    public class GetUserSearchResultsRequestHandler : IRequestHandler<GetUserSearchResultsRequest, Response<List<PropertyListsDto>>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<Domain.Entities.UserSettings> _userSettingsRepo;

        public GetUserSearchResultsRequestHandler(ICurrentUser currentUser, IRepositoryWithEvents<UserSettings> userSettingsRepo)
        {
            _currentUser = currentUser;
            _userSettingsRepo = userSettingsRepo;
        }

        public async Task<Response<List<PropertyListsDto>>> Handle(GetUserSearchResultsRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var userSettings = await _userSettingsRepo.FirstOrDefaultAsync(new GetUserSettingsSpec(currentUserId), cancellationToken);
            if (userSettings != null &&
        userSettings.SearchResults != null &&
        userSettings.SearchResults.TryGetValue(request.ModuleType, out var resultList))
            {
                var result = resultList
                    .Select(p => new PropertyListsDto
                    {
                        PropertyName = p,
                        DisplayName = GetFormattedDisplayName(p) ?? p
                    })
                    .ToList();

                return new Response<List<PropertyListsDto>>(result);
            }

            return new Response<List<PropertyListsDto>>(new List<PropertyListsDto>());

        }
        public static string? GetFormattedDisplayName(string? value)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                return value;
            }
            value = Regex.Replace(value, @"[-_ .#]", string.Empty);
            var words = Regex.Matches(value, @"([A-Z][a-z]+)")
                        .Cast<Match>()
                        .Select(m => m.Value);
            return string.Join(" ", words);
        }
    }

}
