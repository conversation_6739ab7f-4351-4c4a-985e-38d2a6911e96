﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application
{
    public class GlobalSettingsConfig : IEntityTypeConfiguration<GlobalSettings>
    {
        public void Configure(EntityTypeBuilder<GlobalSettings> builder)
        {
            builder.IsMultiTenant();
            builder.HasMany(g => g.Countries)
                             .WithOne(c => c.GlobalSettings)
                             .HasForeignKey(c => c.GlobalSettingsId);
            builder.Property(i => i.DefaultValues).Metadata.SetProviderClrType(null);
            builder.Property(i => i.ShouldEnableEnquiryForm).HasDefaultValue(true);
                                           
        }
    }
    
}
