﻿using Lrb.Application.Lead.Web;
using System.ComponentModel;

namespace Lrb.Application.Property.Web.Dtos
{
    public class PropertyMicrositeDto
    {
        public string? Title { get; set; }
        public SaleType SaleType { get; set; }
        public EnquiryType EnquiredFor { get; set; }
        public string? Notes { get; set; }
        public FurnishStatus FurnishStatus { get; set; }
        public PropertyStatus Status { get; set; }
        public string? Rating { get; set; }
        public DateTime? PossessionDate { get; set; }
        public Facing Facing { get; set; }
        public double NoOfBHKs { get; set; }
        public BHKType BHKType { get; set; }
        public PropertyTypeDto? PropertyType { get; set; }
        public AddressDto? Address { get; set; }
        public PropertyDimensionDto? Dimension { get; set; }
        public PropertyMonetaryInfoDto? MonetaryInfo { get; set; }
        public IEnumerable<PropertyAttributeDto>? Attributes { get; set; }
        public IEnumerable<MasterPropertyAmenitiesDto>? Amenities { get; set; }
        public string? AboutProperty { get; set; }
        public Dictionary<string, List<PropertyGalleryDto>>? ImageUrls { get; set; }
        public List<PropertyVideoGallaryDto>? Videos { get; set; }
        public string? SerialNo { get; set; }
        public DateTime? CreatedOn { get; set; }
        public List<string>? Links { get; set; }
        public List<BrochureDto>? Brochures { get; set; }
        public OrganizationDetails? OrgDetails { get; set; }
        public SecurityDeposit? SecurityDeposit { get; set; }
        public LockInPeriod? LockInPeriod { get; set; }
        public NoticePeriod? NoticePeriod { get; set; }
        public List<int>? NoOfFloorsOccupied { get; set; }
        public string? CoWorkingOperator { get; set; }
        public string? CoWorkingOperatorName { get; set; }
        public string? CoWorkingOperatorPhone { get; set; }
        public TenantContactInfoDto? TenantContactInfo { get; set; }

    }

    public class MasterPropertyAmenitiesDto
    {
        public string? AmenityDisplayName { get; set; }
        public string? ImageURL { get; set; }
        public string? Category { get; set; }
        public string? FullImageURL { get; set; }
        public bool? IsActive { get; set; }

    }

    public class OrganizationDetails
    {
        public string? DisplayName { get; set; }
        public string? LogoImgUrl { get; set; }
    }
    public class PullPropertyMicrositeDto

    {
        public string? Title { get; set; }
        public string SaleType { get; set; }
        public string EnquiredFor { get; set; }
        public string? Notes { get; set; }
        public string FurnishStatus { get; set; }
        public string Status { get; set; }
        public string? Rating { get; set; }
        public DateTime? PossessionDate { get; set; }
        public string Facing { get; set; }
        public double NoOfBHKs { get; set; }
        public string BHKType { get; set; }
        public PropertyTypeDto? PropertyType { get; set; }
        public AddressDto? Address { get; set; }
        public PropertyDimensionDto? Dimension { get; set; }
        public PropertyMonetaryInfoDto? MonetaryInfo { get; set; }
        public IEnumerable<PropertyAttributeDto>? Attributes { get; set; }
        public IEnumerable<MasterPropertyAmenitiesDto>? Amenities { get; set; }
        public string? AboutProperty { get; set; }
        public Dictionary<string, List<PropertyGalleryDto>>? ImageUrls { get; set; }
        public List<PropertyVideoGallaryDto>? Videos { get; set; }
        public string? SerialNo { get; set; }
        public DateTime? CreatedOn { get; set; }
        public List<string>? Links { get; set; }
        public List<BrochureDto>? Brochures { get; set; }
        public OrganizationDetails? OrgDetails { get; set; }
    }

    public class PropertyMicrositeDtoListingManagement
    {
        public string? Title { get; set; }
        public SaleType SaleType { get; set; }
        public EnquiryType EnquiredFor { get; set; }
        public string? Notes { get; set; }
        public FurnishStatus FurnishStatus { get; set; }
        public PropertyStatus Status { get; set; }
        public string? Rating { get; set; }
        public DateTime? PossessionDate { get; set; }
        public Facing Facing { get; set; }
        public double NoOfBHKs { get; set; }
        public BHKType BHKType { get; set; }
        public PropertyTypeDto? PropertyType { get; set; }
        public AddressDto? Address { get; set; }
        public PropertyDimensionDto? Dimension { get; set; }
        public PropertyMonetaryInfoDto? MonetaryInfo { get; set; }
        public IEnumerable<PropertyAttributeDto>? Attributes { get; set; }
        public IEnumerable<MasterPropertyAmenitiesDto>? Amenities { get; set; }
        public string? AboutProperty { get; set; }
        public Dictionary<string, List<PropertyGalleryDto>>? ImageUrls { get; set; }
        public List<PropertyVideoGallaryDto>? Videos { get; set; }
        public string? SerialNo { get; set; }
        public DateTime? CreatedOn { get; set; }
        public List<string>? Links { get; set; }
        public List<BrochureDto>? Brochures { get; set; }
        public OrganizationDetails? OrgDetails { get; set; }
        public SecurityDeposit? SecurityDeposit { get; set; }
        public LockInPeriod? LockInPeriod { get; set; }
        public NoticePeriod? NoticePeriod { get; set; }
        public List<int>? NoOfFloorsOccupied { get; set; }
        public string? CoWorkingOperator { get; set; }
        public string? CoWorkingOperatorName { get; set; }
        public string? CoWorkingOperatorPhone { get; set; }
        public TenantContactInfoDto? TenantContactInfo { get; set; }
        public RegulatoryInfomation? RegulatoryInfomation { get; set; }
    }

    public class RegulatoryInfomation
    {
        public string? Reference { get; set; }
        public DateTime? ListingExpireDate { get; set; }
        public string? BrokerName { get; set; }
        public string? BrokerLicenseNo { get; set; }
        public string? DLDPermitNumber { get; set; }
        public string? DTCMPermitNumber { get; set; }
        public string? ADRECPermitNumber { get; set; }
    }
}
