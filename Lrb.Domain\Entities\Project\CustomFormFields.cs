﻿namespace Lrb.Domain.Entities
{
    public class CustomFormFields : AuditableEntity, IAggregateRoot
    {
        public string FieldName { get; set; } = default!;
        public string? FieldDisplayName { get; set; }
        public QRFormType FieldType { get; set; }
        public string Module { get; set; } = default!;
        public bool IsRequired { get; set; }
        public string? Notes { get; set; }
        public Guid EntityId { get; set; }
        public string? EntityName { get; set; }
    }
}
