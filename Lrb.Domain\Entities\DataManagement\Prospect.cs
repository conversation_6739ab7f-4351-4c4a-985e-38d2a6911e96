﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Domain.Entities
{
    public class Prospect : UserLevelAuditableEntity, IAggregateRoot
    {
        public string? Name { get; set; }
        public string? ContactNo { get; set; }
        public string? AlternateContactNo { get; set; }
        public string? Email { get; set; }
        public string? Notes { get; set; }
        public Guid AssignTo { get; set; }
        public Guid? AssignedFrom { get; set; }
        public string? AgencyName { get; set; }
        public string? CompanyName { get; set; }
        public DateTime? PossesionDate { get; set; }
        public CustomProspectStatus? Status { get; set; }
        public Guid? StatusId { get; set; }
        public IList<ProspectEnquiry>? Enquiries { get; set; }
        public IList<Property>? Properties { get; set; }
        public IList<TempProjects>? TempProjects { get; set; }
        public Profession Profession { get; set; }
        public Guid? ClosingManager { get; set; }
        public Guid? SourcingManager { get; set; }
        public Address? Address { get; set; }
        public string? ExecutiveName { get; set; }
        public string? ExecutiveContactNo { get; set; }
        public IList<ChannelPartner>? ChannelPartners { get; set; }
        [Column(TypeName = "jsonb")]
        public Dictionary<ContactType, int>? ContactRecords { get; set; }
        public DateTime? ScheduleDate { get; set; }
        public bool IsQualified { get; set; }
        public Guid? ArchivedBy { get; set; }
        public DateTime? ArchivedOn { get; set; }
        public Guid? RestoredBy { get; set; }
        public DateTime? RestoredOn { get; set; }
        public bool IsConvertedToLead { get; set; }
        public string? Designation { get; set; }
        public bool IsArchived { get; set; }
        public DateTime? QualifiedDate { get; set; }
        public DateTime? ConvertedDate { get; set; }
        public string? ExcelUpload { get; set; }
        public Guid? QualifiedBy { get; set; }
        public Guid? ConvertedBy { get; set; }
        public IList<ProspectCallLog>? ProspectCallLogs { get; set; }
        public IList<Project>? Projects { get; set; }
        [Column(TypeName = "jsonb")]
        public Dictionary<DateTime, string>? CallRecordingUrls { get; set; }
        public Guid? AccountId { get; set; }
        public IList<Agency>? Agencies { get; set; }
        public string? CountryCode { get; set; }
        public string? AltCountryCode { get; set; }

        public IList<Campaign>? Campaigns { get; set;}
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }
        public string? ChannelPartnerName { get; set; }
        public string? ChannelPartnerExecutiveName { get; set; }
        public string? ChannelPartnerContactNo { get; set; }
        public string? Nationality { get; set; }
        public string? LandLine { get; set; }

        public string? UploadTypeName { get; set; }
        public LeadAssignmentType? AssignmentType { get; set; }
        public Gender? Gender { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public MaritalStatusType? MaritalStatus { get; set; }
    }
}
