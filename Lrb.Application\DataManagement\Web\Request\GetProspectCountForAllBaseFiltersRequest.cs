﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Request.CommonHandler;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Web.Request
{
    public class GetProspectCountForAllBaseFiltersRequest : GetAllProspectParameter, IRequest<PagedResponse<ProspectCountByStatusFilterDto, string>>
    {

    }
    public class GetProspectCountForAllBaseFiltersRequestHandler : BaseFilterL1andL2CommonRequestHandler, IRequestHandler<GetProspectCountForAllBaseFiltersRequest, PagedResponse<ProspectCountByStatusFilterDto, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Prospect> _prospectRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _customProspectRepo;
        private readonly IProspectRepository _efProspectRepository;
        public GetProspectCountForAllBaseFiltersRequestHandler(
            IServiceProvider serviceProvider,
            IDapperRepository dapperRepository,
            IRepositoryWithEvents<Prospect> prospectRepo,
            ICurrentUser currentUser,
            IRepositoryWithEvents<CustomProspectStatus> customPropectRepo,
            IProspectRepository efProspectRepository) : base(serviceProvider)
        {
            _dapperRepository = dapperRepository;
            _prospectRepo = prospectRepo;
            _currentUser = currentUser;
            _customProspectRepo = customPropectRepo;
            _efProspectRepository = efProspectRepository;
        }
        public async Task<PagedResponse<ProspectCountByStatusFilterDto, string>> Handle(GetProspectCountForAllBaseFiltersRequest request, CancellationToken cancellationToken)
        {
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId);
            List<Guid> subIds = new();
            try
            {
                if (request?.AssignTo?.Any() ?? false)
                {
                    if (request?.IsWithTeam ?? false)
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, tenantId ?? string.Empty)).ToList();
                    }
                    else
                    {
                        subIds = request?.AssignTo ?? new List<Guid>();
                    }
                }
                else
                {
                    subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllProspects, isAdmin))?.ToList() ?? new();
                }
            }
            catch
            {

            }
            var statuses = await _customProspectRepo.ListAsync(cancellationToken);
            var prospectCount = await ReturnProspectAllDataCount(request, userId, subIds, statuses, isAdmin);
            return new(prospectCount, prospectCount.Count);
        }
    }
}
