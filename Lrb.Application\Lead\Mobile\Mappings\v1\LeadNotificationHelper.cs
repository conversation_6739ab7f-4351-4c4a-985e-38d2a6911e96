﻿using Lrb.Application.Common.PushNotification;

namespace Lrb.Application.Lead.Mobile.Mappings
{
    public static class LeadNotificationHelper
    {
        public static async Task<bool> SendLeadStatusChangeNotificationsAsync(this Domain.Entities.Lead? lead, ViewLeadDto leadDto, INotificationSenderService _notificationSenderService, Domain.Entities.GlobalSettings? globalSettings, Guid currentUserId = default)
        {
            try
            {
                bool isSent = false;
                switch (leadDto.Status?.Status)
                {
                    case "callback":
                        var response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                        {
                            List<string> callbackStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToCallback, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        }
                        List<string> callbackStatusEvent2jobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.CallbackReminder, lead, leadDto?.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        isSent = true;
                        break;
                    case "dropped":
                        response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (currentUserId != leadDto.AssignTo)
                        {
                            List<string> dropStatusEventJobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadSatusToDropped, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        }
                        isSent = true;
                        break;
                    case "booked":
                        response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (currentUserId != leadDto.AssignTo)
                        {
                            List<string> bookedStatusEventJobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToBook, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        }
                        isSent = true;
                        break;
                    case "not_interested":
                        response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (currentUserId != leadDto.AssignTo)
                        {
                            List<string> notInterestedStatusEventJobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToNotInterest, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        }
                        isSent = true;
                        break;
                    case "site_visit_scheduled":
                        response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                        {
                            List<string> siteVisitScheduledStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToScheduleSiteVisit, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        }
                        List<string> siteVisitScheduledStatusEvent2JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.ScheduleSiteVisitReminder, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        isSent = true;
                        break;
                    case "meeting_scheduled":
                        response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                        {
                            List<string> meetingScheduledStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToScheduleMeeting, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        }
                        List<string> meetingScheduledStatusEvent2JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.ScheduleMeetingReminder, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        isSent = true;
                        break;
                    default:
                        break;
                }

                if ((leadDto?.Status?.ShouldUseForMeeting == true) && (!isSent))
                {
                    if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                    {
                        List<string> meetingScheduledStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToScheduleMeeting, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                    }
                    List<string> meetingScheduledStatusEvent2JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.ScheduleMeetingReminder, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                }
                else if ((leadDto?.Status?.ShouldUseForMeeting == false) && (!isSent))
                {
                    if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                    {
                        List<string> siteVisitScheduledStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToScheduleSiteVisit, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                    }
                    List<string> siteVisitScheduledStatusEvent2JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.ScheduleSiteVisitReminder, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                }
                else if ((leadDto?.Status?.IsScheduled ?? false) && (!isSent))
                {
                    if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                    {
                        List<string> callbackStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToCallback, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                    }
                    List<string> callbackStatusEvent2jobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.CallbackReminder, lead, leadDto?.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                }
                else if ((leadDto?.Status?.WhatsAppTemplateInfoIds?.Any() ?? false))
                {
                    await _notificationSenderService.SendWhatsAppNotificationAsync(lead, leadDto?.Status?.WhatsAppTemplateInfoIds ?? new(), null, globalSettings, leadDto:leadDto);
                }
                else if ((leadDto?.Status?.ChildType?.WhatsAppTemplateInfoIds?.Any() ?? false))
                {
                    await _notificationSenderService.SendWhatsAppNotificationAsync(lead, leadDto?.Status?.ChildType?.WhatsAppTemplateInfoIds ?? new(), null, globalSettings, leadDto:leadDto);
                }
                return true;
            }
            catch { return false; }
        }
        public static async Task<bool> SendLeadStatusChangeNotificationsAsync(this Domain.Entities.Lead? lead, ViewLeadDto leadDto, Common.NotificationService.INotificationService _notificationService, params string[] leadCount)
        {
            try
            {
                List<string> userPhoneNo = new();
                userPhoneNo.Add(leadDto?.AssignedUser?.PhoneNumber ?? string.Empty);
                switch (leadDto.Status?.Status)
                {
                    case "callback":
                        Event callbackStatusEvent1 = Event.LeadStatusToCallback;
                        Event callbackStatusEvent2 = Event.CallbackReminder;
                        if (leadDto.Status?.ChildType != null && leadDto.Status?.ChildType.Status == "to_schedule_site_visit")
                        {
                            callbackStatusEvent1 = Event.LeadStatusToScheduleSiteVisit;
                            callbackStatusEvent2 = Event.ScheduleSiteVisitReminder;
                        }
                        else if (leadDto.Status?.ChildType != null && leadDto.Status?.ChildType.Status == "to_schedule_a_meeting")
                        {
                            callbackStatusEvent1 = Event.LeadStatusToScheduleMeeting;
                            callbackStatusEvent2 = Event.ScheduleMeetingReminder;
                        }
                        await _notificationService.SendNotificationAsync(lead, callbackStatusEvent1, leadDto?.AssignedUser?.Name ?? string.Empty, userPhoneNo, leadCount);
                        await _notificationService.SendNotificationAsync(lead, callbackStatusEvent2, leadDto?.AssignedUser?.Name ?? string.Empty, userPhoneNo, leadCount);
                        break;
                    case "dropped":
                        await _notificationService.SendNotificationAsync(lead, Event.LeadSatusToDropped, leadDto?.AssignedUser?.Name ?? string.Empty, userPhoneNo, leadCount);
                        break;
                    case "booked":
                        await _notificationService.SendNotificationAsync(lead, Event.LeadSatusToDropped, leadDto?.AssignedUser?.Name ?? string.Empty, userPhoneNo, leadCount);
                        break;
                    case "not_interested":
                        await _notificationService.SendNotificationAsync(lead, Event.LeadSatusToDropped, leadDto?.AssignedUser?.Name ?? string.Empty, userPhoneNo, leadCount);
                        break;
                    case "site_visit_scheduled":
                        await _notificationService.SendNotificationAsync(lead, Event.LeadStatusToScheduleSiteVisit, leadDto?.AssignedUser?.Name ?? string.Empty, userPhoneNo, leadCount);
                        await _notificationService.SendNotificationAsync(lead, Event.ScheduleSiteVisitReminder, leadDto?.AssignedUser?.Name ?? string.Empty, userPhoneNo, leadCount);
                        break;
                    case "meeting_scheduled":
                        await _notificationService.SendNotificationAsync(lead, Event.LeadStatusToScheduleMeeting, leadDto?.AssignedUser?.Name ?? string.Empty, userPhoneNo, leadCount);
                        await _notificationService.SendNotificationAsync(lead, Event.LeadStatusToScheduleMeeting, leadDto?.AssignedUser?.Name ?? string.Empty, userPhoneNo, leadCount);
                        break;
                    default:
                        break;
                }
                return true;
            }
            catch { return false; }
        }
    }
}
