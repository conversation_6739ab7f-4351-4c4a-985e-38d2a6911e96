﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class ToggleAutomatioPerTenantRequest : IRequest<Response<bool>>
    {
    }
    public class ToggleAutomatioPerTenantRequestHandler : IRequestHandler<ToggleAutomatioPerTenantRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        public ToggleAutomatioPerTenantRequestHandler(IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo)
        {
            _userDetailsRepo = userDetailsRepo;
        }
        public async Task<Response<bool>> Handle(ToggleAutomatioPerTenantRequest request, CancellationToken cancellationToken)
        {
                var users = await _userDetailsRepo.ListAsync(new GetUsersSpec(), cancellationToken);
                users.ForEach(i => i.IsAutomationEnabled = !i.IsAutomationEnabled);
                await _userDetailsRepo.UpdateRangeAsync(users, cancellationToken);
                return new(true);

        }
    }
}
