using Finbuckle.MultiTenant;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Domain.Common.Contracts;
using Lrb.Infrastructure.Common;
using Lrb.Infrastructure.Multitenancy;
using Lrb.Infrastructure.Persistence.ConnectionString;
using Lrb.Infrastructure.Persistence.Context;
using Lrb.Infrastructure.Persistence.Initialization;
using Lrb.Infrastructure.Persistence.Repository;
using Lrb.Infrastructure.Persistence.Repository.New_Implementation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Serilog;

namespace Lrb.Infrastructure.Persistence;

internal static class Startup
{
    private static readonly ILogger _logger = Log.ForContext(typeof(Startup));

    internal static IServiceCollection AddPersistence(this IServiceCollection services, IConfiguration config)
    {
        services.AddOptions<DatabaseSettings>()
            .BindConfiguration(nameof(DatabaseSettings))
            .PostConfigure(databaseSettings =>
            {
                _logger.Information("Current DB Provider: {dbProvider}", databaseSettings.DBProvider);
            })
            .ValidateDataAnnotations()
            .ValidateOnStart();

        return services
            .AddDbContext<ApplicationDbContext>((p, m) =>
            {
                var tenantInfo = p?.GetRequiredService<IMultiTenantContextAccessor<LrbTenantInfo>>();
                var connection = tenantInfo?.MultiTenantContext?.TenantInfo?.ConnectionString;
                var databaseSettings = p.GetRequiredService<IOptions<DatabaseSettings>>().Value;
                m.UseDatabase(databaseSettings.DBProvider, string.IsNullOrEmpty(connection) ? databaseSettings.ConnectionString : connection);
                //var nm = new NpgsqlDbContextOptionsBuilder(m);
                //nm.EnableRetryOnFailure(1);
            })

            .AddTransient<IDatabaseInitializer, DatabaseInitializer>()
            .AddTransient<ApplicationDbInitializer>()
            .AddTransient<ApplicationDbSeeder>()
            .AddServices(typeof(ICustomSeeder), ServiceLifetime.Transient)
            .AddTransient<CustomSeederRunner>()

            .AddTransient<IConnectionStringSecurer, ConnectionStringSecurer>()
            .AddTransient<IConnectionStringValidator, ConnectionStringValidator>()

            .AddRepositories();
    }

    internal static DbContextOptionsBuilder UseDatabase(this DbContextOptionsBuilder builder, string dbProvider, string connectionString)
    {
        switch (dbProvider.ToLowerInvariant())
        {
            case DbProviderKeys.Npgsql:
                return builder.UseNpgsql(connectionString, e =>
                     e.MigrationsAssembly("Lrb.Migrators.PostgreSQL"));

            default:
                throw new InvalidOperationException($"DB Provider {dbProvider} is not supported.");
        }
    }

    private static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        // Add Repositories
        services.AddScoped(typeof(IRepository<>), typeof(ApplicationDbRepository<>));

        foreach (var aggregateRootType in
            typeof(IAggregateRoot).Assembly.GetExportedTypes()
                .Where(t => typeof(IAggregateRoot).IsAssignableFrom(t) && t.IsClass)
                .ToList())
        {
            // Add ReadRepositories.
            services.AddScoped(typeof(IReadRepository<>).MakeGenericType(aggregateRootType), sp =>
                sp.GetRequiredService(typeof(IRepository<>).MakeGenericType(aggregateRootType)));

            // Decorate the repositories with EventAddingRepositoryDecorators and expose them as IRepositoryWithEvents.
            services.AddScoped(typeof(IRepositoryWithEvents<>).MakeGenericType(aggregateRootType), sp =>
                Activator.CreateInstance(
                    typeof(EventAddingRepositoryDecorator<>).MakeGenericType(aggregateRootType),
                    sp.GetRequiredService(typeof(IRepository<>).MakeGenericType(aggregateRootType)))
                ?? throw new InvalidOperationException($"Couldn't create EventAddingRepositoryDecorator for aggregateRootType {aggregateRootType.Name}"));
        }
        //EF Core Repos
        services.AddScoped(typeof(IEFRepository<>), typeof(EFRepository<>));
        services.AddScoped<ILeadRepository, LeadRepository>();
        services.AddScoped<ILeadHistoryRepository, LeadHistoryRepository>();
        services.AddScoped<IBulkLeadUploadTrackerRepository, BulkLeadUploadTrackerRepository>();
        services.AddScoped<IBulkPropertyUploadTrackerRepository, BulkPropertyUploadTrackerRepository>();
        services.AddScoped<ITodoRepository, TodoRepository>();
        services.AddScoped<IPropertyRepository, PropertyRepository>();
        services.AddScoped<IProjectRepository, ProjectRepository>();
        services.AddScoped<IMasterPropertyRepository, MasterPropertyRepository>();
        services.AddScoped<IProspectRepository, ProspectRepository>();

        return services;
    }
}