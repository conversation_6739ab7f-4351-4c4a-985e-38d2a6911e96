﻿using Lrb.Application.Integration.Web;
using Lrb.Application.Integration.Web.Specs;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Lrb.Application.Dashboard.Web
{
    public class GetCampaignAdMetricsRequest : IRequest<Response<List<CampaignAdMetricsDto>>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public Guid AccountId { get; set; } = new();
        public string? CampaignAds { get; set; }
    }
    public class GetCampaignAdMetricsRequestHandler : IRequestHandler<GetCampaignAdMetricsRequest, Response<List<CampaignAdMetricsDto>>>
    {
        private readonly IRepositoryWithEvents<FacebookConnectedPageAccount> _fbPageRepo;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccInfoRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepository;
        private readonly IDapperRepository _dapperRepository;
        public GetCampaignAdMetricsRequestHandler(
            IRepositoryWithEvents<FacebookConnectedPageAccount> fbPageRepo,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Lead> leadRepo,
            ICurrentUser currentUser,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepository,
            IDapperRepository dapperRepository)
        {
            _fbPageRepo = fbPageRepo;
            _integrationRepo = integrationRepo;
            _leadRepo = leadRepo;
            _currentUser = currentUser;
            _integrationAccInfoRepo = integrationAccInfoRepo;
            _leadRepository = leadRepository;
            _dapperRepository = dapperRepository;
        }

        public async Task<Response<List<CampaignAdMetricsDto>>> Handle(GetCampaignAdMetricsRequest request, CancellationToken cancellationToken)
        {
            var results = new ConcurrentBag<CampaignAdMetricsDto>();
            var integrationInfo = await _integrationAccInfoRepo.FirstOrDefaultAsync(new IntegrationAccInfoByFacebookIdOrIdSpec(request.AccountId));
            var tenantId = _currentUser.GetTenant();
            var today = DateTime.UtcNow.Date;
            var fromDate = request.FromDate ?? DateTime.Today;
            var toDate = request.ToDate ?? DateTime.Today;

            // Build a JSON string with double quotes (important!)
            var timeRangeJson = $"{{\"since\":\"{fromDate:yyyy-MM-dd}\",\"until\":\"{toDate:yyyy-MM-dd}\"}}";
            var fbPage = await _fbPageRepo.FirstOrDefaultAsync(
                new GetFacebookConnectedPageAccountByFBAuthIdSpec(integrationInfo?.FacebookAccountId ?? Guid.Empty),
                cancellationToken
            );

            if (string.IsNullOrWhiteSpace(request.CampaignAds))
                return new();

            List<CampaignAdInputDto> campaignAds;
            try
            {
                var decodedCampaignAds = System.Web.HttpUtility.UrlDecode(request.CampaignAds) ?? request.CampaignAds;
                campaignAds = System.Text.Json.JsonSerializer.Deserialize<List<CampaignAdInputDto>>(decodedCampaignAds, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new();
            }
            catch(Exception ex)
            {
                return new();
            }

            var groupedCampaigns = campaignAds
                .GroupBy(x => x.CampaignId)
                .ToDictionary(g => g.Key, g => g.Select(x => x.AdId).ToList());

            var allAdIds = groupedCampaigns.Values.SelectMany(list => list).Distinct().ToArray();
            var revenueResults = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<FacebookAdRevenueResult>(
                "LeadratBlack", "GetFacebookAdRevenueByAds",
                new { p_tenantid = tenantId, p_adids = allAdIds }
            )).ToList();

            using var client = new HttpClient();
            const string baseUrl = "https://graph.facebook.com/v21.0";
            var accessToken = fbPage?.LongLivedPageAccessToken;

            var adData = await GetAdDataBatchAsync(client, baseUrl, allAdIds.ToList(), accessToken, timeRangeJson, cancellationToken);
            if(adData != null )
            {
                foreach (var campaign in groupedCampaigns)
                {
                    decimal campaignBudget = 0;
                    foreach (var adId in campaign.Value)
                    {
                        if (adData.TryGetValue(adId, out var data))
                        {
                            campaignBudget += data.Budget;
                            var revenue = revenueResults.FirstOrDefault(r => r.AdId == adId)?.TotalRevenue ?? 0;
                            decimal investment = data.Spend > 0 ? data.Spend : data.Budget;
                            decimal? roi = null;
                            if (revenue > 0 && investment > 0)
                            {
                                roi = Math.Round(((revenue - investment) / investment) * 100, 2);
                            }
                            results.Add(new CampaignAdMetricsDto
                            {
                                CampaignId = campaign.Key,
                                AdId = adId,
                                AdBudget = data.Budget,
                                CampaignBudget = campaignBudget,
                                CostPerLead = data.CostPerLead.HasValue ? Math.Round(data.CostPerLead.Value, 2) : 0,
                                TotalRevenue = revenue,
                                RoiPercentage = Math.Round(roi ?? 0, 2)
                            });
                        }
                    }
                }

                return new(results.ToList());
            }
            return null;
        }
        private async Task<Dictionary<string, (decimal Budget, decimal Spend, int Leads, decimal? CostPerLead)>> GetAdDataBatchAsync(
     HttpClient client, string baseUrl, List<string> adIds, string token, string timeRange, CancellationToken ct)
        {
            try
            {
                var results = new Dictionary<string, (decimal Budget, decimal Spend, int Leads, decimal? CostPerLead)>();
                const int batchSize = 50; 

                if (adIds == null || !adIds.Any())
                {
                    return results;
                }

                for (int i = 0; i < adIds.Count; i += batchSize)
                {
                    var batchAdIds = adIds.Skip(i).Take(batchSize).ToList();
                    var batch = batchAdIds.Select(adId => new
                    {
                        method = "GET",
                        relative_url = $"{adId}?fields=adset{{id,daily_budget}},insights.time_range({timeRange}){{spend,actions,cost_per_action_type,date_start,date_stop}}"
                    }).ToList();

                    var batchJson = System.Text.Json.JsonSerializer.Serialize(batch);
                    var formContent = new FormUrlEncodedContent(new[]
                    {
                new KeyValuePair<string, string>("batch", batchJson),
                new KeyValuePair<string, string>("access_token", token)
            });

                    var response = await client.PostAsync(baseUrl, formContent, ct);
                    response.EnsureSuccessStatusCode();

                    var json = await response.Content.ReadAsStringAsync();
                    var batchResults = System.Text.Json.JsonSerializer.Deserialize<List<BatchResponse>>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (batchResults == null)
                    {
                        continue;
                    }

                    foreach (var result in batchResults)
                    {
                        if (result?.Body == null) continue;

                        var data = System.Text.Json.JsonSerializer.Deserialize<AdResponse>(result.Body, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });

                        if (data?.Id == null) continue;

                        decimal budget = data.AdSet?.DailyBudget != null && decimal.TryParse(data.AdSet.DailyBudget, out var val) ? val / 100 : 0;
                        decimal spend = 0;
                        int leads = 0;
                        decimal? cpl = null;
                        if (data.Insights?.Data?.Count > 0)
                        {
                            var item = data.Insights.Data[0];
                            spend = item.Spend != null && decimal.TryParse(item.Spend, out var sp) ? sp : 0;
                            leads = item.Actions?.Where(x => x?.ActionType == "lead")
                                .Sum(x => int.TryParse(x.Value, out var val) ? val : 0) ?? 0;
                            cpl = leads > 0 ? spend / leads : (decimal?)0;
                        }

                        results[data.Id] = (budget, spend, leads, cpl);
                    }
                }

                return results;
            }
            catch (HttpRequestException ex)
            {
             
                return null;
            }
            catch (System.Text.Json.JsonException ex)
            {
                
                return null;
            }
            catch (Exception ex)
            {
       
                return null;
            }
        }
        public class BatchResponse
        {
            [JsonPropertyName("code")]
            public int Code { get; set; }
            [JsonPropertyName("headers")]
            public List<BatchHeader> Headers { get; set; }
            [JsonPropertyName("body")]
            public string Body { get; set; }
        }

        public class AdResponse
        {
            public string Id { get; set; }
            [JsonPropertyName("adset")]
            public AdSetResponse? AdSet { get; set; }
            [JsonPropertyName("insights")]
            public InsightsResponse? Insights { get; set; }
        }

        public class AdSetResponse
        {
            public string Id { get; set; }
            [JsonPropertyName("daily_budget")]
            public string DailyBudget { get; set; }
        }

        public class InsightsResponse
        {
            [JsonPropertyName("data")]
            public List<InsightData> Data { get; set; }
            [JsonPropertyName("paging")]
            public Paging Paging { get; set; }
        }
        public class Paging
        {
            public Cursors Cursors { get; set; }
        }

        public class Cursors
        {
            public string Before { get; set; }
            public string After { get; set; }
        }
        public class InsightData
        {
            public string Spend { get; set; }
            public List<ActionData> Actions { get; set; }
            [JsonPropertyName("cost_per_action_type")]
            public List<ActionData> CostPerActionType { get; set; }
            [JsonPropertyName("date_start")]
            public string DateStart { get; set; }
            [JsonPropertyName("date_stop")]
            public string DateStop { get; set; }
        }

        public class ActionData
        {
            [JsonPropertyName("action_type")]
            public string ActionType { get; set; }
            public string Value { get; set; }
        }
        public class BatchHeader
        {
            [JsonPropertyName("name")]
            public string Name { get; set; }
            [JsonPropertyName("value")]
            public string Value { get; set; }
        }
    }
    public class CampaignAdInputDto
    {
        public string? CampaignId { get; set; }
        public string? AdId { get; set; }
    }
    public class CampaignAdMetricsDto
    {
        public string? AdId { get; set; }
        public string? CampaignId { get; set; }
        public decimal? AdBudget { get; set; }
        public decimal? CampaignBudget { get; set; }
        public decimal? CostPerLead { get; set; }
        public decimal? RoiPercentage { get; set; }
        public decimal? TotalRevenue { get; set; }
    }
    public class FacebookAdRevenueResult
    {
        public string AdId { get; set; }
        public decimal TotalRevenue { get; set; }
    }

}
