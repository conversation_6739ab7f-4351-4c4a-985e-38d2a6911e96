﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class GlobalCheckToRenameSiteVisit : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "ShouldRenameSiteVisitColumn",
                schema: "LeadratBlack",
                table: "GlobalSettings",
                type: "boolean",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ShouldRenameSiteVisitColumn",
                schema: "LeadratBlack",
                table: "GlobalSettings");
        }
    }
}
