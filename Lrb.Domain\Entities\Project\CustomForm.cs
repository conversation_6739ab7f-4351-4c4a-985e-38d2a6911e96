﻿namespace Lrb.Domain.Entities
{
    public class CustomForm : AuditableEntity, IAggregateRoot
    {
        public string Name { get; set; } = default!;
        public string? DisplayName { get; set; }
        public QRFormType FieldType { get; set; }
        public string Module { get; set; } = default!;
        public int OrderRank { get; set; }
        public bool IsRequired { get; set; }
        public string? Notes { get; set; }
        public Guid EntityId { get; set; }
        public string? EntityName { get; set; }
    }
}
