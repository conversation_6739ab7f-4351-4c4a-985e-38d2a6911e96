﻿
using Lrb.Application.Project.Mobile.Requests;
using Lrb.Application.Property.Web;

namespace Lrb.Application.Project.Mobile.Specs
{
    public class GetAllProjectUnitSpecs : EntitiesByPaginationFilterSpec<Domain.Entities.UnitType>
    {
        public GetAllProjectUnitSpecs(GetProjectAllUnitInfoRequest filter, NumericUnitAttributesDto numericAttributesDto,List<CustomPropertyAttributeDto>? attributes=null) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.Attributes)
                .Include(i => i.MasterUnitType)
                .Include(i => i.UnitInfoGalleries)
                .Where(i => i.ProjectId == filter.ProjectId)
                .OrderByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc));

            if (!string.IsNullOrWhiteSpace(filter.Search))
            {
                filter.Search = filter.Search.ToLower().Trim();
                Query.Where(i => i.Name.ToLower().Trim().Contains(filter.Search));
            }
            if (filter.Area != null)
            {
                Query.Where(i => i.Area == filter.Area);
            }
            if (filter.AreaUnitId != default)
            {
                Query.Where(i => i.AreaUnitId == filter.AreaUnitId);
            }
            if (filter.CarpetArea != null)
            {
                Query.Where(i => i.CarpetArea == filter.CarpetArea);
            }
            if (filter.CarpetAreaUnitId != default)
            {
                Query.Where(i => i.CarpetAreaUnitId == filter.CarpetAreaUnitId);
            }
            if (filter.BuiltupArea != null)
            {
                Query.Where(i => i.BuildUpArea == filter.BuiltupArea);
            }
            if (filter.BuiltupAreaUnitId != default && filter.BuiltupAreaUnitId!= Guid.Empty)
            {
                Query.Where(i => i.BuildUpAreaId == filter.BuiltupAreaUnitId);
            }
            if (filter.SuperBuiltupArea != null)
            {
                Query.Where(i => i.SuperBuildUpArea == filter.SuperBuiltupArea);
            }
            if (filter.SuperBuiltupAreaUnitId != default && filter.SuperBuiltupAreaUnitId != Guid.Empty)
            {
                Query.Where(i => i.SuperBuildUpAreaUnit == filter.SuperBuiltupAreaUnitId);
            }
            if (filter.MaintenanceCost != null)
            {
                Query.Where(i => i.MaintenanceCost == filter.MaintenanceCost);
            }
            if (filter.PricePerUnit != null)
            {
                Query.Where(i => i.PricePerUnit == filter.PricePerUnit);
            }
            if (filter.TotalPrice != null)
            {
                Query.Where(i => i.Price == filter.TotalPrice);
            }
            if (filter?.Currency != null)
            {
                Query.Where(i => i.Currency == filter.Currency);
            }
            if (filter?.UnitType != null && filter.UnitType.Any())
            {
                Query.Where(i => i.MasterUnitType != null && filter.UnitType.Contains(i.MasterUnitType.BaseId ?? Guid.Empty));
            }
            if (filter?.UnitSubType != null && filter.UnitSubType.Any())
            {
                Query.Where(i => i.MasterUnitType != null && filter.UnitSubType.Contains(i.MasterUnitType.Id));
            }
            if (filter?.BHKTypes?.Any() ?? false)
            {
                Query.Where(i => filter.BHKTypes.Contains(i.BHKType));
            }
            if (filter?.BHKs?.Any() ?? false)
            {
                Query.Where(i => filter.BHKs.Contains(i.NoOfBHK ?? 0.0D));
            }
            if (filter?.Facings?.Any() ?? false)
            {
                Query.Where(i => i.Facings != null && i.Facings.Any(i => filter.Facings.Contains(i)));
            }
            if (filter?.FurnishingStatuses?.Any() ?? false)
            {
                Query.Where(i => filter.FurnishingStatuses.Contains(i.FurnishingStatus));
            }
            if (filter.MinMaintenanceCost != null && filter.MaxMaintenanceCost != null)
            {
                Query.Where(i => i.MaintenanceCost >= filter.MinMaintenanceCost
                                 && i.MaintenanceCost <= filter.MaxMaintenanceCost);
            }
            else if (filter.MinMaintenanceCost != null)
            {
                Query.Where(i => i.MaintenanceCost >= filter.MinMaintenanceCost);
            }
            else if (filter.MaxMaintenanceCost != null)
            {
                Query.Where(i => i.MaintenanceCost <= filter.MaxMaintenanceCost);
            }
            if (filter.MinPricePerUnit != null && filter.MaxPricePerUnit != null)
            {
                Query.Where(i => i.PricePerUnit >= filter.MinPricePerUnit
                                 && i.PricePerUnit <= filter.MaxPricePerUnit);
            }
            else if (filter.MinPricePerUnit != null)
            {
                Query.Where(i => i.PricePerUnit >= filter.MinPricePerUnit);
            }
            else if (filter.MaxPricePerUnit != null)
            {
                Query.Where(i => i.PricePerUnit <= filter.MaxPricePerUnit);
            }
            if (filter.MinTotalPrice != null && filter.MaxTotalPrice != null)
            {
                Query.Where(i => i.Price >= filter.MinTotalPrice
                                 && i.Price <= filter.MaxTotalPrice);
            }
            else if (filter.MinTotalPrice != null)
            {
                Query.Where(i => i.Price >= filter.MinTotalPrice);
            }
            else if (filter.MaxTotalPrice != null)
            {
                Query.Where(i => i.Price <= filter.MaxTotalPrice);
            }

            if ((filter.MinSuperBuiltupArea != null || filter.MaxSuperBuiltupArea != null) && filter.SuperBuiltupAreaUnitId != default)
            {
                Query.Where(i => i.SuperBuildUpAreaUnit != null
                    && (filter.MinSuperBuiltupArea == null || i.SuperBuildUpArea >= filter.MinSuperBuiltupArea)
                    && (filter.MaxSuperBuiltupArea == null || i.SuperBuildUpArea <= filter.MaxSuperBuiltupArea)
                    && i.SuperBuildUpAreaUnit == filter.SuperBuiltupAreaUnitId);
            }
            else if (filter.MinSuperBuiltupArea != null || filter.MaxSuperBuiltupArea != null)
            {
                Query.Where(i =>
                    (filter.MinSuperBuiltupArea == null || i.SuperBuildUpArea >= filter.MinSuperBuiltupArea) &&
                    (filter.MaxSuperBuiltupArea == null || i.SuperBuildUpArea <= filter.MaxSuperBuiltupArea));
            }
            if ((filter.MinArea != null || filter.MaxArea != null) && filter.AreaUnitId != default)
            {
                Query.Where(i => i.AreaUnitId != null
                    && (filter.MinArea == null || i.Area >= filter.MinArea)
                    && (filter.MaxArea == null || i.Area <= filter.MaxArea)
                    && i.AreaUnitId == filter.AreaUnitId);
            }
            else if (filter.MinArea != null || filter.MaxArea != null)
            {
                Query.Where(i =>
                    (filter.MinArea == null || i.Area >= filter.MinArea) &&
                    (filter.MaxArea == null || i.Area <= filter.MaxArea));
            }
            if ((filter.MinBuildUpArea != null || filter.MaxBuildUpArea != null) && filter.BuiltupAreaUnitId != default)
            {
                Query.Where(i => i.BuildUpAreaId != null
                    && (filter.MinBuildUpArea == null || i.BuildUpArea >= filter.MinBuildUpArea)
                    && (filter.MaxBuildUpArea == null || i.BuildUpArea <= filter.MaxBuildUpArea)
                    && i.BuildUpAreaId == filter.BuiltupAreaUnitId);
            }
            else if (filter.MinBuildUpArea != null || filter.MaxBuildUpArea != null)
            {
                Query.Where(i =>
                    (filter.MinBuildUpArea == null || i.BuildUpArea >= filter.MinBuildUpArea) &&
                    (filter.MaxBuildUpArea == null || i.BuildUpArea <= filter.MaxBuildUpArea));
            }
            if ((filter.MinCarpetArea != null || filter.MaxCarpetArea != null) && filter.CarpetAreaUnitId != default)
            {
                Query.Where(i => i.CarpetAreaUnitId != null
                    && (filter.MinCarpetArea == null || i.CarpetArea >= filter.MinCarpetArea)
                    && (filter.MaxCarpetArea == null || i.CarpetArea <= filter.MaxCarpetArea)
                    && i.CarpetAreaUnitId == filter.CarpetAreaUnitId);
            }
            else if (filter.MinCarpetArea != null || filter.MaxCarpetArea != null)
            {
                Query.Where(i =>
                    (filter.MinCarpetArea == null || i.CarpetArea >= filter.MinCarpetArea) &&
                    (filter.MaxCarpetArea == null || i.CarpetArea <= filter.MaxCarpetArea));
            }

            if (filter.NoOfFloors?.Any() ?? false && numericAttributesDto?.NoOfFloors?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "Total Floors");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfFloors.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfFloors.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0" && i.Value != "Ground Floor"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfFloors.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfBedrooms?.Any() ?? false && numericAttributesDto?.NoOfBedrooms?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Bed Rooms");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfBedrooms.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfKitchens?.Any() ?? false && numericAttributesDto?.NoOfKitchens?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Kitchens");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfKitchens.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                            numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfUtilites?.Any() ?? false && numericAttributesDto?.NoOfUtilites?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Utilities");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfUtilites.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfLivingrooms?.Any() ?? false && numericAttributesDto?.NoOfLivingrooms?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Drawing or Living Rooms");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfLivingrooms.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }

            }
            if (filter.NoOfBalconies?.Any() ?? false && numericAttributesDto?.NoOfBalconies?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Balconies");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfBalconies.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfMaximumOccupants?.Any() ?? false && numericAttributesDto?.NoOfMaximumOccupants?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "Maximum Occupants");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfMaximumOccupants.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfMaximumOccupants.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfMaximumOccupants.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfBathrooms?.Any() ?? false &&  numericAttributesDto?.NoOfBathrooms?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Bath Rooms");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfBathrooms.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0" && i.Value != "Ground Floor"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterProjectUnitAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
        }
    }


    public class GetAllProjectBlocksSpecs : EntitiesByPaginationFilterSpec<Domain.Entities.Block>
    {
        public GetAllProjectBlocksSpecs(GetProjectBlocksRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .Where(i => i.ProjectId == filter.ProjectId)
                .OrderByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc));
        }
    }
}
