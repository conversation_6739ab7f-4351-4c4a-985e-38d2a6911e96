﻿using System.Text.Json.Serialization;

namespace Lrb.Application.Property.Web.V2.Dtos
{
    internal class PFListingRequestDto
    {
    }

    #region Create and Update
    public class UpdateListingOnPFRequestDto : CreateListingOnPFRequestDto
    {
        public string? ListingId { get; set; }
    }

    public class CreateListingOnPFRequestDto
    {
        public int? age { get; set; }
        public List<string>? amenities { get; set; }
        public AssignedTo? assignedTo { get; set; }
        public string? availableFrom { get; set; }
        public string? bathrooms { get; set; }
        public string? bedrooms { get; set; }
        public string? category { get; set; }
        public Compliance? compliance { get; set; }
        public CreatedBy? createdBy { get; set; }
        public Description? description { get; set; }
        public string? developer { get; set; }
        public string? finishingType { get; set; }
        public string? floorNumber { get; set; }
        public string? furnishingType { get; set; }
        public bool? hasGarden { get; set; }
        public bool? hasKitchen { get; set; }
        public bool? hasParkingOnSite { get; set; }
        public string? landNumber { get; set; }
        public Location? location { get; set; }
        public Media? media { get; set; }
        public string? mojDeedLocationDescription { get; set; }
        public int? numberOfFloors { get; set; }
        public string? ownerName { get; set; }
        public int? parkingSlots { get; set; }
        public string? plotNumber { get; set; }
        public int? plotSize { get; set; }
        public Price? price { get; set; }
        public string? projectStatus { get; set; }
        public string? reference { get; set; }
        public int? size { get; set; }
        public Street? street { get; set; }
        public Title? title { get; set; }
        public string? type { get; set; }
        public string? uaeEmirate { get; set; }
        public string? unitNumber { get; set; }
    }

    public class AssignedTo
    {
        public int? id { get; set; }
    }

    public class Compliance
    {
        public DateTime? advertisementLicenseIssuanceDate { get; set; }
        public string? listingAdvertisementNumber { get; set; }
        public string? type { get; set; }
        public bool? userConfirmedDataIsCorrect { get; set; }
    }

    public class CreatedBy
    {
        public int? id { get; set; }
    }

    public class Description
    {
        public string? ar { get; set; }
        public string? en { get; set; }
    }

    public class Location
    {
        public int? id { get; set; }
    }

    public class Media
    {
        public List<Image>? images { get; set; }

        public Videos? videos { get; set; }
    }

    public class Image
    {
        public ImageSize? large { get; set; }
        public ImageSize? medium { get; set; }
        public ImageSize? original { get; set; }
        public ImageSize? thumbnail { get; set; }
        public ImageSize? watermarked { get; set; }
    }

    public class ImageSize
    {
        public int height { get; set; }

        public string? url { get; set; }

        public int width { get; set; }
    }

    public class Videos
    {
        public string? view360 { get; set; }
    }

    public class Price
    {
        public Amounts? amounts { get; set; }
        public int? downpayment { get; set; }
        public int? numberOfCheques { get; set; }
        public string? type { get; set; }
    }

    public class Amounts
    {
        public int? daily { get; set; }

        public int? monthly { get; set; }

        public int? sale { get; set; }

        public int? weekly { get; set; }

        public int? yearly { get; set; }
    }

    public class Street
    {
        public string? direction { get; set; }

        public int width { get; set; }
    }

    public class Title
    {
        public string? ar { get; set; }

        public string? en { get; set; }
    }

    #endregion

    #region PFUser
    public class AgentResponse
    {
        public List<Agent>? Data { get; set; }
        public Pagination? Pagination { get; set; }
    }

    public class Agent
    {
        public int Id { get; set; }

        public string? FirstName { get; set; }

        public string? LastName { get; set; }

        public string? Email { get; set; }

        public string? Mobile { get; set; }

        public string? Status { get; set; }

        public PublicProfile? PublicProfile { get; set; }
    }

    public class PublicProfile
    {
        public int Id { get; set; }

        public string? Name { get; set; }

        public string? Email { get; set; }

        public string? Phone { get; set; }

        public string? PhoneSecondary { get; set; }

        public string? WhatsappPhone { get; set; }

        public bool IsSuperAgent { get; set; }
    }

    public class Pagination
    {
        public int Total { get; set; }

        public int Page { get; set; }

        public int PerPage { get; set; }

        public int TotalPages { get; set; }

        public int? NextPage { get; set; }

        public int? PrevPage { get; set; }
    }
    #endregion

    #region Location
    public class PFLocationFilter
    {
        public int Page { get; set; }
        public int PerPage { get; set; }
        public string? Search { get; set; }
        public int? locationId { get; set; }
    }

    public class ApiResponse
    {
        public List<DataItem>? data { get; set; }
        public Pagination? pagination { get; set; }
    }

    public class DataItem
    {
        public int id { get; set; }
        public string? type { get; set; }
        public string? name { get; set; }
        public List<TreeItem>? tree { get; set; }
        public Coordinates? Coordinates { get; set; }
    }

    public class TreeItem
    {
        public int id { get; set; }
        public string? type { get; set; }
        public string? name { get; set; }
    }

    public class Coordinates
    {
        public double lat { get; set; }
        public double lng { get; set; }
    }

    public class LocationPagination
    {
        public int total { get; set; }
        public int page { get; set; }
        public int perPage { get; set; }
        public int totalPages { get; set; }
        public int nextPage { get; set; }
        public int prevPage { get; set; }
    }

    #endregion

    #region Webhook
    public class PFWebHookRequest
    {
        public string? eventId { get; set; }
        public string? callbackUrl { get; set; }
        public string? secret { get; set; }
    }
    #endregion

    #region Get All Listing Filter
    public class PFListingFilter
    {
        public int? Page { get; set; }
        public int? Perpage { get; set; }
        public string? Search { get; set; }
        public bool? IsDraft { get; set; }
        public string? Reference { get; set; }
        public string? Id { get; set; }
    }
    #endregion
}
