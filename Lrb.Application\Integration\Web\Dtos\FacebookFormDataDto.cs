﻿using Lrb.Application.Agency.Web;
using Lrb.Application.CustomStatus.Web;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Integration.Web.Dtos
{
    public class FacebookFormDataDto : IDto
    {
        public string? PageId { get; set; }
        public string? FormId { get; set; }
        public IDictionary<DataColumns, string>? MappingDetails { get; set; }
    }
    public class ViewFacebookFormDataDto : IDto
    {
        public string? PageId { get; set; }
        public string? FormId { get; set; }
    }
    public class FacebookLeadGenFormDto : IDto
    {
        public Guid Id { get; set; }
        public string FacebookId { get; set; } = default!;
        public string Name { get; set; } = default!;
        public string PageId { get; set; } = default!;
        public string? PageName { get; set; }
        public string Status { get; set; } = default!;
        public bool IsSubscribed { get; set; }
        public string? AgencyName { get; set; }
        public Guid FacebookConnectedPageAccountId { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? DeletedOn { get; set; }
        public Guid? DeletedBy { get; set; }
        public IList<string?>? ProjectNames { get; set; }
        public IList<string>? UserNames { get; set; }
        public Guid AutomationId { get; set; }
        public bool IsAutomated { get; set; }
        public int LeadCount { get; set; }
        public AgencyDto? Agency { get; set; }
    }

    public class FacebookAdsInfoDto : IDto
    {
        public Guid Id { get; set; }
        public string? AdId { get; set; }
        public string? AdName { get; set; }
        public string? Status { get; set; }
        public string? PageId { get; set; }
        public string? AdSetName { get; set; }
        public string? AdSetId { get; set; }
        public string? CampaignName { get; set; }
        public string? CampaignId { get; set; }
        public string? AdAccountName { get; set; }
        public string? AdAccountId { get; set; }
        public string? AgencyName { get; set; }
        public Guid AutomationId { get; set; }
        public bool IsAutomated { get; set; }
        public Guid FacebookAuthResponseId { get; set; }
        public bool IsSubscribed { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? DeletedOn { get; set; }
        public Guid? DeletedBy { get; set; }
        public IList<string?>? ProjectNames { get; set; }
        public IList<string>? UserNames { get; set; }
        public int LeadCount { get; set; }
    }

    public class ViewFacebookAccountWithFormDto : ViewFacebookAccountDto
    {
        public IEnumerable<FacebookLeadGenFormDto>? AllForms { get; set; }
        public IEnumerable<FacebookLeadGenFormDto>? ExternalForms { get; set; }
        public IEnumerable<FacebookLeadGenFormDto>? InternalForms { get; set; }
    }

    public class ViewFacebookAccountDto : IDto
    {
        public Guid Id { get; set; }
        public Guid AccountId { get; set; }
        public string? FacebookUserId { get; set; }
        public string? FacebookAccountName { get; set; }
        public long LeadCount { get; set; }
        public LeadSource LeadSource { get; set; }
        public IEnumerable<FacebookAdsInfoDto>? Ads { get; set; }
        public bool IsAutomated { get; set; }
        public string? PixelId { get; set; }
        public string? ConversionsAccessToken { get; set; }
        public List<ViewCustomStatusDto>? Statuses { get; set; }
    }

    public class ViewFacebookAccountDtoV1 : IDto
    {
        public Guid Id { get; set; }
        public Guid AccountId { get; set; }
        public string? FacebookUserId { get; set; }
        public string? FacebookAccountName { get; set; }
        public long LeadCount { get; set; }
        public LeadSource LeadSource { get; set; }
        public bool IsAutomated { get; set; }
        public string? PixelId { get; set; }
        public string? ConversionsAccessToken { get; set; }
    }
    public class FacebookCampaignInfoDto : IDto
    {
        public string? Status { get; set; }
        public string? PageId { get; set; }
        public string? CampaignName { get; set; }
        public string? CampaignId { get; set; }
        public string? AdAccountName { get; set; }
        public string? AdAccountId { get; set; }
        public Guid FacebookAuthResponseId { get; set; }
        public int LeadCount { get; set; }
    }
}