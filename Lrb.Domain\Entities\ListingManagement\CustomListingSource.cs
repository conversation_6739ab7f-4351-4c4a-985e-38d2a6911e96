﻿using System.Text.Json.Serialization;

namespace Lrb.Domain.Entities
{
    public class CustomListingSource : AuditableEntity, IAggregateRoot
    {
        public string? DisplayName { get; set; }
        public int Value { get; set; }
        public int OrderRank { get; set; }
        public string? ImageURL { get; set; }
        public string? ProgressColor { get; set; }
        public string? BackgroundColor { get; set; }
        public bool IsDefault { get; set; }
        public IList<Property>? Properties { get; set; }
        public bool? ShouldIncludeInRefrenceId { get; set; }
        public string? DisplayImageURL { get; set; }
    }
}
