using Lrb.Domain.Enums;

namespace Lrb.Application.CustomForm.Web.Dtos
{
    public class CreateCustomFormDto : BaseCustomFormDto
    {
    }

    public class UpdateCustomFormDto : BaseCustomFormDto
    {
        public Guid Id { get; set; }
    }

    public class ViewCustomFormDto : BaseCustomFormDto
    {
        public Guid Id { get; set; }
       
    }

    public class BaseCustomFormDto : IDto
    {
        public string FieldName { get; set; } = default!;
        public string? FieldDisplayName { get; set; }
        public QRFormType FieldType { get; set; }
        public string Module { get; set; } = default!;
        public bool IsRequired { get; set; }
        public string? Notes { get; set; }
        public Guid EntityId { get; set; }
        public string? EntityName { get; set; }
    }
}
