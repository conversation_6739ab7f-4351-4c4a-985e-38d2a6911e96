﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Project.Web.Requests.CommonHandler;

namespace Lrb.Application.Project.Web.Requests
{
    public class GetAllProjectAnonymousRequest : PaginationFilter, IRequest<PagedResponse<PullViewProjectDto, string>>
    {
        public ProjectVisibilityType ProjectVisibility { get; set; }
        public List<ProjectStatus>? ProjectStatuses { get; set; }
        public ProjectCurrentStatus? CurrentStatus { get; set; }
        public ProjectType? ProjectType { get; set; }
        public Facing? Facing { get; set; }
        public List<string>? ProjectName { get; set; }
        public List<string>? BuilderName { get; set; }
        public List<Guid>? ProjectSubType { get; set; }
        public long? MinPrice { get; set; }
        public long? MaxPrice { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? Search { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }
        public PossesionType? Possesion { get; set; }
        public List<string>? Locations { get; set; }
        public double? CarpetArea { get; set; }
        public Guid? CarpetAreaUnitId { get; set; }
        public List<Guid>? AmenitesIds { get; set; }
        public string? Currency { get; set; }
        public List<Facing>? Facings { get; set; }
    }
    public class GetAllProjectAnonymousRequestHandler : ProjectCommonRequestHandler, IRequestHandler<GetAllProjectAnonymousRequest, PagedResponse<PullViewProjectDto, string>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepository;
        private readonly IBlobStorageService _blobStorage;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IProjectRepository _projectRepository;
        public GetAllProjectAnonymousRequestHandler(
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepository,
            IBlobStorageService blobStorage,
            IServiceProvider serviceProvider,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser,
            IProjectRepository projectRepository) : base(serviceProvider)
        {
            _globalSettingsRepository = globalSettingsRepository;
            _blobStorage = blobStorage;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _projectRepository = projectRepository;
        }
        public async Task<PagedResponse<PullViewProjectDto, string>> Handle(GetAllProjectAnonymousRequest request, CancellationToken cancellationToken)
        {
            Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            if (globalSettings?.CanAccessAnonymousApis != true)
            {
                throw new UnauthorizedAccessException("Access to projects is restricted.");
            }
            //var projects = await _projectRepo.ListAsync(new ProjectByCustomFilterSpec(request), cancellationToken);
            //var count = await _projectRepo.CountAsync(new ProjectByCustomFilterSpec(request), cancellationToken);
            var projects = await _projectRepository.GetAnonymousProjectsForWebAsync(request);
            var count = await _projectRepository.GetAnonymousProjectsCountForWebAsync(request);
            var result = projects.Adapt<List<PullViewProjectDto>>();
            foreach (var project in result.Where(p => p?.Images != null))
            {
                project.Images?.Values
                    .Where(imageList => imageList != null)
                    .SelectMany(imageList => imageList)
                    .Where(image => !string.IsNullOrEmpty(image.ImageFilePath) &&
                                    !image.ImageFilePath.StartsWith(_blobStorage.AWSS3BucketUrl, StringComparison.OrdinalIgnoreCase))
                    .ToList()
                    .ForEach(image => image.ImageFilePath = $"{_blobStorage.AWSS3BucketUrl}{image.ImageFilePath}");
            }
            foreach (var project in projects.Where(p => p.Amenities != null))
            {
                var amenityIds = project.Amenities?.Select(a => a.MasterProjectAmenityId).ToList();

                var amenities = await _dapperRepository.GetAllAmenitiesForProjetAsync(_currentUser.GetTenant() ?? string.Empty, amenityIds);

                var matchingProject = result.FirstOrDefault(res => res.Id == project.Id);
                if (matchingProject != null)
                {
                    matchingProject.Amenities = amenities;
                }
            }
            return new PagedResponse<PullViewProjectDto, string>(result, count);
        }
    }
}
