﻿using Lrb.Application.Property.Web.V2.Dtos;

namespace ListingBackgroundJobs
{
    public interface IFunctionEntryPoint
    {
        Task<bool> PublishListingAsync(ListAndDelistListingDtoV2 dto, string tenantId, Guid currentUserId);

        Task<bool> DeleteListingAsync(ListAndDelistListingDtoV2 dto, string tenantId, Guid currentUserId);

        Task<bool> UpdateListingAsync(ListAndDelistListingDtoV2 dto, string tenantId, Guid currentUserId);

        Task<bool> PermanentDeleteListingAsync(ListAndDelistListingDtoV2 dto, string tenantId, Guid currentUserId);

        Task<bool> MapListingDetailsToLeadsAsync(MapListingDetailsToLeads dto, string tenantId);

    }
}
