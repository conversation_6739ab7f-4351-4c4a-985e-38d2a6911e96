﻿using Dapper;
using Microsoft.Extensions.Options;
using Npgsql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace LrbIntegrationBackgroundJobs
{
    public class TenantIndependentRepository : ITenantIndependentRepository
    {
        private readonly DatabaseSettings _settings;
        public TenantIndependentRepository(IOptions<DatabaseSettings> options)
        {
            _settings = options.Value;
        }

        public async Task SetLeadStatusToPending()
        {
            var tenants = await GetAllTenantsAsync();
            try
            {
                foreach (var tenant in tenants)
                {
                    await GetLeadStatusIdsAsync(tenant.Id, tenant.ConnectionString);
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<List<TenantInfo>> GetAllTenantsAsync()
        {
            var tenantQuery = "Select \"Id\",\"ConnectionString\" from \"MultiTenancy\".\"Tenants\" where \"IsActive\" = 'true' ";
            var conn = new NpgsqlConnection(_settings.ConnectionString);
            var result = await conn.QueryAsync<TenantInfo>(tenantQuery);
            return result.ToList();
        }
        private async Task GetLeadStatusIdsAsync(string tenant, string? connectionString)
        {
            string status = "new";
            string PendingStatus = "pending";
            Guid StatusIdForNew = Guid.Empty;
            Guid StatusIdForPending = Guid.Empty;
            var conn = new NpgsqlConnection(string.IsNullOrEmpty(connectionString) ? _settings.ConnectionString : connectionString);
            try
            {
                await conn.OpenAsync();
                var commandText = $"SELECT \"Id\" FROM \"LeadratBlack\".\"CustomMasterLeadStatuses\" where \"Status\" = @status and \"TenantId\" = @tenantId and \"IsDeleted\" = false";
                NpgsqlCommand cmd = new(commandText, conn);
                cmd.CommandType = System.Data.CommandType.Text;
                cmd.Parameters.AddWithValue("status", status);
                cmd.Parameters.AddWithValue("tenantId", tenant);
                var reader = await cmd.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    StatusIdForNew = Guid.Parse(reader["Id"].ToString() ?? Guid.Empty.ToString());

                }
                await reader.CloseAsync();


                var commandTxt = $"Select \"Id\" from \"LeadratBlack\".\"CustomMasterLeadStatuses\" where \"Status\" = @status and \"TenantId\" = @tenantId and \"IsDeleted\" = false";
                var commamndForPending = new NpgsqlCommand(commandTxt, conn);
                commamndForPending.CommandType = System.Data.CommandType.Text;
                commamndForPending.Parameters.AddWithValue("status", PendingStatus);
                commamndForPending.Parameters.AddWithValue("tenantId", tenant);
                var readerForPendingStatus = await commamndForPending.ExecuteReaderAsync();
                while (await readerForPendingStatus.ReadAsync())
                {
                    StatusIdForPending = Guid.Parse(readerForPendingStatus["Id"].ToString() ?? Guid.Empty.ToString());
                }
                await readerForPendingStatus.CloseAsync();
            }
            catch { }
            finally { await conn.CloseAsync(); }
            await GeLeadIdsAsync(StatusIdForNew, StatusIdForPending, connectionString);

        }
        private async Task GeLeadIdsAsync(Guid newStatusId, Guid pendingStatusId, string? connectionString)
        {
            List<Guid> leadIds = new();
            var conn = new NpgsqlConnection(string.IsNullOrEmpty(connectionString) ? _settings.ConnectionString : connectionString);
            try
            {
                await conn.OpenAsync();
                var query = $"Select \"Id\" from \"LeadratBlack\".\"Leads\" where \"CustomLeadStatusId\" = @StatusIdForNew and (\"CreatedOn\" + interval '24:00') < now()";
                var commamnd = new NpgsqlCommand(query, conn);
                commamnd.CommandType = System.Data.CommandType.Text;
                commamnd.Parameters.AddWithValue("StatusIdForNew", newStatusId);
                var reader = await commamnd.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    leadIds.Add(Guid.Parse(reader["Id"].ToString() ?? Guid.Empty.ToString()));
                }
                await reader.CloseAsync();
            }
            catch { }
            finally { await conn.CloseAsync(); }
            await UpdateLeadStatusesAsync(leadIds, pendingStatusId, connectionString);


        }
        private async Task UpdateLeadStatusesAsync(List<Guid> leadIds, Guid pendingStatusId, string? connectionString)
        {
            var conn = new NpgsqlConnection(string.IsNullOrEmpty(connectionString) ? _settings.ConnectionString : connectionString);
            try
            {
                await conn.OpenAsync();
                var query = $"UPDATE \"LeadratBlack\".\"Leads\" Set  \"CustomLeadStatusId\"= @statusId, \"LastModifiedOn\"= @modifiedDate where \"Id\" = Any (@LeadIds)";
                var command = new NpgsqlCommand(query, conn);
                command.Parameters.AddWithValue("LeadIds", leadIds);
                command.Parameters.AddWithValue("statusId", pendingStatusId);
                command.Parameters.AddWithValue("modifiedDate", DateTime.UtcNow);
                int affectedRows = command.ExecuteNonQuery();
            }
            catch { }
            finally { await conn.CloseAsync(); }
            await GetLeadHistoryAsync(leadIds, connectionString);
        }
        private async Task GetLeadHistoryAsync(List<Guid> leadIds, string? connectionString)
        {
            List<LeadStatusHistoryDto> leadStatusHistoryDtos = new();
            var conn = new NpgsqlConnection(string.IsNullOrEmpty(connectionString) ? _settings.ConnectionString : connectionString);
            try
            {
                await conn.OpenAsync();
                var query = $"Select distinct on (\"LeadId\") \"Id\", \"BaseLeadStatus\", \"CurrentVersion\", \"ModifiedDate\" from \"LeadratBlack\".\"LeadHistories\" where \"LeadId\" = Any (@leadids) order by \"LeadId\", \"CreatedDate\" DESC";
                var commamnd = new NpgsqlCommand(query, conn);
                commamnd.CommandType = System.Data.CommandType.Text;
                commamnd.Parameters.AddWithValue("leadids", leadIds);
                var reader = await commamnd.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    leadStatusHistoryDtos.Add(new LeadStatusHistoryDto
                    {
                        Id = Guid.Parse(reader["Id"].ToString() ?? Guid.Empty.ToString()),
                        Status = reader["BaseLeadStatus"].ToString() ?? string.Empty,
                        CurrentVersion = (int)reader["currentVersion"],
                        ModifiedDate = reader["ModifiedDate"].ToString() ?? string.Empty

                    });
                }
                await reader.CloseAsync();
            }
            catch { }
            finally { await conn.CloseAsync(); }
            await UpdateLeadHistoryAsync(leadStatusHistoryDtos, connectionString);
        }
        private async Task UpdateLeadHistoryAsync(List<LeadStatusHistoryDto> leadHistorys, string? connectionString)
        {
            foreach (var leadHistory in leadHistorys)
            {
                var keyValuePair = JsonSerializer.Deserialize<Dictionary<int, string>>(leadHistory.Status);
                keyValuePair.Add(leadHistory.CurrentVersion + 1, "Pending");
                var ModifiedDatekeyValuePair = JsonSerializer.Deserialize<Dictionary<int, DateTime>>(leadHistory.ModifiedDate);
                ModifiedDatekeyValuePair.Add(leadHistory.CurrentVersion + 1, DateTime.UtcNow);
                var LastModifiedByUserkeyValuePair = new Dictionary<int, Guid>();
                LastModifiedByUserkeyValuePair.Add(leadHistory.CurrentVersion + 1, Guid.Empty);
                var LastModifiedBySystemkeyValuePair = new Dictionary<int, string>();
                LastModifiedBySystemkeyValuePair.Add(leadHistory.CurrentVersion + 1, "System");
                var updatedCurrentVersion = leadHistory.CurrentVersion + 1;
                var conn = new NpgsqlConnection(string.IsNullOrEmpty(connectionString) ? _settings.ConnectionString : connectionString);
                try
                {
                    await conn.OpenAsync();
                    var query = $"UPDATE \"LeadratBlack\".\"LeadHistories\"  Set  \"BaseLeadStatus\" = @updatedStatusHistory, \"CurrentVersion\"= @updatedCurrentVersion, \"ModifiedDate\"= @modifiedDate, \"LastModifiedByUser\" = @lastModifiedByUser, \"LastModifiedBy\" = @lastModifiedBy where \"Id\" = @leadId";
                    var command = new NpgsqlCommand(query, conn);
                    command.Parameters.AddWithValue("leadId", leadHistory.Id);
                    command.Parameters.AddWithValue("updatedCurrentVersion", updatedCurrentVersion);
                    command.Parameters.Add(new NpgsqlParameter { ParameterName = "updatedStatusHistory", DataTypeName = "jsonb", Value = keyValuePair });
                    command.Parameters.Add(new NpgsqlParameter { ParameterName = "modifiedDate", DataTypeName = "jsonb", Value = ModifiedDatekeyValuePair });
                    command.Parameters.Add(new NpgsqlParameter { ParameterName = "lastModifiedByUser", DataTypeName = "jsonb", Value = LastModifiedByUserkeyValuePair });
                    command.Parameters.Add(new NpgsqlParameter { ParameterName = "lastModifiedBy", DataTypeName = "jsonb", Value = LastModifiedBySystemkeyValuePair });
                    int affectedRows = command.ExecuteNonQuery();
                }
                catch { }
                finally { await conn.CloseAsync(); }
            }
        }
    }
    public class TenantInfo
    {
        public string ConnectionString { get; set; }
        public string Id { get; set; }
    }
}
