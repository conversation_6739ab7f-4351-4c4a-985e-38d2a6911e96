﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web
{

    public class GetAllLeadCountsByBaseFiltersNewRequest : GetAllLeadsParametersNewFilters, IRequest<Response<LeadCountsByNewBaseFiltersDto>>
    {

    }
    public class GetAllLeadCountsByBaseFiltersNewRequestHandler : <PERSON><PERSON><PERSON><PERSON><PERSON>s<PERSON><PERSON>monHandler, IRequestHandler<GetAllLeadCountsByBaseFiltersNewRequest, Response<LeadCountsByNewBaseFiltersDto>>
    {
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetails;

        public GetAllLeadCountsByBaseFiltersNewRequestHandler(
            IDapperRepository dapperRepository,
            ILeadRepository efLeadRepository,
            ICurrentUser currentUser,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customMasterLeadStatusRepo,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetails)
            : base(currentUser, dapperRepository, efLeadRepository, customMasterLeadStatusRepo)
        {
            _leadRepositoryAsync = leadRepositoryAsync;
            _userDetails = userDetails;
        }
        public async Task<Response<LeadCountsByNewBaseFiltersDto>> Handle(GetAllLeadCountsByBaseFiltersNewRequest request, CancellationToken cancellationToken)
        {
            if (request?.LeadTags?.Any() ?? false)
            {
                request.TagFilterDto = GetLeadTagFilter(request.Adapt<GetAllLeadsByNewFiltersRequest>());
                request.LeadTags = null;
            }
            if (request?.DesignationsId?.Any() ?? false)
            {
                var users = await _userDetails.ListAsync(new Lrb.Application.Dashboard.Web.Specs.GetUsersByDesignationIdSpec(request.DesignationsId));
                var userIds = users.Select(i => i.UserId).ToList();
                if (request.AssignTo == null)
                {
                    request.AssignTo = new List<Guid>();
                }
                request.AssignTo.AddRange(userIds);

            }
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var isAdmin = _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty).Result;
            //request.UserType = request?.UserType ?? UserType.None;
            List<Guid> leadHistoryIds = new();
            List<Guid> subIds = new();
            try
            {
                if (request?.AssignTo?.Any() ?? false)
                {
                    if (request?.IsWithTeam ?? false)
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, tenantId ?? string.Empty)).ToList();
                    }
                    else
                    {
                        subIds = request?.AssignTo ?? new List<Guid>();
                    }
                }
                else
                {
                    if (request?.IsOnlyReportees ?? false)
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty))?.ToList() ?? new();
                    }
                    else
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllLeads))?.ToList() ?? new();
                    }
                }
               
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllLeadCountsByBaseFiltersNewRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            if (request?.IsDualOwnershipEnabled == null)
            {
                request.IsDualOwnershipEnabled = await _dapperRepository.GetDualOwnershipDetails(tenantId ?? string.Empty);
            }
            LeadCountsByNewFilterDto leadCounts = new();
            var types = typeof(LeadCountsByNewBaseFiltersDto).GetProperties();
            var customStatus = await _customMasterLeadStatusRepo.ListAsync(cancellationToken);
            if (request?.ShouldShowBookedDetails == true)
            {
                var query = await _efLeadRepository.BuildQueryForLeadsCount(request.Adapt<GetAllLeadsByNewFiltersRequest>(), subIds, userId, leadHistoryIds, customStatus,tenantId, isAdmin);
                foreach (var type in types)
                {
                    leadCounts = await AddLeadsCount(leadCounts, type, request.Adapt<GetAllLeadsByNewFiltersRequest>(), subIds, userId, leadHistoryIds, customStatus, query, isAdmin: isAdmin);
                }
            }
            else
            {
                var query = await _efLeadRepository.BuildQueryForLeadsCount(request.Adapt<GetAllLeadsByNewFiltersRequest>(), subIds, userId, leadHistoryIds, customStatus, tenantId, isAdmin);
                var newRequest = request.Adapt<GetAllLeadsByNewFiltersRequest>();
                foreach (var type in types)
                {
                    leadCounts = await AddLeadsCount(leadCounts, type, newRequest, subIds, userId, leadHistoryIds,customStatus, query, isAdmin: isAdmin);
                }
            }
            return new(leadCounts.Adapt<LeadCountsByNewBaseFiltersDto>());
        }
    }
}
