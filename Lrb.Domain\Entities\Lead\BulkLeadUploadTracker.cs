﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Domain.Entities
{
    public class BulkLeadUploadTracker : AuditableEntity, IAggregateRoot
    {
        public int TotalCount { get; set; }
        public int DistinctLeadCount { get; set; }
        public int TotalUploadedCount { get; set; }
        public int DuplicateCount { get; set; }
        public int LeadsUpdatedCount { get; set; }
        public int InvalidCount { get; set; }
        public string? S3BucketKey { get; set; }
        public string? InvalidDataS3BucketKey { get; set; }
        public string? SheetName { get; set; }

        [Column(TypeName = "jsonb")]
        public Dictionary<DataColumns, string>? MappedColumnsData { get; set; }
        [Column(TypeName = "jsonb")]
        public List<string>? UserIds { get; set; }
        public UploadStatus Status { get; set; }
        public string? Message { get; set; }
        public LeadCreateType CreateType { get; set; }
    }
}
