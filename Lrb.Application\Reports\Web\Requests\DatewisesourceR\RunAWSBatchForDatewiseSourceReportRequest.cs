﻿using Lrb.Application.Common.AWS_Batch;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.Dashboard.Web;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Reports.Web.Requests.DatewiseSourceCount
{
    public class RunAWSBatchForDatewiseSourceReportRequest : IRequest<Response<Guid>>
    {
        public DateType? DateType { get; set; }
        public bool? IsWithTeam { get; set; }
        public List<LeadSource>? LeadSources { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public List<string>? Projects { get; set; }
        public List<string>? SubSources { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<Guid>? UserIds { get; set; }
        public DateTime? ToDateForLeadReceived { get; set; }    
        public DateTime? FromDateForLeadReceived { get; set; }
        public LeadGeneratingFrom? GeneratingFrom { get; set; }
        public List<string>? ToRecipients { get; set; } = new();
        public List<string>? CcRecipients { get; set; } = new();
        public List<string>? BccRecipients { get; set; } = new();
        public int PageNumber { get; set; }
        public int PageSize { get; set; } = int.MaxValue;
        public string? FileName { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
        public string? SearchText { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public string? S3BucketKey { get; set; }


    }

    public class RunAWSBatchForDatewiseSourceReportRequestHandler : IRequestHandler<RunAWSBatchForDatewiseSourceReportRequest, Response<Guid>>
    {
        private readonly IAWSBatchService _batchService;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<ExportReportsTracker> _exportRepo;
        public const string TYPE = "recieveddatebysource";
        private readonly IServiceBus _serviceBus;

        public RunAWSBatchForDatewiseSourceReportRequestHandler(IAWSBatchService batchService,
            ICurrentUser currentUser,
            IRepositoryWithEvents<ExportReportsTracker> exportRepo,
            IServiceBus serviceBus)
        {
            _batchService = batchService;
            _currentUser = currentUser;
            _exportRepo = exportRepo;
            _serviceBus = serviceBus;
        }

        public async Task<Response<Guid>> Handle(RunAWSBatchForDatewiseSourceReportRequest request, CancellationToken cancellationToken)
        {
            try
            {
                ExportReportsTracker tracker = new();
                tracker.Request = JsonConvert.SerializeObject(request);
                tracker.ToRecipients = request.ToRecipients;
                tracker.CcRecipients = request.CcRecipients;
                tracker.BccRecipients = request.BccRecipients;
                tracker.Type = TYPE;
                var tenantId = _currentUser.GetTenant();
                var currentUserId = _currentUser.GetUserId();
                tracker.S3BucketKey = request?.S3BucketKey ?? string.Empty;
                await _exportRepo.AddAsync(tracker, cancellationToken);
                if (string.IsNullOrWhiteSpace(request?.S3BucketKey))
                {
                    InputPayload input = new(tracker.Id, tenantId ?? string.Empty, currentUserId, TYPE);
                    var stringArgument = JsonConvert.SerializeObject(input);
                    var cmdArgs = new List<string>() { stringArgument };
                    await _serviceBus.RunExcelExportJobAsync(cmdArgs);
                }
                return new(tracker.Id);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Something went wrong.");
            }
        }
    }
}
