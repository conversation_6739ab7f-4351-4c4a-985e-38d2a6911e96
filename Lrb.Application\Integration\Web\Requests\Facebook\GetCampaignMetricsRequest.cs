﻿using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Lrb.Application.Integration.Web.Requests.Facebook
{
    public class GetCampaignMetricsRequest : IRequest<Response<List<CampaignMetricsDto>>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public Guid AccountId { get; set; } = new();
        public string? CampaignAdAccountIds { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan? BaseUTcOffset { get; set; }
    }
    public class GetCampaignMetricsRequestHandler : IRequestHandler<GetCampaignMetricsRequest, Response<List<CampaignMetricsDto>>>
    {
        private readonly IRepositoryWithEvents<FacebookConnectedPageAccount> _fbPageRepo;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccInfoRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepository;
        private readonly IDapperRepository _dapperRepository;
        public GetCampaignMetricsRequestHandler(
            IRepositoryWithEvents<FacebookConnectedPageAccount> fbPageRepo,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Lead> leadRepo,
            ICurrentUser currentUser,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepository,
            IDapperRepository dapperRepository)
        {
            _fbPageRepo = fbPageRepo;
            _integrationRepo = integrationRepo;
            _leadRepo = leadRepo;
            _currentUser = currentUser;
            _integrationAccInfoRepo = integrationAccInfoRepo;
            _leadRepository = leadRepository;
            _dapperRepository = dapperRepository;
        }
        public async Task<Response<List<CampaignMetricsDto>>> Handle(GetCampaignMetricsRequest request, CancellationToken cancellationToken)
        {
            var results = new ConcurrentBag<CampaignMetricsDto>();
            var integrationInfo = await _integrationAccInfoRepo.FirstOrDefaultAsync(new IntegrationAccInfoByFacebookIdOrIdSpec(request.AccountId));
            var tenantId = _currentUser.GetTenant();
            TimeSpan timeZoneOffset = request.BaseUTcOffset ?? TimeZoneInfo.Local.GetUtcOffset(DateTime.UtcNow);
            var fromDate = (request.FromDate ?? DateTime.UtcNow.Date) + timeZoneOffset;
            var toDate = (request.ToDate ?? DateTime.UtcNow.Date) + timeZoneOffset;

            // Build a JSON string with double quotes (important!)
            var timeRangeJson = $"{{\"since\":\"{fromDate:yyyy-MM-dd}\",\"until\":\"{toDate:yyyy-MM-dd}\"}}";
            var fbPage = await _fbPageRepo.FirstOrDefaultAsync(
                new GetFacebookConnectedPageAccountByFBAuthIdSpec(integrationInfo?.FacebookAccountId ?? Guid.Empty),
                cancellationToken
            );

            if (string.IsNullOrWhiteSpace(request.CampaignAdAccountIds))
                return new();

            List<CampaignAdAccountInputDto> campaignAdAccounts;
            try
            {
                var decodedCampaignAdAccounts = System.Web.HttpUtility.UrlDecode(request.CampaignAdAccountIds) ?? request.CampaignAdAccountIds;
                campaignAdAccounts = System.Text.Json.JsonSerializer.Deserialize<List<CampaignAdAccountInputDto>>(decodedCampaignAdAccounts, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new();
            }
            catch (Exception ex)
            {
                return new();
            }
            var campaignIds = campaignAdAccounts.Where(x => !string.IsNullOrWhiteSpace(x.CampaignId)).Select(x => x.CampaignId).Distinct().ToArray();
            var revenueResults = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<FacebookCmapaignRevenueResult>(
               "LeadratBlack", "GetFacebookCampaignRevenueByIds",
               new { p_tenantid = tenantId, p_campaignids = campaignIds })).ToList();
            using var client = new HttpClient();
            const string baseUrl = "https://graph.facebook.com/v21.0";
            var accessToken = fbPage?.LongLivedPageAccessToken;
            var adAccountIds = campaignAdAccounts.Where(i => !string.IsNullOrWhiteSpace(i.AdAccountId)).Select(i => i.AdAccountId).Distinct().ToList();
            Dictionary<string, string> currencies = new();
            if (adAccountIds?.Any() ?? false)
            {
                currencies = await GetCurrenciesForAdAccountsBatchAsync(adAccountIds, accessToken, client, baseUrl, cancellationToken);
            }
            var campaignData = await GetCampaignDataBatchAsync(client, baseUrl, campaignIds.ToList(), accessToken, timeRangeJson, cancellationToken);
            if (campaignData != null)
            {
                foreach (var campaignId in campaignIds)
                {
                    if (campaignData.TryGetValue(campaignId, out var data))
                    {
                        var revenue = revenueResults.Where(r => r.CampaignId == campaignId).Sum(r => r.TotalRevenue);
                        decimal investment = data.Spend > 0 ? data.Spend : 0;
                        decimal? roi = null;

                        if (revenue > 0 && investment > 0)
                        {
                            roi = Math.Round(((revenue - investment) / investment) * 100, 2);
                        }

                        // Get a sample AdAccountId (from any ad in this campaign)
                        var adAccountId = campaignAdAccounts.FirstOrDefault(c => c.CampaignId == campaignId)?.AdAccountId;
                        var currency = !string.IsNullOrEmpty(adAccountId) && currencies.TryGetValue(adAccountId, out var c) ? c : "INR";

                        results.Add(new CampaignMetricsDto
                        {
                            CampaignId = campaignId,
                            CampaignBudget = data.Budget,
                            CostPerLead = data.CostPerLead.HasValue ? Math.Round(data.CostPerLead.Value, 2) : 0,
                            TotalRevenue = revenue,
                            RoiPercentage = Math.Round(roi ?? 0, 2),
                            FacebookLeadCount = data.Leads,
                            Currency = currency
                        });
                    }
                }

                return new(results.ToList());
            }
            return null;
        }
        private async Task<Dictionary<string, (decimal Budget, decimal Spend, int Leads, decimal? CostPerLead)>> GetCampaignDataBatchAsync(
        HttpClient client, string baseUrl, List<string> campaignIds, string token, string timeRange, CancellationToken ct)
        {
            try
            {
                var results = new Dictionary<string, (decimal Budget, decimal Spend, int Leads, decimal? CostPerLead)>();
                const int batchSize = 50;

                if (campaignIds == null || !campaignIds.Any())
                {
                    return results;
                }

                for (int i = 0; i < campaignIds.Count; i += batchSize)
                {
                    var batchAdIds = campaignIds.Skip(i).Take(batchSize).ToList();
                    var batch = batchAdIds.Select(campaignId => new
                    {
                        method = "GET",
                        relative_url = $"{campaignId}?fields=insights.time_range({timeRange}){{spend,actions,cost_per_action_type,date_start,date_stop}},budget_remaining,daily_budget"
                    }).ToList();

                    var batchJson = System.Text.Json.JsonSerializer.Serialize(batch);
                    var formContent = new FormUrlEncodedContent(new[]
                    {
                        new KeyValuePair<string, string>("batch", batchJson),
                        new KeyValuePair<string, string>("access_token", token)
                    });

                    var response = await client.PostAsync(baseUrl, formContent, ct);
                    response.EnsureSuccessStatusCode();

                    var json = await response.Content.ReadAsStringAsync();
                    var batchResults = System.Text.Json.JsonSerializer.Deserialize<List<BatchResponse>>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (batchResults == null)
                    {
                        continue;
                    }

                    foreach (var result in batchResults)
                    {
                        if (result?.Body == null) continue;

                        var data = System.Text.Json.JsonSerializer.Deserialize<CampaignResponse>(result.Body, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });

                        if (data?.Id == null) continue;

                        decimal budget = data?.DailyBudget != null && decimal.TryParse(data.DailyBudget, out var val) ? val / 100 : 0;
                        decimal spend = 0;
                        int leads = 0;
                        decimal? cpl = null;
                        if (data.Insights?.Data?.Count > 0)
                        {
                            var item = data.Insights.Data[0];
                            spend = item.Spend != null && decimal.TryParse(item.Spend, out var sp) ? sp : 0;
                            leads = item.Actions?.Where(x => x?.ActionType == "onsite_conversion.lead_grouped")
                                .Sum(x => int.TryParse(x.Value, out var val) ? val : 0) ?? 0;
                            cpl = leads > 0 ? spend / leads : (decimal?)0;
                        }

                        results[data.Id] = (budget, spend, leads, cpl);
                    }
                }

                return results;
            }
            catch (HttpRequestException ex)
            {

                return null;
            }
            catch (System.Text.Json.JsonException ex)
            {

                return null;
            }
            catch (Exception ex)
            {

                return null;
            }
        }
        public async Task<Dictionary<string, string>> GetCurrenciesForAdAccountsBatchAsync(List<string> adAccountIds, string accessToken, HttpClient httpClient, string baseUrl, CancellationToken ct = default)
        {
            try
            {
                var resultDict = new Dictionary<string, string>();
                const int maxBatchSize = 50;

                for (int i = 0; i < adAccountIds.Count; i += maxBatchSize)
                {
                    var batchAdIds = adAccountIds.Skip(i).Take(maxBatchSize).ToList();

                    var batch = batchAdIds.Select(adId => new
                    {
                        method = "GET",
                        relative_url = $"{adId}?fields=currency"
                    }).ToList();

                    var batchJson = System.Text.Json.JsonSerializer.Serialize(batch);
                    var formContent = new FormUrlEncodedContent(new[]
                    {
                    new KeyValuePair<string, string>("batch", batchJson),
                    new KeyValuePair<string, string>("access_token", accessToken)
                });

                    var response = await httpClient.PostAsync(baseUrl, formContent, ct);
                    response.EnsureSuccessStatusCode();

                    var content = await response.Content.ReadAsStringAsync(ct);
                    var batchResults = System.Text.Json.JsonSerializer.Deserialize<List<FacebookBatchResponse>>(content);

                    for (int j = 0; j < batchResults?.Count; j++)
                    {
                        var adAccountId = batchAdIds[j];

                        try
                        {
                            if (batchResults[j].code == 200 && !string.IsNullOrWhiteSpace(batchResults[j].body))
                            {
                                var accountInfo = System.Text.Json.JsonSerializer.Deserialize<FacebookAdAccountCurrencyResponse>(batchResults[j].body);

                                if (!string.IsNullOrWhiteSpace(accountInfo?.currency) && !string.IsNullOrWhiteSpace(accountInfo?.id))
                                {
                                    resultDict[accountInfo.id] = accountInfo.currency;
                                    continue;
                                }
                            }

                            Console.WriteLine($"Currency missing or invalid for AdAccountId: {adAccountId}, defaulting to USD.");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error parsing currency for AdAccountId {adAccountId}: {ex.Message}");
                        }

                        resultDict[adAccountId] = "USD";
                    }
                }

                return resultDict;
            }
            catch (HttpRequestException ex)
            {
                return null;
            }
            catch (System.Text.Json.JsonException ex)
            {
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
    }

    public class CampaignMetricsDto
    {
        public string? CampaignId { get; set; }
        public decimal? CampaignBudget { get; set; }
        public decimal? CostPerLead { get; set; }
        public decimal? RoiPercentage { get; set; }
        public decimal? TotalRevenue { get; set; }
        public int? FacebookLeadCount { get; set; }
        public string Currency { get; set; } = "USD";
    }
    public class CampaignAdAccountInputDto
    {
        public string? CampaignId { get; set; }
        public string? AdAccountId { get; set; }
    }
    public class FacebookCmapaignRevenueResult
    {
        public string CampaignId { get; set; }
        public decimal TotalRevenue { get; set; }
    }
    public class CampaignResponse
    {
        public string Id { get; set; }
        [JsonPropertyName("daily_budget")]
        public string? DailyBudget { get; set; }
        [JsonPropertyName("insights")]
        public InsightsResponse? Insights { get; set; }
    }
}
