﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.Property
{
    public class ListingComplianceConfig : IEntityTypeConfiguration<ListingCompliance>
    {
        public void Configure(EntityTypeBuilder<ListingCompliance> builder)
        {
            builder.IsMultiTenant();
        }
    }
}
