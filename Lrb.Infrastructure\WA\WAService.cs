﻿using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.WA;
using Lrb.Application.QRFormTemplate.Web;
using Lrb.Application.Utils;
using Lrb.Application.WA.Web;
using Lrb.Application.WA.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Enums;
using Newtonsoft.Json.Linq;
using RestSharp;
using SqlKata;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Lrb.Infrastructure.WA
{
    public class WAService : IWAService
    {
        private readonly IReadRepository<WAApiInfo> _waApiRepo;
        private readonly IReadRepository<WAPayloadMapping> _waPayloadRepo;
        public WAService(IReadRepository<WAApiInfo> waApiRepo,
            IReadRepository<WAPayloadMapping> waPayloadRepo
            )
        {
            _waApiRepo = waApiRepo;
            _waPayloadRepo = waPayloadRepo;
        }

        public async Task<List<WATemplate>> RetriveWATemaplteByName(string? templateName, WAApiInfo getTemplateApiInfo, WAPayloadMapping payloadmapping)
        {
            List<WATemplate> wATemplates = new List<WATemplate>();
            var client = new RestClient(getTemplateApiInfo?.URL ?? string.Empty);
            var request = new RestRequest();
            Dictionary<string, string> keyParams = new();
            foreach (var header in getTemplateApiInfo?.Headers ?? new Dictionary<string, string>())
            {
                request.AddHeader(header.Key, header.Value);
            }

            RestResponse restResponse = null;
            if (!string.IsNullOrWhiteSpace(templateName))
            {
                switch (getTemplateApiInfo?.MethodType?.ToLowerInvariant())
                {
                    case "get":
                        request.AddParameter("name", templateName);
                        break;
                    case "post":
                        request.AddJsonBody(new { templateName });
                        break;
                }
            }

            switch (getTemplateApiInfo?.MethodType?.ToLowerInvariant() ?? string.Empty)
            {
                case "get":
                    restResponse = await client.GetAsync(request);
                    break;
                case "post":
                    restResponse = await client.PostAsync(request);
                    break;
            }
            if (restResponse != null && restResponse.IsSuccessStatusCode)
            {
                var content = restResponse.Content;
                WATemplate template = new WATemplate();
                var result = JToken.Parse(content ?? string.Empty);

                if (result is JArray jArray)
                {
                    //var propertyMappings = payloadmapping.TemplateMapping?.FirstOrDefault(x => x.Key == WAApiAction.GetTemplates).Value;
                    //var templateMapping = propertyMappings?
                    //                        .Where(x => x.TargetObject == "TemplateProperty")
                    //                        .ToDictionary(x => x.SourceProperty, x => x.TargetProperty);
                    var filteredItems = jArray
                                        .Where(item =>
                                            item is JObject obj &&
                                            obj.TryGetValue("name", StringComparison.OrdinalIgnoreCase, out JToken? nameToken) &&
                                            string.Equals(nameToken?.ToString(), templateName, StringComparison.OrdinalIgnoreCase))
                                        .FirstOrDefault();
                    template.Name = templateName ?? string.Empty;
                    if (filteredItems != null) 
                    {
                        var variableString = ExtractTemplateVariablesFormatted(filteredItems.ToString());
                        template.Message = variableString;
                        if (!string.IsNullOrWhiteSpace(variableString))
                        {
                            template.BodyValues = StringHelper.ExtractVariablesAsDictionary(variableString);
                        }
                    }
                    template.IsEnabled = true;
                    wATemplates.Add(template);
                    #region Template Mapping Changes
                    //foreach (var item in filteredItems)
                    //{
                    //    if (item is JObject obj)
                    //    {
                    //        keyParams = JTokenHelper.GetWithArrayKeysAndValues(obj);

                    //        var mappedObject = new JObject();
                    //        foreach (var kvp in keyParams)
                    //        {
                    //            var mapping = templateMapping.FirstOrDefault(x => x.Value == kvp.Key);
                    //            if (!string.IsNullOrEmpty(mapping.Key))
                    //            {
                    //                mappedObject[mapping.Key] = kvp.Value;
                    //            }
                    //        }

                    //        var template = mappedObject.ToObject<WATemplate>();
                    //        template.Name = templateName;
                    //        template.Message = ConvertTemplateVariables(template.Message);
                    //        template.IsEnabled = true;
                    //        if (!string.IsNullOrWhiteSpace(template.Message) && Regex.IsMatch(template.Message, @"#([^\s#][\w\s]*[^\s#])#"))
                    //        {
                    //            template.BodyValues = StringHelper.ExtractVariablesAsDictionary(template.Message);
                    //        }
                    //        else if (string.IsNullOrWhiteSpace(template.Message))
                    //        {
                    //            template.Message = string.Empty;
                    //        }
                    //        wATemplates.Add(template);
                    //    }
                    //}
                    #endregion

                }
                else if (result is JObject jObject)
                {
                    if (jObject.TryGetValue("name", StringComparison.OrdinalIgnoreCase, out JToken? nameToken) &&
                        string.Equals(nameToken?.ToString(), templateName, StringComparison.OrdinalIgnoreCase))
                    {
                        template.Name = templateName ?? string.Empty;
                        if (jObject != null)
                        {
                            var variableString = ExtractTemplateVariablesFormatted(jObject.ToString());
                            template.Message = variableString;
                            if (!string.IsNullOrWhiteSpace(variableString))
                            {
                                template.BodyValues = StringHelper.ExtractVariablesAsDictionary(variableString);
                            }
                        }
                        template.IsEnabled = true;
                        wATemplates.Add(template);
                    }
                }
            }
            return wATemplates;
        }
        private static string ExtractTemplateVariablesFormatted(string message)
        {
            if (string.IsNullOrWhiteSpace(message)) return string.Empty;

            var matches = Regex.Matches(message, @"\{\{(\w+)\}\}");

            if (matches.Count == 0)
                return string.Empty;

            var formattedVariables = new List<string>();

            foreach (Match match in matches)
            {
                if (match.Groups.Count > 1)
                {
                    formattedVariables.Add($"#{match.Groups[1].Value}#");
                }
            }

            return string.Join(", ", formattedVariables);
        }



        public async Task<List<WATemplate>> RetrieveWATemplatesAsync()
        {
            var payloadMappings = await _waPayloadRepo.ListAsync(new GetWAPayloadMappingSpec());
            var waApiInfos = await _waApiRepo.ListAsync(new GetWAApiInfoSpec());

            var templateApiInfo = waApiInfos.FirstOrDefault(x => x.WAApiAction == WAApiAction.SendTextWithMediaTemplate || x.WAApiAction == WAApiAction.SendTextTemplate);

            if (!waApiInfos.Any(x => x.WAApiAction == WAApiAction.GetTemplates))
                throw new InvalidOperationException("API info for template sync not found.");

            if (!payloadMappings.Any(x => x.TemplateMapping!= null && x.TemplateMapping.ContainsKey(WAApiAction.GetTemplates)))
                throw new InvalidOperationException("Template Mapping for sync not found.");

            var getTemplateApiInfo = waApiInfos.LastOrDefault(x => x.WAApiAction == WAApiAction.GetTemplates);

            var payloadParam = payloadMappings.LastOrDefault(x => x.TemplateMapping != null && x.TemplateMapping.ContainsKey(WAApiAction.GetTemplates));

            var client = new RestClient(getTemplateApiInfo?.URL ?? string.Empty);
            var request = new RestRequest();

            foreach (var header in getTemplateApiInfo?.Headers ?? new Dictionary<string, string>())
            {
                request.AddHeader(header.Key, header.Value);
            }

            RestResponse restResponse = null;

            switch (getTemplateApiInfo?.MethodType?.ToLowerInvariant() ?? string.Empty)
            {
                case "get":
                    restResponse = await client.GetAsync(request);
                    break;
                case "post":
                    restResponse = await client.PostAsync(request);
                    break;
            }

            if (restResponse != null && restResponse.IsSuccessStatusCode)
            {
                var content = restResponse.Content;
                var result = JToken.Parse(content ?? string.Empty);

                var templates = MapWATemplates(result ?? new JObject(), payloadParam);

                SynchronizeWATemplates(templates);

                templates.ForEach(x => x.WAApiInfo = templateApiInfo);

                return templates;
            }

            return new List<WATemplate>();
        }

        private List<WATemplate> MapWATemplates(JToken result, WAPayloadMapping payloadMapping)
        {
            var PropertyMapping = payloadMapping.TemplateMapping.First(x => x.Key == WAApiAction.GetTemplates).Value;

            var templateResponseMapping = PropertyMapping.Where(x => x.TargetObject == "Result")
               .ToDictionary(d => d.SourceProperty, d => d.TargetProperty);

            var templateMapping = PropertyMapping.Where(x => x.TargetObject == "TemplateProperty")
                .ToDictionary(d => d.SourceProperty, d => d.TargetProperty);

            var buttonPropertyMapping = PropertyMapping.Where(x => x.TargetObject == "ButtonProperty")
                .ToDictionary(d => d.SourceProperty, d => d.TargetProperty);

            var mediaTypeMapping = PropertyMapping.Where(x => x.TargetObject == "MediaTypeMapping")
                .ToDictionary(d => d.SourceProperty, d => d.TargetProperty);

            var templates = new List<WATemplate>();

            JToken dataArray = new JArray();
            bool isSuccess = false;

            var templateProperties = GetPropertyKeyValuePairs<WATemplate>(new WATemplate());
            var templateButtonProperties = GetPropertyKeyValuePairs<WAButton>(new WAButton());

            if (result.Type == JTokenType.Object)
            {
                string succeededKey = string.Empty;
                string dataKey = string.Empty;

                templateResponseMapping?.TryGetValue("Data", out dataKey);
                templateResponseMapping?.TryGetValue("Succeeded", out succeededKey);


                var success = result[succeededKey ?? string.Empty];

                isSuccess = success switch
                {
                    JValue jValue when jValue.Type == JTokenType.Boolean => (bool)jValue,
                    JValue jValue when jValue.Type == JTokenType.String =>
                        ((string)jValue).ToLowerInvariant().Contains("success") ||
                        ((string)jValue).ToLowerInvariant().Contains("true"),
                    _ => false
                };
                dataArray = result[dataKey ?? string.Empty] as JArray ?? new JArray();
            }
            else if (result.Type == JTokenType.Array)
            {
                isSuccess = true;
                dataArray = result;
            }

            if (isSuccess)
            {
                foreach (var data in dataArray)
                {
                    var dataJObject = JObject.FromObject(data);
                    var mappedObject = new JObject();

                    var buttonKeyName = templateResponseMapping["Buttons"];
                    var buttonArray = dataJObject[buttonKeyName] as JArray ?? new JArray();
                    int orderRank = 1;
                    var waButtons = new List<WAButton>();
                    foreach (var button in buttonArray)
                    {
                        string buttonKeyNmae = buttonPropertyMapping["Name"];
                        string buttonKeyType = buttonPropertyMapping["Type"];
                        string buttonKeyValue = buttonPropertyMapping["Value"];
                        //string buttonCountryCode = buttonPropertyMapping["CountryCode"];
                        buttonPropertyMapping.TryGetValue("CountryCode", out string? buttonCountryCode);

                        var type = Enum.TryParse<WAButtonType>(button[buttonKeyType]?.ToString(), out var parsedType)
                        ? parsedType : WAButtonType.None;

                        waButtons.Add(new WAButton
                        {
                            OrderRank = orderRank,
                            Name = button[buttonKeyNmae]?.ToString(),
                            Value = (string.IsNullOrEmpty(buttonCountryCode)
                                ? button[buttonKeyValue]?.ToString()
                                : (button[buttonCountryCode] + button[buttonKeyValue]?.ToString()))
                                ?? string.Empty,
                            Type = type,
                        });
                        orderRank++;

                    }
                    foreach (var property in dataJObject.Properties())
                    {
                        var originalKey = property.Name;
                        var value = property.Value;

                        var mappedKey = templateMapping.FirstOrDefault(x => x.Value == originalKey).Key;
                        if (!string.IsNullOrEmpty(mappedKey))
                        {
                            var propertyName = templateProperties.FirstOrDefault(x => x.Value == mappedKey).Key;

                            if (!string.IsNullOrEmpty(propertyName))
                            {
                                if (mediaTypeMapping != null)
                                {
                                    if (mappedKey == "MediaType")
                                    {
                                        var mediaType = mediaTypeMapping?.FirstOrDefault(x => x.Value == value?.ToString()).Key;
                                        mappedObject[propertyName] = mediaType ?? null;
                                    }
                                    else
                                    {
                                        mappedObject[propertyName] = value;
                                    }
                                }
                                else
                                {
                                    mappedObject[propertyName] = value;
                                }
                            }
                        }
                    }
                    var template = mappedObject.ToObject<WATemplate>();
                    if (template != null && !string.IsNullOrEmpty(template.Name))
                    {
                        template.WAButtons = waButtons;
                        templates.Add(template);
                    }
                }
            }

            return templates;
        }

        private void SynchronizeWATemplates(List<WATemplate> templates)
        {
            string pattern = @"\{\{\d+\}\}";

            foreach (var template in templates)
            {
                template.IsEnabled = !(Regex.IsMatch(template.Message, pattern) || Regex.IsMatch(template.Header ?? string.Empty, pattern));

                if (!string.IsNullOrEmpty(template.MediaURL))
                {
                    template.HeaderValues = new Dictionary<int, string>
                    {
                        { 1, template.MediaURL }
                    };
                }
                else if (string.IsNullOrEmpty(template.MediaType))
                {
                    template.MediaType = "Text";
                }
                template.BodyValues = StringHelper.ExtractVariablesAsDictionary(template.Message);
            }
        }

        public Dictionary<string, string> GetPropertyKeyValuePairs<T>(object model)
        {
            var keyValuePairs = new Dictionary<string, string>();
            Type type = typeof(T);
            PropertyInfo[] properties = type.GetProperties();

            foreach (PropertyInfo property in properties)
            {
                string key = property.Name;
                keyValuePairs[key] = $"{key}";
            }

            return keyValuePairs;
        }

    }
}
