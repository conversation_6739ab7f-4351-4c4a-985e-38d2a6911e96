﻿using Dapper;
using Finbuckle.MultiTenant;
using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.DataManagement.Web.Dtos.CustomData;
using Lrb.Application.MasterData;
using Lrb.Application.ModifiedDate.Mobile;
using Lrb.Application.Multitenancy;
using Lrb.Application.QRFormTemplate.Web.Dtos;
using Lrb.Application.UserDetails.Web;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Integration.GoogleSheet;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Multitenancy;
using Lrb.Infrastructure.Persistence.Configuration;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Npgsql;
using System.Data;

namespace Lrb.Infrastructure.Persistence.Repository
{
    public class NpgsqlRepository : INpgsqlRepository
    {
        private readonly DatabaseSettings _settings;
        private readonly ITenantInfo? _tenantInfo;
        private IBlobStorageService _blobStorageService;
        private ICurrentUser _currentUser;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IDapperRepository _dapperRepository;
        private readonly IDisplayIndexPrefixService _displayIndexPrefixService;

        public NpgsqlRepository(IOptions<DatabaseSettings> options, ITenantInfo tenantInfo, IBlobStorageService blobStorageService, ICurrentUser currentUser, ILeadRepositoryAsync leadRepositoryAsync, IDapperRepository dapperRepository, IDisplayIndexPrefixService displayIndexPrefixService)
        {
            _settings = options.Value;
            _tenantInfo = tenantInfo;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
            _leadRepositoryAsync = leadRepositoryAsync;
            _dapperRepository = dapperRepository;
            _displayIndexPrefixService = displayIndexPrefixService;
        }
        private string GetConnectionStringAsync()
        {
            return string.IsNullOrEmpty(_tenantInfo?.ConnectionString) ? _settings.ConnectionString : _tenantInfo.ConnectionString;
        }
        public  Dictionary<string, string> GetDubaiConnections()
        {
            return new Dictionary<string, string>()
            {
                { _settings.DubaiConnectionString ?? string.Empty, _settings.DubaiReadReplicaConnectionString ?? string.Empty }
            };
        }

        public async Task<List<Guid>> GetAllAssignedUsersAsync(Guid roleId)
        {
            List<Guid> userids = new();
            var conn = new NpgsqlConnection(GetConnectionStringAsync());
            try
            {
                await conn.OpenAsync();
                var query = $"SELECT \"UserId\" FROM \"Identity\".\"UserRoles\" where \"RoleId\" = '{roleId}' and \"TenantId\" = '{_tenantInfo?.Id ?? string.Empty}';";
                var commamnd = new NpgsqlCommand(query, conn);
                commamnd.CommandType = System.Data.CommandType.Text;
                var reader = await commamnd.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    userids.Add(Guid.Parse(reader["UserId"].ToString() ?? Guid.Empty.ToString()));
                }
                await reader.CloseAsync();
                return userids;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> GetAllAssignedUsersAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            finally { conn.Close(); }

        }
        public async Task<bool> CreateDefaultSettingsAsync(string tenantId, string? connectionString = null)
        {
            var notificationSettings = CreateNotificationSettings();
            var callSettings = CreateCallSettings();
            var conn = new NpgsqlConnection(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync());
            try
            {
                await conn.OpenAsync();
                var query = $"INSERT INTO \"LeadratBlack\".\"GlobalSettings\"( \"Id\", \"NotificationSettings\", \"TenantId\", \"CreatedBy\", \"CreatedOn\", \"LastModifiedBy\", \"LastModifiedOn\",\"IsDeleted\",\"CallSettings\",\"IsZoneLocationEnabled\",\"IsMicrositeFeatureEnabled\") VALUES ('{Guid.NewGuid()}', '{notificationSettings}', '{tenantId}', '{Guid.Empty}', '{DateTime.UtcNow:yyyy/MM/dd}','{Guid.Empty}', '{DateTime.UtcNow:yyyy/MM/dd}',{false},'{callSettings}',{true} ,{true});";
                var commamnd = new NpgsqlCommand(query, conn);
                commamnd.CommandType = System.Data.CommandType.Text;
                commamnd.ExecuteNonQuery();
                await conn.CloseAsync();
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> CreateDefaultSettingsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            finally { conn.Close(); }
        }
        public async Task<bool> CreateDuplicateFeatureSettings(string tenantId, string? connectionString = null)
        {
            List<Guid> StatusIds = new() { Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"), Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a") };

            var conn = new NpgsqlConnection(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync());
            try
            {
                await conn.OpenAsync();
                var query = $"INSERT INTO \"LeadratBlack\".\"DuplicateFeatureInfo\"( \"Id\", \"IsFeatureAdded\", \"TenantId\", \"IsDeleted\", \"IsLocationBased\", \"IsProjectBased\", \"IsSourceBased\",\"IsSubSourceBased\",\"StutusIds\") " +
                    $"VALUES ('{Guid.NewGuid()}', 'false', '{tenantId}', 'false', 'false','false', 'false','false','{JsonConvert.SerializeObject(StatusIds)}');";
                var commamnd = new NpgsqlCommand(query, conn);
                commamnd.CommandType = System.Data.CommandType.Text;
                commamnd.ExecuteNonQuery();
                await conn.CloseAsync();
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex.Message,
                    ErrorSource = ex.Source,
                    StackTrace = ex.StackTrace,
                    ErrorModule = "NpgsqlRepository -> CreateDuplicateFeatureSettings()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            finally { conn.Close(); }
        }

        public async Task<bool> CreateDifferentDatabseTenantInfo(CreateTenantRequest request, string? connectionString = null)
        {
            if (!string.IsNullOrWhiteSpace(connectionString))
            {
                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    await conn.OpenAsync();
                    var prefix = _displayIndexPrefixService.GenerateUniquePrefix(request.Id);
                    var query = $"INSERT INTO \"MultiTenancy\".\"Tenants\"(\r\n\t\"Id\", \"Identifier\", \"Name\", \"ConnectionString\", \"AdminEmail\", \"IsActive\", \"ValidUpto\", \"Issuer\", \"DisplayPrefix\", \"ResolutionKeys\", \"ReadReplicaConnectionString\")" +
                        $"VALUES ('{request.Id}', '{request.Id}', '{request.Name}', '{request.ConnectionString}', '{request.AdminEmail}','{true}' ,'{request.ValidUpto}', '{request.Issuer}', '{prefix}', '{{}}', '{request.ReadReplicaConnectionString}');";
                    var commamnd = new NpgsqlCommand(query, conn);
                    commamnd.CommandType = System.Data.CommandType.Text;
                    commamnd.ExecuteNonQuery();
                    await conn.CloseAsync();
                    return true;
                }
                catch(Exception ex) { }
                finally { conn.Close(); }
            }
            return false;
        }
        private string CreateNotificationSettings()
        {
            NotificationSettings notificationSettings = new();
            notificationSettings.ModuleSettings.Add(CreateBaseNotificationSettings(ModuleName.Todo, Event.ScheduledTaskReminder, new List<int>() { 15, 30 }));
            notificationSettings.ModuleSettings.Add(CreateBaseNotificationSettings(ModuleName.Lead, Event.CallbackReminder, new List<int>() { 15, 30 }));
            notificationSettings.ModuleSettings.Add(CreateBaseNotificationSettings(ModuleName.Lead, Event.ScheduleSiteVisitReminder, new List<int>() { 15, 30 }));
            notificationSettings.ModuleSettings.Add(CreateBaseNotificationSettings(ModuleName.Lead, Event.ScheduleMeetingReminder, new List<int>() { 15, 30 }));
            notificationSettings.ChannelSettings = new NotificationChannelSettings()
            {
                IsEmailNotificationEnabled = false,
                IsPushNotificationEnabled = true,
                IsWhatsAppNotificationEnabled = false,
            };
            notificationSettings.EmailNotificationSettings = new EmailNotificationSettings()
            {
                IsContactNoMaskingEnabled = true,
            };
            notificationSettings.WhatsAppNotificationSettings = new()
            {
                LeadNotificationEvents = new(),
                UserNotificationEvents = new(),
                MinutesBefore = new(),
            };
            notificationSettings.IsAdminEnabled = notificationSettings.IsAdminEnabled;
            notificationSettings.IsManagerEnabled = notificationSettings.IsManagerEnabled;
            var result = JsonConvert.SerializeObject(notificationSettings);
            return result;
        }
        private BaseNotificationSettings CreateBaseNotificationSettings(ModuleName moduleName, Event @event, List<int> minutesBefore)
        {
            BaseNotificationSettings baseNotificationSettings = new();
            baseNotificationSettings.ModuleName = moduleName;
            baseNotificationSettings.Event = @event;
            baseNotificationSettings.MinutesBefore = minutesBefore;
            return baseNotificationSettings;
        }
        private string CreateCallSettings()
        {
            CallSettings callSetting = new();
            callSetting.CallType = CallType.NotSet;
            var result = JsonConvert.SerializeObject(callSetting);
            return result;
        }
        public async Task<int> GetLeadCountByStatusAsync(List<string> leadStatuses, LeadVisibility filter)
        {
            var tenantId = _currentUser.GetTenant();
            //var reporteeIds = subIds.Where(i => i != _currentUser.GetUserId() && i != Guid.Empty);
            //var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            //var ids = selfWithReporteeIds.Select(x => x.ToString()).ToList();
            int numberOfLeads = 0;
            var conn = new NpgsqlConnection(GetConnectionStringAsync());
            try
            {
                await conn.OpenAsync();
                var query = $"SELECT COUNT(\"BaseLeadStatus\") AS result FROM \"LeadratBlack\".\"LeadHistories\" WHERE \"TenantId\" ='{tenantId}' and (";
                for (int i = 0; i < leadStatuses.Count; i++)
                {
                    if (i == leadStatuses.Count - 1)
                    {
                        query += $"\"BaseLeadStatus\"::text ILIKE '%{leadStatuses[i]}%')";
                    }
                    else
                    {
                        query += $"\"BaseLeadStatus\"::text ILIKE '%{leadStatuses[i]}%' OR";
                    }
                }
                //switch (filter)
                //{
                //    case LeadVisibility.Me:
                //        query += $"and \"AssignedTo\"::text ILIKE '% {(_currentUser.GetUserId()).ToString().ToUpper()} %'";
                //        break;
                //    case LeadVisibility.MyTeam:
                //        query += "and ";
                //        for (int i = 0; i < ids.Count; i++)
                //        {
                //            if (i == ids.Count - 1)
                //            {
                //                query = query + "\"AssignedTo\"::text ILIKE '%" + ids[i] + "%')";
                //            }
                //            else
                //            {
                //                query = query + "\"AssignedTo\"::text ILIKE '%" + ids[i] + "%') OR";
                //            }
                //        }
                //        break;
                //}
                var command = new NpgsqlCommand(query, conn);
                command.CommandType = System.Data.CommandType.Text;
                var reader = await command.ExecuteReaderAsync();
                try
                {
                    if (reader.Read())
                    {
                        numberOfLeads = int.Parse(reader["result"].ToString());
                    }
                }
                catch (Exception ex) { }

                await reader.CloseAsync();
                await conn.CloseAsync();
                return numberOfLeads;

            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> GetLeadCountByStatusAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return 0;
            }
            finally { await conn.CloseAsync(); }
        }
        public async Task<Dictionary<DateTime, long>> GetLeadsByMeetingScheduled(Guid userId)
        {
            Dictionary<DateTime, long> meetingsScheduled = new();
            string? queryForMeetingScheduled = "SELECT \"ScheduledDate\", COUNT(\"ScheduledDate\") FROM " +
                "\"LeadratBlack\".\"Leads\" WHERE \"ScheduledDate\" IS NOT NULL AND " +
                "\"StatusId\" = '99a7f794-9046-4a9d-b7e2-e0a2196b98dd' AND "
                + " (DATE(\"ScheduledDate\") BETWEEN CURRENT_DATE AND (CURRENT_DATE + 6)) "
                + " AND \"AssignTo\" = " +
                " '" + userId + "' GROUP BY \"ScheduledDate\" ";
            var conn = new NpgsqlConnection(GetConnectionStringAsync());
            try
            {
                await conn.OpenAsync();
                var command = new NpgsqlCommand(queryForMeetingScheduled, conn);
                command.CommandType = System.Data.CommandType.Text;
                var reader = await command.ExecuteReaderAsync();
                while (reader.Read())
                {
                    meetingsScheduled[(DateTime)reader["ScheduledDate"]] = (long)reader["count"];
                }
                await reader.CloseAsync();
                return meetingsScheduled;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> GetLeadsByMeetingScheduled()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            finally { await conn.CloseAsync(); }

        }
        public async Task<Dictionary<DateTime, long>> GetLeadsBySiteVisitScheduled(Guid userId)
        {
            Dictionary<DateTime, long> siteVisitScheduled = new();
            string? queryForSiteVisitScheduled = "SELECT \"ScheduledDate\", COUNT(\"ScheduledDate\") FROM " +
                "\"LeadratBlack\".\"Leads\" WHERE \"ScheduledDate\" IS NOT NULL AND " +
                "\"StatusId\" = '59647294-09d6-44a2-a346-9de5ba829e04' AND " +
                "(DATE(\"ScheduledDate\") BETWEEN CURRENT_DATE AND (CURRENT_DATE + 6)) " +
                "AND \"AssignTo\" = '" + userId + "' GROUP BY \"ScheduledDate\" ";
            var conn = new NpgsqlConnection(GetConnectionStringAsync());
            try
            {
                await conn.OpenAsync();
                var command = new NpgsqlCommand(queryForSiteVisitScheduled, conn);
                command.CommandType = System.Data.CommandType.Text;
                var reader = await command.ExecuteReaderAsync();
                while (reader.Read())
                {
                    siteVisitScheduled[(DateTime)reader["ScheduledDate"]] = (long)reader["count"];
                }
                await reader.CloseAsync();
                await conn.CloseAsync();
                return siteVisitScheduled;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> GetLeadsBySiteVisitScheduled()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            finally { await conn.CloseAsync(); }


        }
        public async Task<bool> PostOrganizationDetailAsync(CreateTenantRequest organizationrequest, string? connectionString = null)
        {
            var addressId = await PostTenanTAddressAsync(organizationrequest,connectionString);
            var query = $"INSERT INTO \"LeadratBlack\".\"Profiles\"( \"Id\", \"DisplayName\", \"LogoImgUrl\",\"TenantId\", \"CreatedBy\", \"CreatedOn\", \"LastModifiedBy\", \"LastModifiedOn\",\"IsDeleted\",\"BannerImgUrl\",\"RERANumber\",\"PhoneNumber\",\"Email\",\"AddressId\", \"GSTNumber\",\"ShouldHideSubscription\") VALUES ('{Guid.NewGuid()}', '{organizationrequest.Name}','{organizationrequest.OrganizationInfo.LogoImgUrl}','{organizationrequest.Id}', '{Guid.Empty}', '{DateTime.UtcNow:yyyy/MM/dd}','{Guid.Empty}', '{DateTime.UtcNow:yyyy/MM/dd}',{false},'{organizationrequest.OrganizationInfo.BanerImgUrl}','{organizationrequest.OrganizationInfo.RERANumber}','{organizationrequest.OrganizationInfo.PhoneNumber}','{organizationrequest.AdminEmail}','{addressId}', '{organizationrequest.OrganizationInfo.GSTNumber}',{organizationrequest.OrganizationInfo?.ShouldHideSubscription ?? false});";
            var conn = new NpgsqlConnection(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync());
            try
            {
                await conn.OpenAsync();
                var commamnd = new NpgsqlCommand(query, conn);
                commamnd.CommandType = System.Data.CommandType.Text;
                commamnd.ExecuteNonQuery();
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> PostOrganizationDetailAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return false;
            }
            finally { await conn.CloseAsync(); }

        }

        public async Task<bool> SeedCustomStatusesAsync(string tenantId, string? connectionString = null)
        {
                var masterStatuses = await GetMasterLeadStatuses(GetConnectionStringAsync());

                var conn = new NpgsqlConnection(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync());

                var query = "INSERT INTO \"LeadratBlack\".\"CustomMasterLeadStatuses\"(\r\n\t\"Id\", \"BaseId\", \"Level\", \"Status\", " +
                    "\"DisplayName\", \"ActionName\", \"OrderRank\", \"MasterLeadStatusId\", " +
                    "\"TenantId\", \"IsDeleted\", \"CreatedBy\", \"CreatedOn\", \"LastModifiedBy\", \"LastModifiedOn\", \"DeletedOn\", \"DeletedBy\",\"MasterLeadStatusBaseId\"," +
                    "\"ShouldBeHidden\",\"IsDefault\",\"IsLrbStatus\",\"IsScheduled\",\"ShouldUseForBooking\",\"ShouldUseForMeeting\"," +
                    "\"ShouldUseForBookingCancel\",\"ShouldOpenAppointmentPage\",\"ShouldUseForInvoice\")" +
                    "\r\n\tVALUES (@Id, @BaseId, @Level, @Status, @DisplayName, @ActionName, @OrderRank, @MasterLeadStatusId," +
                    $" @TenantId, @IsDeleted, @CreatedBy, @CreatedOn, @LastModifiedBy, @LastModifiedOn, @DeletedOn, @DeletedBy, @MasterLeadStatusBaseId," +
                    $"@ShouldBeHidden, @IsDefault, @IsLrbStatus, @IsScheduled, @ShouldUseForBooking, @ShouldUseForMeeting," +
                    $"@ShouldUseForBookingCancel, @ShouldOpenAppointmentPage, @ShouldUseForInvoice);";

                List<MasterCustomLeadStatusMigrationDto> customMasterLeadStatuses = new();
                foreach (var masterStatus in masterStatuses)
                {
                    MasterCustomLeadStatusMigrationDto custumLeadStatus = masterStatus.Adapt<MasterCustomLeadStatusMigrationDto>();
                    custumLeadStatus.Id = Guid.NewGuid();
                    custumLeadStatus.MasterLeadStatusId = masterStatus.Id;
                    custumLeadStatus.MasterLeadStatusBaseId = masterStatus.BaseId;
                    custumLeadStatus.TenantId = tenantId;
                    custumLeadStatus.LastModifiedOn = DateTime.UtcNow;
                    custumLeadStatus.ShouldBeHidden = masterStatus.ShouldBeHidden;
                    custumLeadStatus.IsDefault = masterStatus.IsDefault;
                    custumLeadStatus.IsLrbStatus = masterStatus.IsLrbStatus;
                    custumLeadStatus.IsScheduled = masterStatus.IsScheduled;
                    custumLeadStatus.ShouldUseForBooking = masterStatus.ShouldUseForBooking;
                    custumLeadStatus.ShouldUseForMeeting = masterStatus.ShouldUseForMeeting;
                    custumLeadStatus.ShouldUseForBookingCancel = masterStatus.ShouldUseForBookingCancel;
                    custumLeadStatus.ShouldOpenAppointmentPage = masterStatus.ShouldOpenAppointmentPage;
                    custumLeadStatus.ShouldUseForInvoice = masterStatus.ShouldUseForInvoice;
                    customMasterLeadStatuses.Add(custumLeadStatus);
                }
                foreach (var status in customMasterLeadStatuses)
                {
                    if (status.BaseId != null)
                    {
                        var s = customMasterLeadStatuses.FirstOrDefault(i => i.MasterLeadStatusId == status.BaseId);
                        if (s != null)
                        {
                            status.BaseId = s.Id;
                        }
                    }
                }
                var res = await conn.ExecuteAsync(query, customMasterLeadStatuses);
                Console.WriteLine(res);
                return res > 0;
            }

        private async Task<List<MasterLeadStatus>> GetMasterLeadStatuses(string connectionString)
        {
            var query = "SELECT * FROM \"LeadratBlack\".\"MasterLeadStatuses\"";
            var conn = new NpgsqlConnection(connectionString);
            var result = await conn.QueryAsync<MasterLeadStatus>(query);
            return result.ToList();
        }

        public async Task<bool> SeedCustomProspectStatusesAsync(string tenantId, string? connectionString = null)
        {
            var masterStatuses = await GetMasterProspectStatuses(GetConnectionStringAsync());

            var conn = new NpgsqlConnection(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync());
            string insertquery = $"INSERT INTO \"LeadratBlack\".\"CustomProspectStatuses\"(\"Id\", \"BaseId\", \"Level\", " +
                $"\"Status\", \"DisplayName\", \"ActionName\", \r\n\t\t\t\t\t\t\t\"OrderRank\"," +
                $" \"IsDefault\", \"MasterProspectStatusId\", \"TenantId\", \"IsDeleted\", \"CreatedBy\", " +
                $"\"CreatedOn\", \r\n\t\t\t\t\t\t\t\"LastModifiedBy\", \"LastModifiedOn\", \"DeletedOn\", " +
                $"\"DeletedBy\", \"MasterProspectStatusBaseId\", \"IsLrbStatus\", \"IsScheduled\", \"ShouldBeHidden\")\r\nVALUES (@Id, @BaseId, @Level, @Status," +
                $" @DisplayName, @ActionName, @OrderRank, @IsDefault, @MasterProspectStatusId,\r\n     " +
                $"   @TenantId, @IsDeleted, @CreatedBy, @CreatedOn, @LastModifiedBy, @LastModifiedOn, @DeletedOn," +
                $" @DeletedBy, \r\n\t\t@MasterProspectStatusBaseId, @IsLrbStatus, @IsScheduled, @ShouldBeHidden);";

            List<CustomProspectStatusMigrationDto> customMasterProspectStatuses = new();
            foreach (var masterStatus in masterStatuses)
            {
                CustomProspectStatusMigrationDto custumProspectStatus = masterStatus.Adapt<CustomProspectStatusMigrationDto>();
                custumProspectStatus.Id = Guid.NewGuid();
                custumProspectStatus.MasterProspectStatusId = masterStatus.Id;
                custumProspectStatus.MasterProspectStatusBaseId = masterStatus.BaseId;
                custumProspectStatus.TenantId = tenantId;
                custumProspectStatus.LastModifiedOn = DateTime.UtcNow;
                customMasterProspectStatuses.Add(custumProspectStatus);
            }
            var res = await conn.ExecuteAsync(insertquery, customMasterProspectStatuses);
            try
            {
                await new CustomFiltersMigration().DataMigrateAsync(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync(), tenantId);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> SeedCustomProspectSourcesAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            Console.WriteLine(res);
            return true;
        }

        public async Task<List<MasterProspectStatus>> GetMasterProspectStatuses(string connectionString)
        {
            var query = "SELECT * FROM \"LeadratBlack\".\"MasterPrsoepctStatuses\"";
            var conn = new NpgsqlConnection(connectionString);
            var result = await conn.QueryAsync<MasterProspectStatus>(query);
            return result.ToList();
        }


        public async Task<bool> SeedCustomProspectSourcesAsync(string tenantId, string? connectionString = null)
        {
            var masterSources = await GetMasterProspectSources(GetConnectionStringAsync());
            var conn = new NpgsqlConnection(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync());
            string insertquery = $"INSERT INTO \"LeadratBlack\".\"CustomProspectSources\"(\"Id\", \"DisplayName\", \"Value\", \r\n\t\t\t\t\t\t\t\"OrderRank\", \"ImageURL\", \"ProgressColor\", \"BackgroundColor\", \"IsDefault\", \r\n\t\t\t\t\t\t\t\"TenantId\", \"IsDeleted\", \"CreatedBy\", \"CreatedOn\", \r\n\t\t\t\t\t\t\t\"LastModifiedBy\", \"LastModifiedOn\", \"DeletedOn\", \"DeletedBy\", \"MasterProspectSourceId\")\r\nVALUES (@Id, @DisplayName, @Value, @OrderRank, @ImageURL, @ProgressColor, @BackgroundColor, @IsDefault,\r\n        @TenantId, @IsDeleted, @CreatedBy, @CreatedOn, @LastModifiedBy, @LastModifiedOn, @DeletedOn, @DeletedBy, \r\n\t\t@MasterProspectSourceId);";
            List<CustomProspectSourceMigrationDto> customMasterProspectSources = new();
            foreach (var masterSource in masterSources)
            {
                CustomProspectSourceMigrationDto custumProspectSource = masterSource.Adapt<CustomProspectSourceMigrationDto>();
                custumProspectSource.Id = Guid.NewGuid();
                custumProspectSource.MasterProspectSourceId = masterSource.Id;
                custumProspectSource.TenantId = tenantId;
                custumProspectSource.LastModifiedOn = DateTime.UtcNow;
                customMasterProspectSources.Add(custumProspectSource);
            }
            var res = await conn.ExecuteAsync(insertquery, customMasterProspectSources);
            
            return true;
        }

        public async Task<bool> SeedModifiedDatesAsync(string tenantId, string? connectionString = null)
        {
            var conn = new NpgsqlConnection(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync());
            string query = $"INSERT INTO \"LeadratBlack\".\"ModifiedDates\"(\r\n\t\"Id\", \"EntityType\", \"TenantId\", \"IsDeleted\", \"CreatedBy\", \"CreatedOn\", \"LastModifiedBy\", \"LastModifiedOn\", \"DeletedOn\", \"DeletedBy\", \"Value\")\r\n\tVALUES (@Id, @EntityType, @TenantId, @IsDeleted, @CreatedBy, @CreatedOn, @LastModifiedBy, @LastModifiedOn, @DeletedOn, @DeletedBy, @Value);";
            try
            {
                var result = GetModifiedDateDatas(tenantId);
                if (result?.Any() ?? false)
                {
                    await conn.OpenAsync();
                    var res = await conn.ExecuteAsync(query, result);
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> PostOrganizationDetailAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            finally { await conn.CloseAsync(); }
            return true;
        }
        public List<BaseModifiedDateDto> GetModifiedDateDatas(string tenantId)
        {
            var entitys = new List<BaseModifiedDateDto>();
            try
            {
                var items = Enum.GetValues(typeof(EntityType)).Cast<EntityType>().ToList();
                if (items?.Any() ?? false)
                {
                    foreach (var item in items)
                    {
                        var entity = new BaseModifiedDateDto();
                        entity.Id = Guid.NewGuid();
                        entity.LastModifiedOn = DateTime.UtcNow;
                        entity.CreatedOn = DateTime.UtcNow;
                        entity.EntityType = item;
                        entity.TenantId = tenantId;
                        entity.CreatedBy = Guid.Empty;
                        entity.LastModifiedBy = Guid.Empty;
                        entitys.Add(entity);
                    }
                }
            }
            catch (Exception ex)
            {
            }
            return entitys;
        }

        public async Task<List<MasterProspectSource>> GetMasterProspectSources(string connectionString)
        {
            var query = "SELECT * FROM \"LeadratBlack\".\"MasterProspectSources\"";
            var conn = new NpgsqlConnection(connectionString);
            var result = await conn.QueryAsync<MasterProspectSource>(query);
            return result.ToList();
        }

        public async Task<Guid> PostTenanTAddressAsync(CreateTenantRequest addressRequest, string? connectionString = null)
        {
            var addressId = Guid.NewGuid();
            var query = $"Insert into \"LeadratBlack\".\"Addresses\"(\"Id\", \"PlaceId\", \"SubLocality\", \"Locality\", \"District\", \"City\", \"State\", \"Country\", \"PostalCode\", " +
                $"\"Longitude\", \"Latitude\", \"IsGoogleMapLocation\", \"IsDeleted\", \"LocationId\") values('{addressId}', '{addressRequest?.OrganizationInfo?.Address?.PlaceId}', '{addressRequest?.OrganizationInfo?.Address?.SubLocality}', '{addressRequest?.OrganizationInfo?.Address?.Locality}', '{addressRequest?.OrganizationInfo?.Address?.District}', '{addressRequest?.OrganizationInfo?.Address?.City}', '{addressRequest?.OrganizationInfo?.Address?.State}', '{addressRequest?.OrganizationInfo?.Address?.Country}', '{addressRequest?.OrganizationInfo?.Address?.PostalCode}', '{addressRequest?.OrganizationInfo?.Address?.Longitude}', '{addressRequest?.OrganizationInfo?.Address?.Latitude}', 'false', 'false', null)";
            var conn = new NpgsqlConnection(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync());
            try
            {
                await conn.OpenAsync();
                var command = new NpgsqlCommand(query, conn);
                command.CommandType = System.Data.CommandType.Text;
                var response = command.ExecuteNonQuery();
                return response > 0 ? addressId : Guid.Empty;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> PostOrganizationDetailAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            finally { await conn.CloseAsync(); }
        }

        public async Task<List<MasterLeadForm>> GetMasterQRForms()
        {
            var query = "SELECT * FROM \"LeadratBlack\".\"MasterLeadForms\" Where \"IsQRFrom\" = true";
            var conn = new NpgsqlConnection(GetConnectionStringAsync());
            var result = await conn.QueryAsync<MasterLeadForm>(query);
            return result.ToList();
        }
        public async Task<bool> SeedCustomQRFormField(string tenantId, string? connectionString = null)
        {
            try
            {
                List<MasterLeadForm> qRForms = await GetMasterQRForms();
                var conn = new NpgsqlConnection(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync());
                string insertquery = $"INSERT INTO \"LeadratBlack\".\"CustomMasterQRForms\"(\"Id\", \"Name\", \"DisplayName\", \r\n\"FieldType\", \"IsSelected\", \"MasterLeadFormId\", " +
                    $"\"TenantId\", \"IsDeleted\", \"CreatedBy\", \"CreatedOn\", \r\n\"LastModifiedBy\", \"LastModifiedOn\", \"DeletedOn\", \"DeletedBy\", \"IsDefault\")" +
                    $"\r\nVALUES " +
                    $"(@Id, @Name, @DisplayName, @FieldType, @IsSelected, @MasterLeadFormId, @TenantId, \r\n\t\t@IsDeleted, @CreatedBy, @CreatedOn, @LastModifiedBy, @LastModifiedOn, @DeletedOn, @DeletedBy, @IsDefault);";

                List<ConfigurableQRFormDto> qRFormsDto = new();

                foreach (var qr in qRForms)
                {
                    ConfigurableQRFormDto qRForm = qr.Adapt<ConfigurableQRFormDto>();
                    if (qRForm.DisplayName == "Name" || qRForm.DisplayName == "ContactNo" || qRForm.DisplayName == "Email" || qRForm.DisplayName == "AlternateContactNo")
                    {
                        qRForm.IsDefault = true;
                    }
                    qRForm.Id = Guid.NewGuid();
                    qRForm.MasterLeadFormId = qr.Id;
                    qRForm.TenantId = tenantId;
                    qRForm.LastModifiedOn = DateTime.UtcNow;
                    qRFormsDto.Add(qRForm);
                }
                var res = await conn.ExecuteAsync(insertquery, qRFormsDto);
                Console.WriteLine(res);
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> PostOrganizationDetailAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return false;
            }
        }


        public async Task<List<Guid>> GetAdminIdsAsync(string tenantId)
        {
            List<Guid> adminIds = new();
            string? getRoleIdQuery = $"SELECT \"Id\" FROM \"Identity\".\"Roles\" WHERE \"TenantId\" = '{tenantId}' AND \"Name\" = 'Admin'";
            using (var conn = new NpgsqlConnection(GetConnectionStringAsync()))
            {
                try
                {
                    await conn.OpenAsync();
                    var command = new NpgsqlCommand(getRoleIdQuery, conn);
                    command.CommandType = System.Data.CommandType.Text;
                    var reader = await command.ExecuteReaderAsync();
                    Guid adminId = new();
                    if (reader.Read())
                    {
                        adminId = Guid.Parse(reader["Id"].ToString());
                    }
                    await reader.CloseAsync();
                    try
                    {
                        if (adminId != null && adminId != Guid.Empty)
                        {
                            string? getUsersQuery = $"SELECT ur.\"UserId\" FROM \"Identity\".\"UserRoles\" ur Left Join \"Identity\".\"Users\" u on u.\"Id\" = ur.\"UserId\" WHERE ur.\"RoleId\" = '{adminId}' " +
                                $"and u.\"UserName\" != '{tenantId}.admin' and u.\"TenantId\" = '{tenantId}'";
                            command = new NpgsqlCommand(getUsersQuery, conn);
                            command.CommandType = System.Data.CommandType.Text;
                            reader = await command.ExecuteReaderAsync();
                            while (reader.Read())
                            {
                                adminIds.Add(Guid.Parse(reader["UserId"]?.ToString() ?? string.Empty));
                            }
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                    await reader.CloseAsync();
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "NpgsqlRepository -> GetAdminIdsAsync()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
                finally { await conn.CloseAsync(); }

            }
            return adminIds;
        }
        

        

        public async Task<List<Guid>> GetReportingManagerUserIdsAsync(List<Guid> userIds)
        {
            List<Guid> managerIds = new();
            string getReportsToIdQuery = $"SELECT \"ReportsTo\" FROM \"LeadratBlack\".\"UserDetails\" WHERE \"UserId\" = '{userIds.FirstOrDefault()}'; ";
            using (var conn = new NpgsqlConnection(GetConnectionStringAsync()))
            {
                try
                {
                    await conn.OpenAsync();
                    var command = new NpgsqlCommand(getReportsToIdQuery, conn);
                    command.CommandType = System.Data.CommandType.Text;
                    var reader = await command.ExecuteReaderAsync();
                    while (reader.Read())
                    {
                        managerIds.Add(Guid.Parse(reader["ReportsTo"]?.ToString() ?? string.Empty));
                    }
                    reader.Close();
                }
                catch (Exception ex)
                {

                }
                finally { await conn.CloseAsync(); }
                return managerIds;
            }
        }
        public async Task<bool> UpdateUserOfficeAddress(Profile profile)
        {

            var address = profile?.Address?.ToString();
            var tenant = _currentUser.GetTenant();
            var conn = new NpgsqlConnection(GetConnectionStringAsync());
            try
            {
                await conn.OpenAsync();
                var query = $"UPDATE \"LeadratBlack\".\"UserDetails\" Set  \"OfficeAddress\"= @address , \"OfficeName\" = @officeName  where \"TenantId\" = @tenant";
                var command = new NpgsqlCommand(query, conn);
                command.Parameters.AddWithValue("address", address);
                command.Parameters.AddWithValue("officeName", profile.DisplayName);
                command.Parameters.AddWithValue("@tenant", tenant);
                int affectedRows = command.ExecuteNonQuery();
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> UpdateUserOfficeAddress()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return false;
            }
            finally
            {
                await conn.CloseAsync();
            }
        }

        public async Task<string> GetTenantId(Guid accountId)
        {
            var query = $"SELECT \"TenantId\" FROM \"LeadratBlack\".\"IntegrationAccountInfo\" WHERE \"Id\" = @accountId";
            string tenantId = string.Empty;
            using (var conn = new NpgsqlConnection(GetConnectionStringAsync()))
            {
                try
                {
                    await conn.OpenAsync();
                    var command = new NpgsqlCommand(query, conn);
                    command.Parameters.AddWithValue("@accountId", accountId);
                    var reader = await command.ExecuteReaderAsync();
                    if (reader.Read())
                    {
                        tenantId = reader["TenantId"].ToString();
                    }
                    await reader.CloseAsync();
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "NpgsqlRepository -> GetTenantId()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
                finally { await conn.CloseAsync(); }
            }
            return tenantId;
        }

        public async Task<bool> UpdateNotificationSettingsAsync()
        {
            var queryForTenant = $"SELECT DISTINCT(\"TenantId\") FROM \"LeadratBlack\".\"GlobalSettings\" WHERE \"IsDeleted\" = 'false'";
            List<string> tenantIds = new();
            using (var conn = new NpgsqlConnection(GetConnectionStringAsync()))
            {
                try
                {
                    await conn.OpenAsync();
                    var command = new NpgsqlCommand(queryForTenant, conn);
                    var reader = await command.ExecuteReaderAsync();
                    while (reader.Read())
                    {
                        tenantIds.Add(reader["TenantId"].ToString() ?? string.Empty);
                    }
                    await reader.CloseAsync();
                }
                catch (Exception ex)
                {

                }
                finally { await conn.CloseAsync(); }
            }
            foreach (var tenantId in tenantIds)
            {
                var query = $"SELECT \"NotificationSettings\" FROM \"LeadratBlack\".\"GlobalSettings\" WHERE \"TenantId\" = @accountId";
                string notificationSettingsString = string.Empty;
                using (var conn = new NpgsqlConnection(GetConnectionStringAsync()))
                {
                    try
                    {
                        await conn.OpenAsync();
                        var command = new NpgsqlCommand(query, conn);
                        command.Parameters.AddWithValue("@accountId", tenantId);
                        var reader = await command.ExecuteReaderAsync();
                        if (reader.Read())
                        {
                            notificationSettingsString = reader["NotificationSettings"].ToString() ?? string.Empty;
                        }
                        await reader.CloseAsync();
                    }
                    catch (Exception ex)
                    {

                    }
                    finally { await conn.CloseAsync(); }
                }
                using (var conn = new NpgsqlConnection(GetConnectionStringAsync()))
                {
                    try
                    {

                        if (!string.IsNullOrWhiteSpace(notificationSettingsString))
                        {
                            NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(notificationSettingsString);
                            if (notificationSettings != null && (notificationSettings.ChannelSettings == null || notificationSettings.EmailNotificationSettings == null))
                            {
                                if (notificationSettings.ChannelSettings == null)
                                {
                                    notificationSettings.ChannelSettings = new()
                                    {
                                        IsEmailNotificationEnabled = true,
                                        IsPushNotificationEnabled = true,
                                    };
                                }
                                if (notificationSettings.EmailNotificationSettings == null)
                                {
                                    notificationSettings.EmailNotificationSettings = new()
                                    {
                                        IsContactNoMaskingEnabled = true
                                    };
                                }
                                string notificationSettingsUpdatedString = JsonConvert.SerializeObject(notificationSettings);
                                if (!string.IsNullOrWhiteSpace(notificationSettingsUpdatedString))
                                {
                                    //string updateQuery = $"UPDATE \"LeadratBlack\".\"GlobalSettings\" SET \"NotificationSettings\" = @accountId where \"TenantId\" = @tenantId";
                                    //await conn.OpenAsync();
                                    //var updateCommand = new NpgsqlCommand(updateQuery, conn);
                                    //updateCommand.Parameters.AddWithValue("@accountId", notificationSettingsUpdatedString);
                                    //updateCommand.Parameters.AddWithValue("@tenantId", tenantId);
                                    //var response = await updateCommand.ExecuteNonQueryAsync();
                                }

                            }
                        }
                        else
                        {
                            string notificationSettingsUpdatedString = CreateNotificationSettings();
                            if (!string.IsNullOrWhiteSpace(notificationSettingsUpdatedString))
                            {
                                //string updateQuery = $"UPDATE \"LeadratBlack\".\"GlobalSettings\" SET \"NotificationSettings\" = @accountId where \"TenantId\" = @tenantId";
                                //await conn.OpenAsync();
                                //var updateCommand = new NpgsqlCommand(updateQuery, conn);
                                //updateCommand.Parameters.AddWithValue("@accountId", notificationSettingsUpdatedString);
                                //updateCommand.Parameters.AddWithValue("@tenantId", tenantId);
                                //var response = await updateCommand.ExecuteNonQueryAsync();
                            }
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                    finally { await conn.CloseAsync(); }
                }
            }

            return true;
        }
        public async Task<bool> DisableEmailNotificationForAllTenantsAsync()
        {
            var queryForTenant = $"SELECT DISTINCT(\"TenantId\") FROM \"LeadratBlack\".\"GlobalSettings\" WHERE \"IsDeleted\" = 'false'";
            List<string> tenantIds = new();
            using (var conn = new NpgsqlConnection(GetConnectionStringAsync()))
            {
                try
                {
                    await conn.OpenAsync();
                    var command = new NpgsqlCommand(queryForTenant, conn);
                    var reader = await command.ExecuteReaderAsync();
                    while (reader.Read())
                    {
                        tenantIds.Add(reader.GetString(0).ToString() ?? string.Empty);
                    }
                    await reader.CloseAsync();
                }
                catch (Exception ex)
                {

                }
                finally { await conn.CloseAsync(); }
            }
            foreach (var tenantId in tenantIds)
            {
                var query = $"SELECT \"NotificationSettings\" FROM \"LeadratBlack\".\"GlobalSettings\" WHERE \"TenantId\" = @accountId";
                string notificationSettingsString = string.Empty;
                using (var conn = new NpgsqlConnection(GetConnectionStringAsync()))
                {
                    try
                    {
                        await conn.OpenAsync();
                        var command = new NpgsqlCommand(query, conn);
                        command.Parameters.AddWithValue("@accountId", tenantId);
                        var reader = await command.ExecuteReaderAsync();
                        if (reader.Read())
                        {
                            notificationSettingsString = reader["NotificationSettings"].ToString() ?? string.Empty;
                        }
                        await reader.CloseAsync();
                    }
                    catch (Exception ex)
                    {

                    }
                    finally { await conn.CloseAsync(); }
                }
                using (var conn = new NpgsqlConnection(GetConnectionStringAsync()))
                {
                    try
                    {

                        if (!string.IsNullOrWhiteSpace(notificationSettingsString))
                        {
                            NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(notificationSettingsString);
                            if (notificationSettings != null)
                            {
                                if (notificationSettings.ChannelSettings != null)
                                {
                                    notificationSettings.ChannelSettings.IsPushNotificationEnabled = notificationSettings.ChannelSettings.IsPushNotificationEnabled;
                                    notificationSettings.ChannelSettings.IsEmailNotificationEnabled = notificationSettings.ChannelSettings.IsEmailNotificationEnabled;
                                    notificationSettings.ChannelSettings.IsWhatsAppNotificationEnabled = false;
                                }
                                else
                                {
                                    notificationSettings.ChannelSettings = new()
                                    {
                                        IsPushNotificationEnabled = true,
                                        IsEmailNotificationEnabled = false,
                                        IsWhatsAppNotificationEnabled = false
                                    };
                                }
                                if (notificationSettings.EmailNotificationSettings == null)
                                {
                                    notificationSettings.EmailNotificationSettings = new()
                                    {
                                        IsContactNoMaskingEnabled = true
                                    };
                                }
                                if (notificationSettings.WhatsAppNotificationSettings == null)
                                {
                                    notificationSettings.WhatsAppNotificationSettings = new()
                                    {
                                        LeadNotificationEvents = new(),
                                        UserNotificationEvents = new(),
                                        MinutesBefore = new(),
                                    };
                                }
                                notificationSettings.IsAdminEnabled = notificationSettings.IsAdminEnabled;
                                notificationSettings.IsManagerEnabled = notificationSettings.IsAdminEnabled;
                                string notificationSettingsUpdatedString = JsonConvert.SerializeObject(notificationSettings);
                                if (!string.IsNullOrWhiteSpace(notificationSettingsUpdatedString))
                                {
                                    //string updateQuery = $"UPDATE \"LeadratBlack\".\"GlobalSettings\" SET \"NotificationSettings\" = @notificationSettings where \"TenantId\" = @tenantId";
                                    //await conn.OpenAsync();
                                    //var updateCommand = new NpgsqlCommand(updateQuery, conn);
                                    //updateCommand.Parameters.AddWithValue("@notificationSettings", notificationSettingsUpdatedString);
                                    //updateCommand.Parameters.AddWithValue("@tenantId", tenantId);
                                    //var response = await updateCommand.ExecuteNonQueryAsync();
                                }

                            }
                            else
                            {
                                string notificationSettingsUpdatedString = CreateNotificationSettings();
                                if (!string.IsNullOrWhiteSpace(notificationSettingsUpdatedString))
                                {
                                    //string updateQuery = $"UPDATE \"LeadratBlack\".\"GlobalSettings\" SET \"NotificationSettings\" = @notificationSettings where \"TenantId\" = @tenantId";
                                    //await conn.OpenAsync();
                                    //var updateCommand = new NpgsqlCommand(updateQuery, conn);
                                    //updateCommand.Parameters.AddWithValue("@notificationSettings", notificationSettingsUpdatedString);
                                    //updateCommand.Parameters.AddWithValue("@tenantId", tenantId);
                                    //var response = await updateCommand.ExecuteNonQueryAsync();
                                }
                            }
                        }
                        else
                        {
                            string notificationSettingsUpdatedString = CreateNotificationSettings();
                            if (!string.IsNullOrWhiteSpace(notificationSettingsUpdatedString))
                            {
                                //string updateQuery = $"UPDATE \"LeadratBlack\".\"GlobalSettings\" SET \"NotificationSettings\" = @notificationSettings where \"TenantId\" = @tenantId";
                                //await conn.OpenAsync();
                                //var updateCommand = new NpgsqlCommand(updateQuery, conn);
                                //updateCommand.Parameters.AddWithValue("@notificationSettings", notificationSettingsUpdatedString);
                                //updateCommand.Parameters.AddWithValue("@tenantId", tenantId);
                                //var response = await updateCommand.ExecuteNonQueryAsync();
                            }
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                    finally { await conn.CloseAsync(); }
                }
            }

            return true;
        }

        public async Task<bool> CheckUserNameExists(string userName)
        {
            string name = string.Empty;
            var query = $"select \"UserName\" from \"Identity\".\"Users\" where lower(\"UserName\") = @userName and \"IsDeleted\" = false";
            var conn = new NpgsqlConnection(GetConnectionStringAsync());
            try
            {
                await conn.OpenAsync();
                var command = new NpgsqlCommand(query, conn);
                command.Parameters.AddWithValue("@userName", userName.ToLower());
                var reader = await command.ExecuteReaderAsync();
                if (reader.Read())
                {
                    name = reader["UserName"].ToString();
                }
                await reader.CloseAsync();
                if (!string.IsNullOrEmpty(name))
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> PostOrganizationDetailAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return false;
            }
            finally { await conn.CloseAsync(); }
        }

        public SpreadSheet GetSpreadSheetByNotificationData(GoogleNotificationChannelData request)
        {
            string name = string.Empty;
            var query = $"select * from \"LeadratBlack\".\"SpreadSheets\" " +
                $"WHERE EXISTS (\r\n    SELECT 1\r\n    FROM JSONB_EACH(\"GoogleSheetWatchDeatils\") AS json_obj\r\n    WHERE json_obj.value->>'id' IS NOT NULL\r\n    AND json_obj.value->>'id' = @id\r\n);";
            var conn = new NpgsqlConnection(GetConnectionStringAsync());
            try
            {
                var result = conn.QueryFirstOrDefault<SpreadSheet>(query, new { id = request.ChannelId });
                return result;
            }
            catch (Exception e)
            {
                throw;
            }
            finally { conn.Close(); }
        }

        public async Task<bool> RestoreFBPageAsync(string pageId, string tenantId)
        {
            var conn = new NpgsqlConnection(GetConnectionStringAsync());
            try
            {
                var getDeletedquery = $"SELECT * FROM \"LeadratBlack\".\"FacebookConnectedPageAccount\"\r\n" +
                $"where \"FacebookId\" = '{pageId}' and \"IsDeleted\" = 'true' and \"TenantId\" = '{tenantId}' \r\n" +
                $"order by \"LastModifiedOn\" desc\r\n" +
                $"limit 1";
                var updateCommand = "update \"LeadratBlack\".\"FacebookConnectedPageAccount\"\r\n" +
                "set \"IsDeleted\" = 'false', \"DeletedOn\" = null\r\n" +
                "where \"Id\" = '{0}' and \"TenantId\" = '{1}'";
                FacebookConnectedPageAccount? result;
                try
                {
                    await conn.OpenAsync();
                    result = await conn.QuerySingleAsync<FacebookConnectedPageAccount>(getDeletedquery);
                    await conn.CloseAsync();
                }
                catch (Exception ex)
                {
                    if (ex.Message.Contains("Sequence contains no elements"))
                    {
                        result = null;
                    }
                    else
                    {
                        Console.WriteLine(ex.Message);
                        throw;
                    }
                }
                if (result != null)
                {
                    await conn.OpenAsync();
                    updateCommand = string.Format(updateCommand, result.Id.ToString(), tenantId);
                    await conn.ExecuteAsync(updateCommand);
                    await conn.CloseAsync();
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception e) { return false; }
            finally { conn.Close(); }


        }
        public async Task<bool> SeedCountryInfoAsync(string tenantId, string? connectionString = null)
        {
            var conn = new NpgsqlConnection(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync());
            try
            {
                conn.Open();
                using (NpgsqlCommand command = new NpgsqlCommand("CALL \"LeadratBlack\".\"SeedCountryInfoForTenant\"(@v_tenant_id)", conn))
                {
                    command.Parameters.AddWithValue("v_tenant_id", tenantId);
                    command.ExecuteNonQuery();
                }
                //var formattedSPName = $"\"{"LeadratBlack"}\"" + "." + $"\"{"SeedCountryInfoForTenant"}\"";
                //var res = await conn.QueryAsync(formattedSPName, new
                //{
                //    v_tenant_id = tenantId
                //}, commandType: CommandType.StoredProcedure);
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> PostOrganizationDetailAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return false;
            }
            finally { conn.Close(); }
        }

        public async Task<bool> SeedTempVariablesAsync(string tenantId, string? connectionString = null)
        {
            var conn = new NpgsqlConnection(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync());
            try
            {
                conn.Open();
                using (NpgsqlCommand command = new NpgsqlCommand("CALL \"LeadratBlack\".\"SeedTempVariablesByTenantId\"(@v_tenant_id)", conn))
                {
                    command.Parameters.AddWithValue("v_tenant_id", tenantId);
                    command.ExecuteNonQuery();
                }
                //var formattedSPName = $"\"{"LeadratBlack"}\"" + "." + $"\"{"SeedCountryInfoForTenant"}\"";
                //var res = await conn.QueryAsync(formattedSPName, new
                //{
                //    v_tenant_id = tenantId
                //}, commandType: CommandType.StoredProcedure);
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> PostOrganizationDetailAsync() -> SeedTempVariablesAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return false;
            }
            finally { conn.Close(); }
        }

        public async Task<bool> SeedCustomFlagsAsync(string tenantId, string? connectionString = null)
        {
            var conn = new NpgsqlConnection(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync());
            try
            {
                conn.Open();
                using (NpgsqlCommand command = new NpgsqlCommand("CALL \"LeadratBlack\".\"SeedTagsByTenantId\"(@v_tenant_id)", conn))
                {
                    command.Parameters.AddWithValue("v_tenant_id", tenantId);
                    command.ExecuteNonQuery();
                }

                //var formattedSPName = $"\"{"LeadratBlack"}\"" + "." + $"\"{"SeedTagsByTenantId"}\"";
                //var res = await conn.QueryAsync(formattedSPName, new
                //{
                //    v_tenant_id = tenantId
                //}, commandType: CommandType.StoredProcedure);
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> PostOrganizationDetailAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return false;
            }
            finally { conn.Close(); }
        }

        public async Task<bool> CheckQRTemplateNameExists(string templateName)
        {
            string name = string.Empty;
            var query = $"select \"Name\" from \"LeadratBlack\".\"QRFormTemplates\" where lower(\"Name\") = @templateName and \"IsDeleted\" = false";
            var conn = new NpgsqlConnection(GetConnectionStringAsync());
            try
            {
                await conn.OpenAsync();
                var command = new NpgsqlCommand(query, conn);
                command.Parameters.AddWithValue("@templateName", templateName.ToLower());
                var reader = await command.ExecuteReaderAsync();
                if (reader.Read())
                {
                    name = reader["Name"].ToString();
                }
                await reader.CloseAsync();
                if (!string.IsNullOrEmpty(name))
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> PostOrganizationDetailAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return false;
            }
            finally { await conn.CloseAsync(); }
        }
        public async Task<bool> SeedAmenityInfoAsync(string tenantId, string? connectionString = null)
        {
            var conn = new NpgsqlConnection(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync());
            try
            {
                conn.Open();
                using (NpgsqlCommand command = new NpgsqlCommand("CALL \"LeadratBlack\".\"SeedCustomMasterAmenityToInsert\"(@tenant_id)", conn))
                {
                    command.Parameters.AddWithValue("tenant_id", tenantId);
                    command.ExecuteNonQuery();
                }
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> PostOrganizationDetailAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return false;
            }
            finally { conn.Close(); }
        }
        public async Task<bool> SeedAttributeInfoAsync(string tenantId, string? connectionString = null)
        {
            var conn = new NpgsqlConnection(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync());
            try
            {
                conn.Open();
                using (NpgsqlCommand command = new NpgsqlCommand("CALL \"LeadratBlack\".\"SeedCustomMasterAttributeToInsert\"(@tenant_id)", conn))
                {
                    command.Parameters.AddWithValue("tenant_id", tenantId);
                    command.ExecuteNonQuery();
                }
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> PostOrganizationDetailAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return false;
            }
            finally { conn.Close(); }
        }

        public async Task<bool> SeedMasterSourcesAsync(string tenantId, string? connectionString = null)
        {
            var conn = new NpgsqlConnection(!string.IsNullOrEmpty(connectionString) ? connectionString : GetConnectionStringAsync());
            try
            {
                conn.Open();
                using (NpgsqlCommand command = new NpgsqlCommand("CALL \"LeadratBlack\".\"SeedMatserLeadSources\"(@v_tenant_id)", conn))
                {
                    command.Parameters.AddWithValue("v_tenant_id", tenantId);
                    command.ExecuteNonQuery();
                }
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NpgsqlRepository -> PostOrganizationDetailAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return false;
            }
            finally { conn.Close(); }
        }

    }
}


public class LeadStatusHistoryDto
{
    public Guid Id { get; set; }
    public string Status { get; set; }
    public string ModifiedDate { get; set; }
    public int CurrentVersion { get; set; }
}


