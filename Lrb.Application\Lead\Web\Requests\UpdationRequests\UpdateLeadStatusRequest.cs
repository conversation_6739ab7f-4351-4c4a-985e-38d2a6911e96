﻿using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Lead.Web.Requests;
using Lrb.Application.Property.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Web
{
    public class UpdateLeadStatusRequest : IRequest<Response<bool>>
    {
        public Guid Id { get; set; }
        public Guid LeadStatusId { get; set; }
        public string? Notes { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public DateTime? RevertDate { get; set; }
        public string? SoldPrice { get; set; }
        public string? BookedUnderName { get; set; }
        public DateTime? BookedDate { get; set; }
        public string? Rating { get; set; }
        public List<string>? ProjectsList { get; set; }
        public List<string>? PropertiesList { get; set; }
        public DateTime? PostponedDate { get; set; }
        public long? UnmatchedBudget { get; set; }
        public string? PurchasedFrom { get; set; }
        public string? PreferredLocation { get; set; }
        public AppointmentType MeetingOrSiteVisit { get; set; }
        public bool IsDone { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string? ChosenProject { get; set; }
        public double? AgreementValue { get; set; }
        public string? ChosenProperty { get; set; }
        public string? ChoosenUnit { get; set; }
        public AddressDto? Address { get; set; }
        public Guid? AssignTo { get; set; }
        public List<string>? Projects { get; set; }
        public bool? IsFullyCompleted { get; set; }
        public Guid? SecondaryUserId { get; set; }
        public string? SoldPriceCurrency { get; set; } = "INR";
        public List<AddressDto>? Addresses { get; set; }
        public List<Guid>? ProjectIds { get; set; }
        public List<Guid>? PropertyIds { get; set; }
        public bool? IsChoosenProperty { get; set; }
        public string? Currency { get; set; }
        public Guid? UnitTypeId { get; set; }
        public bool? IsBookingCompleted { get; set; }
        public bool IsNotesUpdated { get; set; }
        public List<LeadDocument>? Documents { get; set; }
    }
    public class UpdateLeadStatusRequestHandler : LeadCommonRequestHandler, IRequestHandler<UpdateLeadStatusRequest, Response<bool>>
    {
        public UpdateLeadStatusRequestHandler(IServiceProvider serviceProvider) : base(serviceProvider, typeof(UpdateLeadStatusRequestHandler).Name, "Handle")
        {
        }
        public async Task<Response<bool>> Handle(UpdateLeadStatusRequest request, CancellationToken cancellationToken)
        {
            try
            {
                Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                Domain.Entities.Lead existingLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.Id), cancellationToken) ?? throw new NotFoundException("No Lead found by this Id");

                try
                {
                    if (existingLead.IsPicked != true)
                    {
                        existingLead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(existingLead, request.Adapt<PickedLeadDto>());
                        existingLead.IsPicked = existingLead.ShouldUpdatePickedDate;
                    }
                }
                catch (Exception ex)
                {
                    throw;
                }
                if ((globalSettings != null) && globalSettings.IsTeamLeadRotationEnabled)
                {
                    var newAssignTo = await GetAutoRetentionAssignmentId(existingLead, request.LeadStatusId);

                    if (newAssignTo != Guid.Empty)
                    {
                        request.AssignTo = newAssignTo;
                    }
                }
                var existingAssignedUserId = existingLead.AssignTo;
                existingLead.SecondaryUserId = request?.SecondaryUserId ?? Guid.Empty;
                if (request?.AssignTo != null && request?.AssignTo != Guid.Empty && (request?.AssignTo != existingAssignedUserId))
                {
                    AssignLeadsBasedOnScenariosRequest assignmentRequest = new()
                    {
                        LeadIds = new() { existingLead.Id },
                        UserIds = new() { request?.AssignTo ?? Guid.Empty },
                        AssignmentType = LeadAssignmentType.WithHistory,
                        LeadSource = existingLead.Enquiries.FirstOrDefault(i => i.IsPrimary)?.LeadSource ?? default,
                    };
                    var assignmentResponse = await _mediator.Send(assignmentRequest);

                }
                else
                {
                    //existingLead.AssignedFrom = existingLead.AssignTo;
                    existingLead.AssignTo = request?.AssignTo ?? Guid.Empty;
                    if (existingLead.AssignTo != existingAssignedUserId)
                    {
                        existingLead.PickedDate = null;
                        existingLead.IsPicked = false;
                        existingLead.ShouldUpdatePickedDate = false;
                    }
                }
                var existingBookedDate = existingLead.BookedDate;
                var existingBookedBy = existingLead.BookedBy;
                await SetLeadStatusAsync(existingLead, request.LeadStatusId, cancellationToken);
                var baseLead = await _customLeadStatusRepo?.GetByIdAsync(existingLead.CustomLeadStatus.BaseId, cancellationToken);
                var existingUnmatchedBudget = existingLead.UnmatchedBudget;
                var existingPurchasedFrom = existingLead.PurchasedFrom;
                existingLead = request.Adapt(existingLead);

                if (request.PostponedDate != null && request.PostponedDate != default)
                {
                    existingLead.ScheduledDate = request.PostponedDate;
                }
                else if (existingLead.CustomLeadStatus != null && existingLead.CustomLeadStatus.MasterLeadStatusBaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                {
                    existingLead.ScheduledDate = null;
                }

                if (request?.AssignTo != null && request.AssignTo == Guid.Empty)
                {
                    existingLead.SecondaryUserId = Guid.Empty;
                }
                bool hasToSendAssignmentNotification = false;

                if (request.AssignTo != null)
                {
                    if (request.AssignTo != Guid.Empty && request.AssignTo != existingAssignedUserId)
                    { 
                        hasToSendAssignmentNotification = true;
                    }
                    //existingLead.AssignTo = request.AssignTo ?? Guid.Empty;
                }
                //else
                //{
                //    existingLead.AssignTo = existingAssignedUserId;
                //}

                var appointment = request.Adapt<LeadAppointment>();


                await SetLeadAppointmentAsync(existingLead, appointment, request.MeetingOrSiteVisit, cancellationToken: cancellationToken);

                //if (request.Address != null)
                //{
                //    Address? diffrentLocationAddress = await CreateAddressAsync(request.Address, cancellationToken);

                //    var primaryEnquiry = existingLead.Enquiries.FirstOrDefault(i => i.IsPrimary);

                //    if (primaryEnquiry != null)
                //    {
                //        primaryEnquiry.Address = diffrentLocationAddress;
                //    }
                //}
                if (request.Addresses != null && (request?.Addresses?.Any() ?? false))
                {
                    List<Address>? differentLocationAddresses = await CreateAddressesAsync(request.Addresses, cancellationToken);

                    var primaryEnquiry = existingLead.Enquiries.FirstOrDefault(i => i.IsPrimary);

                    if (primaryEnquiry != null && primaryEnquiry.Addresses != null)
                    {
                        primaryEnquiry.Addresses = differentLocationAddresses;
                    }
                }
                if (baseLead != null && baseLead?.Status == "booked" || existingLead.CustomLeadStatus?.Status == "booked" || existingLead.CustomLeadStatus?.Status == "invoiced")
                {
                    if (request?.BookedDate != null)
                    {
                        existingLead.BookedDate = request.BookedDate;
                    }
                    else
                    {
                        existingLead.BookedDate = DateTime.UtcNow;
                    }
                    existingLead.BookedBy = _currentUser.GetUserId();
                    await SetBookedDetailsAsync(existingLead, request, cancellationToken);
                }
                else
                {
                    existingLead.BookedDate = existingBookedDate;
                    existingLead.BookedBy = existingBookedBy;
                }
                try
                {
                    if (string.IsNullOrWhiteSpace(request.Notes) || (request?.UnmatchedBudget != null && request?.UnmatchedBudget != 0) || !string.IsNullOrWhiteSpace(request?.PurchasedFrom))
                    {
                        existingLead.Notes = (request?.UnmatchedBudget != null && request?.UnmatchedBudget != 0 && request?.UnmatchedBudget != existingUnmatchedBudget)
                            ? $"Unmatched Budget : {request?.UnmatchedBudget}  \n {(request?.IsNotesUpdated == true ? (request?.Notes ?? string.Empty) : string.Empty)}"
                            : (!string.IsNullOrWhiteSpace(request?.PurchasedFrom) && request?.PurchasedFrom != existingPurchasedFrom)
                                ? $"Purchased From : {request?.PurchasedFrom} \n {(request?.IsNotesUpdated == true ? (request?.Notes ?? string.Empty) : string.Empty)}"
                                    : request.Notes;
                    }

                }
                catch (Exception ex)
                {
                    //ignore
                }
                await SetLeadPropertiesAsync(existingLead, request.PropertiesList, globalSettings, cancellationToken);
                await SetLeadProjectsAsync(existingLead, request.ProjectsList, globalSettings,cancellationToken);
                await _leadRepo.UpdateAsync(existingLead, cancellationToken);
                if ((request?.IsFullyCompleted ?? false) && (request?.Projects?.Any() ?? false))
                {
                    var appointments = await _appointmentRepo.ListAsync(new GetAppointmentsByProjectsSpec(request?.Id ?? Guid.Empty, (request?.Projects?.ConvertAll(i => i.Trim().ToLower()) ?? new List<string>())));
                    appointments?.ForEach(i => i.IsFullyCompleted = true);
                    if (appointments?.Any() ?? false)
                    {
                        try
                        {
                            await _appointmentRepo.UpdateRangeAsync(appointments);
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "UpdateSiteVisitOrMeetingDoneRequestHandler -> Handle()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                    }
                }
                //var users = await _userService.GetListAsync(cancellationToken);
                //await UpdateReassignedLeadHistoryAsync(existingLead, LeadAssignmentType.WithHistory, users, appointmentType: request.MeetingOrSiteVisit, cancellationToken);



                //var users = await _userService.GetListAsync(cancellationToken);
                await UpdateLeadHistoryAsync(existingLead, appointmentType: request.MeetingOrSiteVisit, cancellationToken: cancellationToken, isLeadUpdateRequest: request.IsNotesUpdated);
                //await UpdateReassignedLeadHistoryAsync(existingLead, LeadAssignmentType.WithHistory, users, appointmentType: request.MeetingOrSiteVisit, cancellationToken);

                await SendLeadAppointmentNotificationAsync(existingLead, request.MeetingOrSiteVisit, request.IsDone, globalSettings, cancellationToken);

                if (hasToSendAssignmentNotification)
                {
                    await SendLeadAssignmentNotificationsAsync(existingLead, 1, globalSettings, cancellationToken);
                }

                // Dreamyard and kroft are the only tenants that currently use this feature
                await AutoReassignmentHandler(existingLead, cancellationToken);              

                var updatedLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.Id), cancellationToken) ?? throw new NotFoundException("No Lead found by this Id");
                await ScheduleLeadRetentionRotation(new List<Domain.Entities.Lead>() { updatedLead }, globalSettings, cancellationToken);

                return new(true);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{typeof(AddDocumentRequestHandler).Name} - Handle()");
                throw;
            }
        }
    }
}
