﻿using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Request.CommonHandler;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Domain.Entities.MasterData;
using Lrb.Application.Property.Web.Specs;

namespace Lrb.Application.DataManagement.Web.Request
{
    public class UpdateProspectRequest : UpdateProspectDto, IRequest<Response<Guid>>
    {
    }

    public class UpdatePrpospectRequestHandler : DataCommonRequestHandler, IRequestHandler<UpdateProspectRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<ProspectEnquiry> _prospectEnquiryRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepo;
        private readonly ICurrentUser _currentUserRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.GlobalSettings> _globalSettingRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.MasterData.MasterPropertyType> _propertyTypeRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.MasterProspectSource> _prospectSourceRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.CustomProspectStatus> _prospectStatusRepo;
        private readonly IRepositoryWithEvents<ProspectHistory> _prospectHistoryRepo;
        private readonly IUserService _userService;

        public UpdatePrpospectRequestHandler(
            IRepositoryWithEvents<Prospect> prospectRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Lead> leadRepo,
            ICurrentUser currentUser,
            IRepositoryWithEvents<Lrb.Domain.Entities.GlobalSettings> globalSettingRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.MasterData.MasterPropertyType> propertyTypeRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.MasterProspectSource> prospectSourceRepo,
            IRepositoryWithEvents<ProspectEnquiry> prospectEnquiryRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.CustomProspectStatus> prospectStatusRepo,
            IRepositoryWithEvents<ProspectHistory> prospectHsitoryrepo,
            IUserService userService,
            IServiceProvider serviceProvider
            ) : base(serviceProvider)
        {
            _prospectRepo = prospectRepo;
            _leadRepo = leadRepo;
            _currentUserRepo = currentUser;
            _propertyTypeRepo = propertyTypeRepo;
            _globalSettingRepo = globalSettingRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _prospectEnquiryRepo = prospectEnquiryRepo;
            _prospectStatusRepo = prospectStatusRepo;
            _prospectHistoryRepo = prospectHsitoryrepo;
            _userService = userService;
        }
        public async Task<Response<Guid>> Handle(UpdateProspectRequest request, CancellationToken cancellationToken)
        {
            bool isValidContactNo = false;
            var globalSetting = await _globalSettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var hasInternationSupportEnabled = globalSetting?.HasInternationalSupport ?? false;
            if (request?.ContactNo?.Length == 10 && !hasInternationSupportEnabled)
            {
                request.ContactNo = $"+91{request.ContactNo.Trim()}";
            }
            isValidContactNo = PhoneNumberValidationHelper.ValidateContactNoUsingRegex(request?.ContactNo, hasInternationSupportEnabled);
            if (!isValidContactNo)
            {
                throw new InvalidDataException("Invalid Contact");
            }
            if (request.AlternateContactNo != null)
            {
                if (request?.AlternateContactNo?.Length == 10 && !hasInternationSupportEnabled)
                {
                    request.AlternateContactNo = $"+91{request.AlternateContactNo.Trim()}";
                }
            }
            var chaildStatuses = await _prospectStatusRepo.ListAsync(new GetProspectStatusByBaseIdSpecs(request.StatusId), cancellationToken);
            if (chaildStatuses?.Any() ?? false)
            {
                throw new ArgumentException("Please provide child status id.");
            }
            var prospectStatus = await _prospectStatusRepo.GetByIdAsync(request.StatusId, cancellationToken);
            if (prospectStatus == null)
            {
                throw new ArgumentException("The Status Id is not valid.");
            }
            var currentUserId = _currentUserRepo.GetUserId();
            var existingData = (await _prospectRepo.FirstOrDefaultAsync(new GetProspectByIdSpecs(request.Id), cancellationToken));
            var oldProspect = existingData?.Adapt<ViewProspectDto>();
            if (existingData == null)
            {
                throw new NotFoundException("No prospect found by this Id");
            }
            var existingAssignedUserId = existingData.AssignTo;
            var prospect = request.Adapt(existingData);
            prospect.Status = prospectStatus;
            prospect.StatusId = prospectStatus.Id;
            if (request.AssignTo == default || request.AssignTo == Guid.Empty)
            {
                prospect.AssignTo = currentUserId;
                prospect.AssignedFrom = (existingAssignedUserId != currentUserId) ? existingAssignedUserId : prospect.AssignedFrom;
            }
            else
            {
                prospect.AssignedFrom = (existingAssignedUserId != prospect?.AssignTo) ? existingAssignedUserId : prospect.AssignedFrom;
                prospect.AssignTo = request?.AssignTo ?? Guid.Empty;
            }

            await SetProspectAgenciesAsync(prospect, request?.Agencies, cancellationToken);

            await SetProspectPropertiesAsync(prospect, request?.PropertiesList,globalSetting, cancellationToken);

            await SetProspectProjectAsync(prospect, request?.ProjectsList, globalSetting, cancellationToken);

            await SetProspectChannelPartnersAsync(prospect, request?.ChannelPartnerList, cancellationToken);
            var userIds = new List<string?>
            { 
                 oldProspect ?.AssignTo.ToString(),
                 oldProspect ?.LastModifiedBy.ToString(),
                 oldProspect ?.AssignedFrom.ToString(),
                 oldProspect?.SourcingManager.ToString(),
                 oldProspect?.ClosingManager.ToString(),
                 request?.AssignTo.ToString(),
                 request?.AssignedFrom.ToString(),
                 request?.SourcingManager?.ToString(),
                 request?.ClosingManager?.ToString(),
                 currentUserId.ToString()
            };
            var userDetails = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
            await SetProspectCampaignsAsync(prospect, request?.Campaigns, cancellationToken);


            var newAddress = await CreateAddressAsync(request?.AddressDto, cancellationToken);
            prospect.Address = newAddress;

            await _prospectRepo.UpdateAsync(prospect);

            var fullProspect = await _prospectRepo.FirstOrDefaultAsync(new GetProspectByIdSpecs(request.Id), cancellationToken);

            #region Prospect Enquiry

            var existingEnquiry = fullProspect?.Enquiries?.FirstOrDefault(i => i.IsPrimary) ?? null;

            Address? enquiryAddress = await CreateAddressAsync(request.Enquiry?.Address, cancellationToken);
            List<Address>? enquiryAddresses = new();
            List<MasterPropertyType>? propertyTypeses = null;

            if (request.Enquiry?.Addresses?.Any() ?? false)
            {
                foreach(var EnquiryAddress in request.Enquiry.Addresses)
                {
                    var validatedAddress = await CreateAddressAsync(EnquiryAddress, cancellationToken);
                    if(validatedAddress!=null)
                        enquiryAddresses.Add(validatedAddress);
                }
            }

            var prospectEnquiry = request?.Enquiry?.Adapt<ProspectEnquiry>() ?? new();
            if (request?.Enquiry?.PropertyTypeIds?.Any() ?? false)
            {
                propertyTypeses = await _propertyTypeRepo.ListAsync(new GetMasterPropertyTypeSpec(request?.Enquiry?.PropertyTypeIds), cancellationToken);
                prospectEnquiry.PropertyTypes = propertyTypeses;
                prospectEnquiry.PropertyType = propertyTypeses?.FirstOrDefault();

            }
            MasterProspectSource? source = null;
            if (request?.Enquiry?.ProspectSourceId != Guid.Empty && request?.Enquiry?.ProspectSourceId != null)
            {
                source = await _prospectSourceRepo.GetByIdAsync(request.Enquiry.ProspectSourceId, cancellationToken);
                if (source == null)
                {
                    throw new NotFoundException("No Source found by this Id");
                }
            }
            else
            {
                source = await _prospectSourceRepo.FirstOrDefaultAsync(new GetDefaultProspectSourceSpecs());
            }
            prospectEnquiry.Address = enquiryAddress;
            prospectEnquiry.Addresses = enquiryAddresses;
            if (prospectEnquiry.Address == null)
            {
                prospectEnquiry.AddressId = null;
            }
            prospectEnquiry.Source = source;
            prospectEnquiry.IsPrimary = true;

            if (request?.Enquiry?.CarpetArea != null )
            {
                prospectEnquiry.CarpetAreaInSqMtr = (request.Enquiry.CarpetArea * request.Enquiry.ConversionFactor);
            }
            if (request?.Enquiry?.BuiltUpArea != null)
            {
                prospectEnquiry.BuiltUpAreaInSqMtr = (request.Enquiry.BuiltUpArea * request.Enquiry.BuiltUpAreaConversionFactor);
            }
            if (request?.Enquiry?.SaleableArea != null)
            {
                prospectEnquiry.SaleableAreaInSqMtr = (request.Enquiry.SaleableArea * request.Enquiry.SaleableAreaConversionFactor);
            }
            if (request?.Enquiry?.NetArea != null)
            {
                prospectEnquiry.NetAreaInSqMtr = (request.Enquiry.NetArea * request.Enquiry.NetAreaConversionFactor);
            }
            if (request?.Enquiry?.PropertyArea != null)
            {
                prospectEnquiry.PropertyAreaInSqMtr = (request.Enquiry.PropertyArea * request.Enquiry.PropertyAreaConversionFactor);
            }
            if (existingEnquiry != null)
            {
                existingEnquiry.Update(prospectEnquiry);
                await _prospectEnquiryRepo.UpdateAsync(existingEnquiry);
            }
            else
            {
                prospectEnquiry.ProspectId = prospect.Id;
                await _prospectEnquiryRepo.AddAsync(prospectEnquiry);
            }
            #endregion

            #region History
            var statuses = await _prospectStatusRepo.ListAsync();
            var propertyTypes = await _propertyTypeRepo.ListAsync();
            var sources = await _prospectSourceRepo.ListAsync();
            var fullProspectVM = fullProspect.Adapt<ViewProspectDto>();
            fullProspectVM = await ProspectHistoryHelper.SetUserViewForProspectV1(fullProspectVM, userDetails, cancellationToken);
            oldProspect = await ProspectHistoryHelper.SetUserViewForProspectV1(oldProspect, userDetails, cancellationToken);
            var histories = await ProspectHistoryHelper.UpdateProspectHistoryForVM(fullProspectVM, oldProspect, currentUserId, 1, statuses, propertyTypes, sources, _userService, cancellationToken);
            await _prospectHistoryRepo.AddRangeAsync(histories);
            #endregion


            return new Response<Guid>(prospect.Id);
        }
    }
}
