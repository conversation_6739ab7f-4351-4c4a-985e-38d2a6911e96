﻿using Lrb.Application.Integration.Web;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.WA.Web;
using Lrb.Application.WA.Web.Requests;
using Lrb.Application.WAMessage.Web;
using Lrb.Domain.Entities;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class WAController : VersionedApiController
    {
        private readonly Serilog.ILogger _logger;
        public WAController(Serilog.ILogger logger)
        {
            _logger = logger;
        }

        [HttpPost("param/{tenant}")]
        [OpenApiOperation("Create Parameters for WhatsApp Webhook", "")]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        public async Task<Response<Guid>> CreateAsync(string tenant)
        {
            CreateWATempPayloadParamRequest request = new();
            request.HttpContext = this.HttpContext;
            return await Mediator.Send(request);
        }

        [HttpGet("param/{tenant}")]
        [OpenApiOperation("Get Parameters for WhatsApp Webhook", "")]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        public async Task<Response<WATempPayloadParamDto>> CreateAsync(string tenant, Guid? id)
        {
            if(id == null)
            {
                throw new ArgumentNullException(nameof(id));
            }
            GetWATempPayloadParamRequest request = new()
            {
                Id = id ?? Guid.Empty,
            };
            return await Mediator.Send(request);
        }

        [HttpPost("int-acc")]
        [TenantIdHeader]
        [OpenApiOperation("Create Integration Account for WhatsApp Integration", "")]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        public async Task<Response<string>> CreateAsync(CreateWAIntegrationAccountRequest request)
        {
            request.Tenant = this.HttpContext.Request.Headers["tenant"];
            return await Mediator.Send(request);
        }
        [HttpPut("int-acc")]
        [TenantIdHeader]
        [OpenApiOperation("Update Integration Account for WhatsApp Integration", "")]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        public async Task<Response<bool>> UpdateAsync(UpdateIntegrationAccountRequest request)
        {
            try
            {
                request.Tenant = this.HttpContext.Request.Headers["tenant"];
                return await Mediator.Send(request);
            }
            catch (Exception ex) { return new(false, ex.Message); }
        }

        [HttpGet("int-acc")]
        [TenantIdHeader]
        [OpenApiOperation("Get Integration Account With All WhatsApp Dependencies", "")]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        public async Task<Response<string>> GetAsync()
        {
            return await Mediator.Send(new GetWAIntegrationAccountRequest());
        }
        [HttpGet("int-acc/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Get Integration Account With All WhatsApp Dependencies", "")]
        public async Task<ViewIntegrationAccountDto> GetIntegrationAccountIdAsync(Guid id)
        {
            return await Mediator.Send(new GetWAIntegrationAccountByIdRequest(id));
        }

        [HttpPost("template")]
        [TenantIdHeader]
        [OpenApiOperation("Create a new WA template.", "")]
        [MustHavePermission(LrbAction.Create, LrbResource.Templates)]
        public async Task<Response<Guid>> CreateAsync(CreateWATemplateRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("template")]
        [TenantIdHeader]
        [OpenApiOperation("Get all WA templates.", "")]
        [MustHavePermission(LrbAction.View, LrbResource.Templates)]
        public async Task<Response<WAWrapperDto>> GetAsync([FromQuery] GetAllWATemplateRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("template")]
        [TenantIdHeader]
        [OpenApiOperation("Update a WA template.", "")]
        [MustHavePermission(LrbAction.Update, LrbResource.Templates)]
        public async Task<Response<bool>> UpdateAsync(UpdateWATemplateRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("template/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Get WA template.", "")]
        [MustHavePermission(LrbAction.View, LrbResource.Templates)]
        public async Task<Response<WATemplateDto>> GetAsync(Guid id)
        {
            return await Mediator.Send(new GetWATemplateByIdRequest(id));
        }
        [HttpDelete("template/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Delete a WA template.", "")]
        [MustHavePermission(LrbAction.Delete, LrbResource.Templates)]
        public async Task<Response<bool>> DeleteAsync(Guid id)
        {
            return await Mediator.Send(new DeleteWATemplateRequest(id));
        }
        [HttpGet("24-hour-validation")]
        [TenantIdHeader]
        [OpenApiOperation("Check if any response has been received from the lead in the last 24 hours.", "")]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        public async Task<Response<bool>> Async([FromQuery] Check24HourValidationRequest request)
        {
            return await Mediator.Send(request);
        }

        #region Send Templates and Messages

        [HttpPost("send/template")]
        [TenantIdHeader]
        [OpenApiOperation("Send a WhatsApp Template", "")]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        public async Task<Response<bool>> CreateAsync(SendWATemplateRequest request)
        {
            return await Mediator.Send(request);
        }

        //[AllowAnonymous]
        //[HttpPost("send/message")]
        //[TenantIdHeader]
        //[OpenApiOperation("Send a free text message with or without media attachment(s)", "")]
        //[MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        //public async Task<Response<string>> CreateAsync(SendWAMessageRequest request)
        //{
        //    return await Mediator.Send(request);
        //}

        #endregion


        [AllowAnonymous]
        [HttpPost("webhook/{tenant}/{base64}")]
        [OpenApiOperation("Push end point for Whatsapp Webhook")]
        public async Task<Response<bool>> HandleWebhookPayload(string tenant, string base64)
        {
            var request = new ProcessWAWebhookRequest()
            {
                Tenant = tenant,
                AccountId = base64.GetAccountId(),
                HttpRequest = this.HttpContext.Request
            };
            return await Mediator.Send(request);
        }



        [HttpGet("message")]
        [TenantIdHeader]
        [OpenApiOperation("Get all messages.", "")]
        public async Task<PagedResponse<ViewWAMessageDto, string>> GetAsync([FromQuery] GetAllCustomerWAMessageRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("message")]
        [TenantIdHeader]
        [OpenApiOperation("Update a message.", "")]
        public async Task<Response<Guid>> UpdateAsync(UpdateWAMessageRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpDelete("message/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Delete a message.", "")]
        public async Task<Response<bool>> DeleteMessageAsync(Guid id)
        {
            return await Mediator.Send(new DeleteWAMessageRequest(id));
        }
        [HttpPost("message")]
        [TenantIdHeader]
        [OpenApiOperation("Create a new message.", "")]
        public async Task<Response<Guid>> CreateAsync(CreateWAMessageRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("leads-with-message")]
        [TenantIdHeader]
        [OpenApiOperation("Get all leads with message.", "")]
        public async Task<PagedResponse<ViewLeadsWithWAMessageDto, string>> GetAsync([FromQuery] GetLeadsWithWAMessageRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("child/leads-with-message")]
        [TenantIdHeader]
        [OpenApiOperation("Get all child leads with message.", "")]
        public async Task<PagedResponse<ViewLeadsWithWAMessageDto, string>> GetChildAsync([FromQuery] GetLeadsWithWAMessageRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("sync-WA-templates")]
        [TenantIdHeader]
        [OpenApiOperation("Sync new WA templates.", "")]
        public async Task<Response<bool>> SyncWATemplatesAsync()
        {
            return await Mediator.Send(new WATemplateSyncRequest());
        }

        [AllowAnonymous]
        [HttpPost("get-WA-templates/webhook/{tenant}/{base64}")]
        [HttpGet("get-WA-templates/webhook/{tenant}/{base64}")]
        public async Task<Response<bool>> HandleTemplateWebhookPayload(string tenant, string base64)
        {
            GetWATemplatesByWebhookRequest template = new(this.HttpContext.Request, tenant, base64);
            _logger.Information("IntegrationController -> POST(TemplateWebhook) -> called, Dto: " + tenant + base64);
            return await Mediator.Send(template);
        }
    }
}
