﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.Lead
{
    public class BulkLeadUploadTrackerConfig : IEntityTypeConfiguration<BulkLeadUploadTracker>
    {
        public void Configure(EntityTypeBuilder<BulkLeadUploadTracker> builder)
        {
            builder.IsMultiTenant();
        }
    }
}
