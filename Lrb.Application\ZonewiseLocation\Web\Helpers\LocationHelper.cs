﻿using Lrb.Application.ZonewiseLocation.Web.Dtos;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using System.Data;

namespace Lrb.Application.ZonewiseLocation.Web.Helpers
{
    public static class LocationHelper
    {
        public static List<AddLocationRequest> MaptoLocationRequest(this DataTable dataTable, List<string> columnNames)
        {
            List<AddLocationRequest> locations = new List<AddLocationRequest>();
            foreach (DataRow row in dataTable.Rows)
            {
                AddLocationRequest location = new AddLocationRequest();
                var properties = location.GetType().GetProperties();
                foreach (string columnName in columnNames)
                {
                    properties?.FirstOrDefault(i => i.Name == columnName)?.SetValue(location, row?[columnName]?.ToString() ?? default!);
                }
                locations.Add(location);
            }
            return locations;
        }
        public static CreateLocationDto? MapToLocationRequest(this Address address)
        {
            return address.City != null || address.State != null || address.Country != null|| address?.SubLocality != null || address?.Locality != null ?
             new()
             {
                 Locality = address.SubLocality ?? address.Locality ?? string.Empty,
                 City = address.City ?? string.Empty,
                 State = address.State ?? string.Empty,
                 Country = address.Country ?? string.Empty,
                 PostalCode = address.PostalCode ?? string.Empty,
                 Longitude = address.Longitude ?? string.Empty,
                 Latitude = address.Latitude ?? string.Empty,
                 IsGoogleMapLocation = address.IsGoogleMapLocation,
                 District = address.District ?? string.Empty,
                 PlaceId = address.PlaceId ?? string.Empty,
             } : null;
        }
    }
}
