﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class ChangeTeamConfigurations : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TeamConfigurations_TeamId",
                schema: "LeadratBlack",
                table: "TeamConfigurations");

            migrationBuilder.DropColumn(
                name: "IsForRetention",
                schema: "LeadratBlack",
                table: "Teams");

            migrationBuilder.DropColumn(
                name: "IsSourceLevel",
                schema: "LeadratBlack",
                table: "Teams");

            migrationBuilder.DropColumn(
                name: "LeadSources",
                schema: "LeadratBlack",
                table: "Teams");

            migrationBuilder.AddColumn<bool>(
                name: "IsForRetention",
                schema: "LeadratBlack",
                table: "TeamConfigurations",
                type: "boolean",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsSourceLevel",
                schema: "LeadratBlack",
                table: "TeamConfigurations",
                type: "boolean",
                nullable: true);

            migrationBuilder.AddColumn<int[]>(
                name: "LeadSources",
                schema: "LeadratBlack",
                table: "TeamConfigurations",
                type: "integer[]",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TeamConfigurations_TeamId",
                schema: "LeadratBlack",
                table: "TeamConfigurations",
                column: "TeamId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TeamConfigurations_TeamId",
                schema: "LeadratBlack",
                table: "TeamConfigurations");

            migrationBuilder.DropColumn(
                name: "IsForRetention",
                schema: "LeadratBlack",
                table: "TeamConfigurations");

            migrationBuilder.DropColumn(
                name: "IsSourceLevel",
                schema: "LeadratBlack",
                table: "TeamConfigurations");

            migrationBuilder.DropColumn(
                name: "LeadSources",
                schema: "LeadratBlack",
                table: "TeamConfigurations");

            migrationBuilder.AddColumn<bool>(
                name: "IsForRetention",
                schema: "LeadratBlack",
                table: "Teams",
                type: "boolean",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsSourceLevel",
                schema: "LeadratBlack",
                table: "Teams",
                type: "boolean",
                nullable: true);

            migrationBuilder.AddColumn<int[]>(
                name: "LeadSources",
                schema: "LeadratBlack",
                table: "Teams",
                type: "integer[]",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TeamConfigurations_TeamId",
                schema: "LeadratBlack",
                table: "TeamConfigurations",
                column: "TeamId",
                unique: true);
        }
    }
}
