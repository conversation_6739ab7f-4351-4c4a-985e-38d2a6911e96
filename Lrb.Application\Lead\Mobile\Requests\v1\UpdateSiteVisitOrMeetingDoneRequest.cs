﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile.Specs.v1;
using Lrb.Application.Property.Mobile;
using Lrb.Application.ZonewiseLocation.Mobile.Helpers;
using Lrb.Application.ZonewiseLocation.Mobile.Requests;
using Lrb.Application.ZonewiseLocation.Mobile.Specs;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Domain.Entities.ErrorModule;
using Newtonsoft.Json;
using Lrb.Application.Common.Interfaces;

namespace Lrb.Application.Lead.Mobile.Requests
{
    public class UpdateSiteVisitOrMeetingDoneRequest : IRequest<Response<bool>>
    {
        public Guid LeadId { get; set; }
        public AppointmentType MeetingOrSiteVisit { get; set; }
        public bool IsDone { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string? ProjectName { get; set; }
        public string? ExecutiveName { get; set; }
        public string? ExecutiveContactNo { get; set; }
        public string? Image { get; set; }
        public List<LeadDocument>? ImagesWithName { get; set; }
        public bool? IsManual { get; set; }
        public string? Notes { get; set; }
        public AddressDto? Address { get; set; }
        public Guid? UserId { get; set; }
        public DateTime? AppointmentDoneOn { get; set; }

    }
    public class UpdateSiteVisitOrMeetingDoneRequestHandler : LeadCommonRequestHandler, IRequestHandler<UpdateSiteVisitOrMeetingDoneRequest, Response<bool>>
    {
        public UpdateSiteVisitOrMeetingDoneRequestHandler(
            IServiceProvider serviceProvider)
                : base(serviceProvider, nameof(UpdateSiteVisitOrMeetingDoneRequestHandler).ToString(), nameof(Handle).ToString())
        {
        }
        public async Task<Response<bool>> Handle(UpdateSiteVisitOrMeetingDoneRequest request, CancellationToken cancellationToken)
        {
            var lead = (await _leadRepo.ListAsync(new LeadByIdSpec(request.LeadId), cancellationToken)).FirstOrDefault();
            if (lead == null)
            {
                throw new NotFoundException("No Lead exists with the given Id");
            }
            #region PickedLead
            try
            {
                lead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(lead, request.Adapt<PickedLeadDto>());
            }
            catch (Exception ex)
            {
                throw;
            }
            #endregion
            var appointment = request.Adapt<LeadAppointment>();

            #region appointment done and not done by user
            var currentUser = _currentUser.GetUserId();

            if (request.UserId.HasValue && request.UserId.Value != Guid.Empty)
            {
                appointment.UserId = request.UserId.Value;
                currentUser = request.UserId.Value;
            }
            else
            {
                appointment.UserId = lead.AssignTo;
            }

            #endregion

            appointment.Id = Guid.Empty;
            //appointment.UserId = lead.AssignTo;
            switch (request.MeetingOrSiteVisit)
            {
                case AppointmentType.Meeting:
                    lead.IsMeetingDone = request.IsDone;
                    appointment.Type = AppointmentType.Meeting;
                    var address = GetAddressAsync(request, _addressRepo, _googlePlacesService).Result;
                    if (address != null)
                    {
                        lead.MeetingLocation = address.Id;
                        appointment.Location = address;
                    }
                    break;
                case AppointmentType.SiteVisit:
                    lead.IsSiteVisitDone = request.IsDone;
                    appointment.Type = AppointmentType.SiteVisit;
                    address = GetAddressAsync(request, _addressRepo, _googlePlacesService).Result;
                    if (address != null)
                    {
                        lead.SiteLocation = address.Id;
                        appointment.Location = address;
                    }
                    break;
            }
            //Add Appointment Documents to Lead Documents
            var currentUserId = _currentUser.GetUserId();
            List<LeadDocument> documents = new();
            if (request.ImagesWithName != null)
            {
                foreach (var document in request.ImagesWithName)
                {
                    document.Id = Guid.NewGuid();
                    document.DocumentName = $"{document.DocumentName} ({DateTime.UtcNow})";
                    document.UploadedOn = DateTime.UtcNow;
                    document.CreatedBy = currentUserId;
                    document.LastModifiedBy = currentUserId;
                    document.CreatedOn = DateTime.UtcNow;
                    document.LastModifiedOn = DateTime.UtcNow;
                    document.LeadDocumentType = request.MeetingOrSiteVisit == AppointmentType.SiteVisit ? Domain.Enums.LeadDocumentType.SiteVisit : Domain.Enums.LeadDocumentType.Meeting;
                    documents.Add(document);
                }
            }
            appointment.ImagesWithName = documents;
            if (lead.Appointments?.Any() ?? false)
            {
                lead.Appointments.Add(appointment);
            }
            else
            {
                lead.Appointments = new List<LeadAppointment>() { appointment };
            }
            if (lead.Documents != null)
            {
                lead.Documents.AddRange(documents);
            }
            else
            {
                lead.Documents = documents;
            }
            //if (shouldUpdatePickedDate ?? false)
            //{
            //    lead.ShouldUpdatePickedDate = shouldUpdatePickedDate.Value;
            //}
            try
            {
                lead.AppointmentDoneOn = appointment.AppointmentDoneOn;
                lead.Notes = appointment.Notes; 
                await _leadRepo.UpdateAsync(lead);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "UpdateSiteVisitOrMeetingDoneRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            try
            {
                ViewLeadDto leadDto = new();
                //leadDto = lead.Adapt<ViewLeadDto>();
                leadDto = lead.Adapt(leadDto);
                if (leadDto != null)
                {
                    await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId: currentUser);
                }
                await UpdateLeadHistoryAsync(lead, leadDto, appointmentType: request.MeetingOrSiteVisit, cancellationToken : cancellationToken, currentUserId: currentUser);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "UpdateSiteVisitOrMeetingDoneRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            #region Push Notification
            try
            {
                Event? @event = null;
                if (request.MeetingOrSiteVisit != AppointmentType.None)
                {
                    switch (request.MeetingOrSiteVisit)
                    {
                        case AppointmentType.SiteVisit:
                            switch (request.IsDone)
                            {
                                case true:
                                    @event = Event.LeadSiteVisitDone;
                                    break;
                                case false:
                                    @event = Event.LeadSiteVisitNotDone;
                                    break;
                            }
                            break;
                        case AppointmentType.Meeting:
                            switch (request.IsDone)
                            {
                                case true:
                                    @event = Event.LeadMeetingDone;
                                    break;
                                case false:
                                    @event = Event.LeadMeetingNotDone;
                                    break;
                            }
                            break;
                    }
                    if (@event != null)
                    {
                        Event updatedEvent = (Event)@event;
                        var userDetails = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                        if (userDetails.Id != _currentUser.GetUserId())
                        {
                            await _notificationSenderService.ScheduleNotificationsAsync(updatedEvent, lead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "UpdateSiteVisitOrMeetingDoneRequestHandler ->Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            #endregion
            return new(true);
        }
        private async Task<Address?> GetAddressAsync(UpdateSiteVisitOrMeetingDoneRequest request, IRepositoryWithEvents<Address> addressRepo, IGooglePlacesService googlePlacesServiceRepo)
        {
            Address? address = null;
            if (request.Address?.PlaceId != null)
            {
                address = (await addressRepo.ListAsync(new GetAddressByPlaceIdSpec(request.Address.PlaceId))).FirstOrDefault();
                if (address != null)
                {
                    return address;
                }
                else
                {
                    PlaceDetailsModel? place = null;
                    try
                    {
                        place = await googlePlacesServiceRepo.GetPlaceDetailsByPlaceIdAsync(request.Address.PlaceId);
                    }
                    catch (Exception ex) { }
                    if (place != null)
                    {
                        address = place.Adapt<Address>();
                        address = await addressRepo.AddAsync(address);
                        await MapAddressToLocationAndSaveAsync(address);
                        return address;
                    }
                }
            }
            if ((address == null) && request.Address?.Latitude != null && request.Address.Longitude != null)
            {
                List<PlaceDetailsModel>? places = null;
                try
                {
                    places = await googlePlacesServiceRepo.GetPlaceDetailsByCoordinatesAsync(double.TryParse(request.Address.Latitude, out double latitute) ? latitute : 0,
                                                                                                double.TryParse(request.Address.Longitude, out double longitude) ? longitude : 0);
                }
                catch (Exception ex) { }
                if (places != null)
                {
                    address = addressRepo.ListAsync(new GetAddressByPlaceIdSpec(places.Select(i => i.PlaceId ?? default).ToList())).Result.FirstOrDefault();
                }
                if (address != null)
                {
                    return address;
                }
                else
                {
                    address = places.FirstOrDefault()?.Adapt<Address>();
                    if (address != null)
                    {
                        address = await addressRepo.AddAsync(address);
                        await MapAddressToLocationAndSaveAsync(address);
                        return address;
                    }
                }
            }
            if (address == null)
            {
                address = request.Address?.Adapt<Address>();
                if (address != null)
                {
                    address = await addressRepo.AddAsync(address);
                    await MapAddressToLocationAndSaveAsync(address);
                    return address;
                }
            }
            return address;
        }
        private async Task MapAddressToLocationAndSaveAsync(Address address)
        {
            var location = address.MapToLocationRequest();
            if (location != null)
            {
                var locationRes = await _mediator.Send(location.Adapt<AddLocationRequest>());
                var createdLocation = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(locationRes.Data), default);
                address.Location = createdLocation;
                await _addressRepo.UpdateAsync(address);
            }
        }
    }
}
