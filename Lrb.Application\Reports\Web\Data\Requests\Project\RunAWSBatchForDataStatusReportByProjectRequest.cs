﻿using Lrb.Application.Common.AWS_Batch;
using Lrb.Application.Common.ServiceBus;
using Newtonsoft.Json;

namespace Lrb.Application.Reports.Web
{
    public class RunAWSBatchForDataStatusReportByProjectRequest : IRequest<Response<Guid>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DataManagement.Web.Request.ProspectDateType? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public string? SearchText { get; set; }
        public List<Guid>? SourceIds { get; set; }
        public List<string>? SubSources { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? Projects { get; set; }
        public UserStatus? UserStatus { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? ToRecipients { get; set; } = new();
        public List<string>? CcRecipients { get; set; } = new();
        public List<string>? BccRecipients { get; set; } = new();
        public string? FileName { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
        public string? S3BucketKey { get; set; }

    }
    public class RunAWSBatchForDataStatusReportByProjectRequestHandler : IRequestHandler<RunAWSBatchForDataStatusReportByProjectRequest, Response<Guid>>
    {
        private readonly IAWSBatchService _batchService;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<ExportReportsTracker> _exportRepo;
        public const string TYPE = "dataprojectreportbystatus";
        private readonly IServiceBus _serviceBus;

        public RunAWSBatchForDataStatusReportByProjectRequestHandler(IAWSBatchService batchService,
            ICurrentUser currentUser,
            IRepositoryWithEvents<ExportReportsTracker> exportRepo,
            IServiceBus serviceBus)
        {
            _batchService = batchService;
            _currentUser = currentUser;
            _exportRepo = exportRepo;
            _serviceBus = serviceBus;
        }

        public async Task<Response<Guid>> Handle(RunAWSBatchForDataStatusReportByProjectRequest request, CancellationToken cancellationToken)
        {
            try
            {
                ExportReportsTracker tracker = new();
                tracker.Request = JsonConvert.SerializeObject(request);
                tracker.ToRecipients = request.ToRecipients;
                tracker.CcRecipients = request.CcRecipients;
                tracker.BccRecipients = request.BccRecipients;
                tracker.Type = TYPE;
                var tenantId = _currentUser.GetTenant();
                var currentUserId = _currentUser.GetUserId();
                tracker.S3BucketKey = request?.S3BucketKey ?? string.Empty;

                await _exportRepo.AddAsync(tracker, cancellationToken);
                if (string.IsNullOrWhiteSpace(request?.S3BucketKey))
                {
                    //Submit a job in AWS Batch
                    InputPayload input = new(tracker.Id, tenantId ?? string.Empty, currentUserId, TYPE);
                    var stringArgument = JsonConvert.SerializeObject(input);
                    var cmdArgs = new List<string>() { stringArgument };
                    await _serviceBus.RunExcelExportJobAsync(cmdArgs);
                }
                return new(tracker.Id, "Started Processing.");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Something went wrong.");
            }
        }
    }
}
