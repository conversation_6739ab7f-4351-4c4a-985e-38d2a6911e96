﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace LrbIntegrationBackgroundJobs.Dtos.PFV2
{
    public class PropertyFinderResponseV2
    {
        public List<PFLeadData>? Data { get; set; }
        public PFPagination? Pagination { get; set; }
    }

    public class PFLeadData
    {
        public string? Id { get; set; }
        public string? EntityType { get; set; }
        public string? Channel { get; set; }
        public string? Status { get; set; }
        public PFEnrichment? Enrichment { get; set; }
        public string? DistributionType { get; set; }
        public PFPublicProfile? PublicProfile { get; set; }
        public PFSender? Sender { get; set; }
        public PFListing? Listing { get; set; }
        public PFProject? Project { get; set; }
        public PFDeveloper? Developer { get; set; }
        public PFCall? Call { get; set; }
        public List<string>? Tags { get; set; }
        public DateTime? CreatedAt { get; set; }

        public LrbIntegrationPostDtoV2 MapToLrbIngrDtoV2(List<LrbUserWithThirdPartyId> users)
        {
            var userName = users?.Where(i => i.ThirdPartyId == PublicProfile?.Id?.ToString())?.FirstOrDefault()?.Name;
            var dto = new LrbIntegrationPostDtoV2()
            {
                Name = !string.IsNullOrEmpty(Sender?.Name) ? Sender?.Name : "Unknown",
                Mobile = Sender?.Contacts?.Where(c => c.Type == "phone").FirstOrDefault().Value,
                Email = Sender?.Contacts?.Where(c => c.Type == "email").FirstOrDefault()?.Value,
                SubSource = Channel?.Trim(),
                PrimaryUser = userName,
                ListingId = Listing?.Id,
            };
            if (Channel.ToLower().Trim() == "call".ToLower().Trim())
            {
                dto.CallRecordingUrl = Call?.RecordFile;
                dto.Notes = $"Talk Time: {Call?.TalkTime} seconds, Wait Time: {Call?.WaitTime} seconds";
            }
            return dto;
        }
    }

    public class PFEnrichment
    {
        public string? Property1 { get; set; }
        public string? Property2 { get; set; }
    }

    public class PFPublicProfile
    {
        public int? Id { get; set; }
    }

    public class PFSender
    {
        public string? Name { get; set; }
        public List<PFContact>? Contacts { get; set; }
    }

    public class PFContact
    {
        public string? Type { get; set; }
        public string? Value { get; set; }
    }

    public class PFListing
    {
        public string? Id { get; set; }
    }

    public class PFProject
    {
        public Guid? Id { get; set; }
    }

    public class PFDeveloper
    {
        public Guid? Id { get; set; }
    }

    public class PFCall
    {
        public int? TalkTime { get; set; }
        public int? WaitTime { get; set; }
        public string? RecordFile { get; set; }
    }

    public class PFPagination
    {
        public int? Total { get; set; }
        public int? Page { get; set; }
        public int? PerPage { get; set; }
        public int? TotalPages { get; set; }
        public int? NextPage { get; set; }
        public int? PrevPage { get; set; }
    }


    #region Auth Dto
    public class PFNewAuthDto
    {
        public string? apiKey { get; set; }
        public string? apiSecret { get; set; }
    }
    #endregion

    #region Auth Response
    public class V2PFAuthResponseDto
    {
        public string? accessToken { get; set; }
    }
    #endregion

    #region PF Integration Account Cred
    public class PFIntegrationCredDtoV2
    {
        public string apiKey { get; set; }
        public string secretKey { get; set; }
        public string requestType { get; set; }
    }
    #endregion

    #region PF User
    public class LrbUserWithThirdPartyId
    {
        public string? Name { get; set; }
        public string? ThirdPartyId { get; set; }  
    }
    #endregion
}
