﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "DatabaseSettings": {
    "DBProvider": "postgresql",
    //"ConnectionString": "Host=lrb-prd-rds.cu22ll1a4z3g.ap-south-1.rds.amazonaws.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************;Pooling=true;MinPoolSize=3;MaxPoolSize=500;",
    "ConnectionString": "Host=lrb-prd.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;"
  },
  "WorkingEndPoint": {
    "LrbBaseUri": "https://connect.leadrat.com",
    "JustLeadBaseUri": "https://www.lms.justlead.in",
    "CommonFloorBaseuri": "https://www.commonfloor.com",
    "BayutBaseUri": "https://www.bayut.com/api-v7/stats",
    "PropertyFinderBaseUri": "http://api-v2.mycrm.com/",
    "DubizzleBaseUrl": "https://dubizzle.com/profolio/api-v7/stats",
    "V2PropertyFinderBaseUri": "https://atlas.propertyfinder.com"

  },
  "CosmosSettings": {
    "EndpointUri": "https://lrb-prd.documents.azure.com:443/",
    "PrimaryKey": "****************************************************************************************"
  }
}