﻿using Lrb.Application.UserDetails.Web.Dtos;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GetAllProjectInfoRequest : IRequest<Response<List<UserProjectInfoDto>>>
    {
    }
    public class GetAllProjectInfoRequestHandler : IRequestHandler<GetAllProjectInfoRequest, Response<List<UserProjectInfoDto>>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public GetAllProjectInfoRequestHandler( IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;

        }
        public async Task<Response<List<UserProjectInfoDto>>> Handle(GetAllProjectInfoRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var tenantId = _currentUser.GetTenant();
                var projects = (await _dapperRepository.GetAllUserProjectInfo<UserProjectInfoDto>(tenantId)).ToList();
                return new Response<List<UserProjectInfoDto>>(projects);
            }
            catch (Exception ex)
            {
                return new Response<List<UserProjectInfoDto>>(null, $"Error: {ex.Message}");
            }
        }
    }
}
