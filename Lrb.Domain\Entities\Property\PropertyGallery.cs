﻿
using System.Text.Json.Serialization;

namespace Lrb.Domain.Entities
{
    public class PropertyGallery : BaseEntity, IAggregateRoot
    {
        public string ImageKey { get; set; }
        public string ImageFilePath { get; set; }
        public bool IsCoverImage { get; set; }
        public Guid PropertyId { get; set; }
        [JsonIgnore]
        public Property Property { get; set; }
        public string? Name { get; set; }
        public PropertyGalleryType? GalleryType { get; set; }
        public ImageSegregationType? ImageSegregationType { get; set; }
        public int? OrderRank { get; set; }
        public int? Height { get; set; }
        public int? Width { get; set; }
    }
}
