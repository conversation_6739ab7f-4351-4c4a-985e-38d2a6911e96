﻿using Finbuckle.MultiTenant.Stores;
using Lrb.Infrastructure.Persistence.Configuration;
using Microsoft.EntityFrameworkCore;

namespace Lrb.Infrastructure.Multitenancy;

public class TenantDbContext : EFCoreStoreDbContext<LrbTenantInfo>
{
    public TenantDbContext(DbContextOptions<TenantDbContext> options)
        : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<LrbTenantInfo>().ToTable("Tenants", SchemaNames.MultiTenancy);
    }
}