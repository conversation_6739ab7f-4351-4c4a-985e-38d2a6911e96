﻿using Finbuckle.MultiTenant;
using Lrb.Application.Common.GoogleAuth;
using Lrb.Application.Common.Persistence;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Infrastructure.GoogleSheets;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Serilog;
using System.Net;
using System.Text;

namespace Lrb.Infrastructure.GoogleAuth
{
    public class WebGoogleAuth2Service : IWebGoogleOAuth2Service
    {
        private GoogleAuthSettings _settings;
        private readonly ITenantInfo? _currentTenant;
        private readonly ILogger _logger;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly GoogleSheetsAuthSettings _googleSheetsSettings;

        public WebGoogleAuth2Service(IOptions<GoogleAuthSettings> options, ITenantInfo currentTenant, Serilog.ILogger logger, ILeadRepositoryAsync leadRepositoryAsync, IOptions<GoogleSheetsAuthSettings> googleSheetsSettings)
        {
            _settings = options.Value;
            _currentTenant = currentTenant;
            _settings.RedirectUri = string.IsNullOrWhiteSpace(_settings?.RedirectUri)
                ? default
                : string.Format(_settings.RedirectUri, _currentTenant?.Id).Replace("www.", "");
            _logger = logger;
            _leadRepositoryAsync = leadRepositoryAsync;
            _googleSheetsSettings = googleSheetsSettings.Value;
        }
        public Task<TokenClass> GetAuthUsingRefreshToken(string refreshToken)
        {
            _logger.Information("WebGoogleAuth2Service -> GetAuthUsingRefreshToken() called, Refresh Token: " + refreshToken);
            _logger.Information("WebGoogleAuth2Service -> GetAuthUsingRefreshToken() called, Redirect uri: " + _settings.RedirectUri);
            var poststring = "access_type=offline&grant_type=refresh_token&refresh_token=" + refreshToken + "&client_id=" + _settings.ClientID + "&client_secret=" + _settings.ClientSecret + "&redirect_uri=" + _settings.RedirectUri + "";
            string authTokenUrl = "https://oauth2.googleapis.com/token";
            var request = (HttpWebRequest)WebRequest.Create(authTokenUrl);
            request.ContentType = "application/x-www-form-urlencoded";
            request.ContentLength = 261;
            request.Method = "POST";
            UTF8Encoding utfenc = new();
            byte[] bytes = utfenc.GetBytes(poststring);
            Stream outputstream = null;
            try
            {
                request.ContentLength = bytes.Length;
                using (outputstream = request.GetRequestStream())
                {
                    outputstream.Write(bytes, 0, bytes.Length);
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "WebGoogleAuth2Service -> GetAuthUsingRefreshToken()"
                };
                _leadRepositoryAsync.AddErrorAsync(error);
            }
            var response = (HttpWebResponse)request.GetResponse();
            var streamReader = new StreamReader(response.GetResponseStream());
            string responseFromServer = streamReader.ReadToEnd();
            TokenClass result = JsonConvert.DeserializeObject<TokenClass>(responseFromServer);
            return Task.FromResult(result);
        }

        public async Task<TokenClass> GetToken(string code)
        {
            _logger.Information("WebGoogleAuth2Service -> GetToken() called, Auth code: " + code.ToString());
            _logger.Information("WebGoogleAuth2Service -> GetToken() called, Auth code: {0}", code.ToString());
            _logger.Information("WebGoogleAuth2Service -> GetToken() called, Redirect uri: " + _settings.RedirectUri);
            var poststring = "access_type=offline&grant_type=authorization_code&code=" + code + "&client_id=" + _settings.ClientID + "&client_secret=" + _settings.ClientSecret + "&redirect_uri=" + _settings.RedirectUri + "";
            string url = "https://accounts.google.com/o/oauth2/token";
            using var client = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Post, url);
            //HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            var collection = new List<KeyValuePair<string, string>>();
            collection.Add(new("access_type", "offline"));
            collection.Add(new("grant_type", "authorization_code"));
            collection.Add(new("code", code));
            collection.Add(new("client_id", _settings?.ClientID ?? string.Empty));
            collection.Add(new("client_secret", _settings?.ClientSecret ?? string.Empty));
            collection.Add(new("redirect_uri", _settings?.RedirectUri ?? string.Empty));
            var content = new FormUrlEncodedContent(collection);
            request.Content = content;
            //UTF8Encoding utfenc = new();
            //byte[] bytes = utfenc.GetBytes(poststring);
            //Stream outputstream = null;
            HttpResponseMessage response = new();
            try
            {
                response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();
                //request.ContentLength = bytes.Length;
                //outputstream = request.GetRequestStream();
                //outputstream.Write(bytes, 0, bytes.Length);
            }
            catch { }
            //var response = (HttpWebResponse)request.GetResponse();
            //var streamReader = new StreamReader(response.GetResponseStream());
            //string responseFromServer = streamReader.ReadToEnd();
            var responseFromServer = await response.Content.ReadAsStringAsync();
            _logger.Information("WebGoogleAuth2Service -> GetToken() called, Response: " + responseFromServer);
            TokenClass result = JsonConvert.DeserializeObject<TokenClass>(responseFromServer);
            return result;
        }

        public Task<UserClass> GetUserProfile(string accessToken)
        {
            string url = "https://www.googleapis.com/oauth2/v1/userinfo?alt=json&access_token=" + accessToken + "";
            WebRequest request = WebRequest.Create(url);
            request.Credentials = CredentialCache.DefaultCredentials;
            WebResponse response = request.GetResponse();
            Stream dataStream = response.GetResponseStream();
            StreamReader reader = new(dataStream);
            string responseFromServer = reader.ReadToEnd();
            reader.Close();
            response.Close();
            UserClass userInfo = JsonConvert.DeserializeObject<UserClass>(responseFromServer);
            return Task.FromResult(userInfo);
        }
        public Task<TokenClass> GetTokenGoogleSheets(string code)
        {
            _logger.Information("WebGoogleAuth2Service -> GetToken() called, Auth code: " + code.ToString());
            _logger.Information("WebGoogleAuth2Service -> GetToken() called, Auth code: {0}", code.ToString());
            _logger.Information("WebGoogleAuth2Service -> GetToken() called, Redirect uri: " + _settings.RedirectUri);
            var poststring = "grant_type=authorization_code&code=" + code + "&client_id=" + _googleSheetsSettings.ClientID + "&client_secret=" + _googleSheetsSettings.ClientSecret + "&redirect_uri=" + _googleSheetsSettings.RedirectUri + "";
            string url = "https://accounts.google.com/o/oauth2/token";
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.ContentType = "application/x-www-form-urlencoded";
            request.Method = "POST";
            UTF8Encoding utfenc = new();
            byte[] bytes = utfenc.GetBytes(poststring);
            Stream outputstream = null;
            try
            {
                request.ContentLength = bytes.Length;
                outputstream = request.GetRequestStream();
                outputstream.Write(bytes, 0, bytes.Length);
            }
            catch { }
            var response = (HttpWebResponse)request.GetResponse();
            var streamReader = new StreamReader(response.GetResponseStream());
            string responseFromServer = streamReader.ReadToEnd();
            _logger.Information("WebGoogleAuth2Service -> GetToken() called, Response: " + responseFromServer);
            TokenClass result = JsonConvert.DeserializeObject<TokenClass>(responseFromServer);
            return Task.FromResult(result);
        }
    }
    public class MobileGoogleAuth2Service : IMobileGoogleOAuth2Service
    {
        private readonly GoogleAuthSettings _settings;
        private readonly ITenantInfo _currentTenant;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;

        public MobileGoogleAuth2Service(IOptions<GoogleAuthSettings> options, ITenantInfo currentTenant, ILeadRepositoryAsync leadRepositoryAsync)
        {
            _settings = options.Value;
            _currentTenant = currentTenant;
            _settings.RedirectUri = string.IsNullOrWhiteSpace(_settings?.RedirectUri)
                ? default
                : string.Format(_settings.RedirectUri, _currentTenant?.Id).Replace("www.", "");
            _leadRepositoryAsync = leadRepositoryAsync;
        }
        public Task<TokenClass> GetAuthUsingRefreshToken(string refreshToken)
        {
            var poststring = "access_type=offline&grant_type=refresh_token&refresh_token=" + refreshToken + "&client_id=" + _settings.ClientID + "&redirect_uri=" + _settings.RedirectUri + "";
            string authTokenUrl = "https://oauth2.googleapis.com/token";
            var request = (HttpWebRequest)WebRequest.Create(authTokenUrl);
            request.ContentType = "application/x-www-form-urlencoded";
            request.ContentLength = 261;
            request.Method = "POST";
            UTF8Encoding utfenc = new();
            byte[] bytes = utfenc.GetBytes(poststring);
            Stream outputstream = null;
            try
            {
                request.ContentLength = bytes.Length;
                using (outputstream = request.GetRequestStream())
                {
                    outputstream.Write(bytes, 0, bytes.Length);
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "MobileGoogleAuth2Service -> GetAuthUsingRefreshToken()"
                };
                _leadRepositoryAsync.AddErrorAsync(error);
            }
            var response = (HttpWebResponse)request.GetResponse();
            var streamReader = new StreamReader(response.GetResponseStream());
            string responseFromServer = streamReader.ReadToEnd();
            TokenClass result = JsonConvert.DeserializeObject<TokenClass>(responseFromServer);
            return Task.FromResult(result);
        }

        public Task<TokenClass> GetToken(string code)
        {
            var poststring = "access_type=offline&grant_type=authorization_code&code=" + code + "&client_id=" + _settings.ClientID + "&redirect_uri=" + _settings.RedirectUri + "";
            string url = "https://accounts.google.com/o/oauth2/token";
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.ContentType = "application/x-www-form-urlencoded";
            request.Method = "POST";
            UTF8Encoding utfenc = new();
            byte[] bytes = utfenc.GetBytes(poststring);
            Stream outputstream = null;
            try
            {
                request.ContentLength = bytes.Length;
                outputstream = request.GetRequestStream();
                outputstream.Write(bytes, 0, bytes.Length);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "WebGoogleAuth2Service -> GetToken()"
                };
                _leadRepositoryAsync.AddErrorAsync(error);
            }
            var response = (HttpWebResponse)request.GetResponse();
            var streamReader = new StreamReader(response.GetResponseStream());
            string responseFromServer = streamReader.ReadToEnd();
            TokenClass result = JsonConvert.DeserializeObject<TokenClass>(responseFromServer);
            return Task.FromResult(result);
        }

        public Task<UserClass> GetUserProfile(string accessToken)
        {
            string url = "https://www.googleapis.com/oauth2/v1/userinfo?alt=json&access_token=" + accessToken + "";
            WebRequest request = WebRequest.Create(url);
            request.Credentials = CredentialCache.DefaultCredentials;
            WebResponse response = request.GetResponse();
            Stream dataStream = response.GetResponseStream();
            StreamReader reader = new(dataStream);
            string responseFromServer = reader.ReadToEnd();
            reader.Close();
            response.Close();
            UserClass userInfo = JsonConvert.DeserializeObject<UserClass>(responseFromServer);
            return Task.FromResult(userInfo);
        }



    }
}
