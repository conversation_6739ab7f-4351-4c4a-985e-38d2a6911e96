﻿using Lrb.Application.CustomStatus.Web;
using Lrb.Application.LeadRotation.Web.Dtos;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Team.Web.Dtos
{
    public class UpdateRetentionDto : CreateRetentionTeamsDto
    {

    }
    public class CreateRetentionTeamsDto : BaseRetentionTeamsDto
    {
        public List<Guid>? SubStatusesIds { get; set; }
        public List<Guid>? StatusesIds { get; set; }
    }
    public class ViewRetentionTeamsDto : BaseRetentionTeamsDto
    {
        public List<ViewCustomStatusDto>? SubStatuses { get; set; }
        public List<ViewCustomStatusDto>? Statuses { get; set; }
        public List<TeamConfigurationDto>? Configurations { get; set; }
    }
    public class BaseRetentionTeamsDto
    {
        public Guid? Id { get; set; }
        public bool? IsRotationEnabled { get; set; }
        public TeamConfigurationDto? Configuration { get; set; }
        public bool? IsForRetention { get; set; }
    }
    public class TeamConfigurationDto
    {
        public Guid? Id { get; set; }
        public TimeSpan? RotationTime { get; set; }
        public DateTime? ShiftStartTime { get; set; }
        public DateTime? ShiftEndTime { get; set; }
        public int NoOfRotation { get; set; }
        public Guid? TeamId { get; set; }
        public List<DayOfWeek>? DayOfWeeks { get; set; }
        public TimeSpan? StartTime { get; set; }
        public TimeSpan? EndTime { get; set; }
        public bool? IsSourceLevel { get; set; }
        public List<LeadSource>? LeadSources { get; set; }
        public bool? IsForRetention { get; set; }
        public List<Guid>? IntegrationAccountIds { get; set; }
        public TimeSpan? BufferTime { get; set; }
    }
    public class ViewTeamLeadRotationInfoDto : BaseTeamLeadRotationInfoDto
    {
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public UserDto? LastModifiedByUser { get; set; }
        public List<TeamConfigurationDto>? Configurations { get; set; }

    }

    public class BaseTeamLeadRotationInfoDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public Guid? Manager { get; set; }
        public List<Guid>? UserIds { get; set; }
        public TeamConfigurationDto? Configuration { get; set; }
        public Guid? IntegrationAccountInfoId { get; set; }
    }
}
