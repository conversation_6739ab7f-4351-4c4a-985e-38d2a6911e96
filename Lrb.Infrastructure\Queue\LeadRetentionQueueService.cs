﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Channels;
using System.Threading.Tasks;

namespace Lrb.Infrastructure.Queue
{
    public class LeadRetentionQueueService : BackgroundService
    {
        private readonly Channel<QueueTriggerMessage> _channel;
        private readonly IHttpClientFactory _httpClientFactory;

        public LeadRetentionQueueService(IHttpClientFactory httpClientFactory)
        {
            _channel = Channel.CreateBounded<QueueTriggerMessage>(new BoundedChannelOptions(1000)
            {
                FullMode = BoundedChannelFullMode.Wait
            });
            _httpClientFactory = httpClientFactory;
        }

        public void Enqueue(QueueTriggerMessage message)
        {
            if (string.IsNullOrWhiteSpace(message.Url))
                throw new ArgumentException("URL must not be empty", nameof(message.Url));

            _channel.Writer.TryWrite(message);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            var httpClient = _httpClientFactory.CreateClient();

            await foreach (var message in _channel.Reader.ReadAllAsync(stoppingToken))
            {
                try
                {
                    var json = JsonConvert.SerializeObject(message.Payload);
                    using var content = new StringContent(json, Encoding.UTF8, "application/json");
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));

                    await httpClient.PostAsync(message.Url, content, cts.Token);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Background POST to {message.Url} failed: {ex.Message}");
                }
            }
        }
    }
}
