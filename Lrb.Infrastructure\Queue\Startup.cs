﻿using Lrb.Application.Common.LeadRotation;
using Lrb.Infrastructure.LeadRotation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Infrastructure.Queue
{
    public static class Startup
    {
        public static IServiceCollection AddQueueService(this IServiceCollection service, IConfiguration config)
        {
            service.AddHttpClient();
            service.AddSingleton<LeadRetentionQueueService>();
            service.AddHostedService(provider => provider.GetRequiredService<LeadRetentionQueueService>());
            return service;
        }
    }
}
