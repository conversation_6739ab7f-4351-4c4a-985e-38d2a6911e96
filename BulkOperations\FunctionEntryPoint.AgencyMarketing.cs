﻿using Amazon.DynamoDBv2.Model;
using Lrb.Application.Agency.Web.Dtos;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Marketing.Web.Dtos;
using Lrb.Application.Marketing.Web.Mapping;
using Lrb.Application.Marketing.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Marketing;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using PhoneNumbers;
using System.Collections.Concurrent;
using System.Data;
using System.Text.RegularExpressions;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task MarketingAgencyHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            BulkMarketingAgencyUploadTracker? bulkAgencyUpload = (await _bulkMarketingUploadRepo.ListAsync(new GetBulkMarketingAgencyByTrackerIdSpec(input.TrackerId))).FirstOrDefault();
            try
            {
                if (bulkAgencyUpload != null)
                {
                    try
                    {
                        bulkAgencyUpload.MappedColumnData = bulkAgencyUpload.MappedColumnData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                        bulkAgencyUpload.Status = Lrb.Domain.Enums.UploadStatus.Started;
                        bulkAgencyUpload.LastModifiedBy = input.CurrentUserId;
                        bulkAgencyUpload.CreatedBy = input.CurrentUserId;
                        await _bulkMarketingUploadRepo.UpdateAsync(bulkAgencyUpload);
                        Console.WriteLine($"handler() -> MarketingAgencyHandler Updated Status: {bulkAgencyUpload.Status} \n {JsonConvert.SerializeObject(bulkAgencyUpload)}");
                        #region fetch all required data
                        var existingAgencies = await _agencyRepo.ListAsync(new GetAllBulkAgenciesSpecs(), cancellationToken);
                        var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(cancellationToken));
                        #endregion

                        #region Convert To DataTable
                        Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", bulkAgencyUpload.S3BucketKey);
                        DataTable dataTable = new();
                        if (bulkAgencyUpload.S3BucketKey.Split('.').LastOrDefault() == "csv")
                        {
                            using MemoryStream memoryStream = new();
                            fileStream.CopyTo(memoryStream);
                            dataTable = CSVHelper.CSVToDataTable(memoryStream);
                        }
                        else
                        {
                            dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, bulkAgencyUpload.SheetName);
                        }

                        List<InvalidAgencyDto> invalids = new();
                        int totalRows = dataTable.Rows.Count;
                        for (int i = totalRows - 1; i >= 0; i--)
                        {
                            var row = dataTable.Rows[i];
                            var data1 = row[bulkAgencyUpload.MappedColumnData[MarketingDataColumns.Name]].ToString();
                            var data2 = row[bulkAgencyUpload.MappedColumnData[MarketingDataColumns.PhoneNumber]].ToString();
                            if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                            {
                                row.Delete();
                            }
                            else if (string.IsNullOrEmpty(row[bulkAgencyUpload.MappedColumnData[MarketingDataColumns.Name]].ToString()) && string.IsNullOrEmpty(row[bulkAgencyUpload.MappedColumnData[MarketingDataColumns.PhoneNumber]].ToString()))
                            {
                                var notes = string.Join(",", row.ItemArray.Where(i => !string.IsNullOrEmpty(i.ToString())));
                                var invalidAgency = new InvalidAgencyDto
                                {
                                    Errors = "contact number and name are empty",
                                    //Notes = notes
                                };

                                row.Delete();
                            }
                        }
                        if (dataTable.Rows.Count <= 0)
                        {
                            throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                        }
                        totalRows = dataTable.Rows.Count;
                        Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
                        #endregion

                        #region Checking For new Prosperties and Project and Agencies

                        List<Property> newProperties = new();
                        List<Lrb.Domain.Entities.Project> newProjects = new();

                        #endregion

                        var unMappedColumn = dataTable.GetUnmappedAgencyColumnNames(bulkAgencyUpload?.MappedColumnData);
                        var globalSettingInfoList = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec(), cancellationToken);

                        var toAgencies = dataTable.ConvertToAgency(bulkAgencyUpload.MappedColumnData, unMappedColumn, globalSettingInfoList, bulkAgencyUpload);


                        string callingCode = globalSettingInfoList?.Countries?.FirstOrDefault().DefaultCallingCode;

                        List<Task<(string ContactNo, string? AlternateContactNo)>> validationTasks = new List<Task<(string, string?)>>();

                        foreach (DataRow row in dataTable.Rows)
                        {
                            if (bulkAgencyUpload.MappedColumnData != null && bulkAgencyUpload.MappedColumnData.ContainsKey(MarketingDataColumns.Name) && bulkAgencyUpload.MappedColumnData.ContainsKey(MarketingDataColumns.PhoneNumber))
                            {
                                string countrycodeString = null;
                                if ((bulkAgencyUpload.MappedColumnData?.ContainsKey(MarketingDataColumns.CountryCode) ?? false) && (bulkAgencyUpload.MappedColumnData[MarketingDataColumns.CountryCode] != null))
                                {
                                    countrycodeString = row[bulkAgencyUpload.MappedColumnData[MarketingDataColumns.CountryCode]]?.ToString() ?? string.Empty;
                                }
                                if (string.IsNullOrWhiteSpace(countrycodeString))
                                {
                                    countrycodeString = callingCode ?? "+91";
                                }
                                int countrycode = 0;
                                try
                                {
                                    countrycode = Convert.ToInt32(countrycodeString);
                                }
                                catch
                                {
                                    countrycode = Convert.ToInt32(callingCode);

                                }

                                var globalSettingInfo = globalSettingInfoList;
                                string ContactNumber = row[bulkAgencyUpload.MappedColumnData[MarketingDataColumns.PhoneNumber]]?.ToString() ?? string.Empty;
                                string AlternativeContactNo = string.Empty;
                                int altercountrycode = 0;
                                string altrenativeCountryCodeString = null;

                                string name = null;
                                if ((bulkAgencyUpload.MappedColumnData[MarketingDataColumns.Name] != null))
                                {
                                    name = row[bulkAgencyUpload.MappedColumnData[MarketingDataColumns.Name]]?.ToString() ?? string.Empty;
                                }
                                Guid currentuserId = input.CurrentUserId;
                                var validationTask = ValidateProspectContactNoAsync1(ContactNumber, AlternativeContactNo, countrycode, altercountrycode, globalSettingInfo, toAgencies, invalids, name, currentuserId, cancellationToken);
                                validationTasks.Add(validationTask);
                            }
                            else
                            {
                                throw new Exception("Name and ContactNumber Need Map.");
                            }
                        }
                        List<string> contactNumbers = validationTasks.Where(t => !t.IsFaulted && !t.IsCanceled).Select(t => t.Result.ContactNo)
                        .Where(contactNo => !string.IsNullOrWhiteSpace(contactNo) && contactNo.Length <= 25)
                        .ToList();
                        List<string> altcontactNumbers = validationTasks.Where(t => !t.IsFaulted && !t.IsCanceled).Select(t => t.Result.AlternateContactNo)
                        .Where(i => !string.IsNullOrWhiteSpace(i) && i.Length <= 25)
                        .ToList();
                        toAgencies = toAgencies
                           .Where(age => age.Name != string.Empty)
                           .DistinctBy(prospect => prospect.Name)
                           .ToList();
                        validationTasks = validationTasks
                        .Where(t => !t.IsFaulted && !t.IsCanceled && !string.IsNullOrWhiteSpace(t.Result.ContactNo) && t.Result.ContactNo.Length <= 25)
                        .DistinctBy(t => t.Result.ContactNo)
                        .ToList();
                        var distinctCount = toAgencies.Count();
                        Console.WriteLine($"handler() -> Total Distinct Agency: {distinctCount}");
                        var existingContactNos = existingAgencies.Select(i => i.Name).ToList();
                        List<Agency> agencyToUpdate = new();
                        foreach (Agency agency in toAgencies)
                        {


                            if (existingContactNos.Any(i => !string.IsNullOrWhiteSpace(agency.Name)))
                            {
                                var invalidagency = agency.Adapt<InvalidAgencyDto>();

                                invalidagency.Errors = "Duplicate Agency";
                                var duplicateAgency = existingAgencies.FirstOrDefault(i => !string.IsNullOrWhiteSpace(agency.Name) && i.Name != null && i.Name.Equals(agency.Name));
                                if (duplicateAgency != null)
                                {
                                    invalids.Add(invalidagency);
                                }
                            }
                        }
                        toAgencies.RemoveAll(i => agencyToUpdate.Select(i => i.ContactNo).Contains(i.ContactNo));
                        toAgencies.RemoveAll(i => invalids.Select(i => i.Name).Contains(i.Name));

                        bulkAgencyUpload.Status = UploadStatus.InProgress;
                        bulkAgencyUpload.TotalCount = totalRows;
                        bulkAgencyUpload.DistinctCount = distinctCount;
                        bulkAgencyUpload.LastModifiedBy = input.CurrentUserId;
                        bulkAgencyUpload.CreatedBy = input.CurrentUserId;
                        if (invalids.Any())
                        {
                            bulkAgencyUpload.DuplicateCount = invalids.Where(i => i.Errors == "Duplicate Agency").Count();
                            bulkAgencyUpload.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty").Count();
                            bulkAgencyUpload.Count = agencyToUpdate.Count();
                            byte[] bytes = MarketingAgencyHelper.CreateExcelData(invalids).ToArray();
                            string fileName = $"InvalidAgency-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                            string folder = "Agencies";
                            var key = await _blobStorageService?.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                            bulkAgencyUpload.InvalidS3BucketKey = key;
                        }
                        await _bulkMarketingUploadRepo.UpdateAsync(bulkAgencyUpload);
                        BulkAgencyUploadBackgroundDto backgroundDto = new();
                        if (toAgencies.Count > 0)
                        {
                            int agencyPerchunk = toAgencies.Count > 5000 ? 5000 : toAgencies.Count;
                            var chunks = toAgencies.Chunk(agencyPerchunk).Select(i => new ConcurrentBag<Agency>(i));
                            List<Task> tasks = new();
                            var currentUserId = _currentUser.GetUserId();
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                            if (currentUserId == Guid.Empty)
                            {
                                currentUserId = input.CurrentUserId;
                            }
                            Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                            var chunkIndex = 1;
                            foreach (var chunk in chunks.ToList())
                            {
                                backgroundDto = new BulkAgencyUploadBackgroundDto()
                                {
                                    CurrentUserId = currentUserId,
                                    TrackerId = bulkAgencyUpload.Id,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Agencies = new(chunk),
                                    UserIds = new(bulkAgencyUpload.UserIds ?? new()),
                                    Users = users.ToList()
                                };
                                await ExecuteDBOperationsAsync(backgroundDto);
                                chunkIndex++;
                            }
                        }
                        bulkAgencyUpload.Status = UploadStatus.Completed;
                        bulkAgencyUpload.LastModifiedBy = input.CurrentUserId;
                        bulkAgencyUpload.CreatedBy = input.CurrentUserId;
                        await _bulkMarketingUploadRepo.UpdateAsync(bulkAgencyUpload);
                    }

                    catch (Exception ex)
                    {
                        bulkAgencyUpload = await _bulkMarketingUploadRepo.GetByIdAsync(bulkAgencyUpload.Id);
                        bulkAgencyUpload.Status = UploadStatus.Failed;
                        bulkAgencyUpload.Message = ex.Message;
                        bulkAgencyUpload.LastModifiedBy = input.CurrentUserId;
                        bulkAgencyUpload.CreatedBy = input.CurrentUserId;
                        await _bulkMarketingUploadRepo.UpdateAsync(bulkAgencyUpload);
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CreateBulkProspectleadUploadTrackerUsingEPPlus -> MarketingAgencyHandler()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkProspectleadUploadTrackerUsingEPPlus -> MarketingAgencyHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                Console.WriteLine($"handler() -> Exception:  {JsonConvert.SerializeObject(ex, settings: new() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;
            }
        }

        public async Task ExecuteDBOperationsAsync(BulkAgencyUploadBackgroundDto dto)
        {
            BulkChannelPartnerUploadTracker bulkChannelPartner = new();
            var tracker = (await _bulkMarketingUploadRepo.ListAsync(new GetBulkMarketingAgencyByTrackerIdSpec(dto.TrackerId))).FirstOrDefault();
            try
            {
                try
                {
                     await _agencyRepo.AddRangeAsync(dto.Agencies);
                    await _dapperRepository.UpdateLatModifiedDateAsync(dto?.TenantInfoDto?.Id, (int)EntityType.Agency);


                }
                catch (Exception e)
                {

                }
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.Agencies.Count;
                    if (dto.IsLastChunk)
                    {
                        tracker.Status = UploadStatus.Completed;
                    }
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _bulkMarketingUploadRepo.UpdateAsync(tracker);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == tracker.InvalidCount)
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _bulkMarketingUploadRepo.UpdateAsync(tracker);
            }
        }
        public async Task<(string ContactNo, string? AltContactNo)> ValidateProspectContactNoAsync1(string contactNum, string? alternateContactNum, int countryCode, int alternativecountryCode, GlobalSettings globalSettingInfo, List<Agency> agencies, List<InvalidAgencyDto> invalids, string? name, Guid currentUserId, CancellationToken cancellationToken = default)
        {
            string contactNo = Regex.Replace(contactNum, "[^0-9]", "");
            string alternateContactNo = Regex.Replace(alternateContactNum, "[^0-9]", "");
            try
            {

                string altContactWithCountryCode = null;

                PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
                List<string> regionCodes = CountryCodeToRegionCodeMap.GetCountryCodeToRegionCodeMap().GetValueOrDefault(countryCode, new List<string>());
                string defaultRegion = regionCodes.FirstOrDefault();
                if (defaultRegion == null)
                {
                    defaultRegion = globalSettingInfo?.Countries?.FirstOrDefault()?.Code ?? "IN";
                }
                PhoneNumber phoneNumber = phoneUtil.Parse(contactNo, defaultRegion);
                PhoneNumber numberExample = phoneUtil.GetExampleNumberForType(defaultRegion, PhoneNumberType.MOBILE);
                string formattedNumber = phoneUtil.Format(numberExample, PhoneNumberFormat.E164);
                string contactWithCountryCode = phoneUtil.Format(phoneNumber, PhoneNumberFormat.E164);
                string numericMobileNumber = Regex.Replace(formattedNumber, @"\D", "");

                string altNumericMobileNumber = string.Empty;
                PhoneNumber phoneNumberAltcontNo = null;
                string altContactWithCountryCode1 = string.Empty;
                if (alternateContactNo != string.Empty && alternativecountryCode != 0)
                {
                    List<string> regionCodesforaltcontactNo = CountryCodeToRegionCodeMap.GetCountryCodeToRegionCodeMap().GetValueOrDefault(alternativecountryCode, new List<string>());
                    string defaultRegionforAltContNo = regionCodesforaltcontactNo.FirstOrDefault();
                    if (defaultRegionforAltContNo == null)
                    {
                        defaultRegionforAltContNo = globalSettingInfo?.Countries?.FirstOrDefault()?.Code ?? "IN";
                    }
                    try
                    {
                        phoneNumberAltcontNo = phoneUtil.Parse(alternateContactNo, defaultRegionforAltContNo);

                        PhoneNumber numberExamplealtConctNum = phoneUtil.GetExampleNumberForType(defaultRegionforAltContNo, PhoneNumberType.MOBILE);
                        string formattedNumberaltContNo = phoneUtil.Format(numberExamplealtConctNum, PhoneNumberFormat.E164);
                        altContactWithCountryCode = phoneUtil.Format(phoneNumberAltcontNo, PhoneNumberFormat.E164);
                        altNumericMobileNumber = Regex.Replace(formattedNumberaltContNo, @"\D", "");
                    }
                    catch
                    {
                    }
                }
                bool isValid;
                if ((numericMobileNumber.Length == contactWithCountryCode.Length - 1))
                {
                    isValid = true;
                    try
                    {
                        foreach (var agency in agencies)
                        {
                            if (Regex.Replace(agency.ContactNo, "[^0-9]", "") == contactNo && agency.Name == name)
                            {
                                agency.ContactNo = contactWithCountryCode;
                                agency.CreatedBy = currentUserId;
                                agency.LastModifiedBy = currentUserId;
                                if (string.IsNullOrWhiteSpace(agency.Name))
                                {
                                    var invalidAgency = agency.Adapt<InvalidAgencyDto>();
                                    invalidAgency.Errors = "Invalid Name";
                                    invalids.Add(invalidAgency);
                                }

                            }

                        }
                    }
                    catch
                    {

                    }
                }
                else if ((contactNo.Length > 6 && contactNo.Length <= 20))
                {
                    isValid = true;

                    try
                    {
                        foreach (var agency in agencies)
                        {
                            if (Regex.Replace(agency.ContactNo, "[^0-9]", "") == contactNo && agency.Name == name)
                            {
                                if (contactNum.StartsWith("+"))
                                {
                                    agency.ContactNo = "+" + contactNo;
                                    agency.CreatedBy = currentUserId;
                                    agency.LastModifiedBy = currentUserId;
                                }
                                else
                                {
                                    agency.ContactNo = "+" + countryCode + contactNo;
                                    agency.CreatedBy = currentUserId;
                                    agency.LastModifiedBy = currentUserId;

                                }
                                if (string.IsNullOrWhiteSpace(agency.Name))
                                {

                                    var invalidAgency = agency.Adapt<InvalidAgencyDto>();
                                    invalidAgency.Errors = "Invalid Name";
                                    invalids.Add(invalidAgency);
                                }

                            }

                        }
                    }
                    catch
                    {

                    }
                }
                else
                {
                    isValid = false;

                }

                if (!isValid)
                {
                    return (string.Empty, string.Empty);
                }
                else
                {
                    if (phoneUtil.GetRegionCodeForNumber(phoneNumber) != defaultRegion)
                    {
                        throw new Exception("Invalid ContactNo - International numbers not allowed");
                    }
                    string contactWithCountryCode1 = phoneUtil.Format(phoneNumber, PhoneNumberFormat.E164);
                    if (phoneNumberAltcontNo != null)
                    {
                        altContactWithCountryCode1 = phoneUtil.Format(phoneNumberAltcontNo, PhoneNumberFormat.E164);
                    }
                    return (contactWithCountryCode1, altContactWithCountryCode1);
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
