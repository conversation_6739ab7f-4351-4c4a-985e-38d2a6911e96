﻿namespace Lrb.Application.DataManagement.Web.Specs
{
    public class GetProspectSourceByIdSpecs : Specification<MasterProspectSource>
    {
        public GetProspectSourceByIdSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id);
        }
    }

    public class GetProspectSourceByDisplayNameSpecs : Specification<MasterProspectSource>
    {
        public GetProspectSourceByDisplayNameSpecs(string source)
        {
            Query.Where(i => i.DisplayName.ToLower().Trim().Replace(" ", "").Contains(source.ToLower().Trim().Replace(" ", "")));
        }
    }

    public class GetAllProspectSourceByIdsSpecs : Specification<MasterProspectSource>
    {
        public GetAllProspectSourceByIdsSpecs(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id));
        }
    }
    public class GetProspectStatusForWebSpecs : Specification<CustomProspectStatus>
    {
        public GetProspectStatusForWebSpecs()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }
}
