﻿namespace Lrb.Application.UserDetails.Mobile.Request
{
    public class UpdateUserSearchResultsRequest : IRequest<Response<bool>>
    {
        public ModulePropertiesType? Module { get; set; }
        public List<string>? SearchResults { get; set; }

    }
    public class UpdateUserSearchResultsRequestHandler : IRequestHandler<UpdateUserSearchResultsRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.UserSettings> _userSettingsRepo;
        private readonly ICurrentUser _currentUser;
        public UpdateUserSearchResultsRequestHandler(ICurrentUser currentUser, IRepositoryWithEvents<Domain.Entities.UserSettings> userSettingsRepo)
        {
            _currentUser = currentUser;
            _userSettingsRepo = userSettingsRepo;
        }
        public async Task<Response<bool>> Handle(UpdateUserSearchResultsRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var userSettings = await _userSettingsRepo.FirstOrDefaultAsync(new GetUserSettingsSpec(currentUserId), cancellationToken);
            if (userSettings == null)
            {
                userSettings = new UserSettings
                {
                    UserId = _currentUser.GetUserId(),
                    CreatedBy = _currentUser.GetUserId(),
                    LastModifiedBy = _currentUser.GetUserId(),
                    SearchResults = new Dictionary<ModulePropertiesType, List<string>>
                    {
                        [request.Module.Value] = request.SearchResults ?? new List<string>()
                    }
                };

                await _userSettingsRepo.AddAsync(userSettings, cancellationToken);
            }
            else
            {
                if (userSettings.SearchResults == null)
                {
                    userSettings.SearchResults = new Dictionary<ModulePropertiesType, List<string>>();
                }
                userSettings.SearchResults[request.Module.Value] = request.SearchResults ?? new List<string>();
                await _userSettingsRepo.UpdateAsync(userSettings, cancellationToken);
            }
            return new(true);
        }
    }
}
