﻿using System.Reflection;
using System.Text.RegularExpressions;

namespace Lrb.Application.Utils
{
    public static class ObjectHelper
    {
        public static bool IsNullOrEmpty<T>(this T entity)
        {
            if (entity != null)
            {
                List<PropertyInfo> properties = typeof(T).GetProperties().ToList();
                properties = properties.Where(i => i.Name != "Id").ToList();
                if (properties.All(i => i == default))
                {
                    return false;
                }
            }
            return false;
        }
    }
}
