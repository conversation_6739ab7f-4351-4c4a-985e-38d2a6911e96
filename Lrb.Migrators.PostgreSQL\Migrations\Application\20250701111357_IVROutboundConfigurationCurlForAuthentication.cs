﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class IVROutboundConfigurationCurlForAuthentication : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CurlForAuthentication",
                schema: "LeadratBlack",
                table: "IVROutboundConfigurations",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ResponseParameterKey",
                schema: "LeadratBlack",
                table: "IVROutboundConfigurations",
                type: "integer",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CurlForAuthentication",
                schema: "LeadratBlack",
                table: "IVROutboundConfigurations");

            migrationBuilder.DropColumn(
                name: "ResponseParameter<PERSON><PERSON>",
                schema: "LeadratBlack",
                table: "IVROutboundConfigurations");
        }
    }
}
