using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace AuditOperation
{
    public static class Function1
    {
        private static List<Trail> Trail = new List<Trail>();

        [FunctionName("AuditTrails")]
        public static async Task<IActionResult> AuditTrails(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                log.LogInformation($"C# Http trigger function started at: {DateTime.UtcNow}");
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"C# Http trigger function started at: {DateTime.UtcNow} " + requestBody);
                var data = JsonConvert.DeserializeObject<List<Trail>>(requestBody);
                var startup = new Startup("prd");
                if (data?.Any() ?? false)
                {
                    Trail.AddRange(data);
                }
                else
                {
                    log.LogInformation($"No items found!");
                }

                log.LogInformation($"C# Http trigger function ended at: {DateTime.UtcNow}");
                Console.WriteLine($"C# Http trigger function ended at: {DateTime.UtcNow}");
                return new OkObjectResult("AuditTrails updated in cache");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling AuditTrails:" + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
                return new OkObjectResult(ex);
            }
        }
        [FunctionName("AuditTimeTrigger")]
        public async static Task AuditTimeTrigger([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                log.LogInformation($"C# Timer trigger function started at: {DateTime.UtcNow}");
                Console.WriteLine($"C# Timer trigger function started at: {DateTime.UtcNow}");
                var startup = new Startup("prd");
                // Retrieve the list from the cache
                if (Trail?.Any() ?? false)
                {
                    var trails = new List<Trail>(Trail.OrderBy(i => i.DateTime).ToList());
                    Trail = new List<Trail>();
                    IServiceProvider provider = startup.ConfigureServices();
                    ICosmosService cosmos = provider.GetRequiredService<ICosmosService>();
                    var connection = await cosmos.InitCosmosDbAsync();
                    var auditTrailsList = trails
                        .Select((trail, index) => new { trail, index })
                        .GroupBy(x => x.index / 100)
                        .Select(g => new AuditTrails
                        {
                            DateTimeRange = g.Min(x => x.trail.DateTime).ToString("dd-MM-yyyy HH:mm:ss") + " to " + g.Max(x => x.trail.DateTime).ToString("dd-MM-yyyy HH:mm:ss"),
                            Trails = g.Select(x => x.trail).ToList()
                        })
                        .ToList();
                    foreach (var auditTrails in auditTrailsList)
                    {
                        _ = Task.Run(() => cosmos.AddItemAsync(auditTrails, connection));
                    }
                }
                else
                {
                    log.LogInformation($"No items found!");
                }
                log.LogInformation($"C# Timer trigger function ended at: {DateTime.UtcNow}");
                Console.WriteLine($"C# Timer trigger function ended at: {DateTime.UtcNow}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling QaJustLeadTrigger." + ex.Message);
            }
        }
    }
}
