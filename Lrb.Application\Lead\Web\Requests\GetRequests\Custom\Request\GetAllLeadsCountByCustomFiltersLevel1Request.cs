﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Lead.Web
{
    public class GetAllLeadsCountByCustomFiltersLevel1Request : GetAllLeadsParametersNewFilters, IRequest<Response<List<CustomFiltersDto>>>
    {
    }
    public class GetAllLeadsCountByCustomFiltersLevel1RequestHandler : IRequestHandler<GetAllLeadsCountByCustomFiltersLevel1Request, Response<List<CustomFiltersDto>>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepository _efLeadRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<CustomFilter> _repo;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetails;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _masterLeadStatus;
        public GetAllLeadsCountByCustomFiltersLevel1RequestHandler(
            ICurrentUser currentUser,
            ILeadRepository efLeadRepository,
            IDapperRepository dapperRepository,
            IRepositoryWithEvents<CustomFilter> repo,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetails,
            IRepositoryWithEvents<CustomMasterLeadStatus> masterLeadStatus)
        {
            _currentUser = currentUser;
            _efLeadRepository = efLeadRepository;
            _dapperRepository = dapperRepository;
            _repo = repo;
            _userDetails= userDetails;
            _masterLeadStatus = masterLeadStatus;
        }

        public async Task<Response<List<CustomFiltersDto>>> Handle(GetAllLeadsCountByCustomFiltersLevel1Request request, CancellationToken cancellationToken)
        {
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = new List<Guid>();
            var isAdmin = await _dapperRepository.IsAdminV2Async(userId, tenantId ?? string.Empty);
            List<CustomFilter>? subFilters = null;
            List<CustomFilter>? filters = null;
            CustomFilter? filter = null;
            if (request.IsGenManagerWithTeam ?? false && (request.GeneralManagerIds?.Any() ?? false))
            {
                var generalManagerIds = await _dapperRepository.GeneralManagerAsync(request.GeneralManagerIds ?? new(), tenantId ?? string.Empty);
                if (generalManagerIds?.Any() ?? false)
                {
                    var subordinateIds = await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(generalManagerIds.ToList(), tenantId ?? string.Empty);
                    if (request.AssignTo == null)
                    {
                        request.AssignTo = new List<Guid>();
                    }
                    request.AssignTo.AddRange(subordinateIds);
                    request.AssignTo.AddRange(generalManagerIds);
                    request.AssignTo.AddRange(request.GeneralManagerIds);
                }
                if (request.AssignTo == null)
                {
                    request.AssignTo = new List<Guid>();
                }
                request.AssignTo.AddRange(request.GeneralManagerIds);
            }
            else if ((request.GeneralManagerIds?.Any() ?? false))
            {
                if (request.AssignTo == null)
                {
                    request.AssignTo = new List<Guid>();
                }
                request.AssignTo.AddRange(request.GeneralManagerIds);

            }
            if (request?.DesignationsId?.Any() ?? false)
            {
                var users = await _userDetails.ListAsync(new Lrb.Application.Dashboard.Web.Specs.GetUsersByDesignationIdSpec(request.DesignationsId));
                var userIds = users.Select(i => i.UserId).ToList();
                if (request.AssignTo == null)
                {
                    request.AssignTo = new List<Guid>();
                }
                request.AssignTo.AddRange(userIds);

            }
            var tasks = new Task[]
            {
               Task.Run(async () => request.IsDualOwnershipEnabled = request.IsDualOwnershipEnabled == null ? await _dapperRepository.V2GetDualOwnershipDetails(tenantId ?? string.Empty) : request.IsDualOwnershipEnabled),
               Task.Run(async () => subIds = await GetSubordinateIdsAsync(request.Adapt<GetAllLeadsOnlyByNewFiltersRequest>(), userId, tenantId ?? string.Empty,isAdmin)),
               Task.Run(async () => filters = await _repo.ListAsync(new GetCustomFiltersSpec(userId, isAdmin), cancellationToken)),
            };
            await Task.WhenAll(tasks);
            if (request.CustomFilterBaseIds?.Any() ?? false)
            {
                subFilters = filters?.Where(i => request.CustomFilterBaseIds.Contains(i.BaseId ?? Guid.Empty))?.ToList();
            }
            else
            {
                filter = filters?.FirstOrDefault(i => (i.BaseId == null || i.BaseId == Guid.Empty) && i.IsDefault && i.Level == 1);
            }
            if (filter != null)
            {
                subFilters = filters?.Where(i => i.BaseId == filter.Id)?.ToList();
                filters?.RemoveAll(i => i.BaseId == filter.Id);
            }
            if (subFilters?.Any() ?? false)
            {
                filters?.RemoveAll(j => subFilters.Any(i => i.Id == j.Id));
            }
            List<CustomMasterLeadStatus> customStatus = null;
            try
            {
                if ((request?.PropertyToSearch?.Any() ?? false) && !string.IsNullOrWhiteSpace(request.SearchByNameOrNumber) && request.PropertyToSearch.Contains("Status"))
                {
                    customStatus = await _masterLeadStatus.ListAsync(new Lrb.Application.CustomStatus.Web.GetAllStatusByNameSpec(request.SearchByNameOrNumber ?? string.Empty, false), cancellationToken);
                }
            }
            catch (Exception ex) { }
            List<CustomFiltersDto> baseFilters = new();
            List<CustomFiltersDto> childFilters = new();
            var secondTasks = new Task[]
            {
               Task.Run(async () => baseFilters = await _efLeadRepository.GetLeadsCountByCustomFiltersForWebAsync(filters, request, subIds, userId, isAdmin, new(), false, customMasterLeadStatus:customStatus)),
               Task.Run(async () => childFilters = await _efLeadRepository.GetLeadsCountByCustomFiltersForWebAsync(subFilters, request, subIds, userId, isAdmin, new(), true, customMasterLeadStatus:customStatus)),
            };
            await Task.WhenAll(secondTasks);
            baseFilters.AddRange(childFilters);
            var data = CustomFilterHelper.GetCustomFilters(baseFilters);
            return new(data ?? new List<CustomFiltersDto>());
        }
        private async Task<List<Guid>> GetSubordinateIdsAsync(GetAllLeadsOnlyByNewFiltersRequest request, Guid userId, string tenantId, bool isAdmin)
        {
           
            var assignToIds = request?.AssignTo ?? new List<Guid>();
            return assignToIds.Any()
            ? (request?.IsWithTeam ?? false)
                    ? (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesV2Async(assignToIds, tenantId ?? string.Empty)).ToList()
            : assignToIds
                : (await _dapperRepository.GetSubordinateIdsV2Async(userId, tenantId ?? string.Empty, request.CanAccessAllLeads, isAdmin))?.ToList() ?? new List<Guid>();
        }
    }
}
