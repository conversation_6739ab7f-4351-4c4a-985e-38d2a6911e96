﻿using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.LeadGenRequests;
using Lrb.Shared.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.WebUtilities;
using Newtonsoft.Json;

namespace Lrb.Application.Integration.Web.Requests.Bayut
{
    public class PropertyFinderWAPushIntegrationRequest : IRequest<Response<bool>>
    {
        public string ApiKey { get; set; }
        public HttpRequest HttpRequest { get; set; }
        public string TenantId { get; set; }

        public PropertyFinderWAPushIntegrationRequest(HttpRequest httpRequest, string tenantId, string base64)
        {
            HttpRequest = httpRequest;
            TenantId = tenantId;
            ApiKey = base64;
        }
    }

    public class PropertyFinderWAPushIntegrationRequestHandler : IRequestHandler<PropertyFinderWAPushIntegrationRequest, Response<bool>>
    {
        private readonly IMediator _mediator;
        private readonly Serilog.ILogger _logger;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _intAccRepo;

        public PropertyFinderWAPushIntegrationRequestHandler(IMediator mediator, Serilog.ILogger logger, IRepositoryWithEvents<IntegrationAccountInfo> intAccRepo)
        {
            _mediator = mediator;
            _logger = logger;
            _intAccRepo = intAccRepo;
        }

        public async Task<Response<bool>> Handle(PropertyFinderWAPushIntegrationRequest request, CancellationToken cancellationToken)
        {
            var accountId = request.ApiKey.GetAccountId();
            var integrationAccount = await _intAccRepo.GetByIdAsync(accountId, cancellationToken);
            if (integrationAccount == null)
            {
                _logger.Error("PropertyFinderWAPushIntegrationRequestHandler -> POST(PropertyFinder) -> Integration Account not found for ApiKey: " + request.ApiKey);
                return new(false, "Integration Account not found");
            }
            var httpRequest = request.HttpRequest;
            var bodyInString = "";
            PropertyFinderWhatsappDto? requestBody = null;
            _logger.Information("PropertyFinderWAPushIntegrationRequestHandler -> POST(PropertyFinder) -> HttpRequest: " + httpRequest);

            if (request.HttpRequest.HasFormContentType)
            {
                var form = await httpRequest.ReadFormAsync();
                var formData = form.ToDictionary(x => x.Key, x => x.Value.ToString());
                bodyInString = JsonConvert.SerializeObject(formData);
                _logger.Information("PropertyFinderWAPushIntegrationRequestHandler -> POST(PropertyFinder) -> called, Dto: " + bodyInString);
                if (string.IsNullOrWhiteSpace(bodyInString))
                {
                    throw new ArgumentNullException("Payload Cannot be empty");
                }
                requestBody = JsonConvert.DeserializeObject<PropertyFinderWhatsappDto>(bodyInString);
            }
            else if (httpRequest.QueryString.HasValue)
            {
                var queryParamsData = httpRequest.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
                bodyInString = JsonConvert.SerializeObject(queryParamsData);
                _logger.Information("PropertyFinderWAPushIntegrationRequestHandler -> POST(PropertyFinder) -> called, Dto: " + bodyInString);
                if (string.IsNullOrWhiteSpace(bodyInString))
                {
                    throw new ArgumentNullException("Payload Cannot be empty");
                }
                requestBody = JsonConvert.DeserializeObject<PropertyFinderWhatsappDto>(bodyInString);
            }
            else
            {
                Stream stream = httpRequest.Body;
                HttpRequestStreamReader reader = new(stream, System.Text.Encoding.UTF8);
                bodyInString = await reader.ReadToEndAsync();
                _logger.Information("PropertyFinderWAPushIntegrationRequestHandler -> POST(PropertyFinder) -> called, Dto: " + bodyInString);
                if (string.IsNullOrWhiteSpace(bodyInString))
                {
                    throw new ArgumentNullException("Payload Cannot be empty");
                }
                requestBody = JsonConvert.DeserializeObject<PropertyFinderWhatsappDto>(bodyInString);
            }

            if (requestBody != null)
            {
                _logger.Information("PropertyFinderWAPushIntegrationRequestHandler -> POST(PropertyFinder) -> called, Dto: " + requestBody);
                CreateLeadGenRequest leadGenRequest = new(LeadSource.PropertyFinder, requestBody);

                await _mediator.Send(leadGenRequest);

                var lead = new ListingSitesIntegrationRequest()
                {
                    LeadSource = LeadSource.PropertyFinder,
                    ApiKey = request.ApiKey,
                    Mobile = requestBody?.data?.attributes?.enquirer_phone_number,
                    Notes = $"{requestBody?.data?.attributes?.message} \nTracking Url: {requestBody?.data?.attributes?.tracking_link ?? string.Empty}",
                    PropertyType = requestBody?.data?.relationships?.properties?.data?.attributes?.type,
                    AdditionalProperties = new Dictionary<string, string>() { { "EnquiredFor", (requestBody?.data?.relationships?.properties?.data?.attributes?.category ?? string.Empty) } },
                    PrimaryUser = requestBody?.data?.relationships?.agents?.data?.attributes?.full_name ?? string.Empty,
                    Link = requestBody?.data?.attributes?.tracking_link ?? string.Empty,
                    RefrenceNo = requestBody?.data?.relationships?.properties?.data?.attributes?.reference ?? string.Empty
                };
                if (requestBody?.data?.relationships?.properties?.data?.relationships?.locations?.data?.Any() ?? false)
                {
                    try
                    {
                        lead.City = requestBody?.data?.relationships?.properties?.data?.relationships?.locations?.data?[0]?.attributes?.name ?? string.Empty;
                        lead.Community = requestBody?.data?.relationships?.properties?.data?.relationships?.locations?.data?[1]?.attributes?.name ?? string.Empty;
                        lead.SubCommunity = requestBody?.data?.relationships?.properties?.data?.relationships?.locations?.data?[2]?.attributes?.name ?? string.Empty;
                        lead.TowerName = requestBody?.data?.relationships?.properties?.data?.relationships?.locations?.data?[3]?.attributes?.name ?? string.Empty;
                    }
                    catch { }
                }

                lead.Name = $"Enquiry For {lead.SubCommunity}";

                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await _mediator.Send(checkDuplicateLeadRequest);

                if(shouldCreateNewLead.Data)
                {
                    await _mediator.Send(lead);
                }

                
            }
            return new(true);
        }
    }
}
