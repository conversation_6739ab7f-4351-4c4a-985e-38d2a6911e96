﻿using Lrb.Application.OrgProfile.Mobile;
using Lrb.Application.OrgProfile.Mobile.Requests;
using Lrb.Application.TimeZone.Requests;
using Lrb.Infrastructure.DomainSettings;
using Lrb.Shared.Multitenancy;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Primitives;
using System.Web;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class ProfileController : VersionedApiController
    {
        private readonly DomainSettings _domainSettings;

        public ProfileController(IOptions<DomainSettings> options)
        {
            _domainSettings = options.Value;
        }

        [HttpPut]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.OrgProfile)]
        [OpenApiOperation("Update a profile.", "")]
        public async Task<ActionResult<bool>> UpdateAsync(UpdateProfileRequest request)
        {
            return Ok(await Mediator.Send(request));
        }
        [HttpPut("About")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.OrgProfile)]
        [OpenApiOperation("Update about profile", "")]
        public async Task<ActionResult<bool>> UpdateAsync(UpdateAboutUsRequest request)
        {
            return Ok(await Mediator.Send(request));
        }
        [HttpPut("bannerImg")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.OrgProfile)]
        [OpenApiOperation("Update profile bannerImg", "")]
        public async Task<Response<bool>> UpdateBannerImgAsync([FromBody] string? bannerImgUrl)
        {
            return await Mediator.Send(new UpdateProfileBannerImgRequest(bannerImgUrl));
        }
        [HttpPut("socialMedia")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.OrgProfile)]
        [OpenApiOperation("Update profile socialMedia", "")]
        public async Task<Response<bool>> UpdateAsync(UpdateSocialMediaRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("logoImg")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.OrgProfile)]
        [OpenApiOperation("Update profile logoImg", "")]
        public async Task<Response<bool>> UpdateLogoImgAsync([FromBody] string? logoImgUrl)
        {
            return await Mediator.Send(new UpdateProfileLogoImgRequest(logoImgUrl));
        }
        [HttpDelete("bannerImg")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.OrgProfile)]
        [OpenApiOperation("Delete profile bannerImg", "")]
        public async Task<Response<bool>> DeleteBannerImgAsync()
        {
            return await Mediator.Send(new DeleteProfileBannerImgRequest());
        }
        [HttpDelete("logoImg")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.OrgProfile)]
        [OpenApiOperation("Delete profile logoImg", "")]
        public async Task<Response<bool>> DeleteLogoImgAsync()
        {
            return await Mediator.Send(new DeleteProfileLogoImgRequest());
        }
        [HttpDelete("Delete")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.OrgProfile)]
        [OpenApiOperation("Delete a Profile", "")]
        public async Task<Response<bool>> DeleteAsync(DeleteProfileRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.OrgProfile)]
        [OpenApiOperation("Get Profile", "")]
        public async Task<Response<ViewProfileDto>> GetAsync()
        {
            return await Mediator.Send(new GetProfileRequest());
        }

        [HttpPost("testimonial")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.OrgProfile)]
        [OpenApiOperation("Create a new testimonial.", "")]
        public async Task<Response<bool>> CreateAsync(CreateTestimonialRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("testimonial")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.OrgProfile)]
        [OpenApiOperation("Get all testimonials.", "")]
        public async Task<PagedResponse<ViewTestimonialDto, string>> SearchAsync([FromQuery] GetAllTestimonialsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpDelete("testimonial/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.OrgProfile)]
        [OpenApiOperation("Delete a testimonial.", "")]
        public async Task<Response<bool>> DeleteTestimonialAsync(Guid id)
        {
            return await Mediator.Send(new DeleteTestimonialRequest(id));
        }

        [HttpPut("testimonial/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.OrgProfile)]
        [OpenApiOperation("Update a testimonial.", "")]
        public async Task<ActionResult<Guid>> UpdateAsync(UpdateTestimonialRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }

        [HttpPost("recognition")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.OrgProfile)]
        [OpenApiOperation("create a Recognition.", "")]
        public async Task<Response<bool>> CreateAsync(CreateRecognitionRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("recognition")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.OrgProfile)]
        [OpenApiOperation("Get all recognitions details.", "")]
        public async Task<PagedResponse<ViewRecognitionDto, string>> SearchAsync([FromQuery] GetAllRecognitionsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpDelete("recognition/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.OrgProfile)]
        [OpenApiOperation("Delete a recognition.", "")]
        public async Task<Response<bool>> DeleteRecognitionAsync(Guid id)
        {
            return await Mediator.Send(new DeleteRecognitionRequest(id));
        }

        [HttpGet("QR-code")]
        [TenantIdHeader]
        [AllowAnonymous]
        [OpenApiOperation("Get the QR code to add lead.", "")]
        public async Task<Response<byte[]>> GetQRCodeAsync([FromQuery] string? url = null)
        {
            StringValues tenantIds;
            this.HttpContext.Request.Headers.TryGetValue(MultitenancyConstants.TenantIdName, out tenantIds);
            var tenantId = tenantIds.FirstOrDefault() ?? string.Empty;
            url = string.IsNullOrWhiteSpace(url) ? string.Format(_domainSettings.Web, tenantId) + "/leads/add-lead" : HttpUtility.UrlDecode(url);
            GetQRCodeRequest request = new()
            {
                QRUrl = url,
                TenantId = tenantId
            };
            return await Mediator.Send(request);
        }
        [HttpGet("TimeZone-Info")]
        [TenantIdHeader]
        [OpenApiOperation("Get All Time Zones Info.", "")]
        public async Task<Response<List<TimezoneInfo>>> GetTimeZoneInfos()
        {
            return await Mediator.Send(new GetAllTimeZoneInfoRequest());
        }
    }
}
