﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.Project
{
    public class CustomFormConfig : IEntityTypeConfiguration<CustomForm>
    {
        public void Configure(EntityTypeBuilder<CustomForm> builder)
        {
            builder.IsMultiTenant();
        }
    }
}