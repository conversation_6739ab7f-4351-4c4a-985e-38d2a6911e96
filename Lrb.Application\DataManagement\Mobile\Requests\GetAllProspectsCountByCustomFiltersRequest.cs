﻿using Amazon.Runtime.Internal;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Specs;

namespace Lrb.Application.DataManagement.Mobile.Requests
{
    public class GetAllProspectsCountByCustomFiltersRequest : GetAllProspectFilterParameter, IRequest<Response<List<CustomFiltersDto>>>
    {
        
    }
    public class GetAllProspectsCountByCustomFiltersRequestHandler : IRequestHandler<GetAllProspectsCountByCustomFiltersRequest, Response<List<CustomFiltersDto>>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<CustomFilter> _customFiltersRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly IProspectRepository _efProspectRepository;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _customProspectRepo;
        public GetAllProspectsCountByCustomFiltersRequestHandler(ICurrentUser currentUser,
            IRepositoryWithEvents<CustomFilter> customFiltersRepo,
            IDapperRepository dapperRepository,
            IProspectRepository efProspectRepository,
            IRepositoryWithEvents<CustomProspectStatus> customProspectRepo)
        {
            _currentUser = currentUser;
            _customFiltersRepo = customFiltersRepo;
            _dapperRepository = dapperRepository;
            _efProspectRepository = efProspectRepository;
            _customProspectRepo = customProspectRepo;
        }
        public async Task<Response<List<CustomFiltersDto>>> Handle(GetAllProspectsCountByCustomFiltersRequest request, CancellationToken cancellationToken)
        {
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = new List<Guid>();
            var isAdmin = await _dapperRepository.IsAdminV2Async(userId, tenantId ?? string.Empty);
            List<CustomFilter>? subFilters = null;
            List<CustomFilter>? filters = null;
            CustomFilter? filter = null;
            int totalCount = default;
            List<CustomFiltersDto> totalCounts = new();
            try
            {
                if (request?.AssignTo?.Any() ?? false)
                {
                    if (request?.IsWithTeam ?? false)
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, tenantId ?? string.Empty)).ToList();
                    }
                    else
                    {
                        subIds = request?.AssignTo ?? new List<Guid>();
                    }
                }
                else
                {
                    subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllProspects, isAdmin))?.ToList() ?? new();
                }
            }
            catch
            {

            }
            if (request.CustomFilterIds?.Any() ?? false)
            {
                filters = await _customFiltersRepo.ListAsync(new GetProspectCustomFiltersSpec(request.CustomFilterIds ?? new()), cancellationToken);
            }
            else
            {
                filters = await _customFiltersRepo.ListAsync(new GetProspectCustomFiltersSpec(userId, true, true, isAdmin), cancellationToken);
            }
            List<CustomProspectStatus> statuses = null;
            try
            {
                if (!string.IsNullOrWhiteSpace(request.ProspectSearch) && request.PropertyToSearch != null && request.PropertyToSearch.Contains("Status"))
                {
                    statuses = await _customProspectRepo.ListAsync(new Web.Request.GetProspectStatusByNameSpecs(request.ProspectSearch));
                }
            }
            catch (Exception ex)
            {
            }
            await Task.WhenAll(filters.Select(async filter =>
            {
                totalCount = await _efProspectRepository.GetProspectsCountByCustomFiltersForMobileAsync(filter ?? new CustomFilter(), request.Adapt<GetAllProspectFilterParameter>(), subIds, userId, isAdmin, statuses);
                var result = new CustomFiltersDto
                {
                    Name=filter.Name ?? string.Empty,
                    OrderRank = filter?.OrderRank ?? 0,
                    Id = filter?.Id ?? Guid.Empty,
                    Count = totalCount
                };
                totalCounts.Add(result);
            }));

            var sortedResponse = totalCounts.OrderBy(i => i.OrderRank).ToList();
            return new (sortedResponse ?? new List<CustomFiltersDto>()); 
        }
    }
}
