﻿using Dapper;
using GoogleApi.Entities.Common;
using Lrb.Application.Agency;
using Lrb.Application.Agency.Web.Dtos;
using Lrb.Application.ChannelPartner;
using Lrb.Application.ChannelPartner.Web.Dtos;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Project;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.Property;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Application.ZonewiseLocation.Web.Helpers;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Lrb.Shared.Utils;
using Mapster;

using Microsoft.Azure.Amqp.Framing;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Npgsql;
using System.Collections.Concurrent;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Linq;
using System.Reflection;
using V2InvalidData = Lrb.Application.Lead.Web.V2InvalidData;
using LeadDapperDto = Lrb.Application.Lead.Web.LeadDapperDto;
using Lrb.Application.Common.Persistence;
using Microsoft.Graph;
using Lrb.Application.Campaigns.Spec;
using Lrb.Application;

namespace ExcelUpload
{

    public partial class FunctionEntryPoint : IFunctionEntryPoint
    {
        public async Task<DataTable> GetDataTableAsync(BulkLeadUploadTracker leadUploadTracker)
        {
            #region Convert file to Datatable
            DataTable dataTable = new();
            List<V2InvalidData> invalids = new();
            int totalRows = 0;
            if (!string.IsNullOrWhiteSpace(leadUploadTracker.S3BucketKey))
            {
                Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", leadUploadTracker.S3BucketKey);
                if (leadUploadTracker.S3BucketKey.Split('.').LastOrDefault() == "csv")
                {
                    using MemoryStream memoryStream = new();
                    fileStream.CopyTo(memoryStream);
                    dataTable = CSVHelper.CSVToDataTable(memoryStream);
                }
                else
                {
                    dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, leadUploadTracker.SheetName);
                }
                totalRows = dataTable.Rows.Count;
                for (int i = totalRows - 1; i >= 0; i--)
                {
                    DataRow row = dataTable.Rows[i];
                    if (row.ItemArray.All(i => string.IsNullOrEmpty(i?.ToString())))
                    {
                        row.Delete();
                    }
                    else if (string.IsNullOrEmpty(row[leadUploadTracker.MappedColumnsData?[DataColumns.Name] ?? string.Empty].ToString()) && string.IsNullOrEmpty(row[leadUploadTracker.MappedColumnsData?[DataColumns.ContactNo] ?? string.Empty].ToString()))
                    {
                        var notes = string.Join(",", row.ItemArray.Where(i => !string.IsNullOrEmpty(i?.ToString())));
                        var invalidData = new V2InvalidData
                        {
                            Errors = "Contact number and name are empty.",
                            Notes = notes
                        };
                        if (!invalids.Any(i => i.Notes == invalidData.Notes))
                        {
                            invalids.Add(invalidData);
                        }
                        row.Delete();
                    }
                }
                if (dataTable.Rows.Count <= 0)
                {
                    throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                }
                totalRows = dataTable.Rows.Count;
                Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
            }
            #endregion
            return dataTable;
        }

        public async Task<MasterItems> GetMasterItemsAsync(DataTable dataTable, BulkLeadUploadTracker leadUploadTracker, string currentUserId, string tenantId)
        {
            #region featching data from excel
            List<string> properties = new List<string>();
            List<string> projects = new List<string>();
            List<string> agencies = new List<string>();
            List<string> channelPartners = new List<string>();
            List<string> campaigns = new List<string>();
            List<string>? assignedToUsers = new();
            if (((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Property) ?? false))
                || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Project) ?? false))
                || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AgencyName) ?? false))
                || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.ChannelPartnerName) ?? false))
                || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.CampaignName) ?? false))
                || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.SourcingManager) ?? false))
                || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.ClosingManager) ?? false))
                || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AssignToUser) ?? false))
                || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AssignToSecondaryUser) ?? false)))


            {
                var isPropertyPresent = (leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Property) ?? false);
                var isProjectPresent = (leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Project) ?? false);
                var isAgencyNamePresent = (leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AgencyName) ?? false);
                var isChannelPresent = (leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.ChannelPartnerName) ?? false);
                var isCamapignPresent = (leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.CampaignName) ?? false);
                var isSourcingManagerPresent = (leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.SourcingManager) ?? false);
                var isClosingManagerPresent = (leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.ClosingManager) ?? false);
                var isassignedToUserPresent = (leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AssignToUser) ?? false);
                var isSecondaryAssignedToUserPresent = (leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AssignToSecondaryUser) ?? false);
                dataTable.AsEnumerable().ToList().ForEach(row =>
                {
                    if (isPropertyPresent)
                    {
                        var propertyName = row[leadUploadTracker.MappedColumnsData[DataColumns.Property]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(propertyName))
                        {
                            properties.Add(propertyName.Trim());
                        }
                    }
                    if (isProjectPresent)
                    {
                        var projectName = row[leadUploadTracker.MappedColumnsData[DataColumns.Project]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(projectName))
                        {
                            projects.Add(projectName.Trim());
                        }
                    }
                    if (isAgencyNamePresent)
                    {
                        var agencyName = row[leadUploadTracker.MappedColumnsData[DataColumns.AgencyName]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(agencyName))
                        {
                            agencies.Add(agencyName.Trim());
                        }
                    }
                    if (isChannelPresent)
                    {
                        var cpName = row[leadUploadTracker.MappedColumnsData[DataColumns.ChannelPartnerName]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(cpName))
                        {
                            channelPartners.Add(cpName.Trim());
                        }
                    }
                    if (isCamapignPresent)
                    {
                        var campaignName = row[leadUploadTracker.MappedColumnsData[DataColumns.CampaignName]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(campaignName))
                        {
                            campaigns.Add(campaignName.Trim());
                        }
                    }
                    if (isSourcingManagerPresent)
                    {
                        var sourcingUser = row[leadUploadTracker.MappedColumnsData[DataColumns.SourcingManager]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(sourcingUser))
                        {
                            assignedToUsers.Add(sourcingUser.Trim().ToLower());
                        }
                    }
                    if (isClosingManagerPresent)
                    {
                        var closingUser = row[leadUploadTracker.MappedColumnsData[DataColumns.ClosingManager]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(closingUser))
                        {
                            assignedToUsers.Add(closingUser.Trim().ToLower());
                        }
                    }
                    if (isassignedToUserPresent)
                    {
                        var assinedUser = row[leadUploadTracker.MappedColumnsData[DataColumns.AssignToUser]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(assinedUser))
                        {
                            assignedToUsers.Add(assinedUser.Trim().ToLower());
                        }
                    }
                    if (isSecondaryAssignedToUserPresent)
                    {
                        var secondarAssinedUser = row[leadUploadTracker.MappedColumnsData[DataColumns.AssignToSecondaryUser]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(secondarAssinedUser))
                        {
                            assignedToUsers.Add(secondarAssinedUser.Trim().ToLower());
                        }
                    }
                });
            }
            #endregion

            var masterItems = new MasterItems();
            var cancellationToken = CancellationToken.None;
            var connection = (_dapperRepository.GetConnectionStringAsync()) ?? throw new InvalidOperationException("Connection string can't be null");
            masterItems.TenantDisplayPrefix = await _dapperRepository.GetDisplayIndexPrefixByTenantIdAsync(tenantId);
            #region Projects
            var allProjects = await _newProjectRepo.ListAsync(new GetAllProjectByNamesSpec(projects.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            allProjects = allProjects.GroupBy(p => p.Name.ToLower().Trim()).Select(g => g.First()).ToList();
            var remainingProjects = projects.Select(i => i).ToHashSet()
                .Where(project => !allProjects.ConvertAll(i => i.Name.ToLower().Trim().Replace(" ", "")).Contains(project.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingProjects?.Any() ?? false)
            {
                var maxserial = await _dapperRepository.GetProjectMaxSerialNumberAsync(masterItems.TenantDisplayPrefix ?? string.Empty);
                var newprojects = remainingProjects
                    .Select(i =>
                    {
                        var serialNumber = BulkUploadHelper.GenerateDisplayIndexPrefixAsync(masterItems.TenantDisplayPrefix ?? string.Empty, maxserial);
                        maxserial = serialNumber.Item2;
                        return new Project
                        {
                            Id = Guid.NewGuid(),
                            Name = i,
                            CreatedOn = DateTime.UtcNow,
                            LastModifiedOn = DateTime.UtcNow,
                            SerialNo = serialNumber.Item1,
                        };
                    }).ToList();
                var newProjects = await CreateProejctsInDapperAsync(newprojects, connection, tenantId);
                if (newProjects.Item1 != null)
                {
                    allProjects.AddRange(newProjects.Item1);
                }
            }
            var deletedProjects = allProjects.Where(i => i.IsArchived).ToList();
            allProjects.RemoveAll(p => deletedProjects.Any(d => d.Id == p.Id));
            if (deletedProjects?.Any() ?? false)
            {
                var restoredProjects = await RestoreProejctsInDapperAsync(deletedProjects, connection, tenantId);
                if (restoredProjects.Item1 != null)
                {
                    allProjects.AddRange(restoredProjects.Item1);
                }
            }
            masterItems.Projects = allProjects;
            #endregion

            #region Properties
            var allProperties = await _propertyRepo.ListAsync(new GetAllPropertyByTitlesSpec(properties.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            allProperties = allProperties.GroupBy(p => p.Title.ToLower().Trim()).Select(g => g.First()).ToList();
            var remainingProperties = properties.Select(i => i).ToHashSet()
                .Where(property => !string.IsNullOrEmpty(property) && !allProperties.ConvertAll(i => i.Title.ToLower().Trim().Replace(" ", "")).Contains(property.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingProperties?.Any() ?? false)
            {
                var maxserial = await _dapperRepository.GetPropertiesMaxSerialNumberAsync(masterItems.TenantDisplayPrefix ?? string.Empty);
                var newproperties = remainingProperties
                    .Select(i =>
                    {
                        var serialNumber = BulkUploadHelper.GenerateDisplayIndexPrefixAsync(masterItems.TenantDisplayPrefix ?? string.Empty, maxserial);
                        maxserial = serialNumber.Item2;
                        return new Property
                        {
                            Id = Guid.NewGuid(),
                            Title = i,
                            CreatedOn = DateTime.UtcNow,
                            LastModifiedOn = DateTime.UtcNow,
                            SerialNo = serialNumber.Item1,
                        };
                    }).ToList();
                var newProperties = await CreateProrpertyInDapperAsync(newproperties, connection, tenantId);
                if (newProperties.Item1 != null)
                {
                    allProperties.AddRange(newProperties.Item1);
                }
            }
            var deletedProperties = allProperties.Where(i => i.IsArchived).ToList();
            allProperties.RemoveAll(p => deletedProperties.Any(d => d.Id == p.Id));
            if (deletedProperties?.Any() ?? false)
            {
                var restoredProperties = await RestorePropertiesInDapperAsync(deletedProperties, connection, tenantId);
                if (restoredProperties.Item1 != null)
                {
                    allProperties.AddRange(restoredProperties.Item1);
                }
            }
            masterItems.Properties = allProperties;
            #endregion

            #region CommonData
            masterItems.PropetyTypes = await _propertyTypeRepo.ListAsync(cancellationToken);
            masterItems.AreaUnits = await _masterAreaUnitRepo.ListAsync(cancellationToken);
            masterItems.DuplicateLeadFeatureInfo = (await _duplicateFeatureInfo.ListAsync()).FirstOrDefault();
            masterItems.LeadStatuses = await _customMastereadStatus.ListAsync(CancellationToken.None);
            masterItems.GlobalSettings = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec(), cancellationToken);
            masterItems.SubSources = (await _dapperRepository.GetAllIntegrationSubSourceAsync<Lrb.Application.Lead.Web.SourceDto>(tenantId)).ToList();
            #endregion

            #region Users
            if (leadUploadTracker.UserIds?.Any() ?? false)
            {
                leadUploadTracker.UserIds ??= new();
                var userIds = leadUploadTracker.UserIds.Concat(new List<string> { leadUploadTracker.CreatedBy.ToString(),
                    leadUploadTracker.LastModifiedBy.ToString(),currentUserId }).ToList().ConvertAll(i => Guid.Parse(i));
                masterItems.HistoryUsers = await _userViewRepo.ListAsync(new UserViewByIdSpec(userIds));
                masterItems.Users = masterItems.HistoryUsers.Where(i => leadUploadTracker.UserIds.Contains(i.Id.ToString())).ToList();
            }
            else
            {
                var userIds = (new List<string> { leadUploadTracker.CreatedBy.ToString(),
                    leadUploadTracker.LastModifiedBy.ToString(),currentUserId }).ConvertAll(i => Guid.Parse(i));
                masterItems.HistoryUsers = await _userViewRepo.ListAsync(new UserViewByIdSpec(userIds));
                var users = await _userViewRepo.ListAsync(new GetUsersByUsernamesSpec(assignedToUsers.Distinct().ToList() ?? new()), cancellationToken);
                if (users?.Any() ?? false)
                {
                    if (masterItems.Users == null)
                    {
                        masterItems.Users = new List<UserView>();
                    }
                    masterItems.Users.AddRange(users);
                    masterItems.HistoryUsers.AddRange(users);
                }
            }
            if (assignedToUsers?.Any() ?? false)
            {
                var usersnameById = await _userViewRepo.ListAsync(new GetUsersByUsernamesSpec(assignedToUsers.Distinct().ToList() ?? new()), cancellationToken);
                if(usersnameById.Count >= 1)
                {
                    masterItems?.Users?.AddRange(usersnameById);
                }
            }
            #endregion

            #region Agencies
            var allAgencies = await _agencyRepo.ListAsync(new GetAllAgencyByNameSpec(agencies.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            var remainingAgencies = agencies.Select(i => i).ToHashSet()
                .Where(agency => !string.IsNullOrEmpty(agency) && !allAgencies.ConvertAll(i => i.Name.ToLower().Trim().Replace(" ", "")).Contains(agency.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingAgencies?.Any() ?? false)
            {
                var newagencies = remainingAgencies.Select(i => new Agency { Id = Guid.NewGuid(), Name = i, CreatedOn = DateTime.UtcNow, LastModifiedOn = DateTime.UtcNow }).ToList();
                var newAgencies = await CreateAgencyInDapperAsync(newagencies, connection, tenantId);
                if (newAgencies.Item1 != null)
                {
                    allAgencies.AddRange(newAgencies.Item1);
                }
            }
            masterItems.Agencies = allAgencies;
            #endregion

            #region ChannelPartner
            var allChannelPartner = await _cpRepository.ListAsync(new GetAllChannelPartnerByNameSpec(channelPartners.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            var remainingChannelPartner = channelPartners.Select(i => i).ToHashSet()
                .Where(channelPartner => !string.IsNullOrEmpty(channelPartner) && !allChannelPartner.ConvertAll(i => i.FirmName.ToLower().Trim().Replace(" ", "")).Contains(channelPartner.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingChannelPartner?.Any() ?? false)
            {
                var newchannelPartner = remainingChannelPartner.Select(i => new ChannelPartner { Id = Guid.NewGuid(), FirmName = i, CreatedOn = DateTime.UtcNow, LastModifiedOn = DateTime.UtcNow }).ToList();
                var newChannelPartner = await CreateChannalPartnerInDapperAsync(newchannelPartner, connection, tenantId);
                if (newChannelPartner.Item1 != null)
                {
                    allChannelPartner.AddRange(newChannelPartner.Item1);
                }
            }
            masterItems.ChannelPartners = allChannelPartner;
            #endregion

            #region Campaign
            var allCampaigns = await _campaignRepo.ListAsync(new GetCampaignByNameSpec(campaigns.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            var remainingCampaigns = campaigns.Select(i => i).ToHashSet()
                .Where(campaign => !string.IsNullOrEmpty(campaign) && !allCampaigns.ConvertAll(i => i.Name.ToLower().Trim().Replace(" ", "")).Contains(campaign.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingCampaigns?.Any() ?? false)
            {
                //var newChannelPartner = await _cpRepository.AddRangeAsync(remainingChannelPartner.Select(i => new ChannelPartner() { FirmName = i }));
                var newCampaign = remainingCampaigns.Select(i => new Campaign { Id = Guid.NewGuid(), Name = i, CreatedOn = DateTime.UtcNow, LastModifiedOn = DateTime.UtcNow }).ToList();
                var updatedCampaign = await CreateCampaignInDapperAsync(newCampaign, connection, tenantId);
                if (updatedCampaign.Item1 != null)
                {
                    allCampaigns.AddRange(updatedCampaign.Item1);
                }
            }
            masterItems.Campaigns = allCampaigns;
            #endregion

            return masterItems;
        }

        public async Task ImportLeadHandlerV2(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            //GEt the Tracker by Id 
            BulkLeadUploadTracker leadUploadTracker = await _bulkLeadUploadTrackerRepository.GetByIdAsync(input.TrackerId);
            Console.WriteLine($"handler() -> BulkLeadUploadTracker GetById(): {JsonConvert.SerializeObject(leadUploadTracker)}");

            if (leadUploadTracker != null)
            {
                try
                {
                    // updating tracker
                    leadUploadTracker.MappedColumnsData = leadUploadTracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                    leadUploadTracker.Status = UploadStatus.Started;
                    leadUploadTracker.LastModifiedBy = input.CurrentUserId;
                    leadUploadTracker.CreatedBy = input.CurrentUserId;
                    var createType = leadUploadTracker.CreateType;
                    await _bulkLeadUploadTrackerRepository.UpdateAsync(leadUploadTracker);
                    Console.WriteLine($"handler() -> BulkLeadUploadTracker Updated Status: {leadUploadTracker.Status} \n {JsonConvert.SerializeObject(leadUploadTracker)}");

                    // retrieving the data from excel  
                    DataTable dataTable = await GetDataTableAsync(leadUploadTracker);

                    //retrieving the master items  
                    var masterItems = await GetMasterItemsAsync(dataTable, leadUploadTracker, input.CurrentUserId.ToString(), input.TenantId);

                    // Un Mapped Columns
                    var unMappedColumns = dataTable.GetUnmappedColumnNames(leadUploadTracker.MappedColumnsData ?? new());
                    var results = dataTable.ConvertToLeadsV3(leadUploadTracker.MappedColumnsData ?? new(), unMappedColumns, masterItems, leadUploadTracker,_sourceRepo, input.JsonData ?? string.Empty);
                    List<Lead> leads = results.Item1;
                    var invalids = results.Item2;
                    leads = leads.DistinctBy(i => i.ContactNo).ToList();
                    List<DuplicateDto> existingLeads = (await _dapperRepository.CheckDuplicateLeadsAsync(leads.Select(i => i.ContactNo).ToList() ?? new(), leads.Where(i => !string.IsNullOrEmpty(i.AlternateContactNo))?.Select(i => i.AlternateContactNo ?? string.Empty)?.ToList() ?? new(), input.TenantId)).ToList();
                    var existingContactNos = existingLeads.Where(i => !string.IsNullOrEmpty(i.ContactNo)).Select(i => i.ContactNo).ToList().Concat(existingLeads.Where(i => !string.IsNullOrEmpty(i.AlternateContactNo)).Select(i => i.AlternateContactNo).ToList());
                    var duplicateLeads = leads.Where(i => existingContactNos.Contains(i.ContactNo) || existingContactNos.Contains(i.AlternateContactNo)).ToList();
                    leads.RemoveAll(i => existingContactNos.Contains(i.ContactNo) || existingContactNos.Contains(i.AlternateContactNo));
                    if (duplicateLeads?.Any() ?? false)
                    {
                        var duplicateLead = duplicateLeads
                        .Select(lead =>
                        {
                            var invalidLead = lead.Adapt<V2InvalidData>();
                            invalidLead.Errors = "Duplicate Lead";
                            invalidLead.Source = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.LeadSource.ToString() ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                            invalidLead.SubSource = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                            invalidLead.Created = lead.CreatedOn.Date;
                            return invalidLead;
                        })
                        .ToList();
                        invalids.AddRange(duplicateLead);
                    }

                    //update Tracker
                    leadUploadTracker.Status = UploadStatus.InProgress;
                    leadUploadTracker.TotalCount = dataTable.Rows.Count;
                    leadUploadTracker.DistinctLeadCount = leads.Count() + results.Item2.Count();
                    leadUploadTracker.LastModifiedBy = input.CurrentUserId;
                    leadUploadTracker.CreatedBy = input.CurrentUserId;

                    if (invalids.Any())
                    {
                        leadUploadTracker.DuplicateCount = duplicateLeads?.Count() ?? 0;
                        leadUploadTracker.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty" || i.Errors == "Duplicate ContactNo" || i.Errors == "Invalid Source").Count();
                        byte[] bytes = CreateLeadHelper.V2CreateExcelData(invalids).ToArray();
                        string fileName = $"InvalidLeads-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                        string folder = "Leads";
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        leadUploadTracker.InvalidDataS3BucketKey = key;
                    }
                    if (leads.Any())
                    {
                        leadUploadTracker.LeadsUpdatedCount = leads?.Count() ?? 0;
                    }
                    await _bulkLeadUploadTrackerRepository.UpdateAsync(leadUploadTracker);
                    V2BulkUploadbackgroundDto backgroundDto = new();
                    if (leads?.Any() ?? false)
                    {
                        int leadsPerchunk = leads.Count > 5000 ? 5000 : leads.Count;
                        var chunks = leads.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Lead>(i));
                        List<Task> tasks = new();
                        var currentUserId = _currentUser.GetUserId();
                        var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                        if (currentUserId == Guid.Empty)
                        {
                            currentUserId = input.CurrentUserId;
                            
                        }
                        Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                        var chunkIndex = 1;
                        foreach (var chunk in chunks.ToList())
                        {
                            backgroundDto = new V2BulkUploadbackgroundDto()
                            {
                                CurrentUserId = currentUserId,
                                TrackerId = leadUploadTracker.Id,
                                TenantInfoDto = tenantInfo,
                                CancellationToken = CancellationToken.None,
                                Leads = new(chunk),
                                UserIds = new(leadUploadTracker.UserIds ?? new()),
                                CreateType = createType,
                                UserViews = masterItems.Users,
                                HistoryUsers = masterItems.HistoryUsers,
                            };
                            await V2ExecuteDBOperationsAsync(backgroundDto, leadUploadTracker, masterItems);
                            chunkIndex++;
                        }
                        leadUploadTracker.TotalUploadedCount = leads.Count();
                        leadUploadTracker.LastModifiedOn = DateTime.UtcNow;
                        leadUploadTracker.Status = UploadStatus.Completed;
                        await _bulkLeadUploadTrackerRepository.UpdateAsync(leadUploadTracker);
                    }
                    else
                    {
                        leadUploadTracker.TotalUploadedCount = 0;
                        leadUploadTracker.LastModifiedOn = DateTime.UtcNow;
                        leadUploadTracker.Status = UploadStatus.Completed;
                        await _bulkLeadUploadTrackerRepository.UpdateAsync(leadUploadTracker);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(ex)}");
                    leadUploadTracker.Status = UploadStatus.Failed;
                    leadUploadTracker.Message = ex?.InnerException?.Message ?? ex?.Message;
                    leadUploadTracker.LastModifiedBy = input.CurrentUserId;
                    leadUploadTracker.CreatedBy = input.CurrentUserId;
                    await _bulkLeadUploadTrackerRepository.UpdateAsync(leadUploadTracker);
                }
            }
        }

        public async Task<(List<LeadHistory>?, bool)> CreateLeadHistoryInDapperAsync(List<LeadHistory> leadHistories, string connectionString, string tenantId)
        {

            if (!string.IsNullOrEmpty(connectionString))
            {

                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    conn.Open();

                    // Ensure 'properties' is a list
                    var properties = leadHistories.FirstOrDefault()?.GetType().GetProperties()
                        .Where(prop => !prop.GetCustomAttributes<NotMappedAttribute>().Any())
                        .Select(i => i.Name)
                        .ToList() ?? new List<string>(); // Ensure it's a valid list

                    // Generate the insert query using the properties
                    var insertQuery = QueryGenerator.GenerateInsertLeadHistoryQuery(tenantId, DataBaseDetails.LRBSchema, "LeadHistories", properties, leadHistories);

                    // Execute the insert query for the list of LeadHistory records
                    await conn.ExecuteAsync(insertQuery, commandTimeout: 1000);

                    conn.Close();
                    return (leadHistories, true);
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
            else
            {
                return (null, false);
            }
        }
        public async Task<(List<Lead>?, bool)> CreateLeadInDapperAsync(List<Lead> leads, string connectionString, string tenantId, Guid currentUserId, MasterItems masterItems)
        {
           
            if (!string.IsNullOrEmpty(connectionString))
            {
                var maxserial = await _dapperRepository.GetLeadMaxSerialNumberAsync(masterItems.TenantDisplayPrefix ?? string.Empty);
                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    conn.Open();
                   
                    var address = leads
                        .Where(lead => lead.Address != null)
                        .Select(lead => lead.Address)
                        .Adapt<List<AddressDapperDto>>(); 
                    if (address.Any())
                    {
                        var addressProperties = GetMappedProperties<AddressDapperDto>();
                        var addressInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "Addresses", addressProperties, address);
                        await conn.ExecuteAsync(addressInsertQuery, commandTimeout: 1000);
                    }
                    //leads
                    var addLeads = leads.Select(i =>
                    {
                        var serialNumber = BulkUploadHelper.GenerateDisplayIndexPrefixAsync(masterItems.TenantDisplayPrefix ?? string.Empty, maxserial);
                        var leadDto = i.Adapt<LeadDapperDto>();
                        leadDto.CustomLeadStatusId = i.CustomLeadStatus?.Id ?? Guid.Empty;
                        leadDto.SerialNumber = serialNumber.Item1;
                        maxserial = serialNumber.Item2;
                        leadDto.AddressId = i.Address?.Id ?? null;
                        return leadDto;
                    }).ToList();

                    var properties = GetMappedProperties<LeadDapperDto>();
                    var insertQuery = QueryGenerator.GenerateInsertQuery(tenantId, DataBaseDetails.LRBSchema, "Leads", properties, addLeads);
                    await conn.ExecuteAsync(insertQuery, commandTimeout: 1000);

                    //enquiries
                    var enquiries = leads
                       .Where(lead => lead.Enquiries != null && lead.Enquiries.Any())
                       .SelectMany(lead =>
                            lead.Enquiries.Select(enquiry =>
                            {
                                var leadEnquiryDto = enquiry.Adapt<LeadEnquiryDapperDto>();
                                leadEnquiryDto.LeadId = lead.Id;
                                leadEnquiryDto.PropertyTypeId = enquiry.PropertyType?.Id ?? null;
                                return leadEnquiryDto;
                            })
                     ).ToList();
                    if (enquiries.Any())
                    {
                        var enquiryProperties = GetMappedProperties<LeadEnquiryDapperDto>();
                        var enquiryInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "LeadEnquiries", enquiryProperties, enquiries);
                        await conn.ExecuteAsync(enquiryInsertQuery, commandTimeout: 1000);
                    }

                    //addresses
                    var addresses = leads.Select(i => i.Enquiries.FirstOrDefault())
                        .Where(i => i.Addresses != null && i.Addresses.Any()).SelectMany(address => address.Addresses).Adapt<List<AddressDapperDto>>();

                    if (addresses.Any())
                    {
                        var addressProperties = GetMappedProperties<AddressDapperDto>();
                        var addressInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "Addresses", addressProperties, addresses);
                        await conn.ExecuteAsync(addressInsertQuery, commandTimeout: 1000);
                    }

                    // enquiry address
                    var enquiryAddresses = leads.Select(i => i.Enquiries.FirstOrDefault())
                    .Where(i => i.Addresses != null && i.Addresses.Any())
                    .SelectMany(en =>
                        en.Addresses.Select(address => new AddressLeadEnquiryDto
                        {
                            EnquiriesId = en.Id,
                            AddressesId = address.Id
                        })
                    ).ToList();
                    if (enquiryAddresses.Any())
                    {
                        var enquiryAddressProperties = GetMappedProperties<AddressLeadEnquiryDto>();
                        var enquiryAddressInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "AddressLeadEnquiry", enquiryAddressProperties, enquiryAddresses);
                        await conn.ExecuteAsync(enquiryAddressInsertQuery, commandTimeout: 1000);
                    }
                    var EnquiryPropTypes = leads.SelectMany(lead => lead.Enquiries)
                        .Where(en => en.PropertyTypes != null && en.PropertyTypes.Any())
                        .SelectMany(en => en.PropertyTypes.Select(p => new LeadEnquiryPropertyTypesDTO
                        {
                            EnquiriesId = en.Id,
                            PropertyTypesId = p.Id,

                        }));
                    if (EnquiryPropTypes.Any())
                    {
                        List<string> columnNames = new List<string> {"EnquiriesId", "PropertyTypesId"};
                        var projectInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "LeadEnquiryMasterPropertyType", columnNames, EnquiryPropTypes);
                        await conn.ExecuteAsync(projectInsertQuery, commandTimeout: 1000);
                    }
                    //projects
                    var leadProjects = leads
                    .Where(lead => lead.Projects != null && lead.Projects.Any())
                    .SelectMany(lead => lead.Projects.Select(project => new LeadProjectDTO
                    {
                        LeadsId = lead.Id,
                        ProjectsId = project.Id
                    })).ToList();
                    if (leadProjects.Any())
                    {
                        List<string> columnNames = new List<string> { "LeadsId", "ProjectsId" };
                        var projectInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "LeadProject", columnNames, leadProjects);
                        await conn.ExecuteAsync(projectInsertQuery, commandTimeout: 1000);
                    }
                    
                    //properties
                    var leadProperties = leads
                    .Where(lead => lead.Properties != null && lead.Properties.Any())
                    .SelectMany(lead => lead.Properties.Select(i => new LeadPropertyDto
                    {
                        LeadsId = lead.Id,
                        PropertiesId = i.Id
                    })).ToList();
                    if (leadProperties.Any())
                    {
                        List<string> columnNames = new List<string> { "LeadsId", "PropertiesId" };
                        var propertyInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "LeadProperty", columnNames, leadProperties);
                        await conn.ExecuteAsync(propertyInsertQuery, commandTimeout: 1000);
                    }

                    //agencies
                    var leadAgencies = leads
                    .Where(lead => lead.Agencies != null && lead.Agencies.Any())
                    .SelectMany(lead => lead.Agencies.Select(i => new AgencyLeadDto
                    {
                        LeadsId = lead.Id,
                        AgenciesId = i.Id
                    })).ToList();
                    if (leadAgencies.Any())
                    {
                        List<string> columnNames = new List<string> { "AgenciesId", "LeadsId" };
                        var agencyInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "AgencyLead", columnNames, leadAgencies);
                        await conn.ExecuteAsync(agencyInsertQuery, commandTimeout: 1000);
                    }

                    //channel partners
                    var leadChannelpartners = leads
                    .Where(lead => lead.ChannelPartners != null && lead.ChannelPartners.Any())
                    .SelectMany(lead => lead.ChannelPartners.Select(i => new ChannelPartnerLeadDto
                    {
                        LeadsId = lead.Id,
                        ChannelPartnersId = i.Id
                    })).ToList();
                    if (leadChannelpartners.Any())
                    {
                        List<string> columnNames = new List<string> { "ChannelPartnersId", "LeadsId" };
                        var channelpartnersInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "ChannelPartnerLead", columnNames, leadChannelpartners);
                        await conn.ExecuteAsync(channelpartnersInsertQuery, commandTimeout: 1000);
                    }

                    // appointments
                    var appointments = leads
                             .Where(lead => lead.Appointments != null && lead.Appointments.Any())
                             .SelectMany(lead =>
                             lead.Appointments.Select(i =>
                             {
                                 var leadAppointmentsDto = i.Adapt<LeadAppointmentDapperDto>();
                                 leadAppointmentsDto.Id = Guid.NewGuid();
                                 leadAppointmentsDto.LeadId = lead.Id;
                                 return leadAppointmentsDto;
                             })
                             ).ToList();
                    if (appointments.Any())
                    {
                        var appointmentProperties = GetMappedProperties<LeadAppointmentDapperDto>();
                        var appointmentInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "LeadAppointments", appointmentProperties, appointments);
                        await conn.ExecuteAsync(appointmentInsertQuery, commandTimeout: 1000);
                    }

                    //Custom flags
                    var customFlags = leads
                           .Where(lead => lead.CustomFlags != null && lead.CustomFlags.Any())
                           .SelectMany(lead =>
                           lead.CustomFlags.Select(i =>
                           {
                               var customFlagsDto = i.Adapt<CustomFlagDapperDto>();
                               customFlagsDto.Id = Guid.NewGuid();
                               customFlagsDto.CreatedOn = DateTime.UtcNow;
                               customFlagsDto.CreatedBy = currentUserId;
                               customFlagsDto.UserId = currentUserId;
                               customFlagsDto.LastModifiedBy = currentUserId;
                               customFlagsDto.LeadId = lead.Id;
                               return customFlagsDto;
                           })
                           ).ToList();
                    if (customFlags.Any())
                    {
                        var customFlagsProperties = GetMappedProperties<CustomFlagDapperDto>();
                        var customFlagsInsertQuery = QueryGenerator.GenerateInsertQuery(tenantId, DataBaseDetails.LRBSchema, "CustomFlags", customFlagsProperties, customFlags);
                        await conn.ExecuteAsync(customFlagsInsertQuery, commandTimeout: 1000);
                    }

                    //Campaigns
                    var leadCampaigns = leads
                   .Where(lead => lead.Campaigns != null && lead.Campaigns.Any())
                   .SelectMany(lead => lead.Campaigns.Select(i => new Lrb.Application.Lead.Web.Dtos.CampaignLeadDto
                   {
                       LeadsId = lead.Id,
                       CampaignsId = i.Id
                   })).ToList();
                    if (leadCampaigns.Any())
                    {
                        List<string> columnNames = new List<string> { "CampaignsId", "LeadsId" };
                        var campaignInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "CampaignLead", columnNames, leadCampaigns);
                        await conn.ExecuteAsync(campaignInsertQuery, commandTimeout: 1000);
                    }
                    conn.Close();
                    return (leads.ToList(), true);
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
            else
            {
                return (null, false);
            }
        }
        public async Task<(List<Project>?, bool)> CreateProejctsInDapperAsync(List<Project> projects, string connectionString, string tenantId)
        {

            if (!string.IsNullOrEmpty(connectionString))
            {
                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    conn.Open();
                    var addProjects = projects.Adapt<List<ProjectDapperDto>>();
                    var properties = GetMappedProperties<ProjectDapperDto>();
                    var insertQuery = QueryGenerator.GenerateInsertQuery(tenantId, DataBaseDetails.LRBSchema, "Projects", properties, addProjects);
                    await conn.ExecuteAsync(insertQuery, commandTimeout: 1000);

                    conn.Close();
                    return (projects.ToList(), true);
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
            else
            {
                return (null, false);
            }
        }
        public async Task<(List<Project>?, bool)> RestoreProejctsInDapperAsync(List<Project> projects, string connectionString, string tenantId)
        {

            if (!string.IsNullOrEmpty(connectionString))
            {

                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    conn.Open();
                    var insertQuery = QueryGenerator.GenerateResotreByIdsQuery(tenantId, DataBaseDetails.LRBSchema, "Projects", projects.Select(i => i.Id).ToList());
                    await conn.ExecuteAsync(insertQuery, commandTimeout: 1000);

                    conn.Close();
                    return (projects.ToList(), true);
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
            else
            {
                return (null, false);
            }
        }
        public async Task<(List<Property>?, bool)> RestorePropertiesInDapperAsync(List<Property> properties, string connectionString, string tenantId)
        {

            if (!string.IsNullOrEmpty(connectionString))
            {

                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    conn.Open();
                    var insertQuery = QueryGenerator.GenerateResotreByIdsQuery(tenantId, DataBaseDetails.LRBSchema, "Properties", properties.Select(i => i.Id).ToList());
                    await conn.ExecuteAsync(insertQuery, commandTimeout: 1000);

                    conn.Close();
                    return (properties.ToList(), true);
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
            else
            {
                return (null, false);
            }
        }
        public async Task<(List<Property>?, bool)> CreateProrpertyInDapperAsync(List<Property> properties, string connectionString, string tenantId)
        {

            if (!string.IsNullOrEmpty(connectionString))
            {

                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    conn.Open();
                    var addProperties = properties.Adapt<List<PropertyDapperDto>>();
                    var columns = GetMappedProperties<PropertyDapperDto>();
                    var insertQuery = QueryGenerator.GenerateInsertQuery(tenantId, DataBaseDetails.LRBSchema, "Properties", columns, addProperties);
                    await conn.ExecuteAsync(insertQuery, commandTimeout: 1000);

                    conn.Close();
                    return (properties.ToList(), true);
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
            else
            {
                return (null, false);
            }
        }
        public async Task<(List<Agency>?, bool)> CreateAgencyInDapperAsync(List<Agency> agencies, string connectionString, string tenantId)
        {
            if (!string.IsNullOrEmpty(connectionString))
            {
                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    conn.Open();
                    var addAgencies = agencies.Adapt<List<AgencyDapperDto>>();
                    var columns = GetMappedProperties<AgencyDapperDto>();
                    var insertQuery = QueryGenerator.GenerateInsertQuery(tenantId, DataBaseDetails.LRBSchema, "Agencies", columns, addAgencies);
                    await conn.ExecuteAsync(insertQuery, commandTimeout: 1000);

                    conn.Close();
                    return (agencies.ToList(), true);
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
            else
            {
                return (null, false);
            }
        }
        public async Task V3AddBulkLocations(List<Lrb.Domain.Entities.Address> addresses, string connectionString, string tenantId)
        {
            if (!string.IsNullOrEmpty(connectionString))
            {
                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    var locations = await _locationRepo.ListAsync(new LocationByLocalitySpec(addresses), CancellationToken.None);
                    foreach (var location in locations)
                    {
                        addresses = addresses
                        .Select(address =>
                        {
                            if (address.SubLocality?.ToLower().Trim() == location.Locality.ToLower().Trim() &&
                                address.City?.ToLower().Trim() == location.City?.NormalizedName &&
                                address.State?.ToLower().Trim() == location.State?.NormalizedName)
                            {
                                address.LocationId = location.Id;
                            }
                            return address;
                        }).ToList();
                    }
                    var AddresseswithLocation = addresses.Where(i => i.LocationId != null).ToList();
                    if (AddresseswithLocation?.Any() ?? false)
                    {
                        conn.Open();
                        var query = QueryGenerator.AddressUpdateQueryForMultiple(DataBaseDetails.LRBSchema, "Addresses", AddresseswithLocation);
                        await conn.ExecuteAsync(query, commandTimeout: 1000);
                        conn.Close();
                    }
                    var newAddresses = addresses.Where(i => i.LocationId == null).ToList();
                    if (newAddresses?.Any() ?? false)
                    {
                        conn.Open();
                        foreach (var address in newAddresses)
                        {
                            var locationRequest = address.MapToLocationRequest();
                            if (locationRequest != null)
                            {
                                var res = await _mediator.Send(locationRequest.Adapt<AddLocationRequest>());
                                if (res.Data != Guid.Empty)
                                {
                                    var query = QueryGenerator.AddressUpdateQuery(DataBaseDetails.LRBSchema, "Addresses", res.Data, address.Id);
                                    await conn.ExecuteAsync(query, commandTimeout: 1000);
                                }
                            }
                        }
                        conn.Close();
                    }
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
        }

        public async Task<(List<ChannelPartner>?, bool)> CreateChannalPartnerInDapperAsync(List<ChannelPartner> channelPartners, string connectionString, string tenantId)
        {
            if (!string.IsNullOrEmpty(connectionString))
            {
                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    conn.Open();
                    var addChannelPartners = channelPartners.Adapt<List<ChannelPartnerDapperDto>>();
                    var columns = GetMappedProperties<ChannelPartnerDapperDto>();
                    var insertQuery = QueryGenerator.GenerateInsertQuery(tenantId, DataBaseDetails.LRBSchema, "ChannelPartners", columns, addChannelPartners);
                    await conn.ExecuteAsync(insertQuery, commandTimeout: 1000);

                    conn.Close();
                    return (channelPartners.ToList(), true);
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
            else
            {
                return (null, false);
            }
        }
        private static List<string> GetMappedProperties<T>()
        {
            return typeof(T).GetProperties()
                .Where(prop => !prop.GetCustomAttributes<NotMappedAttribute>().Any())
                .Select(prop => prop.Name)
                .ToList();
        }

        private async Task V2ExecuteDBOperationsAsync(V2BulkUploadbackgroundDto dto, BulkLeadUploadTracker? tracker, MasterItems masterItems)
        {
            if (!(dto.Leads?.Any() ?? false))
            {
                return;
            }
            try
            {
                if (dto.UserIds?.Any() ?? false)
                {
                    dto.Leads.AssignLead(dto.UserViews?.Where(i => dto.UserIds.Contains(i.Id.ToString()) && i.IsAutomationEnabled).Select(i => i.Id).ToList() ?? new(), dto.CurrentUserId);
                }
                //var leads = await _leadRepo.AddRangeAsync(dto.Leads);
                var connection = (_dapperRepository.GetConnectionStringAsync()) ?? throw new InvalidOperationException("Connection string can't be null");
                var leads = await CreateLeadInDapperAsync(dto.Leads, connection, dto.TenantInfoDto?.Id ?? string.Empty, dto.CurrentUserId, masterItems);
                var addresses = V2ExtractAddressFromLeads(leads.Item1);
                //await V2AddBulkLocations(addresses);
                if (addresses?.Any() ?? false)
                {
                    await V3AddBulkLocations(addresses, connection, dto.TenantInfoDto?.Id ?? string.Empty);
                }
                var leadDtos = dto.Leads.Adapt<List<ViewLeadDto>>();
                dto.LeadDtos = leadDtos;
                List<LeadHistory> leadHistories = new();
                leadDtos.ForEach(leadDto =>
                {
                    leadDto.SetUsersInViewLeadDto(dto.HistoryUsers?.Adapt<List<UserDetailsDto>>() ?? new(), currentUserId: dto.CurrentUserId);
                    var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                    leadHistory.Id = Guid.NewGuid();
                    leadHistory.CreatedDate = DateTime.UtcNow;
                    leadHistory.CreatedBy = dto.CurrentUserId;
                    leadHistory.IsDeleted = false;
                    leadHistories.Add(leadHistory);
                });
                //(await _leadHistoryRepo.AddRangeAsync(leadHistories)).ToList();
                await CreateLeadHistoryInDapperAsync(leadHistories, connection, dto.TenantInfoDto?.Id ?? string.Empty);
                await CreateLeadAssignmentHistory(leadDtos, dto.CurrentUserId, connection, dto.TenantInfoDto?.Id ?? string.Empty, CancellationToken.None);
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.Leads.Count;
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _bulkLeadUploadTrackerRepository.UpdateAsync(tracker);
                }
            }

            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == (tracker.DistinctLeadCount - tracker.InvalidCount - tracker.DuplicateCount))
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _bulkLeadUploadTrackerRepository.UpdateAsync(tracker);
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> ExecuteDBOperationsAsync()",
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }
        public async Task CreateLeadAssignmentHistory(List<ViewLeadDto> leads, Guid currentUserId, string connectionString, string tenantId, CancellationToken cancellationToken)
        {
            try
            {
                var leadAssignments = leads.Where(lead => lead.AssignTo != null && lead.AssignTo != Guid.Empty)
                    .Select(lead =>
                    {
                        var assignment = new LeadAssignment
                        {
                            Id = Guid.NewGuid(),
                            AssignTo = lead.AssignTo,
                            AssignedFrom = lead.AssignedFrom,
                            Notes = lead.Notes,
                            LeadId = lead.Id,
                            UserId = lead.AssignTo ?? Guid.Empty,
                            LeadAssignmentType = LeadAssignmentType.WithHistory,
                            AssignmentDate = lead.CreatedOn,
                            LastModifiedBy = currentUserId,
                            LastModifiedOn = lead.LastModifiedOn,
                            CreatedBy = currentUserId,
                            CreatedOn = lead.CreatedOn,
                        };

                        if (lead.SecondaryUserId != null && lead.SecondaryUserId != Guid.Empty)
                        {
                            assignment.SecondaryAssignTo = lead.SecondaryUserId;
                        }
                        if (lead.SecondaryFromUserId != null && lead.SecondaryFromUserId != Guid.Empty)
                        {
                            assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                        }
                        return assignment;
                    });
                if (leadAssignments?.Any() ?? false)
                {
                    await CreateLeadAssignmentsInDapperAsync(leadAssignments.ToList(), connectionString, tenantId);
                }

            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<List<LeadAssignment>> CreateLeadAssignmentsInDapperAsync(List<LeadAssignment> leadAssignments, string connectionString, string tenantId)
        {
            if (!string.IsNullOrEmpty(connectionString))
            {
                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    conn.Open();
                    var addLeadAssignments = leadAssignments.Adapt<List<LeadAssignmentDapperDto>>();
                    var columns = GetMappedProperties<LeadAssignmentDapperDto>();
                    var insertQuery = QueryGenerator.GenerateInsertQuery(tenantId, DataBaseDetails.LRBSchema, "LeadAssignments", columns, addLeadAssignments);
                    await conn.ExecuteAsync(insertQuery, commandTimeout: 1000);

                    conn.Close();
                    return leadAssignments.ToList();
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
            else
            {
                return null;
            }
        }
        public async Task<(List<Campaign>?, bool)> CreateCampaignInDapperAsync(List<Campaign> campaigns, string connectionString, string tenantId)
        {
            if (!string.IsNullOrEmpty(connectionString))
            {
                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    conn.Open();
                    var addCamapigns = campaigns.Adapt<List<CampaignDapperDto>>();
                    var columns = GetMappedProperties<CampaignDapperDto>();
                    var insertQuery = QueryGenerator.GenerateInsertQuery(tenantId, DataBaseDetails.LRBSchema, "Campaigns", columns, addCamapigns);
                    await conn.ExecuteAsync(insertQuery, commandTimeout: 1000);

                    conn.Close();
                    return (campaigns.ToList(), true);
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
            else
            {
                return (null, false);
            }
        }
    }
}
