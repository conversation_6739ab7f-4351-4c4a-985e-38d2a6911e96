﻿

using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.GlobalSettings.Mobile;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile.Specs.v1;
using Lrb.Application.Lead.Mobile.v2;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Project.Mobile;
using Lrb.Application.Project.Mobile.Specs;
using Lrb.Application.Property.Mobile;
using Lrb.Application.ZonewiseLocation.Mobile.Dtos;
using Lrb.Application.ZonewiseLocation.Mobile.Requests;
using Lrb.Application.ZonewiseLocation.Mobile.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Extensions;
using Lrb.Shared.Utils;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Serilog;
using System.Threading;

namespace Lrb.Application.Lead.Mobile.Requests.UpdateStatusHandler
{
    public class UpdateStatusHandler
    {
        protected readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        protected readonly IUserService _userService;
        protected readonly ICurrentUser _currentUserRepo;
        private readonly string _className;
        private readonly string _methodName;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        protected readonly INotificationSenderService _notificationSenderService;
        protected readonly ILeadRepositoryAsync _leadRepositoryAsync;
        protected readonly IServiceProvider _serviceProvider;
        protected readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        protected readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        protected readonly IRepositoryWithEvents<LeadAssignment> _leadAssignmentRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepository;
        protected readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalsettingRepo;
        protected readonly IMediator _mediator;
        protected readonly IRepositoryWithEvents<Domain.Entities.Location> _locationRepo;
        protected readonly IGooglePlacesService _googlePlacesService;
        protected readonly IRepositoryWithEvents<Address> _addressRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.LeadBookedDetail> _leadBookedDetailRepo;
        protected readonly IRepositoryWithEvents<UnitType> _unitType;
        protected readonly Serilog.ILogger _logger;
        protected readonly INpgsqlRepository _npgsqlRepo;
        protected readonly IServiceBus _serviceBus;
        protected readonly IRepositoryWithEvents<LeadAppointment> _appointmentRepo;
        public readonly IDapperRepository _dapperRepository;
        protected readonly IReadRepository<CustomMasterLeadStatus> _masterLeadStatusRepo;

        public UpdateStatusHandler
       (
           IServiceProvider serviceProvider,
           string className,
           string methodName
       )
        {
            _serviceProvider = serviceProvider;
            _className = className;
            _methodName = methodName;
            _leadRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Lead>>();
            _userService = _serviceProvider.GetRequiredService<IUserService>();
            _notificationSenderService = _serviceProvider.GetRequiredService<INotificationSenderService>();
            _leadRepositoryAsync = _serviceProvider.GetRequiredService<ILeadRepositoryAsync>();
            _projectRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Project>>();
            _leadHistoryRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadHistory>>();
            _customLeadStatusRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<CustomMasterLeadStatus>>();
            _leadAssignmentRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadAssignment>>();
            _notificationSenderService = _serviceProvider.GetRequiredService<INotificationSenderService>();
            _currentUserRepo = _serviceProvider.GetRequiredService<ICurrentUser>();
            _propertyRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Property>>();
            _globalsettingRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.GlobalSettings>>();
            _mediator = _serviceProvider.GetRequiredService<IMediator>();
            _locationRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Location>>();
            _googlePlacesService = _serviceProvider.GetRequiredService<IGooglePlacesService>();
            _addressRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Address>>();
            _leadBookedDetailRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.LeadBookedDetail>>();
            _unitType = _serviceProvider.GetRequiredService<IRepositoryWithEvents<UnitType>>();
            _logger = _serviceProvider.GetRequiredService<Serilog.ILogger>();
            _npgsqlRepo = _serviceProvider.GetRequiredService<INpgsqlRepository>();
            _serviceBus = _serviceProvider.GetRequiredService<IServiceBus>();
            _appointmentRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadAppointment>>();
            _dapperRepository = _serviceProvider.GetRequiredService<IDapperRepository>();
            _masterLeadStatusRepo = _serviceProvider.GetRequiredService<IReadRepository<CustomMasterLeadStatus>>();
        }
        protected async Task V2UpdateLeadStatusAsync(V2UpdateLeadStatusRequest request, Guid? currentUserId, Domain.Entities.Lead existingLead,ViewLeadDto leadDto, Domain.Entities.GlobalSettings globalSettings ,CancellationToken cancellationToken = default, string? tenantId = null)
        {
            string query = string.Empty;
            try
            {
              //  Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                if (existingLead != null)
                {
                    await UpdateAppointmentsAsync(request, existingLead, cancellationToken);
                    List<Address> diffrentLocationAddresses = new List<Address>();
                    if (request?.Addresses?.Any() ?? false)
                    {
                        foreach (var address in request.Addresses)
                        {
                            Address? diffrentLocationAddress = null;

                            if (address.LocationId != null && address.LocationId != Guid.Empty)
                            {
                                diffrentLocationAddress = await _addressRepo.FirstOrDefaultAsync(new AddressByLocationIdSpec(new() { address.LocationId ?? Guid.Empty }), cancellationToken);
                                if (diffrentLocationAddress == null)
                                {
                                    var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(address.LocationId ?? Guid.Empty), cancellationToken);
                                    if (location != null)
                                    {
                                        diffrentLocationAddress = location.MapToAddress();
                                        if (diffrentLocationAddress != null)
                                        {
                                            diffrentLocationAddress.Id = Guid.Empty;
                                            diffrentLocationAddress = await _addressRepo.AddAsync(diffrentLocationAddress);
                                        }
                                    }
                                }
                            }
                            else if (!string.IsNullOrEmpty(address.PlaceId) && diffrentLocationAddress == null)
                            {
                                diffrentLocationAddress = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(address.PlaceId), cancellationToken)).FirstOrDefault();
                                if (diffrentLocationAddress == null)
                                {
                                    try
                                    {
                                        diffrentLocationAddress = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(address.PlaceId))?.Adapt<Address>() ?? null;
                                    }
                                    catch (Exception ex) { }
                                    diffrentLocationAddress = await _addressRepo.AddAsync(diffrentLocationAddress);

                                }
                            }

                            else if (diffrentLocationAddress == null && (address.Adapt<Address>()?.Validate(out Address? newAddress) ?? false))
                            {
                                if (newAddress != null)
                                {
                                    var existingAddress = await _addressRepo.GetByIdAsync(address?.Id ?? Guid.Empty);
                                    if (existingAddress == null)
                                    {
                                        diffrentLocationAddress = await _addressRepo.AddAsync(newAddress);
                                    }
                                    else
                                    {
                                        diffrentLocationAddress = existingAddress;
                                    }
                                    await MapAddressToLocationAndSaveAsync(diffrentLocationAddress);
                                }
                            }
                            diffrentLocationAddresses.Add(diffrentLocationAddress);
                        }

                    }

                    if (existingLead?.Enquiries?.FirstOrDefault(i => i.IsPrimary) != null && (diffrentLocationAddresses?.Any() ?? false))
                    {
                        existingLead.Enquiries.FirstOrDefault(i => i.IsPrimary).Addresses = diffrentLocationAddresses;
                    }
                    #region projects
                    if (request.ProjectsList?.Any() ?? false)
                    {
                        List<Lrb.Domain.Entities.Project>? projects = new();
                        request.ProjectsList = request.ProjectsList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                        if (request?.ProjectsList?.Any() ?? false)
                        {
                            foreach (var newProject in request.ProjectsList)
                            {
                                Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new GetProjectByIdSpecsV2(newProject), cancellationToken)).FirstOrDefault();
                                if (existingProject != null)
                                {
                                    projects.Add(existingProject);
                                }
                                else
                                {
                                    Domain.Entities.Project project = new() { Name = newProject };
                                    project = await _projectRepo.AddAsync(project, cancellationToken);
                                    projects.Add(project);
                                }
                            }
                        }
                        existingLead.Projects = projects;
                    }

                    #endregion

                    #region Properties

                    if (request?.PropertiesList?.Any() ?? false)
                    {
                        List<Domain.Entities.Property>? properties = new();
                        foreach (var newProperty in request.PropertiesList)
                        {
                            var existingProperty = (await _propertyRepository.ListAsync(new GetPropertyByTitleSpec(newProperty), cancellationToken)).FirstOrDefault();
                            if (existingProperty != null)
                            {
                                properties.Add(existingProperty);
                            }
                            else
                            {
                                Domain.Entities.Property property = new() { Title = newProperty };
                                property = await _propertyRepository.AddAsync(property, cancellationToken);
                                properties.Add(property);
                            }
                        }
                        existingLead.Properties = properties;
                    }
                    #endregion
                    
                    if ((request?.IsFullyCompleted ?? false) && (request?.Projects?.Any() ?? false))
                    {
                        var appointments = await _appointmentRepo.ListAsync(new GetAppointmentsByProjectsSpec(request?.Id ?? Guid.Empty, (request?.Projects?.ConvertAll(i => i.Trim().ToLower()) ?? new List<string>())));
                        appointments?.ForEach(i => i.IsFullyCompleted = true);
                        if (appointments?.Any() ?? false)
                        {
                            try
                            {
                                await _appointmentRepo.UpdateRangeAsync(appointments);
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "UpdateSiteVisitOrMeetingDoneRequestHandler -> Handle()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                        }
                    }
                     leadDto ??= existingLead?.Adapt<ViewLeadDto>();
                    await leadDto?.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId: currentUserId);
                    await UpdateLeadHistoryAsync(existingLead, leadDto, currentUserId: currentUserId, cancellationToken: cancellationToken, shouldUpdateNotes: request.IsNotesUpdated ?? false);


                }
            }
            catch (Exception ex)
            {
               // Console.WriteLine("UpdateLeadStatusRequestHandler exception details  => " + ex.Serialize());
                Log.Information("UpdateLeadStatusRequestHandler exception details  => " + ex.Serialize());
                throw;
            }
        }
        public async Task UpdateAppointmentsAsync(V2UpdateLeadStatusRequest request, Domain.Entities.Lead existingLead, CancellationToken cancellationToken)
        {
            #region Updating Meeting and SiteVisit Done
            if (request.MeetingOrSiteVisit != AppointmentType.None)
            {
                try
                {
                    var appointment = request.Adapt<LeadAppointment>();
                    appointment.Id = Guid.Empty;
                    appointment.UserId = existingLead.AssignTo;
                    switch (request.MeetingOrSiteVisit)
                    {
                        case AppointmentType.Meeting:
                            existingLead.IsMeetingDone = request.IsDone;
                            appointment.Type = AppointmentType.Meeting;
                            var places = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(request.Latitude, request.Longitude);
                            var place = places.FirstOrDefault();
                            if (place != null && place.PlaceId != null)
                            {
                                var address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                                if (address != null)
                                {
                                    existingLead.MeetingLocation = address.Id;
                                    appointment.Location = address;
                                }
                                else
                                {
                                    address = place.Adapt<Address>();
                                    address = await _addressRepo.AddAsync(address);
                                    existingLead.MeetingLocation = address.Id;
                                    appointment.Location = address;
                                }
                            }
                            break;
                        case AppointmentType.SiteVisit:
                            existingLead.IsSiteVisitDone = request.IsDone;
                            appointment.Type = AppointmentType.SiteVisit;
                            places = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(request.Latitude, request.Longitude);
                            place = places.FirstOrDefault();
                            if (place != null && place.PlaceId != null)
                            {
                                var address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                                if (address != null)
                                {
                                    existingLead.SiteLocation = address.Id;
                                    appointment.Location = address;
                                }
                                else
                                {
                                    address = place.Adapt<Address>();
                                    address = await _addressRepo.AddAsync(address);
                                    existingLead.SiteLocation = address.Id;
                                    appointment.Location = address;
                                }
                            }
                            break;
                    }
                    if (existingLead.Appointments?.Any() ?? false)
                    {
                        existingLead.Appointments.Add(appointment);
                    }
                    else
                    {
                        existingLead.Appointments = new List<LeadAppointment>() { appointment };
                    }
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "UpdateLeadStatusRequestHandler -> Handle()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
            }
            #endregion
        }
        private async Task MapAddressToLocationAndSaveAsync(Address address)
        {
            var location = MapToLocationRequest(address);
            if (location != null)
            {
                var locationRes = await _mediator.Send(location.Adapt<AddLocationRequest>());
                var createdLocation = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(locationRes.Data), default);
                address.Location = createdLocation;
                await _addressRepo.UpdateAsync(address);
            }
        }
        public async Task<CreateLocationDto?> MapToLocationRequest(Address address)
        {
            return address?.SubLocality != null || address?.Locality != null ?
             new()
             {
                 Locality = address.SubLocality ?? address.Locality ?? string.Empty,
                 City = address.City ?? string.Empty,
                 State = address.State ?? string.Empty,
                 Country = address.Country ?? string.Empty,
                 PostalCode = address.PostalCode ?? string.Empty,
                 Longitude = address.Longitude ?? string.Empty,
                 Latitude = address.Latitude ?? string.Empty,
                 IsGoogleMapLocation = address.IsGoogleMapLocation,
                 District = address.District ?? string.Empty,
             } : null;
        }
        protected async Task UpdateLeadHistoryAsync(Domain.Entities.Lead lead, ViewLeadDto? leadDto = null, AppointmentType? appointmentType = null, CancellationToken cancellationToken = default, bool? shouldUpdateContactRecord = null, Guid? currentUserId = null, bool shouldUpdateNotes = false)
        {
            try
            {
                var userId = currentUserId ?? _currentUserRepo.GetUserId();
                leadDto ??= await GetFullLeadDtoAsync(lead, cancellationToken);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto, appointmentType, shouldUpdateContactRecord: shouldUpdateContactRecord);
                if (userId != Guid.Empty && lead.AssignTo == Guid.Empty && userId != lead.AssignTo)
                {
                    leadHistory.UserId = userId;
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, userId));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: shouldUpdateNotes), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: shouldUpdateNotes), cancellationToken);
                        }
                    }
                    else
                    {
                        await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                    }
                }
                else
                {
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: shouldUpdateNotes), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: shouldUpdateNotes), cancellationToken);
                        }
                    }
                    else
                    {
                        existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                        if (existingLeadHistory != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
            }
        }
        public static async Task<bool> SendLeadStatusChangeNotificationsAsync(Domain.Entities.Lead? lead, ViewLeadDto leadDto, INotificationSenderService _notificationSenderService, Domain.Entities.GlobalSettings? globalSettings, Guid currentUserId = default)
        {
            try
            {
                bool isSent = false;
                switch (leadDto.Status?.Status)
                {
                    case "callback":
                        var response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                        {
                            List<string> callbackStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToCallback, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        }
                        List<string> callbackStatusEvent2jobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.CallbackReminder, lead, leadDto?.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        isSent = true;
                        break;
                    case "dropped":
                        response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (currentUserId != leadDto.AssignTo)
                        {
                            List<string> dropStatusEventJobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadSatusToDropped, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        }
                        isSent = true;
                        break;
                    case "booked":
                        response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (currentUserId != leadDto.AssignTo)
                        {
                            List<string> bookedStatusEventJobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToBook, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        }
                        isSent = true;
                        break;
                    case "not_interested":
                        response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (currentUserId != leadDto.AssignTo)
                        {
                            List<string> notInterestedStatusEventJobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToNotInterest, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        }
                        isSent = true;
                        break;
                    case "site_visit_scheduled":
                        response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                        {
                            List<string> siteVisitScheduledStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToScheduleSiteVisit, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        }
                        List<string> siteVisitScheduledStatusEvent2JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.ScheduleSiteVisitReminder, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        isSent = true;
                        break;
                    case "meeting_scheduled":
                        response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                        {
                            List<string> meetingScheduledStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToScheduleMeeting, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        }
                        List<string> meetingScheduledStatusEvent2JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.ScheduleMeetingReminder, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                        isSent = true;
                        break;
                    default:
                        break;
                }

                if ((leadDto?.Status?.ShouldUseForMeeting == true) && (!isSent))
                {
                    if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                    {
                        List<string> meetingScheduledStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToScheduleMeeting, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                    }
                    List<string> meetingScheduledStatusEvent2JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.ScheduleMeetingReminder, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                }
                else if ((leadDto?.Status?.ShouldUseForMeeting == false) && (!isSent))
                {
                    if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                    {
                        List<string> siteVisitScheduledStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToScheduleSiteVisit, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                    }
                    List<string> siteVisitScheduledStatusEvent2JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.ScheduleSiteVisitReminder, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                }
                else if ((leadDto?.Status?.IsScheduled ?? false) && (!isSent))
                {
                    if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                    {
                        List<string> callbackStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadStatusToCallback, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                    }
                    List<string> callbackStatusEvent2jobIds = await _notificationSenderService.ScheduleNotificationsAsync(Event.CallbackReminder, lead, leadDto?.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                }
                else if ((leadDto?.Status?.WhatsAppTemplateInfoIds?.Any() ?? false))
                {
                    await _notificationSenderService.SendWhatsAppNotificationAsync(lead, leadDto?.Status?.WhatsAppTemplateInfoIds ?? new(), null, globalSettings, leadDto: leadDto);
                }
                else if ((leadDto?.Status?.ChildType?.WhatsAppTemplateInfoIds?.Any() ?? false))
                {
                    await _notificationSenderService.SendWhatsAppNotificationAsync(lead, leadDto?.Status?.ChildType?.WhatsAppTemplateInfoIds ?? new(), null, globalSettings, leadDto: leadDto);
                }
                else
                {
                    var eventIds = new List<Guid>
                    {
                        leadDto?.Status?.Id ?? Guid.Empty,
                        leadDto?.Status?.ChildType?.Id ?? Guid.Empty
                    };
                    if (globalSettings?.IsEngageToEnabled ?? false)
                    {
                        await _notificationSenderService.SendTemplateNotificationAsync(lead, eventIds, Event.Engegato);
                    }
                    else
                    {
                        await _notificationSenderService.SendTemplateNotificationAsync(lead, eventIds, null);
                    }

                }
                return true;
            }
            catch { return false; }
        }
        public async Task SendNotificationToAdminsAndReporteesAsync(Domain.Entities.Lead lead, ViewLeadDto? leadDto, Domain.Entities.GlobalSettings globalSettings, string tenantId, Guid currentUserId)
        {
            leadDto ??= await GetFullLeadDtoAsync(lead, CancellationToken.None, currentUserId);
            var userInfo = (await _dapperRepository.GetUserDetailsAsync(tenantId ?? string.Empty, new List<Guid> { lead.AssignTo }))?.FirstOrDefault();
            List<Guid> adminIds = (await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty)).Where(i => i != (currentUserId)).ToList();
            var notificationSetting = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty) ?? new();
            if ((notificationSetting != null) && notificationSetting.IsAdminEnabled && (adminIds?.Any() ?? false))
            {
                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, leadDto.AssignedUser?.Name, globalSettings: globalSettings, userIds: adminIds);
            }
            if ((notificationSetting != null) && notificationSetting.IsManagerEnabled && (userInfo != null) && (userInfo?.ReportsTo != null) && (userInfo.ReportsTo.Id != Guid.Empty) && !(adminIds != null && adminIds.Any(i => userInfo.ReportsTo.Id == i)) && (userInfo?.ReportsTo.Id != currentUserId))
            {
                List<Guid> userIds = new() { userInfo.ReportsTo.Id };
                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, leadDto.AssignedUser?.Name, globalSettings: globalSettings, userIds: userIds);
            }
            if ((notificationSetting != null) && notificationSetting.IsGeneralManagerEnabled && (userInfo != null) && (userInfo?.GeneralManager != null) && (userInfo.GeneralManager.Id != Guid.Empty) && !(adminIds != null && adminIds.Any(i => userInfo.GeneralManager.Id == i)) && (userInfo.GeneralManager.Id != currentUserId))
            {
                List<Guid> userIds = new() { userInfo.GeneralManager.Id };
                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, leadDto.AssignedUser?.Name, globalSettings: globalSettings, userIds: userIds);
            }
        }
        public async Task<ViewLeadDto> GetFullLeadDtoAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default, Guid? currentUserId = null)
        {
            var leadDto = lead.Adapt<ViewLeadDto>();
            await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId: currentUserId);
            return leadDto;
        }
        public async Task AddLrbErrorAsync(Exception ex, string moduleName)
        {
            var error = new LrbError()
            {
                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                ErrorSource = ex?.Source,
                StackTrace = ex?.StackTrace,
                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                ErrorModule = moduleName
            };
            await _leadRepositoryAsync.AddErrorAsync(error);
        }
        public async Task SendStatusChangeNofificationAsync(UpdateStatusChangeDto request,CancellationToken cancellationToken,Guid? currentUserId,string? tenantId)
        {
            var existingLead = request.Lead ?? (await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.Id), cancellationToken));
            var leadDto = request.LeadDto ?? existingLead.Adapt<ViewLeadDto>();
            var globalSettings = request.GlobalSettings ?? await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            currentUserId ??= request.CurrentUserId ?? _currentUserRepo.GetUserId();
            tenantId ??= request.TenantId ?? _currentUserRepo.GetTenant();
            try
            {
                await SendLeadStatusChangeNotificationsAsync(existingLead, leadDto, _notificationSenderService, globalSettings, currentUserId ?? Guid.Empty);
                Event? @event = null;
                if (request.MeetingOrSiteVisit != AppointmentType.None)
                {
                    switch (request.MeetingOrSiteVisit)
                    {
                        case AppointmentType.SiteVisit:
                            switch (request.IsDone)
                            {
                                case true:
                                    @event = Event.LeadSiteVisitDone;
                                    break;
                                case false:
                                    @event = Event.LeadSiteVisitNotDone;
                                    break;
                            }
                            break;
                        case AppointmentType.Meeting:
                            switch (request.IsDone)
                            {
                                case true:
                                    @event = Event.LeadMeetingDone;
                                    break;
                                case false:
                                    @event = Event.LeadMeetingNotDone;
                                    break;
                            }
                            break;
                    }
                    if (@event != null)
                    {
                        Event updatedEvent = (Event)@event;
                        var userDetails = await _userService.GetAsync(existingLead.AssignTo.ToString(), cancellationToken);
                        if (userDetails.Id != currentUserId && currentUserId != Guid.Empty)
                        {
                            await _notificationSenderService.ScheduleNotificationsAsync(updatedEvent, existingLead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, currentUserId: currentUserId);
                            await SendNotificationToAdminsAndReporteesAsync(existingLead, leadDto, globalSettings, tenantId, currentUserId ?? leadDto.LastModifiedBy);
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "UpdateLeadStatusRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
    }
}
