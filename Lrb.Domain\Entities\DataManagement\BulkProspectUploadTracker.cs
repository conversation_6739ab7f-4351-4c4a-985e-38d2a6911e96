﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Domain.Entities
{
    public class BulkProspectUploadTracker : AuditableEntity, IAggregateRoot
    {
        public int TotalCount { get;set; }
        public int DistinctProspectCount { get;set; }
        public int TotalUploadedCount { get;set; }
        public int DuplicateCount { get;set; }
        public int ProspectCount { get; set; }
        public int InvalidCount { get;set; }
        public string? S3BucketKey { get; set; }
        public string? InvalidDataS3BucketKey { get;set; }
        public string? SheetName { get; set; }
        [Column(TypeName ="jsonb")]
        public Dictionary<ProspectDataColumn, string>? MappedColumnData { get; set; }
        public UploadStatus Status { get; set; }
        public string? Message { get; set; }
        [Column(TypeName = "jsonb")]
        public List<string>? UserIds { get; set; }
    }
}
