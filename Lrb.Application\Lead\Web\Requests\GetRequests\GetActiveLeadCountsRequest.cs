﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Web
{

    public class GetActiveLeadCountsRequest : GetAllLeadsParametersNewFilters, IRequest<ActiveLeadCountsDto>
    {

    }
    public class GetActiveLeadsCountRequestHandler : GetAll<PERSON><PERSON>s<PERSON><PERSON><PERSON><PERSON>and<PERSON>, IRequestHandler<GetActiveLeadCountsRequest, ActiveLeadCountsDto>
    {
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetails;
        public GetActiveLeadsCountRequestHandler(
            ICurrentUser currentUser,
            IDapperRepository dapperRepository,
            ILeadRepository efLeadRepository,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customMasterLeadStatusRepo,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetails)
            : base(currentUser,
                   dapperRepository,
                   efLeadRepository, customMasterLeadStatusRepo)
        {
            _leadRepositoryAsync = leadRepositoryAsync;
            _userDetails = userDetails;
        }
        public async Task<ActiveLeadCountsDto> Handle(GetActiveLeadCountsRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (request?.LeadTags?.Any() ?? false)
                {
                    request.TagFilterDto = GetLeadTagFilter(request.Adapt<GetAllLeadsByNewFiltersRequest>());
                    request.LeadTags = null;
                }
                if (request?.DesignationsId?.Any() ?? false)
                {
                    var users = await _userDetails.ListAsync(new Lrb.Application.Dashboard.Web.Specs.GetUsersByDesignationIdSpec(request.DesignationsId));
                    var userIds = users.Select(i => i.UserId).ToList();
                    if (request.AssignTo == null)
                    {
                        request.AssignTo = new List<Guid>();
                    }
                    request.AssignTo.AddRange(userIds);

                }
                var userId = _currentUser.GetUserId();
                var tenantId = _currentUser.GetTenant();
                var isAdmin = _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty).Result;
                //request.UserType = request?.UserType ?? UserType.None;
                List<Guid> leadHistoryIds = new();
                List<Guid> subIds = new();
                try
                {
                    if (request?.AssignTo?.Any() ?? false)
                    {
                        if (request?.IsWithTeam ?? false)
                        {
                            subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, tenantId ?? string.Empty)).ToList();
                        }
                        else
                        {
                            subIds = request?.AssignTo ?? new List<Guid>();
                        }
                    }
                    else
                    {
                        if (request?.IsOnlyReportees ?? false)
                        {
                            subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty))?.ToList() ?? new();
                        }
                        else
                        {
                            subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllLeads))?.ToList() ?? new();
                        }
                    }

                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "GetActiveLeadsCountRequestHandler -> Handle()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                    return new();
                }
                LeadCountsByNewFilterDto leadCounts = new();
                var activeStatuses = typeof(ActiveLeadCountsDto).GetProperties().ToList();
                var customStatus = await _customMasterLeadStatusRepo.ListAsync(cancellationToken);
                var query = await _efLeadRepository.BuildQueryForLeadsCount(request.Adapt<GetActiveLeadCountsRequest>(), subIds, userId, leadHistoryIds, customStatus, tenantId, isAdmin);
                foreach (var activeStatus in activeStatuses)
                {
                    leadCounts = AddLeadsCount(leadCounts, activeStatus, request.Adapt<GetActiveLeadCountsRequest>(), subIds, userId, leadHistoryIds, customStatus, query, isAdmin: isAdmin).Result;
                }
                return leadCounts.Adapt<ActiveLeadCountsDto>();
            }
            catch (Exception ex)
            {
                return new();
            }
        }

    }
}
