using Lrb.Application.Multitenancy;

namespace Lrb.Identity.Host.Controllers.Multitenancy;

[AllowAnonymous]
public class TenantsController : VersionNeutralApiController
{
    //[HttpGet]
    //[MustHavePermission(LrbAction.View, LrbResource.Tenants)]
    //[OpenApiOperation("Get a list of all tenants.", "")]
    //public async Task<Response<List<TenantDto>>> GetListAsync()
    //{
    //    return new(await Mediator.Send(new GetAllTenantsRequest()));
    //}

    [HttpGet("{id}")]
    [MustHavePermission(LrbAction.View, LrbResource.Tenants)]
    [OpenApiOperation("Get tenant details.", "")]
    public async Task<Response<TenantDto>> GetAsync(string id)
    {
        return  new(await Mediator.Send(new GetTenantRequest(id)));
    }

    [HttpPost]
    [MustHavePermission(LrbAction.Create, LrbResource.Tenants)]
    [OpenApiOperation("Create a new tenant.", "")]
    public async Task<Response<string>> CreateAsync(CreateTenantRequest request)
    {
        return new(await Mediator.Send(request), null);
    }

    [HttpPost("{id}/activate")]
    [MustHavePermission(LrbAction.Update, LrbResource.Tenants)]
    [OpenApiOperation("Activate a tenant.", "")]
    public async Task<Response<string>> ActivateAsync(string id)
    {
        return new(await Mediator.Send(new ActivateTenantRequest(id)), null);
    }

    [HttpPost("{id}/deactivate")]
    [MustHavePermission(LrbAction.Update, LrbResource.Tenants)]
    [OpenApiOperation("Deactivate a tenant.", "")]
    public async Task<Response<string>> DeactivateAsync(string id)
    {
        return new(await Mediator.Send(new DeactivateTenantRequest(id)), null);
    }

    [HttpPost("{id}/upgrade")]
    [MustHavePermission(LrbAction.UpgradeSubscription, LrbResource.Tenants)]
    [OpenApiOperation("Upgrade a tenant's subscription.", "")]
    public async Task<ActionResult<Response<string>>> UpgradeSubscriptionAsync(string id, UpgradeSubscriptionRequest request)
    {
        if(id != request.TenantId)
        {
            return BadRequest();
        }
        var res = new Response<string>(await Mediator.Send(request), null);
        return Ok(res);
    }
    [HttpGet("tenantCacheUpdate")]
    [AllowAnonymous]
    [OpenApiOperation("Update Tenant Cache", "")]
    public async Task<Response<bool>> UpdateMultitenantCache()
    {
        return new(await Mediator.Send(new MultitenantCacheUpdateRequest()));
    }
    [HttpGet("tenantCatche")]
    [AllowAnonymous]
    [OpenApiOperation("Get Tenant Cache by tenantId.", "")]
    public async Task<Response<List<TenantDto>>> GetTenantsAsync(string? tenantId)
    {
        return await Mediator.Send(new GetTenantCacheByIdRequest(tenantId));
    }
    [HttpPut("UpdateTenantCache")]
    [AllowAnonymous]
    [OpenApiOperation("Update tenant Cache by tenantId.", "")]
    public async Task<Response<bool>> UpdateTenantCacheById(string? tenantId)
    {
        return await Mediator.Send(new UpdateTenantCacheByIdRequest(tenantId));
    }

    [HttpPost("SeedDatabaseAsync")]
    [MustHavePermission(LrbAction.Create, LrbResource.Tenants)]
    [OpenApiOperation("SeedDatabaseAsync", "")]
    public async Task<Response<string>> SeedDatabaseAsync(SeedingDatabaseRequest request)
    {
        return new(await Mediator.Send(request), null);
    }
}