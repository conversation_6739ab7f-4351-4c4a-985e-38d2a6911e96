﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Mobile.Specs
{
    public class GetProspectHistoryByProspectIdSpecs : Specification<ProspectHistory>
    {
        public GetProspectHistoryByProspectIdSpecs(Guid prospectid)
        {
            Query.Where(i => !i.IsDeleted && i.ProspectId == prospectid && i.Version < 3);
        }

        public GetProspectHistoryByProspectIdSpecs(Guid prospectid, List<Guid>? userIds)
        {
            Query.Where(i => !i.IsDeleted && i.ProspectId == prospectid && (userIds.Contains(i.UserId ?? Guid.Empty)|| userIds.Contains(i.LastModifiedById ?? Guid.Empty)));
        }
    }
}
