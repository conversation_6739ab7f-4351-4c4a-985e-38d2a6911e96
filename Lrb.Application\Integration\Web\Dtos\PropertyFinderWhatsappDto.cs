﻿using System.Text.Json.Serialization;

namespace Lrb.Application.Integration.Web.Dtos
{

    public class PropertyFinderWhatsappDto : IDto
    {
        public PFWebHookBody? data { get; set; }
    }
    public class PFWebHookBody
    {
        public string? type { get; set; }
        public string? id { get; set; }
        public PFAttributes? attributes { get; set; }
        public PFRelationships? relationships { get; set; }
    }
    public class PFRelationships
    {
        public PFAgents? agents { get; set; }  
        public PFProperties? properties { get; set; }
    }

    public class PFAttributes
    {
        public string? enquirer_phone_number { get; set;}
        public string? language { get; set; }
        public string? message { get; set; }
        public string? received_at { get; set; }
        public string? tracking_link { get; set; }
    }

    #region PF Agent
    public class PFAgents
    {
        public PFAgentData? data { get; set; }
    }

    public class PFAgentData
    {
        public string? type { get; set; }
        public string? id { get; set; }
        public PFDataAttributes? attributes { get; set; }
    }

    public class PFDataAttributes
    {
        public string? email { get; set; }
        public string? full_name { get; set; }
        public string? whatsapp_phone_number { get; set; }
    }
    #endregion

    #region Pf Properties
    public class PFProperties
    {
        public PFPropertiesData? data { get; set; }
    }

    public class PFPropertiesData
    {
        public string? type { get; set; }
        public string? id { get; set; }
        public PFPropertiesDataAttributes? attributes { get; set; }
        public PFPropertiesDataRelationships? relationships { get; set; }
    }

    public class PFPropertiesDataAttributes
    {
        public string? category { get; set; }
        public PFPropertiesDataAttributesPrice? prices { get; set; }
        public string? reference { get; set; }
        public string? type { get; set; }
        public string? website_link { get; set; }
    }

    public class PFPropertiesDataAttributesPrice
    {
        public string? monthly { get; set; }
        public string? yearly { get; set; }
    }

    public class PFPropertiesDataRelationships
    {
        public PFPropertiesDataRelationshipsLocations? locations { get; set; }
    }

    public class PFPropertiesDataRelationshipsLocations
    {
        public List<PFPropertiesDataRelationshipsLocationsData>? data { get; set; }
    }

    public class PFPropertiesDataRelationshipsLocationsData
    {
        public string? type { get; set; }
        public string? id { get; set; }
        public PFLOcationAttributes? attributes { get; set; }
    }

    public class PFLOcationAttributes
    {
        public string? name { get; set; }
    }
    #endregion

    #region Pf Webhokk Params
    public class PFWebhookRequestParams
    {
        public string? type { get; set; }
        public string? id { get; set; }
        public string? enquirer_phone_number { get; set;}
        public string? language { get; set; }
        public string? message { get; set; }
        public string? received_at { get; set; }
        public string? tracking_link { get; set; }
    }
    #endregion

    #region PF Cred Dto
    public class PFIntegrationCredDto : IDto
    {
        [JsonPropertyName("apiKey")]
        public string? ApiKey { get; set; }
        [JsonPropertyName("secretKey")]
        public string? SecretKey { get; set; }
    }

    public class PFIntegrationCredDtoV2 : IDto
    {
        public string? ApiKey { get; set; }
        public string? SecretKey { get; set; }
        public string? PFEventId { get; set; }
    }
    #endregion

    #region Assign Pf Property Dto
    public class LrbAssignPfPropertyDto
    {
        public Guid LeadId { get; set; }
        public string? RefrenceNo { get; set; }
        public string? ApiKey { get; set; }
        public string? SecretKey { get; set; }
        public bool? IsPropertyListingEnable { get; set; }
    }
    #endregion

    #region Assign Pf Property Dto V2
    public class LrbAssignPfPropertyDtoV2
    {
        public Guid LeadId { get; set; }
        public string? ListingId { get; set; }
        public string? ApiKey { get; set; }
        public string? SecretKey { get; set; }
    }
    #endregion


    #region PF Push Api Response V2
    public class PFRoot
    {
        public string? Id { get; set; }
        public string? Type { get; set; } = string.Empty;
        public string? Timestamp { get; set; }
        public PFEntity? Entity { get; set; } = new();
        public PFPayload? Payload { get; set; } = new();
    }

    public class PFEntity
    {
        public string? Id { get; set; } = string.Empty;
        public string? Type { get; set; } = string.Empty;
    }

    public class PFPayload
    {
        public string? Channel { get; set; } = string.Empty;
        public string? Status { get; set; } = string.Empty;
        public string? EntityType { get; set; } = string.Empty;
        public string? ResponseLink { get; set; }
        public PFPublicProfile? PublicProfile { get; set; } = new();
        public PFListing? Listing { get; set; } = new();
        public PFProject? Project { get; set; } = new();
        public PFDeveloper? Developer { get; set; } = new();
        public PFSender? Sender { get; set; } = new();
    }

    public class PFPublicProfile
    {
        public string? Id { get; set; }
    }

    public class PFListing
    {
        public string? Id { get; set; }
    }

    public class PFProject
    {
        public string? Id { get; set; }
    }

    public class PFDeveloper
    {
        public string? Id { get; set; }
    }

    public class PFSender
    {
        public string Name { get; set; } = string.Empty;
        public List<Contact> Contacts { get; set; } = new();
    }

    public class Contact
    {
        public string Type { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
    }

    #endregion
}
