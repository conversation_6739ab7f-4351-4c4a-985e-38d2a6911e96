﻿using Microsoft.AspNetCore.Components.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Common.HttpService
{
    public interface IHttpService : ITransientService
    {

        /// <summary>
        /// Executes Http POST Request
        /// </summary>
        /// <typeparam name="TBody"></typeparam>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="resource"></param>
        /// <param name="DTO"></param>
        /// <param name="headers"></param>
        /// <returns name="TResponse"></returns>
        /// <exception cref="ArgumentNullException"></exception>
        Task<TResponse> PostAsync<TBody, TResponse>(string baseUrl, string resource, TBody DTO, Dictionary<string, string> headers = null) where TBody : class;
        /// <summary>
        /// Executes Any Http Resquest
        /// </summary>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns name="TResponse"></returns>
        /// <exception cref="UnauthorizedAccessException"></exception>
        /// <exception cref="ArgumentNullException"></exception>
        Task<TResponse> ExcuteAsync<TResponse>(HttpRequestMessage request, CancellationToken cancellationToken);
        /// <summary>
        /// Executes Any Http Resquest
        /// </summary>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns name="TResponse"></returns>
        /// <exception cref="UnauthorizedAccessException"></exception>
        /// <exception cref="ArgumentNullException"></exception>
        public Task<Dictionary<string, string>> ExecuteCurlCommandAsync(string curlCommand);
    }
}
