﻿using Lrb.Application.Identity.Users;
using Lrb.Application.Utils;
using Lrb.Domain.Constants;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Data;
using System.Text.RegularExpressions;

namespace Lrb.Application.UserDetails.Mobile
{
    public class CreateBulkUsersRequest : IRequest<Response<bool>>
    {
        public IFormFile FormFile;
        public string Origin { get; set; }
        public CreateBulkUsersRequest(IFormFile formFile, string originRequest)
        {
            FormFile = formFile;
            Origin = originRequest;
        }
    }
    public class CreateBulkUsersRequestHandler : IRequestHandler<CreateBulkUsersRequest, Response<bool>>
    {
        public const string DefaultPassword = "123Pa$$word!";
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepository;

        public CreateBulkUsersRequestHandler(IUserService userService,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepository)
        {
            _userService = userService;
            _userDetailsRepository = userDetailsRepository;
        }

        public async Task<Response<bool>> Handle(CreateBulkUsersRequest file, CancellationToken cancellationToken)
        {
            var dataTable = ExcelHelper.ExcelToDataTable(file.FormFile);
            int totalRows = dataTable.Rows.Count;
            for (int i = totalRows - 1; i >= 0; i--)
            {
                var row = dataTable.Rows[i];
                if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                {
                    row.Delete();
                }
            }
            if (dataTable.Rows.Count <= 0)
            {
                return new(false, "Excel sheet is empty. Please fill some data in the excel sheet template.");
            }
            List<DuplicateUsersDto> duplicateUsersDto = new List<DuplicateUsersDto>();
            List<CreateUserRequest> users = new List<CreateUserRequest>();
            foreach (DataRow row in dataTable.Rows)
            {
                DuplicateUsersDto duplicateUsers = new DuplicateUsersDto();
                CreateUserRequest userDetails = new CreateUserRequest();
                #region Name Validation
                userDetails.FirstName = row["FirstName"].ToString();
                if (string.IsNullOrWhiteSpace(userDetails.FirstName))
                {
                    return new(false, "FirstName can not be null!");
                }
                userDetails.LastName = row["LastName"].ToString();
                if (string.IsNullOrWhiteSpace(userDetails.LastName))
                {
                    return new(false, "LastName can not be null!");
                }
                userDetails.UserName = row["UserName"].ToString();
                if (string.IsNullOrWhiteSpace(userDetails?.UserName?.Trim()))
                {
                    return new(false, "UserName can not be null!");
                }
                if (userDetails.UserName.Contains(" "))
                {
                    return new(false, "UserName can not contain a space character! " + userDetails.UserName);
                }
                if (users.Select(i => i.UserName).Contains(userDetails.UserName.Trim()))
                {
                    return new(false, "UserName already exists in excel sheet! " + userDetails.UserName);
                }
                if (await _userService.ExistsWithNameAsync(userDetails.UserName))
                {
                    duplicateUsers.UserName = userDetails.UserName;
                }
                #endregion
                #region PhoneNumber Validation
                var phoneNumber = row["PhoneNumber"].ToString();
                if (string.IsNullOrWhiteSpace(phoneNumber.Trim()))
                {
                    return new(false, "PhoneNumber can not be null!");
                }
                else if (phoneNumber.Trim().Length < 5)
                {
                    return new(false, "Invalid PhoneNumber format! " + phoneNumber);
                }
                userDetails.PhoneNumber = phoneNumber;
                if (userDetails.PhoneNumber.Trim().Length == 10)
                {
                    userDetails.PhoneNumber = "+91" + userDetails.PhoneNumber.ToString();
                }
                if (users.Select(i => i.PhoneNumber).Contains(userDetails.PhoneNumber.Trim()))
                {
                    return new(false, "PhoneNumber already exists in excel sheet! " + userDetails.PhoneNumber);
                }
                if (await _userService.ExistsWithPhoneNumberAsync(userDetails.PhoneNumber.Trim()))
                {
                    duplicateUsers.PhoneNumber = userDetails.PhoneNumber;
                }
                #endregion
                #region Email Validation
                userDetails.Email = row["Email"].ToString();
                if (!string.IsNullOrWhiteSpace(userDetails.Email.Trim()))
                {
                    if (Regex.IsMatch(userDetails.Email, RegexPatterns.EmailPattern))
                    {
                        userDetails.Email = row["Email"].ToString();
                    }
                    else
                    {
                        return new(false, "Email can not be null! " + userDetails.Email);
                    }
                }
                if (await _userService.ExistsWithEmailAsync(userDetails.Email.Trim()))
                {
                    duplicateUsers.Email = userDetails.Email;
                }
                if (users.Select(i => i.Email).Contains(userDetails.Email.Trim()))
                {
                    return new(false, "Email already exist in excel sheet! " + userDetails.Email);
                }
                #endregion

                userDetails.Address = row["Address"].ToString();
                if (string.IsNullOrWhiteSpace(row["BloodGroupType"].ToString()))
                {
                    userDetails.BloodGroup = BloodGroupType.None;
                }
                else
                {
                    userDetails.BloodGroup = Enum.Parse<BloodGroupType>(row["BloodGroupType"].ToString());
                }

                if (string.IsNullOrWhiteSpace(row["Gender"].ToString()))
                {
                    userDetails.Gender = Gender.NotMentioned;
                }
                else
                {
                    userDetails.Gender = Enum.Parse<Gender>(row["Gender"].ToString());
                }
                userDetails.Description = row["Description"].ToString();
                users.Add(userDetails);
                if (duplicateUsers.UserName != null || duplicateUsers.Email != null || duplicateUsers.PhoneNumber != null)
                {
                    duplicateUsersDto.Add(duplicateUsers);
                }
                try
                {
                    var brokerNumber = row["BrokerNumber"]?.ToString();

                    userDetails.LicenseNo = !string.IsNullOrWhiteSpace(brokerNumber) ? brokerNumber : null;

                }
                catch (Exception ex)
                {
                }
            }
            GetAllDuplicateItemsModel duplicates = new();
            if (duplicateUsersDto.Any(i => i != null))
            {
                List<DuplicateUsersDto> duplicateItems = new();
                duplicateUsersDto.ToList().ForEach(i => duplicateItems.Add(new DuplicateUsersDto(i.UserName, i.PhoneNumber, i.Email, DuplicateItemType.User)));
                duplicates.DuplicateItems.AddRange(duplicateItems);
                duplicates.Count = duplicateItems.Count;
                duplicates.RequestedItemCount = dataTable.Rows.Count;
            }
            if (duplicates.DuplicateItems.Any())
            {
                return new Response<bool>(true, JsonConvert.SerializeObject(duplicates));
            }
            foreach (var user in users)
            {
                user.Password = DefaultPassword;
                user.ConfirmPassword = DefaultPassword;
                var res = await _userService.CreateAsync(user, file.Origin);
                Domain.Entities.UserDetails userDetails = user.Adapt<Domain.Entities.UserDetails>();
                userDetails.CurrentAddress = user.Address;
                userDetails.UserId = Guid.Parse(res.userId);
                await _userDetailsRepository.AddAsync(userDetails, cancellationToken);
            }
            return new(true, "Default password for all created users : " + DefaultPassword);
        }
    }
}
