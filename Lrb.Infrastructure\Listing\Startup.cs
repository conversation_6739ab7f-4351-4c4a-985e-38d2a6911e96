﻿using Lrb.Application.Common.Listing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Infrastructure.Listing
{
    public static class Startup
    {
        public static IServiceCollection AddListing(this IServiceCollection service, IConfiguration config)
        {
            service.AddTransient<IPropertyFinderListingService, PropertyFinderListingService>();
            return service;
        }
    }
}
