using Lrb.Application.CustomForm.Web.Dtos;
using Lrb.Domain.Entities;

namespace Lrb.Application.CustomForm.Web.Requests
{
    public class CreateCustomFormRequest :  IRequest<Response<bool>>
    {
        public List<CreateCustomFormDto>? Fields { get; set; }
    }

    public class CreateCustomFormRequestHandler : IRequestHandler<CreateCustomFormRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.CustomFormFields> _customFormRepo;

        public CreateCustomFormRequestHandler(IRepositoryWithEvents<Domain.Entities.CustomFormFields> customFormRepo)
        {
            _customFormRepo = customFormRepo;
        }
        public async Task<Response<bool>> Handle(CreateCustomFormRequest request, CancellationToken cancellationToken)
        {
            if (request?.Fields?.Count() < 0)
            {
                throw new InvalidOperationException("Fields is required");
            }
            List<Domain.Entities.CustomFormFields>? customForms = new();
            foreach (var field in request?.Fields)
            {
                var customForm = field.Adapt<Domain.Entities.CustomFormFields>();
                customForms.Add(customForm);
            }
            await _customFormRepo.AddRangeAsync(customForms);

            return new(true);
        }
    }
}
