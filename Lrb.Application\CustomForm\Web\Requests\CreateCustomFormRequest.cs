using Lrb.Application.CustomForm.Web.Dtos;
using Lrb.Domain.Entities;

namespace Lrb.Application.CustomForm.Web.Requests
{
    public class CreateCustomFormRequest :  IRequest<Response<bool>>
    {
        public List<string>? FieldDisplayName { get; set; }
        public List<string>? Module { get; set; } = default!;
        public List<Guid>? EntityId { get; set; }

    }

    public class CreateCustomFormRequestHandler : IRequestHandler<CreateCustomFormRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.CustomFormFields> _customFormRepo;

        public CreateCustomFormRequestHandler(IRepositoryWithEvents<Domain.Entities.CustomFormFields> customFormRepo)
        {
            _customFormRepo = customFormRepo;
        }
        public async Task<Response<bool>> Handle(CreateCustomFormRequest request, CancellationToken cancellationToken)
        {
            if (request?.FieldDisplayName==null && request?.EntityId == null && request?.Module ==null )
            {
                throw new InvalidOperationException("Fields is required");
            }
            List<CustomFormFields> customForms = new();

            for (int i = 0; i < request.Module.Count; i++)
            {
                var module = request.Module[i];
                var entityId = request.EntityId[i];

                foreach (var fieldDisplayName in request.FieldDisplayName)
                {
                    var field = new CustomFormFields
                    {
                        FieldName = fieldDisplayName,
                        FieldDisplayName = fieldDisplayName,
                        FieldType = QRFormType.String, // You can customize this
                        Module = module,
                        IsRequired = true,
                        Notes = null,
                        EntityId = entityId,
                        EntityName = null
                    };

                    customForms.Add(field);
                }
            }


            await _customFormRepo.AddRangeAsync(customForms, cancellationToken);

            return new Response<bool>(true);


        }
    }
}
