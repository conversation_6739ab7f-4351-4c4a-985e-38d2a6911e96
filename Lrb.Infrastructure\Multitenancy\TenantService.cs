﻿using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.Stores;
using Lrb.Application.Common.Exceptions;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Multitenancy;
using Lrb.Infrastructure.ApiClientService;
using Lrb.Infrastructure.Facebook;
using Lrb.Infrastructure.Persistence;
using Lrb.Infrastructure.Persistence.Initialization;
using Mapster;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Options;
using System.Threading;

namespace Lrb.Infrastructure.Multitenancy;

internal class TenantService : ITenantService
{
    private readonly IMultiTenantStore<LrbTenantInfo> _tenantStore;
    private readonly IConnectionStringSecurer _csSecurer;
    private readonly IDatabaseInitializer _dbInitializer;
    private readonly DatabaseSettings _dbSettings;
    private readonly IDisplayIndexPrefixService _displayIndexPrefixService;
    private readonly TenantDbContext _tenantDbContext;
    private readonly ApiClientSettings _settings;

    public TenantService(
        IMultiTenantStore<LrbTenantInfo> tenantStore,
        IConnectionStringSecurer csSecurer,
        IDatabaseInitializer dbInitializer,
        IOptions<DatabaseSettings> dbSettings,
        IDisplayIndexPrefixService displayIndexPrefixService,
        TenantDbContext tenantDbContext,
        IOptions<ApiClientSettings> option)
    {

        _tenantStore = tenantStore;
        _csSecurer = csSecurer;
        _dbInitializer = dbInitializer;
        _dbSettings = dbSettings.Value;
        _displayIndexPrefixService = displayIndexPrefixService;
        _tenantDbContext = tenantDbContext;
        _settings = option.Value;
    }

    public async Task<List<TenantDto>> GetAllAsync()
    {

        var tenants = (await _tenantStore.GetAllAsync()).Adapt<List<TenantDto>>();
        tenants.ForEach(t => t.ConnectionString = _csSecurer.MakeSecure(t.ConnectionString));
        return tenants;
    }

    public async Task<bool> ExistsWithIdAsync(string id) =>
        await _tenantStore.TryGetAsync(id) is not null;
    public async Task<bool> IsTenantAleadyExitsAsync(string id, CancellationToken cancellationToken ) =>
       (await _tenantStore.GetAllAsync()).Any(t => !string.IsNullOrWhiteSpace(id) && t.Id.Trim().ToLower()==id.Trim().ToLower()) ;

    public async Task<bool> ExistsWithNameAsync(string name) =>
        (await _tenantStore.GetAllAsync()).Any(t => t.Name == name);

    public async Task<TenantDto> GetByIdAsync(string id) =>
        (await GetTenantInfoAsync(id))
            .Adapt<TenantDto>();

    public async Task<string> CreateAsync(CreateTenantRequest request, CancellationToken cancellationToken)
    {
        var prefix = _displayIndexPrefixService.GenerateUniquePrefix(request.Id);
        if (request.ConnectionString?.Trim() == _dbSettings.ConnectionString.Trim()) request.ConnectionString = string.Empty;

        var tenant = new LrbTenantInfo(request.Id, request.Name, request.ConnectionString, request.AdminEmail, prefix, request.Issuer, request.ValidUpto, request.ReadReplicaConnectionString);
        var storeResult = await _tenantStore.TryAddAsync(tenant);

        if (storeResult)
        {
            try
            {

                _tenantDbContext.Set<LrbTenantInfo>().Add(tenant);
                await _tenantDbContext.SaveChangesAsync(cancellationToken);
                // Adding the tenant to the Memory Cache
                try
                {
                    if (_tenantStore is InMemoryStore<LrbTenantInfo> inMemoryStore)
                    {
                        await inMemoryStore.TryAddAsync(tenant);
                    }
                }
                catch(Exception ex)
                {

                }

                await _dbInitializer.InitializeApplicationDbForTenantAsync(tenant, cancellationToken);
                await _dbInitializer.SeedDatabaseAsync(tenant, cancellationToken);
            }
            catch (Exception)
            {

                await _tenantStore.TryRemoveAsync(request.Id);
                throw;
            }

            return $"Tenant {tenant.Id} is Created.";
        }
        else
        {
            return "Tenant creation failed. Duplicate or invalid tenant information.";
        }

        /*await _tenantStore.TryAddAsync(tenant);

        // TODO: run this in a hangfire job? will then have to send mail when it's ready or not
        try
        {
            await _dbInitializer.InitializeApplicationDbForTenantAsync(tenant, cancellationToken);
        }
        catch
        {
            await _tenantStore.TryRemoveAsync(request.Id);
            throw;
        }
        return String.Format("Tenant {0} is Created.", tenant.Id);*/
    }

    public async Task<string> SeedingDatabase(TenantDto request, CancellationToken cancellationToken)
    {
        try
        {
            var prefix = _displayIndexPrefixService.GenerateUniquePrefix(request.Id);
            if (request.ConnectionString?.Trim() == _dbSettings.ConnectionString.Trim()) request.ConnectionString = string.Empty;

            var tenant = new LrbTenantInfo(request.Id, request.Name, request.ConnectionString, request.AdminEmail, prefix, request.Issuer, request.ValidUpto, request.ReadReplicaConnectionString);
            await _dbInitializer.SeedDatabaseAsync(tenant, cancellationToken);
            return "Seeding Successfull";
        }
        catch (Exception ex) 
        { 
            return ex.Message; 
        }
        
    }

    public async Task<string> ActivateAsync(string id)
    {
        var tenant = await GetTenantInfoAsync(id);

        if (tenant.IsActive)
        {
            throw new ConflictException("Tenant is already Activated.");
        }

        tenant.Activate();

        await _tenantStore.TryUpdateAsync(tenant);
        try
        {
            _tenantDbContext.Set<LrbTenantInfo>().Update(tenant);
            await _tenantDbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {


            throw new Exception($"Tenant {tenant} could not Active");
        }
        return String.Format("Tenant {0} is now Activated.", id);
    }

    public async Task<string> DeactivateAsync(string id)
    {
        var tenant = await GetTenantInfoAsync(id);

        if (!tenant.IsActive)
        {
            throw new ConflictException("Tenant is already Deactivated.");
        }

        tenant.Deactivate();
        await _tenantStore.TryUpdateAsync(tenant);
        try
        {
            _tenantDbContext.Set<LrbTenantInfo>().Update(tenant);
            await _tenantDbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {


            throw new Exception($"Tenant {tenant} could not Deactive");
        }
        return String.Format("Tenant {0} is now Deactivated.", id);
    }

    public async Task<string> UpdateSubscription(string id, DateTime extendedExpiryDate)
    {
        var tenant = await GetTenantInfoAsync(id);

        tenant.SetValidity(extendedExpiryDate);

        await _tenantStore.TryUpdateAsync(tenant);
        try
        {
            _tenantDbContext.Set<LrbTenantInfo>().Update(tenant);
            await _tenantDbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {


            throw new Exception($"Could not update the subscription \n{e.Message}");
        }
        return string.Format("Tenant {0}'s Subscription Upgraded. Now Valid till {1}.", id, tenant.ValidUpto);
    }

    private async Task<LrbTenantInfo> GetTenantInfoAsync(string id) =>
        await _tenantStore.TryGetAsync(id.ToLowerInvariant())
            ?? throw new NotFoundException(string.Format("{0} {1} Not Found.", typeof(LrbTenantInfo).Name, id));

    public async Task<bool> GetAllTenantsFromTenantDBAsync()
    {
        try
        {

            var Tenants = await _tenantDbContext.TenantInfo.ToListAsync();
            if (Tenants != null && _tenantStore is InMemoryStore<LrbTenantInfo> inMemoryStore)
            {
                foreach (var tenant in Tenants)
                {
                    var tenantInfo = await inMemoryStore.TryGetAsync(tenant.Id);
                    if (tenantInfo != null)
                    {
                        await inMemoryStore.TryUpdateAsync(tenant);
                    }
                    else
                    {
                        await inMemoryStore.TryAddAsync(tenant);
                    }
                }

                return true;

            }
            else
            {
                return false;
            }
        }
        catch (Exception ex)
        {
            throw ex.InnerException;
        }
    }

    public async Task<TenantDto> GetTenantCachebyIdAsync(string tenantId)
    {
        TenantDto tenantInfo = new();
        if(!string.IsNullOrEmpty(tenantId) && _tenantStore is InMemoryStore<LrbTenantInfo> inMemoryStore)
        {
            var result = await inMemoryStore.TryGetAsync(tenantId);
            if(result != null)
            {
                tenantInfo = result.Adapt<TenantDto>();
            }
        }
        return tenantInfo;
    }

    public async Task<List<TenantDto>> GetAllTenantsCacheAsync()
    {
        List<TenantDto> tenantsInfo = new List<TenantDto>();
        var Tenants = await _tenantDbContext.TenantInfo.ToListAsync();
        if (Tenants != null && _tenantStore is InMemoryStore<LrbTenantInfo> inMemoryStore)
        {
            foreach (var tenant in Tenants)
            {
                var tenantInfo = await inMemoryStore.TryGetAsync(tenant.Id);
                if(tenantInfo != null)
                {
                    tenantsInfo.Add(tenantInfo.Adapt<TenantDto>());
                }                
            }
        }
        return tenantsInfo;
    }
    public async Task<bool> UpdateTenantCacheByIdAsync(string tenantId)
    {
        bool result = false;
        if(!string.IsNullOrEmpty(tenantId) && _tenantStore is InMemoryStore<LrbTenantInfo> inMemoryStore)
        {
            var tenant = _tenantDbContext.TenantInfo.ToList().Where(i => i.Id == tenantId).FirstOrDefault();
            if(tenant != null)
            {
                result = await inMemoryStore.TryUpdateAsync(tenant);
            }
        }
        return result;
    }
}
