using Lrb.Domain.Enums;

namespace Lrb.Application.CustomForm.Mobile.Dtos
{
    public class CreateCustomFormDto : BaseCustomFormDto
    {
    }

    public class UpdateCustomFormDto : BaseCustomFormDto
    {
        public Guid Id { get; set; }
    }

    public class ViewCustomFormDto : BaseCustomFormDto
    {
        public Guid Id { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
    }

    public class BaseCustomFormDto : IDto
    {
        public string Name { get; set; } = default!;
        public string? DisplayName { get; set; }
        public QRFormType FieldType { get; set; }
        public string Module { get; set; } = default!;
        public int OrderRank { get; set; }
        public bool IsRequired { get; set; }
        public string? Notes { get; set; }
        public Guid EntityId { get; set; }
        public string? EntityName { get; set; }
    }
}
