﻿using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Utils;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Xml.Linq;

namespace Lrb.Domain.Entities
{
    public class Lead : UserLevelAuditableEntity, IAggregateRoot, IPickedDate
    {
        public readonly IServiceProvider HttpContext;
        private string? name;
        private string? alternateContactNo;
        private string? leadNumber;

        [Required]
        public string Name { get => string.IsNullOrEmpty(name) ? "Unknown" : name; set => name = value; }
        [Required]
        public string ContactNo { get; set; } = default!;
        public string? AlternateContactNo { get => alternateContactNo; set => alternateContactNo = value.ValidateContactNumber(); }
        public string? LandLine { get; set; }
        public string? Email { get; set; }
        public string? Notes { get; set; }
        public string? ConfidentialNotes { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public DateTime? RevertDate { get; set; }
        public string? ChosenProject { get; set; }
        public string? ChosenProperty { get; set; }
        public string? BookedUnderName { get; set; }
        public string? LeadNumber { get => leadNumber; set => leadNumber = value.ExtractKeyboardCharacters(); }
        public Guid AssignTo { get; set; }
        public int ShareCount { get; set; }
        public string? SoldPrice { get; set; }
        public string? Rating { get; set; }
        [Column(TypeName = "jsonb")]
        public Dictionary<DateTime, string>? CallRecordingUrls { get; set; }
        [Column(TypeName = "jsonb")]
        public List<LeadDocument>? Documents { get; set; }
        //table references
        public LeadTag? TagInfo { get; set; }

        //[Obsolete(message: "Use Custom Status instead.")]
        // public MasterLeadStatus? Status { get; set; }
        public DefaultIdType AccountId { get; set; }
        public IList<LeadEnquiry> Enquiries { get; set; } = default!;
        public IList<TempProjects>? TempProjects { get; set; }
        public IList<Property>? Properties { get; set; }
        public DateTime? PostponedDate { get; set; }
        public long? UnmatchedBudget { get; set; }
        public string? PurchasedFrom { get; set; }
        public string? PreferredLocation { get; set; }
        public Guid? AssignedFrom { get; set; }

        [Column(TypeName = "jsonb")]
        public Dictionary<ContactType, int>? ContactRecords { get; set; }
        public IList<LeadCommunication>? Communications { get; set; }

        public bool IsArchived { get; set; }
        public Guid? ArchivedBy { get; set; }
        public DateTime? ArchivedOn { get; set; }
        public Guid? RestoredBy { get; set; }
        public DateTime? RestoredOn { get; set; }
        public bool IsMeetingDone { get; set; }
        public Guid? MeetingLocation { get; set; }
        public bool IsSiteVisitDone { get; set; }
        public Guid? SiteLocation { get; set; }
        public IList<LeadCallLog>? LeadCallLogs { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }

        public string? AgencyName { get; set; }
        public string? CompanyName { get; set; }
        public IList<LeadAppointment>? Appointments { get; set; }
        public string? SerialNumber { get; set; }
        public string? DuplicateLeadVersion { get; set; }
        public int ChildLeadsCount { get; set; }
        public Guid? ParentLeadId { get; set; }
        [NotMapped]
        public bool ShouldUpdatePickedDate { get; set; }
        public Guid? RootId { get; set; }
        public DateTime? CreatedOnPortal { get; set; }
        public Guid? ClosingManager { get; set; }
        public Guid? SourcingManager { get; set; }
        public Profession Profession { get; set; }
        public Address? Address { get; set; }
        public string? ChannelPartnerName { get; set; }
        public string? ChannelPartnerExecutiveName { get; set; }
        public string? ChannelPartnerContactNo { get; set; }
        public IList<ChannelPartner>? ChannelPartners { get; set; }
        public CustomMasterLeadStatus? CustomLeadStatus { get; set; }
        public Guid? CustomLeadStatusId { get; set; }
        public string? Designation { get; set; }
        public DateTime? PickedDate { get; set; }
        public bool IsPicked { get; set; }
        public DateTime? BookedDate { get; set; }
        public Guid? BookedBy { get; set; }
        public Guid? SecondaryUserId { get; set; }
        public IList<LeadBookedDetail>? BookedDetails { get; set; }
        public UploadType UploadType { get; set; }
        public string? UploadTypeName { get; set; }
        public IList<CustomFlag>? CustomFlags { get; set; }
        public IList<Project>? Projects { get; set; }
        public string? SoldPriceCurrency { get; set; }
        public string? ApiKey { get; set; }
        public IList<Agency>? Agencies { get; set; }
        public string? CountryCode { get; set; }
        public string? AltCountryCode { get; set; }

        public DateTime? AssignDate { get; set; }
        public BulkType? BulkCategory { get; set; }
        public IList<LeadAssignment>? Assignments { get; set; }
        public Guid? SecondaryFromUserId { get; set; }
        public IList<Campaign>? Campaigns { get; set; }
        [Column(TypeName = "jsonb")]
        public List<Link>? Links { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<string, string>? AdditionalProperties { get; set; }
        public string? ReferralEmail { get; set; }
        public bool? IsConvertedFromData { get; set; }
        public Guid? QualifiedBy {  get; set; }
        public string? Nationality { get; set; }

        public Lead CreateDeepCopy()
        {
            var person = (Lead)MemberwiseClone();
            return person;
        }
        public string? MetaLeadId {  get; set; }
        public string? PixelId {  get; set; }
        public Guid? OriginalOwner { get; set; }
        public PossesionType? PossesionType { get; set; }
        public Gender? Gender { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public MaritalStatusType? MaritalStatus { get; set; }
        public DateTime? AppointmentDoneOn { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<string, string>? GoogleAdsProperties { get; set; }
    }
}
