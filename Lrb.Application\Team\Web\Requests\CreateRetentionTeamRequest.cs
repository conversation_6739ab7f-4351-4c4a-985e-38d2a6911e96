﻿using Lrb.Application.CustomStatus.Web;
using Lrb.Domain.Entities.MasterData;
using Lrb.Application.Team.Web.Dtos;

namespace Lrb.Application.Team.Web.Requests
{
    public class CreateRetentionTeamRequest : CreateRetentionTeamsDto, IRequest<Response<Guid>>
    {

    }

    public class CreateRetentionTeamRequestHandler : IRequestHandler<CreateRetentionTeamRequest, Response<Guid>>
    {

        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Team> _teamRepo;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _custLeadStatusRepo;
        private readonly IRepositoryWithEvents<TeamConfiguration> _teamConfigRepo;
        public CreateRetentionTeamRequestHandler(
            IRepositoryWithEvents<CustomMasterLeadStatus> custLeadStatusRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Team> teamRepo,
            IRepositoryWithEvents<TeamConfiguration> teamConfigRepo)
        {
            _teamRepo = teamRepo;
            _custLeadStatusRepo = custLeadStatusRepo;
            _teamConfigRepo = teamConfigRepo;
        }
        public async Task<Response<Guid>> Handle(CreateRetentionTeamRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var team = await _teamRepo.FirstOrDefaultAsync(new TeamByIdSpec(request.Id ?? Guid.Empty));
                if (team == null)
                {
                    throw new InvalidOperationException("Team not found by this Id");
                }
                List<CustomMasterLeadStatus> customMasterLeadStatuses = new();
                if (request.StatusesIds?.Any() ?? false)
                {
                    var statuses = await _custLeadStatusRepo.ListAsync(new GetAllStatusByIdSpec(request.StatusesIds ?? new List<Guid>()), cancellationToken);
                    if (statuses?.Any() ?? false)
                    {
                        customMasterLeadStatuses.AddRange(statuses);
                    }
                }
                if (request.SubStatusesIds?.Any() ?? false)
                {
                    var subStatuses = await _custLeadStatusRepo.ListAsync(new GetAllStatusByIdSpec(request.SubStatusesIds ?? new List<Guid>()), cancellationToken);
                    if (subStatuses?.Any() ?? false)
                    {
                        customMasterLeadStatuses.AddRange(subStatuses);
                    }
                }
                team.Statuses = customMasterLeadStatuses;
                team.IsRotationEnabled = request.IsRotationEnabled;
                await _teamRepo.UpdateAsync(team);
                if (request.Configuration != null)
                {
                    var teamConfig = request.Configuration.Adapt<TeamConfiguration>();
                        teamConfig.TeamId = team.Id;
                        teamConfig.IsForRetention = true;
                        teamConfig = await _teamConfigRepo.AddAsync(teamConfig);
                      
                    return new(teamConfig.Id);
                }
                return new(Guid.Empty);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
