﻿using Lrb.Domain.Entities.ErrorModule;
using Newtonsoft.Json;

namespace Lrb.Application.UserDetails.Mobile.Request
{
    public class UpdateGeofenceActiveStatusRequest : IRequest<Response<bool>>
    {
        public Guid UserId { get; set; }
        public bool? IsGeoFenceActive { get; set; }
    }

    public class UpdateGeofenceActiveStatusRequestHandler : IRequestHandler<UpdateGeofenceActiveStatusRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;

        public UpdateGeofenceActiveStatusRequestHandler(IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,ILeadRepositoryAsync leadRepositoryAsync)
        {
            _userDetailsRepo = userDetailsRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
        }

        public async Task<Response<bool>> Handle(UpdateGeofenceActiveStatusRequest request, CancellationToken cancellationToken)
        {
            try
            {
                // Get the user details
                var userDetails = await _userDetailsRepo.FirstOrDefaultAsync(new GetUserDetailsByIdSpec(request.UserId), cancellationToken);

                if (userDetails == null)
                {
                    return new Response<bool>(false, "User details not found");
                }

                // Update the IsGeoFenceActive property if provided
                if (request.IsGeoFenceActive.HasValue)
                {
                    userDetails.IsGeoFenceActive = request.IsGeoFenceActive.Value;
                }

                // Save the changes
                await _userDetailsRepo.UpdateAsync(userDetails, cancellationToken);

                return new Response<bool>(true, $"Geo-fencing status has been updated successfully");
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "UpdateGeofenceActiveStatusRequestHandler -> Handle() "
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return new Response<bool>(false, "An error occurred while updating geo-fencing status");
            }
        }
    }
}
