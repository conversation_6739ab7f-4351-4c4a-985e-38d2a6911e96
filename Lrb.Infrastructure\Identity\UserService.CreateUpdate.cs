﻿using DocumentFormat.OpenXml.Wordprocessing;
using Lrb.Application.Common.Exceptions;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Identity.Cognito;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.OrgProfile.Web.Dtos;
using Lrb.Application.OrgProfile.Web.Mappings;
using Lrb.Application.Subscription.Web.Requests;
using Lrb.Application.Utils;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Lrb.Domain.Identity;
using Lrb.Shared.Authorization;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Graph;
using Microsoft.Identity.Web;
using Newtonsoft.Json;
using System.Security.Claims;
using System.Security.Policy;
using System.Text;

namespace Lrb.Infrastructure.Identity;

internal partial class UserService
{
    public UserService()
    {
    }

    /// <summary>
    /// This is used when authenticating with AzureAd.
    /// The local user is retrieved using the objectidentifier claim present in the ClaimsPrincipal.
    /// If no such claim is found, an InternalServerException is thrown.
    /// If no user is found with that ObjectId, a new one is created and populated with the values from the ClaimsPrincipal.
    /// If a role claim is present in the principal, and the user is not yet in that roll, then the user is added to that role.
    /// </summary>
    public async Task<string> GetOrCreateFromPrincipalAsync(ClaimsPrincipal principal)
    {
        string? objectId = principal.GetObjectId();
        if (string.IsNullOrWhiteSpace(objectId))
        {
            throw new InternalServerException("Invalid objectId", null, ErrorActionCode.NoOp);
        }

        var user = await _userManager.Users.Where(u => u.ObjectId == objectId).FirstOrDefaultAsync()
            ?? await CreateOrUpdateFromPrincipalAsync(principal);

        if (principal.FindFirstValue(ClaimTypes.Role) is string role &&
            await _roleManager.RoleExistsAsync(role) &&
            !await _userManager.IsInRoleAsync(user, role))
        {
            await _userManager.AddToRoleAsync(user, role);
        }

        return user.Id;
    }

    private async Task<ApplicationUser> CreateOrUpdateFromPrincipalAsync(ClaimsPrincipal principal)
    {
        string? email = principal.FindFirstValue(ClaimTypes.Upn);
        string? username = principal.GetDisplayName();
        if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(username))
        {
            throw new InternalServerException(string.Format("Username or Email not valid."), null, ErrorActionCode.NoOp);
        }

        var user = await _userManager.FindByNameAsync(username);
        if (user is not null && !string.IsNullOrWhiteSpace(user.ObjectId))
        {
            throw new InternalServerException(string.Format("Username {0} is already taken.", username), null, ErrorActionCode.NoOp);
        }

        if (user is null)
        {
            user = await _userManager.FindByEmailAsync(email);
            if (user is not null && !string.IsNullOrWhiteSpace(user.ObjectId))
            {
                throw new InternalServerException(string.Format("Email {0} is already taken.", email), null, ErrorActionCode.NoOp);
            }
        }

        IdentityResult? result;
        if (user is not null)
        {
            user.ObjectId = principal.GetObjectId();
            result = await _userManager.UpdateAsync(user);

            await _events.PublishAsync(new ApplicationUserUpdatedEvent(user.Id));
        }
        else
        {
            user = new ApplicationUser
            {
                ObjectId = principal.GetObjectId(),
                FirstName = principal.FindFirstValue(ClaimTypes.GivenName),
                LastName = principal.FindFirstValue(ClaimTypes.Surname),
                Email = email,
                NormalizedEmail = email.ToUpperInvariant(),
                UserName = username,
                NormalizedUserName = username.ToUpperInvariant(),
                EmailConfirmed = true,
                PhoneNumberConfirmed = true,
                IsActive = true
            };
            result = await _userManager.CreateAsync(user);

            await _events.PublishAsync(new ApplicationUserCreatedEvent(user.Id));
        }

        if (!result.Succeeded)
        {
            throw new InternalServerException("Validation Errors Occurred.", result.GetErrors(), ErrorActionCode.NoOp);
        }

        return user;
    }
    private async Task<CreateCognitoUserResponse> CreateCognitoUserAsync(CreateUserRequest request, string origin)
    {
        EnsureValidTenant();
        var cognitoRequest = request.Adapt<CreateCognitoUserRequest>();
        cognitoRequest.TenantId = _currentTenant.Id;
        return await _cognitoService.CreateUserAsync(cognitoRequest, CancellationToken.None);
    }
    public async Task<(string messages, string userId)> CreateAsync(CreateUserRequest request, string origin)
    {
        #region Subscription Threshold
        var tenantId = _currentTenant.Id;
        if (await _dapperRepository.CheckUserMoreThanLicenseBought(tenantId ?? string.Empty))
        {
            var admins = await _dapperRepository.GetAdminsNameAndNumberAsync(tenantId ?? string.Empty);
            var emailBody = await _dapperRepository.GetEmailTemplateBodyAsync();
            //var subscriptionDetails = await _dapperRepository.GetTenantSubsciptionInfoDetailsAsync(tenantId ?? string.Empty);
            
            var subscriptionDetails = await _mediator.Send(new GetSubscriptionInfoRequest() { TenantId = request.TenantId });

            await SubscriptionHelper.SendSubscriptionThresholdMails(tenantId ?? string.Empty, _currentTenant.Name ?? string.Empty, request.UserName, request.FirstName + " " + request.LastName, request.PhoneNumber, emailBody, admins, subscriptionDetails.Data, _graphEmailService);

            throw new InvalidOperationException("No. of active users excceded the licensed users limit");
        }

        #endregion

        //Create cognito user
        request.PasswordTimeStamp = DateTime.UtcNow;
        var cognitoResponse = await CreateCognitoUserAsync(request, origin);

        if (cognitoResponse is null)
        {
            throw new InternalServerException("Validation Errors Occurred In Cognito.", null, ErrorActionCode.NoOp);
        }
        var user = new ApplicationUser
        {
            Id = cognitoResponse.UUID,
            Email = request.Email,
            FirstName = request.FirstName,
            LastName = request.LastName,
            UserName = request.UserName,
            PhoneNumber = request.PhoneNumber,
            PasswordTimeStamp = request.PasswordTimeStamp,
            CreatedOn = DateTime.UtcNow,
            LastModifiedOn = DateTime.UtcNow,
            CreatedBy = _currentUser.GetUserId(),
            LastModifiedBy = _currentUser.GetUserId(),
            IsActive = true,
            IsMFAEnabled = request.IsMFAEnabled,
            LicenseNo=request.LicenseNo
        };

        var result = await _userManager.CreateAsync(user, request.Password);
        if (!result.Succeeded)
        {
            throw new InternalServerException("Validation Errors Occurred.", result.GetErrors(), ErrorActionCode.NoOp);
        }

        if (!(request.UserRoles?.Any() ?? false))
        {
            await _userManager.AddToRoleAsync(user, LrbRoles.Basic);
        }

        var messages = new List<string> { string.Format("User {0} Registered.", user.UserName) };

        if (_securitySettings.RequireConfirmedAccount && !string.IsNullOrEmpty(user.Email))
        {
            // send verification email
            string emailVerificationUri = await GetEmailVerificationUriAsync(user, origin);
            RegisterUserEmailModel eMailModel = new RegisterUserEmailModel()
            {
                Email = user.Email,
                UserName = user.UserName,
                Url = emailVerificationUri
            };
            try
            {
                await _graphEmailService.SendEmailWithoutCCAsync(LeadratEmails.NoReplyEmail,
               "Confirm Registration", $"Email: {user.Email},\n UserName: {user.UserName},\n Url: {emailVerificationUri} ",
               new List<string> { user.Email }, Microsoft.Graph.BodyType.Text);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "UserService -> CreateAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            messages.Add($"Please check {user.Email} to verify your account!");
        }

        await _events.PublishAsync(new ApplicationUserCreatedEvent(user.Id));

        return new(string.Join(Environment.NewLine, messages), user.Id);
    }
    public async Task<bool> UpdateImageAsync(string userId, string? imageUrl)
    {
        var user = await _userManager.FindByIdAsync(userId);
        _ = user ?? throw new NotFoundException("User Not Found.");
        user.ImageUrl = imageUrl ?? null;
        user.LastModifiedOn = DateTime.UtcNow;
        var result = await _userManager.UpdateAsync(user);
        await _signInManager.RefreshSignInAsync(user);
        await _events.PublishAsync(new ApplicationUserUpdatedEvent(user.Id));
        return result.Succeeded;
    }
    public async Task<bool> UpdateOTPAsync(string userId, string otp)
    {
        var user = await _userManager.FindByIdAsync(userId);
        _ = user ?? throw new NotFoundException("User Not Found.");
        user.OTP = otp ?? null;
        user.OTPUpdatedOn = user.LastModifiedOn = DateTime.UtcNow;
        var result = await _userManager.UpdateAsync(user);
        await _signInManager.RefreshSignInAsync(user);
        await _events.PublishAsync(new ApplicationUserUpdatedEvent(user.Id));
        return result.Succeeded;
    }
    public async Task UpdateAsync(UpdateUserRequest request, string userId)
    {
        var user = await _userManager.FindByIdAsync(userId);
        _ = user ?? throw new NotFoundException("User Not Found.");

        //Update attributs in cognito
        UpdateCognitoAttributesRequest cognitoRequest = request.Adapt<UpdateCognitoAttributesRequest>();
        cognitoRequest.UserName = user.UserName;
        await _cognitoService.UpdateUserAttributesAsync(cognitoRequest, CancellationToken.None);

        //string currentImage = user.ImageUrl ?? string.Empty;
        //if (request.Image != null || request.DeleteCurrentImage)
        //{
        //    user.ImageUrl = await _fileStorage.UploadAsync<ApplicationUser>(request.Image, FileType.Image);
        //    if (request.DeleteCurrentImage && !string.IsNullOrEmpty(currentImage))
        //    {
        //        string root = Directory.GetCurrentDirectory();
        //        _fileStorage.Remove(Path.Combine(root, currentImage));
        //    }
        //}
        user.ImageUrl = request?.ImageUrl ?? null;
        user.LastModifiedOn = DateTime.UtcNow;
        user.LastModifiedBy = _currentUser.GetUserId();
        user.FirstName = request.FirstName;
        user.LastName = request.LastName;
        user.PhoneNumber = request.PhoneNumber;
        user.Email = request.Email;
        user.LicenseNo = request.LicenseNo;
        string phoneNumber = await _userManager.GetPhoneNumberAsync(user);
        if (request.PhoneNumber != phoneNumber)
        {
            await _userManager.SetPhoneNumberAsync(user, request.PhoneNumber);
        }

        var result = await _userManager.UpdateAsync(user);

        await _signInManager.RefreshSignInAsync(user);

        await _events.PublishAsync(new ApplicationUserUpdatedEvent(user.Id));

        if (!result.Succeeded)
        {
            throw new InternalServerException("Update profile failed", result.GetErrors());
        }
    }
    public async Task UpdateUserAsync(string userId)
    {
        var user = await _userManager.FindByIdAsync(userId);
        _ = user ?? throw new NotFoundException("User Not Found.");

        user.LastModifiedOn = DateTime.UtcNow;
        user.LastModifiedBy = _currentUser.GetUserId();
        var result = await _userManager.UpdateAsync(user);
    }
}
