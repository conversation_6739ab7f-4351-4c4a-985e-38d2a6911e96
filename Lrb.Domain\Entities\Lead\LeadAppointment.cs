﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace Lrb.Domain.Entities
{
    public class LeadAppointment : AuditableEntity, IAggregateRoot
    {
        public AppointmentType Type { get; set; }
        public bool IsDone { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public Guid? LocationId { get; set; }
        public Address? Location { get; set; }
        public string? ProjectName { get; set; }
        public string? ExecutiveName { get; set; }
        public string? ExecutiveContactNo { get; set; }
        public string? Image { get; set; }
        [Column(TypeName = "jsonb")]
        public List<string>? Images { get; set; }
        [Column(TypeName = "jsonb")]
        public List<LeadDocument>? ImagesWithName { get; set; }
        public bool? IsManual { get; set; }
        public string? Notes { get; set; }
        public Guid LeadId { get; set; }
        [JsonIgnore]
        public Lead Lead { get; set; }
        public Guid UserId { get; set; }
        public bool IsFullyCompleted { get; set; }
        public Guid? UniqueKey { get; set; }
        public string? PropertyName { get; set; }
        public DateTime? AppointmentDoneOn { get; set; }

    }
}
