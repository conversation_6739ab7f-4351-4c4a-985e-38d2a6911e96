﻿using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.LeadRotation.Web.Specs;
using Lrb.Application.Team.Web;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Lead.Web.Requests.AssignmentRequests
{
    public class LeadStatusBiasAutoAssignmentWithNewHistoryRequest : IRequest<Response<bool>>
    {
        public List<Guid>? LeadIds { get; set; }
    }

    public class LeadStatusBiasAutoAssignmentWithNewHistoryRequestHandler : LeadCommonRequestHandler, IRequestHandler<LeadStatusBiasAutoAssignmentWithNewHistoryRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Team> _teamRepo;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _leadStatusRepo;
        private readonly IRepositoryWithEvents<LeadRotationTracker> _leadRotationTrackerRepo;

        public LeadStatusBiasAutoAssignmentWithNewHistoryRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Team> teamRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> leadStatusRepo,
            IRepositoryWithEvents<LeadRotationTracker> leadRotationTrackerRepo,
            IServiceProvider serviceProvider) : base(serviceProvider, typeof(LeadStatusBiasAutoAssignmentWithNewHistoryRequestHandler).Name, "Handler")
        {
            _teamRepo = teamRepo;
            _leadStatusRepo = leadStatusRepo;
            _leadRotationTrackerRepo = leadRotationTrackerRepo;
        }

        public async Task<Response<bool>> Handle(LeadStatusBiasAutoAssignmentWithNewHistoryRequest request, CancellationToken cancellationToken)
        {
            Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var leads = await _leadRepo.ListAsync(new GetLeadForAutoAssignmentByLeadIdsSpecs(request?.LeadIds ?? new()), cancellationToken);
            if (leads?.Any() ?? false)
            {
                var i = 0;
                bool isLeadAssigned = true;
                foreach (var lead in leads)
                {
                    var team = await _teamRepo.FirstOrDefaultAsync(new GetTeamByLeadStatusId(lead?.CustomLeadStatus?.Id ?? Guid.Empty), cancellationToken);
                    if (team != null && lead != null)
                    {
                        var rotationtracker = await _leadRotationTrackerRepo.ListAsync(new GetLeadRotationTrackerSpecs(lead.Id), cancellationToken);
                        LeadRotationTracker? tracker = null;
                        if (rotationtracker.Count == 1)
                        {
                            tracker = rotationtracker.FirstOrDefault();
                        }
                        else
                        {
                            tracker = rotationtracker.Where(i => i.AssignedUsers != null).FirstOrDefault();
                        }
                        var userIds = team.UserIds?.Select(i => i.ToString()).ToList() ?? new();
                        var assignedUserIds = tracker?.AssignedUsers?.Select(i => i.Value).ToList();
                        if (assignedUserIds?.Any() ?? false)
                        {
                            var assignedIds = assignedUserIds.Select(i => i.ToString()).ToList();
                            userIds = userIds?.Except(assignedIds).ToList();
                        }
                        var users = await _userService.GetListOfUsersByIdsAsync(userIds ?? new(), cancellationToken);
                        var activeUsers = (users.Where(i => i.IsActive)).ToList();


                        if (tracker != null && tracker.RotationCount < tracker.NoOfRotation)
                        {
                            while (isLeadAssigned)
                            {
                                var user = activeUsers[i];
                                var assignedLead = await _leadRepo.FirstOrDefaultAsync(new GetLeadByAssignToSpecs(user.Id, lead.ContactNo[^10..]), cancellationToken);
                                if (assignedLead != null)
                                {
                                    i++;
                                    continue;
                                }
                                else
                                {
                                    lead.AssignedFrom = lead.AssignTo;
                                    lead.AssignTo = user.Id;

                                    await SetReassignedLeadDetailsAsync(lead, cancellationToken);

                                    await _leadRepo.UpdateAsync(lead); 

                                    await UpdateReassignedLeadHistoryAsync(lead, LeadAssignmentType.WithoutHistoryWithNewStatus, activeUsers, cancellationToken, null, null); 

                                    await SendLeadAssignmentNotificationsAsync(lead, 1, globalSettings, cancellationToken);

                                    #region Update Lead Rotation Tracker

                                    tracker.RotationCount++;
                                    if (tracker.AssignedUsers != null)
                                    {
                                        tracker.AssignedUsers.Add(DateTime.UtcNow, lead.AssignTo);
                                    }
                                    else
                                    {
                                        tracker.AssignedUsers = new Dictionary<DateTime, Guid>() { { DateTime.UtcNow, lead.AssignTo } };
                                    }
                                    await _leadRotationTrackerRepo.UpdateAsync(tracker);

                                    #endregion

                                    break;
                                }
                            }
                        }
                        i++;
                        if (i == activeUsers.Count)
                            i = 0;
                    }
                }
                return new(true);
            }
            return new(false);
        }


        private async Task SetReassignedLeadDetailsAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken)
        {
            await InitializeLeadStatusAsync(lead, cancellationToken);
            lead.ScheduledDate = null;
            if (lead.CustomFlags != null)
            {
                lead.CustomFlags = null;
            }
        }
    }
}
