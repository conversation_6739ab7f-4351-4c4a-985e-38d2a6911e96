﻿using Lrb.Application.CustomStatus.Web;
using Lrb.Application.CustomStatus.Web.Request;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class StatusController : VersionedApiController
    {
        [HttpGet]
        [TenantIdHeader]
        [OpenApiOperation("Get all status.", "")]
        public async Task<PagedResponse<ViewCustomStatusDto, string>> GetAsync([FromQuery] GetAllStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        [AllowAnonymous]
        [HttpGet("GetAllStatusAnonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get all status.", "")]
        public async Task<PagedResponse<StatusNamesDto, string>> GetAllStatusAnonymous([FromQuery] GetAllStatusAnonymousRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut]
        [TenantIdHeader]
        [OpenApiOperation("Update a status.", "")]
        public async Task<Response<bool>> UpdateAsync(UpdateStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Get status.", "")]
        public async Task<Response<ViewCustomStatusDto>> GetAsync(Guid id)
        {
            return await Mediator.Send(new GetStatusByIdRequest(id));
        }
        [HttpDelete("{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Delete a status.", "")]
        public async Task<Response<bool>> DeleteAsync(Guid id)
        {
            return await Mediator.Send(new DeleteStatusRequest(id));
        }
        [HttpPost]
        [TenantIdHeader]
        [OpenApiOperation("Create a new status.", "")]
        public async Task<Response<Guid>> CreateAsync(CreateStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("leads/count")]
        [TenantIdHeader]
        [OpenApiOperation("Get all status with lead count.", "")]
        public async Task<PagedResponse<ViewCustomStatusDto,string>> GetStatusWithLeadsCountAsync()
        {
            return await Mediator.Send( new GetLeadsCountAccordingToStatusRequest());
        }
    }
}
