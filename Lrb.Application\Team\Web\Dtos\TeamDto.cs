﻿
using Lrb.Application.Team.Web.Dtos;

namespace Lrb.Application.Team.Web
{
    public class TeamDto : IDto
    {
        public Guid Id { get; set; }
        public string? TeamName { get; set; }
        public int TotalMembers { get; set; }
        public int LeadsAssigned { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public List<TeamUserDto>? Users { get; set; }
        public string? ImageUrl {  get; set; }
        public TeamUserDto? Manager { get; set; }
        public List<TeamConfigurationDto>? Configurations { get; set; }
    }
}
