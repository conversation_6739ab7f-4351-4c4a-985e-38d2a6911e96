﻿namespace Lrb.Domain.Entities
{
    public class Subscription : AuditableEntity, IAggregateRoot
    {
        public string? Name { get; set; }
        public string? TenantName { get; set; }
        public DateTime LicenseValidity { get; set; }
        public double SoldLicense { get; set; }
        public double NetAmount { get; set; }
        public double PaidAmount { get; set; }
        public double DueAmount { get; set; }
        public DateTime? DueDate { get; set; }
        public double TotalAmount { get; set; }
        public double GSTAmount { get; set; }
        public BillingType BillingType { get; set; }
        public DateTime? PaymentDate { get; set; }
        public string? GSTNumber { get; set; }
        public int SoldLicenses { get; set; }
        public bool IsActive { get; set; }
        public bool IsExpired { get; set; }
        public DateTime? ExpiredOn { get; set; }
        public IList<Payment>? Payments { get; set; }
        public IList<SubscriptionAddOn>? SubscriptionAddOns { get; set; }
        public string? Currency { get; set; }

    }
}
