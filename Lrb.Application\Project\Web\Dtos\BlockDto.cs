﻿namespace Lrb.Application.Project.Web
{

    public class ViewBlockDto : BaseBlockDto
    {
    }
    public class CreateBlockDto : BaseBlockDto
    {
        public Guid ProjectId { get; set; }
    }
    public class UpdateBlockDto : BaseBlockDto
    {

    }
    public class BaseBlockDto : IDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public double? Area { get; set; }
        public Guid? AreaUnitId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? PossessionDate { get; set; }
        public PossesionType PossesionType { get; set; }
        public int NumberOfFloors { get; set; }
        public List<FormFieldValueDto>? CustomFields { get; set; }

    }
}
