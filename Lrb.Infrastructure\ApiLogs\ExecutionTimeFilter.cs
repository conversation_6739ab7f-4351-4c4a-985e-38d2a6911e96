﻿using Lrb.Application.Common.Persistence;
using Lrb.Shared.Authorization;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json;
using Npgsql;
using System.Diagnostics;

namespace Lrb.Infrastructure.ApiResposeTimeInfo
{
    public class ExecutionTimeFilter : IExecutionTimeFilter
    {
        private Stopwatch _stopWatch;
        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            _stopWatch = new Stopwatch();
            _stopWatch.Start();
            var resultContext = await next();
            _stopWatch.Stop();
            if (resultContext != null)
            {
                double elapsed = _stopWatch.Elapsed.TotalMilliseconds;
                var exception = resultContext.Exception;
                string endpoint = context.ActionDescriptor.DisplayName ?? string.Empty;
                string tenantId = context.HttpContext.Items[ContextKeys.TenantId]?.ToString() ?? string.Empty;
                Guid? userId = Guid.Parse(context.HttpContext.Items[ContextKeys.UserIdGuid]?.ToString() ?? Guid.Empty.ToString());
                string userName = $"{context.HttpContext.Items[ContextKeys.FirstName]?.ToString()} {context.HttpContext.Items[ContextKeys.LastName]?.ToString()}";
                if (elapsed > 800)
                {
                    //var queryParameters = context.HttpContext.Request.Query
                    //    .Select(q => new { q.Key, Value = q.Value.ToString() })
                    //    .ToDictionary(q => q.Key, q => q.Value);
                    //var settings = context.HttpContext.RequestServices.GetService(typeof(IOptions<DatabaseSettings>)) as IOptions<DatabaseSettings>;
                    //string? tenantConnection = null;
                    //try
                    //{
                    //    var tenantInfo = context.HttpContext.RequestServices.GetService(typeof(IMultiTenantContextAccessor<LrbTenantInfo>)) as IMultiTenantContextAccessor<LrbTenantInfo>;
                    //    tenantConnection = tenantInfo?.MultiTenantContext?.TenantInfo?.ConnectionString ?? string.Empty;
                    //}
                    //catch(Exception ex) { }
                    //var connectionString = string.IsNullOrEmpty(tenantConnection) ?  settings?.Value.ConnectionString ?? string.Empty : tenantConnection;
                    //int statusCode = context.HttpContext.Response.StatusCode;
                    //try
                    //{
                    //    //StoreApiLogsAsync(endpoint, userName, queryParameters, context.HttpContext.Request.Method, elapsed, context,
                    //       //connectionString, tenantId, userId, statusCode, exception);
                    //}
                    //catch(Exception ex)
                    //{
                    //    Console.WriteLine($"{JsonConvert.SerializeObject(ex)}");
                    //}
                }
            }
        }
        public async void StoreApiLogsAsync(string? endpoint, string? userName, Dictionary<string, string>? queryParameters,
            string? methodName, double elapsed, ActionExecutingContext context, string connectionString, string tenantId, Guid? userId, int statusCode, Exception? exception)
        {
            var conn = new NpgsqlConnection(connectionString);
            try
            {
                await conn.OpenAsync();
                var query = $"INSERT INTO \"ApiLogs\".\"ApiLog\"(\r\n\t\"Id\", \"EndPoint\", \"ResponseTime\", \"UserId\", \"UserName\", " +
                    $"\"LogMessage\", \"QueryParameters\", \"TenantId\", \"IsDeleted\", \"CreatedBy\", \"CreatedOn\", \"LastModifiedBy\"," +
                    $" \"LastModifiedOn\")\r\n\tVALUES (@id,'{endpoint}','{elapsed}',@userId,'{userName}'," +
                    $" '{exception?.Message}','{JsonConvert.SerializeObject(queryParameters)}','{tenantId}', '{false}',@userId, @dateTime,@userId,@dateTime);";
                var commamnd = new NpgsqlCommand(query, conn);
                commamnd.CommandType = System.Data.CommandType.Text;
                commamnd.Parameters.AddWithValue("id", Guid.NewGuid());
                commamnd.Parameters.AddWithValue("userId", userId);
                commamnd.Parameters.AddWithValue("@dateTime", DateTime.UtcNow);
                commamnd.ExecuteNonQuery();
                await conn.CloseAsync();
            }
            catch (Exception ex)
            {

            }
            finally { await conn.CloseAsync(); }
        }
    }
}


