﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Domain.Entities
{
    public class NotificationInfo: AuditableEntity, IAggregateRoot
    {
        public Guid TemplateId { get; set; } = default!;
        public List<Guid>? StatusIds { get; set; }
        public TemplateMode TemplateMode { get; set; }
        public bool IsLeadSpecific { get; set; }
        public bool IsUserSpecific { get; set; }
        [Column(TypeName = "jsonb")]
        public IList<int>? MinutesBefore { get; set; }
        public bool? IsAllLead { get; set; }
        public bool? IsAllUser { get; set; }
        public bool IsChannelPartnerSpecific {  get; set; }
        public List<Event>? Events { get; set; }
        public string? WhatsAppNotificationRecipients { get; set; }
    }
}
