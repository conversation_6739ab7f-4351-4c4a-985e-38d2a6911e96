﻿using Lrb.Application.Notifications.Dtos;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Application.PushNotification.Web.Dtos
{
    public class NotificationInfoDto : IDto
    {
        public Guid? Id { get; set; }
        public Guid TemplateId { get; set; }
        public IList<Guid>? StatusIds { get; set; }
        public TemplateMode TemplateMode { get; set; }
        public bool IsLeadSpecific { get; set; }
        public bool IsUserSpecific { get; set; }
        [Column(TypeName = "jsonb")]
        public IList<int>? MinutesBefore { get; set; }
        public bool? IsAllLead { get; set; }
        public bool? IsAllUser { get; set; }
        public List<Guid>? LeadIds { get; set; }
        public List<Guid>? UserIds { get; set; }
        public bool IsChannelPartnerSpecific { get; set; }
        public List<Event>? Events { get; set; }
        public string? WhatsAppNotificationRecipients { get; set; }
        public LrbTenantInfoDto? TenantInfoDto { get; set; }
    }
}
