﻿using Lrb.Application.DataCallLogs.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.Lead.Mobile;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Application.DataManagement.Mobile
{
    public class ProspectMapping
    {
        private static IRepository<MasterPropertyType> _masterPropertyTypeRepo = null;
        public static IRepository<MasterAreaUnit> _masterAreaUnitRepo = null;

        public static async void Configure(IServiceProvider provider)
        {
            _masterPropertyTypeRepo = (IRepository<MasterPropertyType>)provider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterPropertyType)));
            _masterAreaUnitRepo = (IRepository<MasterAreaUnit>)provider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterAreaUnit)));
            List<MasterPropertyType>? propertytypes = null;
            if (_masterPropertyTypeRepo != null)
            {
                propertytypes = _masterPropertyTypeRepo.ListAsync().Result;
            }
            List<MasterAreaUnit>? areaUnits = null;
            if (_masterAreaUnitRepo != null)
            {
                areaUnits = _masterAreaUnitRepo.ListAsync().Result;
            }
            List<CreateProspectEnquiryDto> prospectEnquiry = new();
            TypeAdapterConfig<Lrb.Domain.Entities.Prospect, ViewProspectDto>
                .NewConfig()
                .Map(dest => dest.Enquiry, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary) : null)
                .Map(dest => dest.AddressDto, src => src.Address)
                .Map(dest => dest.CallRecordingUrls, src => (src.CallRecordingUrls != null && src.CallRecordingUrls.Any()) ? src.CallRecordingUrls.GroupBy(i => i.Key.Year).ToDictionary(i => i.Key, i => i.GroupBy(i => i.Key.Month).ToDictionary(i => i.Key, i => i.ToList().ToDictionary(i => i.Key, i => i.Value))) : null)
                ;

            TypeAdapterConfig<ProspectEnquiry, ViewProspectEnquiryDto>
                .NewConfig()
                .Map(dest => dest.PropertyType, src => src.PropertyType != null ? new PropertyTypeDto
                {
                    Id = src.PropertyType.BaseId != null ? src.PropertyType.BaseId.Value : default,
                    BaseId = null,
                    DisplayName = src.PropertyType.BaseId != null ? propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName : default,
                    Type = src.PropertyType.BaseId != null ? propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type : default,
                    Level = 0,
                    ChildType = new()
                    {
                        Id = src.PropertyType.Id,
                        BaseId = src.PropertyType.BaseId != null ? src.PropertyType.BaseId : default,
                        DisplayName = src.PropertyType.DisplayName,
                        Level = src.PropertyType.Level,
                        Type = src.PropertyType.Type,
                        ChildType = null
                    }
                } : null)
                .Map(dest => dest.ProspectSource, src => src.Source)
                .Map(dest => dest.BhKType, src => src.BHKType)
                .Map(dest => dest.CarpetAreaUnit, src => src.CarpetAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.CarpetAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.CarpetAreaUnitId).Unit : default)
                .Map(dest => dest.BuiltUpAreaUnit, src => src.BuiltUpAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.BuiltUpAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.BuiltUpAreaUnitId).Unit : default)
                .Map(dest => dest.SaleableAreaUnit, src => src.SaleableAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.SaleableAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.SaleableAreaUnitId).Unit : default)
                .Map(dest => dest.PropertyAreaUnit, src => src.PropertyAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.PropertyAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.PropertyAreaUnitId).Unit : default)
                .Map(dest => dest.NetAreaUnit, src => src.NetAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.NetAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.NetAreaUnitId).Unit : default)
                .Map(dest => dest.PropertyTypes, src => src.PropertyTypes != null ? src.PropertyTypes.Select(pt => new PropertyTypeDto
                 {
                     Id = pt.BaseId.HasValue ? pt.BaseId.Value : default,
                     BaseId = null,
                     DisplayName = pt.BaseId.HasValue ? (propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value) != null ? propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value).DisplayName : default) : default,
                     Type = pt.BaseId.HasValue ? (propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value) != null ? propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value).Type : default) : default,
                     Level = 0,
                     ChildType = new PropertyTypeDto
                     {
                         Id = pt.Id,
                         BaseId = pt.BaseId,
                         DisplayName = pt.DisplayName,
                         Level = pt.Level,
                         Type = pt.Type,
                         ChildType = null
                     }
                }).ToList() : null);

            TypeAdapterConfig<IVRCommonCallLog, ProspectCallLogDto>
               .NewConfig()
               .Map(dest => dest.CallStartTime, src => src.StartStamp)
               .Map(dest => dest.CallEndTime, src => src.EndStamp)
               .Map(dest => dest.CallDuration, src => src.Duration)
               .Map(dest => dest.CallDirection, src => (((!string.IsNullOrWhiteSpace(src.Direction)) && src.Direction.Contains("inbound")) ? CallDirection.Incoming :
                                                       ((!string.IsNullOrWhiteSpace(src.Direction)) && src.Direction.Contains("outbound")) ? CallDirection.Outgoing :
                                                       CallDirection.None))
               .Map(dest => dest.UpdatedCallStatus, src => src.CallStatus)
               .Map(dest => dest.LastModifiedOn, src => src.LastModifiedOn)
               .Map(dest => dest.UpdatedCallDirection, src => src.Direction)
               .Map(dest => dest.CallRecordingUrl, src => !string.IsNullOrWhiteSpace(src.CallRecordingURL) ? src.CallRecordingURL : string.Empty)
               .Ignore(dest => dest.CallStatus);
        }
    }
}
