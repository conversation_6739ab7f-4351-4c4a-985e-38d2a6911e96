using Microsoft.AspNetCore.Builder;

namespace Lrb.Infrastructure.Extensions
{
    public static class SecurityHeadersExtensions
    {
        public static IApplicationBuilder UseCustomSecurityHeaders(this IApplicationBuilder app)
        {
            app.Use(async (context, next) =>
            {
                // Remove server information headers
                context.Response.Headers.Remove("Server");
                context.Response.Headers.Remove("X-Powered-By");

                // Add security headers
                context.Response.Headers["X-Content-Type-Options"] = "nosniff";
                context.Response.Headers["X-Frame-Options"] = "DENY"; // Denies all framing attempts
                context.Response.Headers["X-XSS-Protection"] = "1; mode=block";
                context.Response.Headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains";
                context.Response.Headers["Content-Security-Policy"] =
                    "default-src 'self'; " +
                    "script-src 'self' 'unsafe-inline' 'nonce-" + Guid.NewGuid().ToString("N") + "'; " +
                    "style-src 'self' 'unsafe-inline'; " +
                    "img-src 'self' data:; " +
                    "font-src 'self'; " +
                    "frame-ancestors 'none'; " + // This also prevents framing
                    "form-action 'self'; " +
                    "upgrade-insecure-requests;";
                context.Response.Headers["Referrer-Policy"] = "strict-origin-when-cross-origin";
                context.Response.Headers["Permissions-Policy"] =
                    "accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()";

                await next();
            });

            return app;
        }
    }
}
