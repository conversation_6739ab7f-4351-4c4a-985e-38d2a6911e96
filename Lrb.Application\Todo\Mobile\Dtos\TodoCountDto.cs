﻿using Lrb.Application.Dashboard.Mobile;

namespace Lrb.Application.Todo.Mobile
{
    public class TodoCountDto : IDto
    {
        public int TodaysTodosCount { get; set; }
        public int UpcomingTodosCount { get; set; }
        public int CompletedTodosCount { get; set; }
        public int OverdueTodosCount { get; set; }
        public int AllTodosCount { get; set; }
        public TasksDto? TasksDto { get; set; }
    }

}
