﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Domain.Enums
{
    public enum ProspectDataColumn
    {
        Name,
        ContactNo,
        AltContactNo,
        Email,
        Notes,
        EnquiryFor,
        City,
        State,
        Location,
        BHKType,
        NoOfBHKs,
        Source,
        SubSource,
        BasePropertyType,
        SubPropertyType,
        UpperBudget,
        LowerBudget,
        AssignedToUser,
        AgencyName,
        CompanyName,
        Project,
        Property, 
        CountryCode,
        AlternativeNoCountryCode,
        Currency,
        ChannelPartnerName,
        Community,
        SubCommunity,
        TowerName,
        ReferralEmail,
        Baths,
        Beds,
        FurnishStatus,
        OfferingType,
        Country,
        PreferredFloor,
        ReferralContactNo,
        ReferralName,
        ChannelPartnerExecutiveName,
        ChannelPartnerContactNo,
        Designation,
        CarpetArea,
        CarpetAreaUnit,
        PropertyArea,
        PropertyAreaUnit,
        NetArea,
        NetAreaUnit,
        BuiltUpArea,
        BuiltUpAreaUnit,
        SaleableArea,
        SaleableAreaUnit,
        UnitName,
        ClusterName,
        Nationality,
        CampaignName,
        Purpose,
        PostalCode,
        CustomerCity,
        CustomerState,
        CustomerLocation,
        CustomerCommunity,
        CustomerSubCommunity,
        CustomerTowerName,
        CustomerCountry,
        CustomerPostalCode,
        SourcingManager,
        ClosingManager,
        Profession,
        PossesionType,
        PossessionDate,
        LandLine,
        Gender,
        DateOfBirth,
        MaritalStatus
    }
}
