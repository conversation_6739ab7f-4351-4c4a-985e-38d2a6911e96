﻿using Lrb.Application.ChannelPartner.Web.Dtos;
using Lrb.Application.ChannelPartner.Web.Mapping;
using Lrb.Application.ChannelPartner.Web.Specs;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Enums;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Data;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task ChannelPartnerHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            BulkChannelPartnerUploadTracker? channelPartnerUploadTracker = (await _bulkChannelPartnerUploadRepo.ListAsync(new GetBulkTrackeByIdSpecs(input.TrackerId))).FirstOrDefault();
            try
            {
                if (channelPartnerUploadTracker != null)
                {
                    try
                    {
                        channelPartnerUploadTracker.MappedColumnsData = channelPartnerUploadTracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                        channelPartnerUploadTracker.Status = UploadStatus.Started;
                        channelPartnerUploadTracker.LastModifiedBy = input.CurrentUserId;
                        channelPartnerUploadTracker.CreatedBy = input.CurrentUserId;
                        channelPartnerUploadTracker.SheetName = channelPartnerUploadTracker.S3BucketKey.Split('/').Last() + "/" + channelPartnerUploadTracker.SheetName;
                        await _bulkChannelPartnerUploadRepo.UpdateAsync(channelPartnerUploadTracker);
                        Console.WriteLine($"handler() -> BulkChannelPartnerUploadTracker Updated Status: {channelPartnerUploadTracker.Status} \n {JsonConvert.SerializeObject(channelPartnerUploadTracker)}");

                        #region Convert file to DataTable
                        Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "dleadrat-black", channelPartnerUploadTracker.S3BucketKey);
                        DataTable dataTable = new();
                        if (channelPartnerUploadTracker.S3BucketKey.Split('.').LastOrDefault() == "csv")
                        {
                            using MemoryStream memoryStream = new();
                            fileStream.CopyTo(memoryStream);
                            dataTable = CSVHelper.CSVToDataTable(memoryStream);
                        }
                        else
                        {
                            dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, channelPartnerUploadTracker.SheetName);
                        }
                        List<InvalidData> invalids = new();
                        int totalRows = dataTable.Rows.Count;
                        for (int i = totalRows - 1; i >= 0; i--)
                        {
                            var row = dataTable.Rows[i];
                            if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                            {
                                row.Delete();
                            }
                            else if (string.IsNullOrEmpty(row[channelPartnerUploadTracker.MappedColumnsData[ChannelPartnerDataColumn.FirmName]].ToString()))
                            {
                                var firmName = string.Join(",", row.ItemArray.Where(i => !string.IsNullOrEmpty(i.ToString())));
                                var invalidData = new InvalidData
                                {
                                    Errors = "firm name is empty",
                                    FirmName = firmName
                                };
                                if (!invalids.Any(i => i.FirmName == invalidData.FirmName))
                                {
                                    invalids.Add(invalidData);
                                }
                                else
                                {
                                    var index = invalids.FindIndex(i => i.FirmName == invalidData.FirmName);
                                    if (index != -1)
                                    {
                                        invalids[index].RepeatedCount++;
                                    }
                                }
                                row.Delete();
                            }

                        }
                        if (dataTable.Rows.Count <= 0)
                        {
                            throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                        }
                        Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
                        #endregion
                        var channelPartners = dataTable.ConvertToChannelPartner(channelPartnerUploadTracker.MappedColumnsData);
                        channelPartners.ForEach(i => i.SetChannelPartner(channelPartnerUploadTracker.MappedColumnsData, input.CurrentUserId));
                        var invalidChannelPartners = ChannelPartnerHelper.GetInvalidChannelPartner(channelPartners);
                        channelPartnerUploadTracker.Status = UploadStatus.InProgress;
                        channelPartnerUploadTracker.TotalCount = totalRows;
                        channelPartnerUploadTracker.InvalidCount = invalidChannelPartners.Count;
                        await _bulkChannelPartnerUploadRepo.UpdateAsync(channelPartnerUploadTracker);
                        if (channelPartners.Count > 0)
                        {
                            int channelPartnerChunks = channelPartners.Count > 5000 ? 5000 : channelPartners.Count;
                            var chunks = channelPartners.Chunk(channelPartnerChunks).Select(i => new ConcurrentBag<Lrb.Domain.Entities.ChannelPartner>(i));
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);
                            var chunkIndex = 1;
                            foreach (var chunk in chunks.ToList())
                            {
                                var backgroundDto = new BulkUploadBackgroundDto()
                                {
                                    TrackerId = channelPartnerUploadTracker?.Id ?? Guid.Empty,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    ChannelPartners = new(chunk)
                                };
                                if (chunkIndex == chunks.Count())
                                {
                                    backgroundDto.IsLastChunk = true;
                                }
                                await ExecuteDBOperationsAsync(backgroundDto);
                                chunkIndex++;
                            }
                        }
                    }
                    catch { }
                }
            }
            catch (Exception ex)
            {
            }
        }

        public async Task ExecuteDBOperationsAsync(BulkUploadBackgroundDto dto)
        {
            BulkChannelPartnerUploadTracker bulkChannelPartner = new();
            var tracker = (await _bulkChannelPartnerUploadRepo.ListAsync(new GetBulkTrackeByIdSpecs(dto.TrackerId))).FirstOrDefault();
            try
            {
                await _cpRepository.AddRangeAsync(dto.ChannelPartners);
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.ChannelPartners.Count;
                    if (dto.IsLastChunk)
                    {
                        tracker.Status = UploadStatus.Completed;
                    }
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _bulkChannelPartnerUploadRepo.UpdateAsync(tracker);
                    await _dapperRepository.UpdateLatModifiedDateAsync(dto?.TenantInfoDto?.Id, (int)EntityType.ChannelPartner);

                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == tracker.InvalidCount)
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _bulkChannelPartnerUploadRepo.UpdateAsync(tracker);
            }
        }
    }
}
