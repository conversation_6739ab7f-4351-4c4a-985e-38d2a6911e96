﻿using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.Project.Web.Specs;

namespace Lrb.Application.Project.Web.Requests
{
    public class GetAllProjectNameAndRequest : IRequest<Response<List<ProjectInfoDto>>>
    {
    }

    public class GetAllProjectNameAndRequestHandler : IRequestHandler<GetAllProjectNameAndRequest, Response<List<ProjectInfoDto>>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        public GetAllProjectNameAndRequestHandler(IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo)
        {
            _projectRepo = projectRepo;
        }
        public async Task<Response<List<ProjectInfoDto>>> Handle(GetAllProjectNameAndRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var projects = await _projectRepo.ListAsync(new GetAllProjectNamesSpecs(), cancellationToken);
                var projectDto = projects.Adapt<List<ProjectInfoDto>>();
                return new(projectDto);
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }
    }
}
