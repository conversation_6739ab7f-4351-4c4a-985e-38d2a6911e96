﻿using Lrb.Application.Integration.Web.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Integration.Web.Requests.Facebook
{
    public class GetAllFbCampaignsRequest : PaginationFilter, IRequest<PagedResponse<FacebookCampaignInfoDto, string>>
    {
        public Guid AccountId { get; set; }
        public string? SearchText { get; set; }
    }
    public class GetAllFbCampaignsRequestHnadler : IRequestHandler<GetAllFbCampaignsRequest, PagedResponse<FacebookCampaignInfoDto, string>>
    {
        private readonly IRepositoryWithEvents<FacebookAdsInfo> _fbAdsInfoRepo;
        public GetAllFbCampaignsRequestHnadler(IRepositoryWithEvents<FacebookAdsInfo> fbAdsInfoRepo)
        {
            _fbAdsInfoRepo = fbAdsInfoRepo;
        }

        public async Task<PagedResponse<FacebookCampaignInfoDto, string>> Handle(GetAllFbCampaignsRequest request, CancellationToken cancellationToken)
        {
            var allStoredAds = await _fbAdsInfoRepo.ListAsync(new FacebookCampaignssByFbAccountRequestSpec(request), cancellationToken);
            var groupedByCampaign = allStoredAds
                .GroupBy(ad => ad.CampaignId)
                .Select(group =>
                {
                    var firstAd = group.FirstOrDefault();

                    return new FacebookCampaignInfoDto
                    {
                        PageId = firstAd?.PageId,
                        CampaignName = firstAd?.CampaignName,
                        CampaignId = group.Key,
                        AdAccountName = firstAd?.AdAccountName,
                        AdAccountId = firstAd?.AdAccountId,
                        FacebookAuthResponseId = firstAd?.FacebookAuthResponseId ?? Guid.Empty,
                        LeadCount = group.Sum(ad => ad.LeadsCount)
                    };
                })
                .ToList();

            var totalCampaigns = groupedByCampaign.Count;
            return new PagedResponse<FacebookCampaignInfoDto, string>(groupedByCampaign, totalCampaigns);
        }
    }
}
