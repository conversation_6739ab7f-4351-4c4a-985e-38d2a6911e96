﻿using LrbIntegrationBackgroundJobs.Dtos.Bayut;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace LrbIntegrationBackgroundJobs.Services
{
    public interface IBayutService
    {
        Task<List<BayutWhatsappResponseDto>> FetchLeadsFromBayutWAAsync(BayutCredentialDto cred);
        Task<List<BayutCallLogsResponseDto>> FetchLeadsFromBayutCallLogsAsync(BayutCredentialDto cred);
        Task<List<BayutEmailResponseDto>> FetchLeadsFromBayutEmailAsync(BayutCredentialDto cred);
        Task ProcessBayutAccountsAsync(ILogger logger);
    }
}
