﻿using Lrb.Application.Activity.Utils;
using Lrb.Application.Email.Web;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.GlobalSettings.Web.Dto;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.Reports.Web;
using Lrb.Application.Reports.Web.Data.Dtos.Common;
using Lrb.Application.Reports.Web.Dtos.Activity;
using Lrb.Application.Reports.Web.Dtos.DateWiseSource;
using Lrb.Application.Reports.Web.Dtos.ExportTrackerDto;
using Lrb.Application.Reports.Web.Dtos.FiltersName;
using Lrb.Application.Reports.Web.Dtos.ProjectvsSubStatus;
using Lrb.Application.Reports.Web.Lead.Dtos.UserVsSource;
using Lrb.Application.Reports.Web.Lead.Dtos.UserVsSubSource;
using Lrb.Application.Reports.Web.Requests.DatewiseSourceCount;
using Lrb.Application.Reports.Web.Requests.ProjectvsSubStatus;
using Lrb.Application.Reports.Web.UserVsSource.Requests;
using Lrb.Application.Utils;
using Lrb.Application.WA.Web;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Lrb.Shared.Extensions;
using Mapster;
using Newtonsoft.Json;
using RestSharp;
using StackExchange.Redis;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using static Lrb.Application.Lead.Utils.LeadExcelHelper;
using StatusDto = Lrb.Application.Reports.Web.StatusDto;
namespace ExcelUpload
{
    partial class FunctionEntryPoint
    {
        public async Task ExportLeadMeetingAndVisitReportByUserHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();

            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };

            try
            {
                if (tracker != null)
                {
                    //CreateExcelForVisitAndMeetingReportByUserRequest? request = JsonConvert.DeserializeObject<CreateExcelForVisitAndMeetingReportByUserRequest>(tracker?.Request ?? string.Empty);
                    RunAWSBatchForVisitAndMeetingReportByUserRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForVisitAndMeetingReportByUserRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        formattedFiltersDto.ToDate = request.ToDateForMeetingOrVisit;
                        formattedFiltersDto.FromDate = request.FromDateForMeetingOrVisit;
                        List<Guid> UsersByIds = request.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }

                            formattedFiltersDto.UserNames = userstodetails;

                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                //request.UserIds = new List<Guid>() { userId };
                                //teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();

                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        request.FromDateForMeetingOrVisit = request.FromDateForMeetingOrVisit.HasValue ? request.FromDateForMeetingOrVisit.Value.ConvertFromDateToUtc() : null;
                        request.ToDateForMeetingOrVisit = request.ToDateForMeetingOrVisit.HasValue ? request.ToDateForMeetingOrVisit.Value.ConvertToDateToUtc() : null;
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadAppointmentByUserDto>("LeadratBlack", "GetMeetingAndSitevisitReportByUserV3", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            fromdateformeetingorvisit = request.FromDateForMeetingOrVisit,
                            todateformeetingorvisit = request.ToDateForMeetingOrVisit,
                            datetype = request.DateType,
                            userids = teamUserIds,
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            tenantid = tenantId,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            userstatus = (request?.UserStatus ?? 0),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                            ownerselection = request?.OwnerSelection ?? OwnerSelectionType.PrimaryOwner,
                        }, 300))?.ToList();
                        var meetingAndVisitResult = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadAppointmentByUserDto>("LeadratBlack", "GetMeetingAndSitevisitReportByUserV2", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            fromdateformeetingorvisit = request.FromDateForMeetingOrVisit,
                            todateformeetingorvisit = request.ToDateForMeetingOrVisit,
                            datetype = request.DateType,
                            userids = teamUserIds,
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            tenantid = tenantId,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            userstatus = (request?.UserStatus ?? 0),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                            ownerselection = request?.OwnerSelection ?? OwnerSelectionType.PrimaryOwner,
                        }, 300))?.ToList();
                        var history = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadAppointmentFromHistory>("LeadratBlack", "GetMeetingAndVisitFromHistoryCounts", new
                        {
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            from_date = request.FromDate,
                            to_date = request.ToDate,
                            tenant_id = tenantId,
                            user_ids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            userstatus = request?.UserStatus ?? 0,
                        }, 300))?.ToList();
                        List<LeadAppointmentByUserDto> leadAppointments = new();
                        foreach (var item in res)
                        {

                            var matchingHistory = history?.FirstOrDefault(i => i.UserId == item.Id);
                            var matchedResult = meetingAndVisitResult?.FirstOrDefault(i => i.Id == item.Id);
                            if (matchingHistory != null)
                            {
                                item.UserName = matchingHistory.UserName;
                                item.SiteVisitScheduledCountFromHistory = matchingHistory.SiteVisitScheduledCountFromHistory;
                                item.MeetingScheduledCountFromHistory = matchingHistory.MeetingScheduledCountFromHistory;
                                item.TotalCount = matchedResult?.MeetingScheduledCount + matchingHistory.MeetingScheduledCountFromHistory + item.MeetingDoneCount + item.MeetingNotDoneCount + matchedResult?.SiteVisitScheduledCount + matchingHistory.SiteVisitScheduledCountFromHistory + item.SiteVisitDoneCount + item.SiteVisitNotDoneCount + matchedResult?.NotInterestedAfterMeetingDone + matchedResult?.NotInterestedAfterSiteVisitDone + matchedResult?.DroppedAfterMeetingDone + matchedResult?.DroppedAfterSiteVisitDone + matchedResult?.BookedCount + matchedResult?.BookingCancelCount;
                                item.TotalUniqueCount = item.SiteVisitDoneUniqueCount + item.SiteVisitNotDoneUniqueCount + item.MeetingDoneUniqueCount + item.MeetingNotDoneUniqueCount;
                                item.MeetingScheduledCount = matchedResult?.MeetingScheduledCount;
                                item.SiteVisitScheduledCount = matchedResult?.SiteVisitScheduledCount;
                                item.NotInterestedAfterMeetingDone = matchedResult?.NotInterestedAfterMeetingDone ?? default;
                                item.NotInterestedAfterSiteVisitDone = matchedResult?.NotInterestedAfterSiteVisitDone ?? default;
                                item.DroppedAfterMeetingDone = matchedResult?.DroppedAfterMeetingDone ?? default;
                                item.DroppedAfterSiteVisitDone = matchedResult?.DroppedAfterSiteVisitDone ?? default;
                                item.BookedCount = matchedResult?.BookedCount ?? default;
                                item.BookingCancelCount = matchedResult?.BookingCancelCount ?? default;
                                item.InvoicedLeadsCount = matchedResult?.InvoicedLeadsCount ?? default;
                                item.NotInterestedCount = matchedResult?.NotInterestedCount ?? default;
                                leadAppointments.Add(item);
                            }
                        }

                        var globalSetting = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                        byte[] fileBytes;
                        if (globalSetting?.ShouldRenameSiteVisitColumn == true)
                        {
                            var dtos = leadAppointments?.Adapt<List<LeadAppointmentNewFormattedDto>>().ToList();
                            fileBytes = ExcelGeneration<LeadAppointmentNewFormattedDto>.GenerateExcel(dtos, "Visit and Meeting Reports", formattedFiltersDto, exportTracker, request.TimeZoneId, request.BaseUTcOffset);
                        }
                        else
                        {
                            var dtos = leadAppointments?.Adapt<List<LeadAppointmentFormattedDto>>().ToList();
                            fileBytes = ExcelGeneration<LeadAppointmentFormattedDto>.GenerateExcel(dtos, "Visit and Meeting Reports", formattedFiltersDto, exportTracker, request.TimeZoneId, request.BaseUTcOffset);
                        }
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Visit_and_Meeting", $"Export_Visit_and_Meeting_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        _logger.Information($"Visit_and_Meeting Report: {presignedUrl}, {exportTracker.CreatedBy}, {input.TenantId}");
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Meeting And Visit Report");
                        isSent = true;
                        tracker.Count = res.Count();
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Visit_and_Meeting_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                    /*   if (errorEmailTemplate != null && !isSent && serviceProvider != null)
                       {
                           await SendExceptionEmailAsync(tracker, errorEmailTemplate, serviceProvider);
                       }*/
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),

                    ErrorModule = "FunctionEntryPoint -> ExportLeadMeetingAndVisitReportByUserHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }

        }
        public async Task ExportLeadStatusReportByProjectHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();


            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    //CreateExcelForLeadStatusReportByProjectsRequest? request = JsonConvert.DeserializeObject<CreateExcelForLeadStatusReportByProjectsRequest>(tracker?.Request ?? string.Empty);
                    RunAWSBatchForLeadStatusReportByProjectsRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForLeadStatusReportByProjectsRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        formattedFiltersDto.FromDate = request.FromDateForProject;
                        formattedFiltersDto.ToDate = request.ToDateForProject;
                        List<Guid> UsersByIds = request.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }

                            formattedFiltersDto.UserNames = userstodetails;

                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                //request.UserIds = new List<Guid>() { userId };
                                //teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();

                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        List<ProjectReportV2Dto> leadsReportByProjects = new List<ProjectReportV2Dto>();
                        List<ProjectReportDto> newReportByProjects = new List<ProjectReportDto>();
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<ProjectsReportDto>("LeadratBlack", "GetLeadReportByProject", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                            todateforproject = request?.ToDateForProject,
                            fromdateforproject = request?.FromDateForProject
                        })).ToList();
                        res.ForEach(i => leadsReportByProjects.Add(JsonConvert.DeserializeObject<ProjectReportV2Dto>(i.Report ?? string.Empty) ?? new ProjectReportV2Dto()));
                        var groupedResult = leadsReportByProjects.GroupBy(i => i?.Project ?? new Projects()).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.Status).ToList());
                        foreach (var group in groupedResult)
                        {
                            newReportByProjects.Add(new()
                            {
                                ProjectId = group.Key.Id,
                                ProjectTitle = group.Key.Name,
                                AllCount = group.Value?.Sum(i => i.Count) ?? 0,
                                ActiveCount = group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel" && i.BaseStatus != "invoiced").Sum(i => i.Count) ?? 0,
                                MeetingDoneCount = group.Value?.Sum(i => i.MeetingDoneCount) ?? 0,
                                OverdueCount = group.Value?.Sum(i => i.OverdueCount) ?? 0,
                                MeetingNotDoneCount = group.Value?.Sum(i => i.MeetingNotDoneCount) ?? 0,
                                SiteVisitDoneCount = group.Value?.Sum(i => i.SiteVisitDoneCount) ?? 0,
                                SiteVisitNotDoneCount = group.Value?.Sum(i => i.SiteVisitNotDoneCount) ?? 0,
                                CallbackCount = group.Value?.Where(i => i.BaseStatus == "callback" && i.SubStatus != "callback")?.Sum(i => i.Count) ?? 0,
                                BookedCount = group.Value?.Where(i => i.BaseStatus == "booked")?.Sum(i => i.Count) ?? 0,
                                NewCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "new")?.Count ?? 0,
                                DroppedCount = group.Value?.Where(i => i.BaseStatus == "dropped" && i.SubStatus != "dropped")?.Sum(i => i.Count) ?? 0,
                                MeetingScheduledCount = group.Value?.Where(i => i.BaseStatus == "meeting_scheduled" && i.SubStatus != "meeting_scheduled")?.Sum(i => i.Count) ?? 0,
                                SiteVisitScheduledCount = group.Value?.Where(i => i.BaseStatus == "site_visit_scheduled" && i.SubStatus != "site_visit_scheduled")?.Sum(i => i.Count) ?? 0,
                                NotInterestedCount = group.Value?.Where(i => i.BaseStatus == "not_interested" && i.SubStatus != "not_interested")?.Sum(i => i.Count) ?? 0,
                                PendingCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "pending")?.Count ?? 0,
                                MeetingDoneUniqueCount = group.Value?.Sum(i => i.MeetingDoneUniqueCount) ?? 0,
                                MeetingNotDoneUniqueCount = group.Value?.Sum(i => i.MeetingNotDoneUniqueCount) ?? 0,
                                SiteVisitDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0,
                                SiteVisitNotDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0,
                                BookingCancelCount = group.Value?.Where(i => i.BaseStatus == "booking_cancel")?.Sum(i => i.Count) ?? 0,
                                ExpressionOfInterestLeadCount = group.Value?.Where(i => i.BaseStatus == "expression_of_interest")?.Sum(i => i.Count) ?? 0,
                                InvoicedLeadsCount = group.Value?.Where(i => i.BaseStatus == "invoiced")?.Sum(i => i.Count) ?? 0
                            });
                        }

                        var globalSetting = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                        byte[] fileBytes;
                        if (globalSetting?.ShouldRenameSiteVisitColumn == true)
                        {
                            var dtos = newReportByProjects.Adapt<List<ProjectNewFormattedDto>>();
                            fileBytes = ExcelGeneration<ProjectNewFormattedDto>.GenerateExcel(dtos, "Project Reports", formattedFiltersDto, exportTracker, request.TimeZoneId, request.BaseUTcOffset);
                        }
                        else
                        {
                            var dtos = newReportByProjects.Adapt<List<ProjectFormattedDto>>();
                            fileBytes = ExcelGeneration<ProjectFormattedDto>.GenerateExcel(dtos, "Project Reports", formattedFiltersDto, exportTracker, request.TimeZoneId, request.BaseUTcOffset);
                        }
                        //var fileBytes = ExcelHelper.CreateExcelFromList(newReportByProjects, new List<string>(), new List<string>() { "ProjectId" }).ToArray();
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Project", $"Export_Leads_Project_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        _logger.Information($"Export_Leads_Project_Reports_: {presignedUrl}, {exportTracker.CreatedBy}, {input.TenantId}");
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Leads Project Report");
                        isSent = true;
                        tracker.Count = res.Count();
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Leads_Project_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }

            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadStatusReportByProjectHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }

        public async Task ExportLeadStatusReportBySourceHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();

            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    //CreateExcelForLeadStatusReportBySourceRequest? request = JsonConvert.DeserializeObject<CreateExcelForLeadStatusReportBySourceRequest>(tracker?.Request ?? string.Empty);
                    RunAWSBatchForLeadStatusReportBySourceRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForLeadStatusReportBySourceRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        formattedFiltersDto.FromDate = request.FromDateForSource;
                        formattedFiltersDto.ToDate = request.ToDateForSource;
                        List<Guid> UsersByIds = request.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }

                            formattedFiltersDto.UserNames = userstodetails;

                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                //request.UserIds = new List<Guid>() { userId };
                                //teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();

                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        //if (!string.IsNullOrEmpty(request?.SearchText))
                        //{
                        //    var result = ReportHelper.GetLeadSourceInfo(request.SearchText);
                        //    if (result?.IsValidInfo ?? false)
                        //    {
                        //        request?.Sources?.Add(result.LeadSource);
                        //        request.SearchText = null;
                        //    }
                        //}
                        List<LeadsSourceReportDto> newReportBySourceDtos = new List<LeadsSourceReportDto>();
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        request.FromDateForSource = request.FromDateForSource.HasValue ? request.FromDateForSource.Value.ConvertFromDateToUtc() : null;
                        request.ToDateForSource = request.ToDateForSource.HasValue ? request.ToDateForSource.Value.ConvertToDateToUtc() : null;
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<SourceReportDto>("LeadratBlack", "GetLeadSourceReport", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            userids = teamUserIds,
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            tenantid = tenantId,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                            fromdateforsource = request?.FromDateForSource,
                            todateforsource = request?.ToDateForSource
                        }))?.ToList() ?? new List<SourceReportDto>();
                        res.ForEach(i => i.StatusCount = JsonConvert.DeserializeObject<List<StatusCountDto>>(i?.Report ?? string.Empty));
                        var groupedResult = res.GroupBy(i => i?.Source ?? Lrb.Domain.Enums.LeadSource.Direct).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.StatusCount).ToList());
                        foreach (var group in groupedResult)
                        {
                            newReportBySourceDtos.Add(new()
                            {
                                Source = group.Key,
                                AllCount = group.Value?.Sum(i => i.Count) ?? 0,
                                ActiveCount = group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel" && i.BaseStatus != "invoiced").Sum(i => i.Count) ?? 0,
                                MeetingDoneCount = group.Value?.Sum(i => i.MeetingDoneCount) ?? 0,
                                OverdueCount = group.Value?.Sum(i => i.OverdueCount) ?? 0,
                                MeetingNotDoneCount = group.Value?.Sum(i => i.MeetingNotDoneCount) ?? 0,
                                SiteVisitDoneCount = group.Value?.Sum(i => i.SiteVisitDoneCount) ?? 0,
                                SiteVisitNotDoneCount = group.Value?.Sum(i => i.SiteVisitNotDoneCount) ?? 0,
                                CallbackCount = group.Value?.Where(i => i.BaseStatus == "callback" && i.SubStatus != "callback")?.Sum(i => i.Count) ?? 0,
                                BookedCount = group.Value?.Where(i => i.BaseStatus == "booked")?.Sum(i => i.Count) ?? 0,
                                NewCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "new")?.Count ?? 0,
                                DroppedCount = group.Value?.Where(i => i.BaseStatus == "dropped" && i.SubStatus != "dropped")?.Sum(i => i.Count) ?? 0,
                                MeetingScheduledCount = group.Value?.Where(i => i.BaseStatus == "meeting_scheduled" && i.SubStatus != "meeting_scheduled")?.Sum(i => i.Count) ?? 0,
                                SiteVisitScheduledCount = group.Value?.Where(i => i.BaseStatus == "site_visit_scheduled" && i.SubStatus != "site_visit_scheduled")?.Sum(i => i.Count) ?? 0,
                                NotInterestedCount = group.Value?.Where(i => i.BaseStatus == "not_interested" && i.SubStatus != "not_interested")?.Sum(i => i.Count) ?? 0,
                                PendingCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "pending")?.Count ?? 0,
                                MeetingDoneUniqueCount = group.Value?.Sum(i => i.MeetingDoneUniqueCount) ?? 0,
                                MeetingNotDoneUniqueCount = group.Value?.Sum(i => i.MeetingNotDoneUniqueCount) ?? 0,
                                SiteVisitDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0,
                                SiteVisitNotDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0,
                                BookingCancelCount = group.Value?.Where(i => i.BaseStatus == "booking_cancel")?.Sum(i => i.Count) ?? 0,
                                ExpressionOfInterestCount = group.Value?.Where(i => i.BaseStatus == "expression_of_interest")?.Sum(i => i.Count) ?? 0,
                                InvoicedLeadsCount = group.Value?.Where(i => i.BaseStatus == "invoiced")?.Sum(i => i.Count) ?? 0


                            });
                        }
                        var globalSetting = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                        byte[] fileBytes;
                        if (globalSetting?.ShouldRenameSiteVisitColumn == true)
                        {
                            var dtos = newReportBySourceDtos.Adapt<List<SoureNewFormettedDto>>();
                            fileBytes = ExcelGeneration<SoureNewFormettedDto>.GenerateExcel(dtos, "Source Reports", formattedFiltersDto, exportTracker, request.TimeZoneId, request.BaseUTcOffset);
                        }
                        else
                        {
                            var dtos = newReportBySourceDtos.Adapt<List<SoureFormettedDto>>();
                            fileBytes = ExcelGeneration<SoureFormettedDto>.GenerateExcel(dtos, "Source Reports", formattedFiltersDto, exportTracker, request.TimeZoneId, request.BaseUTcOffset);
                        }
                        //var fileBytes = ExcelHelper.CreateExcelFromList(newReportBySourceDtos).ToArray();
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Source Reports", $"Export_Leads_Source_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Leads Source Report");
                        isSent = true;
                        tracker.Count = res.Count();
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Leads_Source_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }

            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadStatusReportBySourceHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
        public async Task ExportLeadStatusReportByUserHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();

            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    CreateExcelForLeadStatusReportByUsersRequest? request = JsonConvert.DeserializeObject<CreateExcelForLeadStatusReportByUsersRequest>(tracker?.Request ?? string.Empty);
                    RunAWSBatchForLeadStatusReportByUsersRequest? requestforFileName = JsonConvert.DeserializeObject<RunAWSBatchForLeadStatusReportByUsersRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds;
                        List<string>? defaultColumns = new() { "SlNo", "FirstName", "LastName", "Name" };
                        var selectedColumns = filtersDto.SelectedColumns?.Select(i => i.Trim().Replace(" ", "")).ToList() ?? null;
                        if (selectedColumns?.Any(i => i == "AllLeads") ?? false)
                        {
                            selectedColumns = selectedColumns.Select(i =>
                            {
                                if (i == "AllLeads")
                                {
                                    i = "All";
                                }
                                return i;
                            }).ToList();

                        }
                        if (selectedColumns?.Any(i => i == "ActiveLeads") ?? false)
                        {
                            selectedColumns = selectedColumns.Select(i =>
                            {
                                if (i == "ActiveLeads")
                                {
                                    i = "Active";
                                }
                                return i;
                            }).ToList();

                        }
                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }
                            formattedFiltersDto.UserNames = userstodetails;

                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input?.TenantId ?? string.Empty;
                        var userId = input?.CurrentUserId ?? Guid.Empty;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else if (request?.IsWithGeneralManager ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithColumnNameAsync(request.UserIds, tenantId ?? string.Empty, "GeneralManager")).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        List<LeadsReportByUserV2Dto> leadsReportByUserDtos = new List<LeadsReportByUserV2Dto>();
                        List<LeadsReportByUserDto> newReportByUserDtos = new List<LeadsReportByUserDto>();
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadsReportDto>("LeadratBlack", "GetLeadReportByUser", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            userstatus = (request?.UserStatus ?? 0),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize())
                        })).ToList();
                        res.ForEach(i => leadsReportByUserDtos.Add(JsonConvert.DeserializeObject<LeadsReportByUserV2Dto>(i.Report ?? string.Empty) ?? new LeadsReportByUserV2Dto()));
                        var groupedResult = leadsReportByUserDtos.GroupBy(i => i?.User ?? new User()).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.Status).ToList());
                        foreach (var group in groupedResult)
                        {
                            float totalCount = ((float)(group.Value?.Sum(i => i.Count) ?? 1));
                            totalCount = totalCount == 0 ? 1 : totalCount;
                            newReportByUserDtos.Add(new()
                            {
                                UserId = group.Key?.UserId ?? Guid.Empty,
                                FirstName = group.Key?.FirstName ?? string.Empty,
                                LastName = group.Key?.LastName ?? string.Empty,
                                GeneralManager = group.Key?.GeneralManager ?? string.Empty,
                                ReportingManager = group.Key?.ReportingManager ?? string.Empty,
                                AllCount = group.Value?.Sum(i => i.Count) ?? 0,
                                ActiveCount = group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel" && i.BaseStatus != "invoiced").Sum(i => i.Count) ?? 0,
                                MeetingDoneCount = group.Value?.Sum(i => i.MeetingDoneCount) ?? 0,
                                OverdueCount = group.Value?.Sum(i => i.OverdueCount) ?? 0,
                                OverdueCountPercentage = GetRoundValue(((group.Value?.Sum(i => i.OverdueCount) ?? 0) / totalCount) * 100),
                                MeetingNotDoneCount = group.Value?.Sum(i => i.MeetingNotDoneCount) ?? 0,
                                SiteVisitDoneCount = group.Value?.Sum(i => i.SiteVisitDoneCount) ?? 0,
                                SiteVisitNotDoneCount = group.Value?.Sum(i => i.SiteVisitNotDoneCount) ?? 0,
                                CallbackCount = group.Value?.Where(i => i.BaseStatus == "callback" && i.SubStatus != "callback")?.Sum(i => i.Count) ?? 0,
                                CallbackCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "callback" && i.SubStatus != "callback")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                                BookedCount = group.Value?.Where(i => i.BaseStatus == "booked")?.Sum(i => i.Count) ?? 0,
                                BookedCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "booked")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                                NewCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "new")?.Count ?? 0,
                                NewCountPercentage = GetRoundValue(((group.Value?.FirstOrDefault(i => i.BaseStatus == "new")?.Count ?? 0) / totalCount) * 100),
                                DroppedCount = group.Value?.Where(i => i.BaseStatus == "dropped" && i.SubStatus != "dropped")?.Sum(i => i.Count) ?? 0,
                                DroppedCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "dropped" && i.SubStatus != "dropped")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                                MeetingScheduledCount = group.Value?.Where(i => i.BaseStatus == "meeting_scheduled" && i.SubStatus != "meeting_scheduled")?.Sum(i => i.Count) ?? 0,
                                MeetingScheduledCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "meeting_scheduled" && i.SubStatus != "meeting_scheduled")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                                SiteVisitScheduledCount = group.Value?.Where(i => i.BaseStatus == "site_visit_scheduled" && i.SubStatus != "site_visit_scheduled")?.Sum(i => i.Count) ?? 0,
                                SiteVisitScheduledCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "site_visit_scheduled" && i.SubStatus != "site_visit_scheduled")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                                NotInterestedCount = group.Value?.Where(i => i.BaseStatus == "not_interested" && i.SubStatus != "not_interested")?.Sum(i => i.Count) ?? 0,
                                NotInterestedCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "not_interested" && i.SubStatus != "not_interested")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                                PendingCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "pending")?.Count ?? 0,
                                PendingCountPercentage = GetRoundValue(((group.Value?.FirstOrDefault(i => i.BaseStatus == "pending")?.Count ?? 0) / totalCount) * 100),
                                MeetingDoneUniqueCount = group.Value?.Sum(i => i.MeetingDoneUniqueCount) ?? 0,
                                MeetingDoneUniqueCountPercentage = GetRoundValue(((group.Value?.Sum(i => i.MeetingDoneUniqueCount) ?? 0) / totalCount) * 100),
                                MeetingNotDoneUniqueCount = group.Value?.Sum(i => i.MeetingNotDoneUniqueCount) ?? 0,
                                MeetingNotDoneUniqueCountPercentage = GetRoundValue(((group.Value?.Sum(i => i.MeetingNotDoneUniqueCount) ?? 0) / totalCount) * 100),
                                SiteVisitDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0,
                                SiteVisitDoneUniqueCountPercentage = GetRoundValue(((group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0) / totalCount) * 100),
                                SiteVisitNotDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0,
                                SiteVisitNotDoneUniqueCountPercentage = GetRoundValue(((group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0) / totalCount) * 100),
                                BookingCancelCount = group.Value?.Where(i => i.BaseStatus == "booking_cancel")?.Sum(i => i.Count) ?? 0,
                                BookingCancelCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "booking_cancel")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                                ExpressionOfInterestLeadCount = group.Value?.Where(i => i.BaseStatus == "expression_of_interest")?.Sum(i => i.Count) ?? 0,
                                ExpressionOfInterestLeadCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "expression_of_interest")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                                InvoicedLeadsCount = group.Value?.Where(i => i.BaseStatus == "invoiced")?.Sum(i => i.Count) ?? 0,
                                InvoicedLeadsCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "invoiced")?.Sum(i => i.Count) ?? 0) / totalCount) * 100)
                            });
                        }
                        var headers = new List<LeadHeadersDto>();
                        var properties = typeof(LeadsReportByFormattedUserDto).GetProperties();
                        foreach (var property in properties)
                        {
                            if (IsSimpleProperty(property))
                            {
                                headers.Add(new LeadHeadersDto() { Header = property.Name });
                            }
                        }
                        int startIndex = 3;
                        List<string> newSelectedColumns = new List<string>();
                        if ((selectedColumns?.Any() ?? false) && (selectedColumns != null) && (request?.ShouldShowPercentage == true))
                        {
                            // List<string> newSelectedColumns = new List<string>(selectedColumns);
                            foreach (string status in selectedColumns)
                            {
                                newSelectedColumns.Add($"{status}Count");
                                newSelectedColumns.Add($"{status}UniqueCount");
                                newSelectedColumns.Add($"{status}UniqueCountPercentage");
                            }
                            headers = headers.Where(i => selectedColumns.Contains(i.Header.Trim().Replace(" ", "")) || defaultColumns.Contains(i.Header.Trim().Replace(" ", "")) || newSelectedColumns.Contains(i.Header.Trim().Replace(" ", ""))).ToList();
                        }
                        else if ((selectedColumns?.Any() ?? false) && selectedColumns != null)
                        {
                            foreach (string status in selectedColumns)
                            {
                                newSelectedColumns.Add($"{status}Count");
                                newSelectedColumns.Add($"{status}UniqueCount");
                            }
                            headers = headers.Where(i => selectedColumns.Contains(i.Header.Trim().Replace(" ", "")) || defaultColumns.Contains(i.Header.Trim().Replace(" ", "")) || newSelectedColumns.Contains(i.Header.Trim().Replace(" ", ""))).ToList();
                        }
                        if ((selectedColumns?.Any() ?? false) && selectedColumns.Contains("GeneralManager"))
                        {
                            startIndex = 4;
                        }
                        if ((selectedColumns?.Any() ?? false) && selectedColumns.Contains("ReportingManager"))
                        {
                            startIndex = 5;
                        }
                        var initialPart = headers.Take(startIndex).ToList();
                        var partToSort = headers.Skip(startIndex).ToList();
                        partToSort = partToSort.OrderBy(i => i.Header.Trim()).ToList();
                        headers = initialPart.Concat(partToSort).ToList();
                        var globalSetting = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                        byte[] fileBytes;
                        if (globalSetting?.ShouldRenameSiteVisitColumn == true)
                        {
                            var dtos = newReportByUserDtos.Adapt<List<LeadsReportByNewFormattedUserDto>>();
                            fileBytes = LeadExcelGeneration<LeadsReportByNewFormattedUserDto>.GenerateExcel(dtos, "Lead Status Reports", formattedFiltersDto, exportTracker, headers, request.TimeZoneId, request.BaseUTcOffset);
                        }
                        else
                        {
                            var dtos = newReportByUserDtos.Adapt<List<LeadsReportByFormattedUserDto>>();
                            fileBytes = LeadExcelGeneration<LeadsReportByFormattedUserDto>.GenerateExcel(dtos, "Lead Status Reports", formattedFiltersDto, exportTracker, headers, request.TimeZoneId, request.BaseUTcOffset);
                        }
                        // var fileBytes = ExcelHelper.CreateExcelFromList(newReportByUserDtos, new List<string>(), new List<string>() { "UserId" }).ToArray();
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Lead_Status", $"Export_Leads_Status_Reports_" + input.TenantId + requestforFileName.FileName + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Leads Status Report");
                        isSent = true;
                        tracker.Count = res.Count();
                        tracker.S3BucketKey = presignedUrl;
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(requestforFileName.TimeZoneId ?? "Asia/Kolkata");
                        tracker.FileName = $"Export_Leads_Status_Reports_" + requestforFileName.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }


            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadStatusReportByUserHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
        private string GetRoundValue(float percentage)
        {
            if (percentage > 0)
            {
                return (float)Math.Round(percentage, 2) + " %";
            }
            return percentage + " %";
        }
        public async Task ExportLeadStatusReportByAgencyHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;
            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    //CreateExcelForLeadStatusReportByAgencyRequest? request = JsonConvert.DeserializeObject<CreateExcelForLeadStatusReportByAgencyRequest>(tracker?.Request ?? string.Empty);
                    RunAWSBatchForLeadStatusReportByAgencyRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForLeadStatusReportByAgencyRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }

                            formattedFiltersDto.UserNames = userstodetails;

                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }

                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                //request.UserIds = new List<Guid>() { userId };
                                //teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();

                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        List<LeadsAgencyReportDto> newReportBySubSourceDtos = new List<LeadsAgencyReportDto>();
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        request.FromDateForAgency = request.FromDateForAgency.HasValue ? request.FromDateForAgency.Value.ConvertFromDateToUtc() : null;
                        request.ToDateForAgency = request.ToDateForAgency.HasValue ? request.ToDateForAgency.Value.ConvertFromDateToUtc() : null;
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<AgencyNameReportDto>("LeadratBlack", "GetLeadAgencyNameReport", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            userids = teamUserIds,
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            tenantid = tenantId,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                            fromdateformeetingorvisit = request?.FromDateForAgency,
                            todateformeetingorvisit = request?.ToDateForAgency
                        }))?.ToList() ?? new List<AgencyNameReportDto>();

                        res.ForEach(i => i.StatusCount = JsonConvert.DeserializeObject<List<StatusCountDto>>(i?.Report ?? string.Empty));
                        var groupedResult = res.GroupBy(i => i?.AgencyName ?? string.Empty).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.StatusCount).ToList());
                        foreach (var group in groupedResult)
                        {
                            newReportBySubSourceDtos.Add(new()
                            {
                                AgencyName = group.Key,
                                AllCount = group.Value?.Sum(i => i.Count) ?? 0,
                                ActiveCount = group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel" && i.BaseStatus != "invoiced").Sum(i => i.Count) ?? 0,
                                MeetingDoneCount = group.Value?.Sum(i => i.MeetingDoneCount) ?? 0,
                                OverdueCount = group.Value?.Sum(i => i.OverdueCount) ?? 0,
                                MeetingNotDoneCount = group.Value?.Sum(i => i.MeetingNotDoneCount) ?? 0,
                                SiteVisitDoneCount = group.Value?.Sum(i => i.SiteVisitDoneCount) ?? 0,
                                SiteVisitNotDoneCount = group.Value?.Sum(i => i.SiteVisitNotDoneCount) ?? 0,
                                CallbackCount = group.Value?.Where(i => i.BaseStatus == "callback" && i.SubStatus != "callback")?.Sum(i => i.Count) ?? 0,
                                BookedCount = group.Value?.Where(i => i.BaseStatus == "booked")?.Sum(i => i.Count) ?? 0,
                                NewCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "new")?.Count ?? 0,
                                DroppedCount = group.Value?.Where(i => i.BaseStatus == "dropped" && i.SubStatus != "dropped")?.Sum(i => i.Count) ?? 0,
                                MeetingScheduledCount = group.Value?.Where(i => i.BaseStatus == "meeting_scheduled" && i.SubStatus != "meeting_scheduled")?.Sum(i => i.Count) ?? 0,
                                SiteVisitScheduledCount = group.Value?.Where(i => i.BaseStatus == "site_visit_scheduled" && i.SubStatus != "site_visit_scheduled")?.Sum(i => i.Count) ?? 0,
                                NotInterestedCount = group.Value?.Where(i => i.BaseStatus == "not_interested" && i.SubStatus != "not_interested")?.Sum(i => i.Count) ?? 0,
                                PendingCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "pending")?.Count ?? 0,
                                MeetingDoneUniqueCount = group.Value?.Sum(i => i.MeetingDoneUniqueCount) ?? 0,
                                MeetingNotDoneUniqueCount = group.Value?.Sum(i => i.MeetingNotDoneUniqueCount) ?? 0,
                                SiteVisitDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0,
                                SiteVisitNotDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0,
                                BookingCancelCount = group.Value?.Where(i => i.BaseStatus == "booking_cancel")?.Sum(i => i.Count) ?? 0,
                                ExpressionOfInterestLeadCount = group.Value?.Where(i => i.BaseStatus == "expression_of_interest")?.Sum(i => i.Count) ?? 0,
                                InvoicedLeadsCount = group.Value?.Where(i => i.BaseStatus == "invoiced")?.Sum(i => i.Count) ?? 0
                            });
                        }
                        var globalSetting = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                        byte[] fileBytes;
                        if (globalSetting?.ShouldRenameSiteVisitColumn == true)
                        {
                            var dtos = newReportBySubSourceDtos.Adapt<List<LeadsAgencyNewFormattedDto>>();
                            fileBytes = ExcelGeneration<LeadsAgencyNewFormattedDto>.GenerateExcel(dtos, "Agency Reports", formattedFiltersDto, exportTracker, request.TimeZoneId, request.BaseUTcOffset);
                        }
                        else
                        {
                            var dtos = newReportBySubSourceDtos.Adapt<List<LeadsAgencyFormattedDto>>();
                            fileBytes = ExcelGeneration<LeadsAgencyFormattedDto>.GenerateExcel(dtos, "Agency Reports", formattedFiltersDto, exportTracker, request.TimeZoneId, request.BaseUTcOffset);
                        }

                        //var fileBytes = ExcelHelper.CreateExcelFromList(newReportBySubSourceDtos).ToArray();
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Reports/{tenantId ?? "Default"}", $"Export_Leads_Agency_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Leads Agency Report");
                        isSent = true;
                        tracker.Count = res.Count();
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Leads_Agency_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }

            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadStatusReportByAgencyHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
        public async Task ExportLeadStatusReportBySubSourceHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    //CreateExcelForLeadStatusReportBySubSourceRequest? request = JsonConvert.DeserializeObject<CreateExcelForLeadStatusReportBySubSourceRequest>(tracker?.Request ?? string.Empty);
                    RunAWSBatchForLeadStatusReportBySubSourceRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForLeadStatusReportBySubSourceRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }

                            formattedFiltersDto.UserNames = userstodetails;

                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                //request.UserIds = new List<Guid>() { userId };
                                //teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();

                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        List<LeadsSubSourceReportDto> newReportBySubSourceDtos = new List<LeadsSubSourceReportDto>();
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        request.FromDateForSubSource = request.FromDateForSubSource.HasValue ? request.FromDateForSubSource.Value.ConvertFromDateToUtc() : null;
                        request.ToDateForSubSource = request.ToDateForSubSource.HasValue ? request.ToDateForSubSource.Value.ConvertToDateToUtc() : null;
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<SubSourceReportDto>("LeadratBlack", "GetLeadSubSourceReport", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            userids = teamUserIds,
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            tenantid = tenantId,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                            fromdateforsubsource = request.FromDateForSubSource,
                            todateforsubsource = request.ToDateForSubSource
                        }))?.ToList() ?? new List<SubSourceReportDto>();

                        res.ForEach(i => i.StatusCount = JsonConvert.DeserializeObject<List<StatusCountDto>>(i?.Report ?? string.Empty));
                        var groupedResult = res.GroupBy(i => i?.SubSource ?? string.Empty).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.StatusCount).ToList());
                        foreach (var group in groupedResult)
                        {
                            newReportBySubSourceDtos.Add(new()
                            {
                                SubSource = group.Key,
                                AllCount = group.Value?.Sum(i => i.Count) ?? 0,
                                ActiveCount = group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel" && i.BaseStatus != "invoiced").Sum(i => i.Count) ?? 0,
                                MeetingDoneCount = group.Value?.Sum(i => i.MeetingDoneCount) ?? 0,
                                OverdueCount = group.Value?.Sum(i => i.OverdueCount) ?? 0,
                                MeetingNotDoneCount = group.Value?.Sum(i => i.MeetingNotDoneCount) ?? 0,
                                SiteVisitDoneCount = group.Value?.Sum(i => i.SiteVisitDoneCount) ?? 0,
                                SiteVisitNotDoneCount = group.Value?.Sum(i => i.SiteVisitNotDoneCount) ?? 0,
                                CallbackCount = group.Value?.Where(i => i.BaseStatus == "callback" && i.SubStatus != "callback")?.Sum(i => i.Count) ?? 0,
                                BookedCount = group.Value?.Where(i => i.BaseStatus == "booked")?.Sum(i => i.Count) ?? 0,
                                NewCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "new")?.Count ?? 0,
                                DroppedCount = group.Value?.Where(i => i.BaseStatus == "dropped" && i.SubStatus != "dropped")?.Sum(i => i.Count) ?? 0,
                                MeetingScheduledCount = group.Value?.Where(i => i.BaseStatus == "meeting_scheduled" && i.SubStatus != "meeting_scheduled")?.Sum(i => i.Count) ?? 0,
                                SiteVisitScheduledCount = group.Value?.Where(i => i.BaseStatus == "site_visit_scheduled" && i.SubStatus != "site_visit_scheduled")?.Sum(i => i.Count) ?? 0,
                                NotInterestedCount = group.Value?.Where(i => i.BaseStatus == "not_interested" && i.SubStatus != "not_interested")?.Sum(i => i.Count) ?? 0,
                                PendingCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "pending")?.Count ?? 0,
                                MeetingDoneUniqueCount = group.Value?.Sum(i => i.MeetingDoneUniqueCount) ?? 0,
                                MeetingNotDoneUniqueCount = group.Value?.Sum(i => i.MeetingNotDoneUniqueCount) ?? 0,
                                SiteVisitDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0,
                                SiteVisitNotDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0,
                                BookingCancelCount = group.Value?.Where(i => i.BaseStatus == "booking_cancel")?.Sum(i => i.Count) ?? 0,
                                ExpressionOfInterestCount = group.Value?.Where(i => i.BaseStatus == "expression_of_interest")?.Sum(i => i.Count) ?? 0,
                                InvoicedLeadsCount = group.Value?.Where(i => i.BaseStatus == "invoiced")?.Sum(i => i.Count) ?? 0
                            });
                        }
                        var globalSetting = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                        byte[] fileBytes;
                        if (globalSetting?.ShouldRenameSiteVisitColumn == true)
                        {
                            var dtos = newReportBySubSourceDtos.Adapt<List<LeadsSubSourceNewFormattedDto>>();
                            fileBytes = ExcelGeneration<LeadsSubSourceNewFormattedDto>.GenerateExcel(dtos, "SubSource Reports", formattedFiltersDto, exportTracker, request.TimeZoneId, request.BaseUTcOffset);
                        }
                        else
                        {
                            var dtos = newReportBySubSourceDtos.Adapt<List<LeadsSubSourceFormattedDto>>();
                            fileBytes = ExcelGeneration<LeadsSubSourceFormattedDto>.GenerateExcel(dtos, "SubSource Reports", formattedFiltersDto, exportTracker, request.TimeZoneId, request.BaseUTcOffset);
                        }
                        //var fileBytes = ExcelHelper.CreateExcelFromList(newReportBySubSourceDtos).ToArray
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"SubSource", $"Export_Leads_SubSource_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Leads SubSource Report");
                        isSent = true;
                        tracker.Count = res.Count();
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Leads_SubSource_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }

            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadStatusReportBySubSourceHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
        public async Task ExportLeadSubStatusReportByUserHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    //CreateExcelForSubStatusnewReportRequest? request = JsonConvert.DeserializeObject<CreateExcelForSubStatusnewReportRequest>(tracker?.Request ?? string.Empty);
                    RunAWSBatchForSubStatusReportRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForSubStatusReportRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }

                            formattedFiltersDto.UserNames = userstodetails;

                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                //request.UserIds = new List<Guid>() { userId };
                                //teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();

                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadsReportDto>("LeadratBlack", "GetLeadSubStatusReportByUser", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            userstatus = (request?.UserStatus ?? 0),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize())
                        })).ToList();
                        List<SubStatusReportDto> subStatusDtos = new List<SubStatusReportDto>();
                        res.ForEach(i => subStatusDtos.Add(JsonConvert.DeserializeObject<SubStatusReportDto>(i.Report ?? string.Empty) ?? new()));
                        var groupedResult = subStatusDtos.GroupBy(i => i.User ?? new User()).ToDictionary(i => i.Key, j => j.SelectMany(i => i.Status ?? new()).ToList());
                        var customStatuses = await _customMastereadStatus.ListAsync();
                        List<Dictionary<string, object>> subStatusByUserDtos = new();

                        List<string> headers = new();

                        headers.Add("Sl No");
                        headers.Add("User Name");
                        headers.Add("All");
                        headers.Add("Active");
                        headers.Add("Overdue");

                        var tempStatus = customStatuses.Where(i => i.BaseId == null || i.BaseId == Guid.Empty);
                        var newLeadSTatus = customStatuses.Where(i => i.BaseId != null || i.BaseId != Guid.Empty);
                        var allStatus = new List<CustomMasterLeadStatus>();
                        var newLeadSTatus1 = customStatuses.Where(i => i.BaseId != null || i.BaseId != Guid.Empty);

                        var allCount = new List<Dictionary<string, int>>();

                        foreach (var status in customStatuses)
                        {
                            if (status != null)
                            {
                                if (status.Level == 0)
                                {
                                    int count = newLeadSTatus1.Count(i => i.BaseId == status.Id);

                                    var statusCountEntry = new Dictionary<string, int>
                                    {
                                        { status.DisplayName, count }
                                    };

                                    allCount.Add(statusCountEntry);
                                }
                                else
                                {
                                    var customStatus = newLeadSTatus1.Where(i => i.BaseId == status.Id).ToList();

                                    if (customStatus?.Any() ?? false)
                                    {
                                        foreach (var customStat in customStatus)
                                        {
                                            int count = newLeadSTatus1.Count(i => i.BaseId == customStat.Id);

                                            var subStatusCountEntry = new Dictionary<string, int>
                                            {
                                                { customStat.DisplayName, count }
                                            };

                                            allCount.Add(subStatusCountEntry);
                                        }
                                    }
                                }
                            }
                        }
                        foreach (var status in tempStatus)
                        {
                            if (status != null)
                            {
                                var customStatus = newLeadSTatus.Where(i => i.BaseId == status.Id).ToList();
                                allStatus.Add(status);
                                if (customStatus?.Any() ?? false)
                                {
                                    allStatus.AddRange(customStatus);
                                }
                            }
                        }
                        customStatuses = allStatus;

                        foreach (var status in customStatuses)
                        {
                            if (status?.DisplayName != null)
                            {
                                string subStatusDisplayName = (status.DisplayName[0]) + status?.DisplayName?[1..];
                                headers.Add((subStatusDisplayName));
                            }
                        }
                        int slNo = 1;

                        List<ModifiedAllReportDto> modifiedSubStatusReportDtos = new();

                        foreach (var group in groupedResult)
                        {
                            ModifiedAllReportDto reportDto = new();

                            var baseStatusWithSubStatusCount = await GetGroupedStatusAsync(group, customStatuses);
                            reportDto.BaseStatusWithSubStatusCount = baseStatusWithSubStatusCount ?? new();
                            reportDto.BaseStatusWithSubStatusCount.Add("Sl No", slNo);
                            reportDto.BaseStatusWithSubStatusCount.Add("User Name", $"{group.Key.FirstName ?? string.Empty} {group.Key.LastName ?? string.Empty}");
                            reportDto.BaseStatusWithSubStatusCount.Add("All", group.Value?.Sum(i => i.Count) ?? 0);
                            reportDto.BaseStatusWithSubStatusCount.Add("Active", group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel" && i.BaseStatus != "invoiced").Sum(i => i.Count) ?? 0);
                            reportDto.BaseStatusWithSubStatusCount.Add("Overdue", group.Value?.Sum(i => i.OverdueCount) ?? 0);
                            modifiedSubStatusReportDtos.Add(reportDto);

                            slNo++;
                        }

                        var totalCount = (await _dapperRepository.QueryStoredProcedureCountFromReadReplicaAsync("LeadratBlack", "GetLeadSubStatusReportCountByUser", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            userstatus = (request?.UserStatus ?? 0),
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize())
                        }));
                        var fileBytes = ExcelHelper.GenerateExcel(modifiedSubStatusReportDtos, headers, formattedFiltersDto, exportTracker, "Lead Sub Status Reports", allCount, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Lead_Sub_Status", $"Export_Leads_SubStatus_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Leads SubStatus Report");
                        isSent = true;
                        tracker.Count = totalCount;
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Leads_SubStatus_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadSubStatusReportByUserHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }


        private async Task<Dictionary<string, object>?> GetGroupedStatusAsync(KeyValuePair<User, List<StatusDto>> group, List<CustomMasterLeadStatus> customStatuses)
        {
            var groupedValues = group.Value?.GroupBy(i => i.BaseStatus)?.ToDictionary(i => i.Key, j => j.ToList());
            Dictionary<string, object> baseStatusWithSubStatusCount = new();

            if (groupedValues == null)
            {
                return null;
            }

            foreach (var baseStatus in groupedValues)
            {
                Dictionary<string, int> subStatus = new();

                foreach (var status in baseStatus.Value)
                {
                    var customStatus = customStatuses.FirstOrDefault(i => i.Id == status.Id);

                    if (!string.IsNullOrEmpty(customStatus?.DisplayName ?? string.Empty))
                    {
                        string subStatusDisplayName = customStatus.DisplayName;
                        subStatus.Add(subStatusDisplayName, status.Count);
                    }
                }

                if (!string.IsNullOrEmpty(baseStatus.Key ?? string.Empty))
                {
                    string baseKey = baseStatus.Key.Replace(" ", "").Replace("_", "").ToLower();
                    if (baseKey == subStatus.FirstOrDefault().Key)
                    {
                        baseStatusWithSubStatusCount.Add(baseStatus.Key, subStatus.First().Value);
                    }
                    else
                    {
                        baseStatusWithSubStatusCount.Add(baseStatus.Key, subStatus);
                    }
                }
            }

            return baseStatusWithSubStatusCount;
        }

        public async Task ExportLeadSubStatusReportBySubSourceHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    //CreateExcelForSubStatusReportBySubSourceRequest? request = JsonConvert.DeserializeObject<CreateExcelForSubStatusReportBySubSourceRequest>(tracker?.Request ?? string.Empty);
                    RunAWSBatchForSubStatusReportBySubSourceRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForSubStatusReportBySubSourceRequest>(tracker?.Request ?? string.Empty);

                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds;
                        var globalSetting = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }
                            formattedFiltersDto.UserNames = userstodetails;
                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request?.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }

                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }

                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadSubStatusBySubSourceDto>("LeadratBlack", "GetLeadSubStatusReportBySubSource", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            userstatus = (request?.UserStatus ?? 0),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize())
                        })).ToList();
                        res.ForEach(i => i.StatusDtos = JsonConvert.DeserializeObject<List<StatusDto>>(i?.Status ?? string.Empty));
                        var groupedResult = res.GroupBy(i => i?.SubSource ?? string.Empty).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.StatusDtos ?? new()).ToList());
                        List<ModifiedAllReportDto> modifiedSubStatusReportDtos = new();
                        var customStatuses = await _customMastereadStatus.ListAsync();
                        List<string> headers = new();
                        headers.Add("Sl No");
                        headers.Add("Sub Source");
                        headers.Add("All");
                        headers.Add("Active");
                        headers.Add("Overdue");
                        headers.Add("Meeting Done");
                        headers.Add("Meeting Done Unique");
                        headers.Add("Meeting Not Done");
                        headers.Add("Meeting Not Done Unique");
                        if (globalSetting?.ShouldRenameSiteVisitColumn == true)
                        {
                            headers.Add("Referral Taken");
                            headers.Add("Referral Taken Unique");
                            headers.Add("Referral Not Taken");
                            headers.Add("Referral Not Takne Unique");
                        }
                        else
                        {
                            headers.Add("Site Visit Done");
                            headers.Add("Site Visit Done Unique");
                            headers.Add("Site Visit Not Done");
                            headers.Add("Site Visit Not Done Unique");
                        }

                        var tempStatus = customStatuses.Where(i => i.BaseId == null || i.BaseId == Guid.Empty);
                        var newLeadSTatus = customStatuses.Where(i => i.BaseId != null || i.BaseId != Guid.Empty);
                        var allStatus = new List<CustomMasterLeadStatus>();
                        var newLeadSTatus1 = customStatuses.Where(i => i.BaseId != null || i.BaseId != Guid.Empty);

                        var allCount = new List<Dictionary<string, int>>();

                        foreach (var status in customStatuses)
                        {
                            if (status != null)
                            {
                                if (status.Level == 0)
                                {
                                    int count = newLeadSTatus1.Count(i => i.BaseId == status.Id);

                                    var statusCountEntry = new Dictionary<string, int>
                                    {
                                            { status.DisplayName, count }
                                    };

                                    allCount.Add(statusCountEntry);
                                }
                                else
                                {
                                    var customStatus = newLeadSTatus1.Where(i => i.BaseId == status.Id).ToList();

                                    if (customStatus?.Any() ?? false)
                                    {
                                        foreach (var customStat in customStatus)
                                        {
                                            int count = newLeadSTatus1.Count(i => i.BaseId == customStat.Id);

                                            var subStatusCountEntry = new Dictionary<string, int>
                                            {
                                                { customStat.DisplayName, count }
                                            };

                                            allCount.Add(subStatusCountEntry);
                                        }
                                    }
                                }
                            }
                        }
                        foreach (var status in tempStatus)
                        {
                            if (status != null)
                            {
                                var customStatus = newLeadSTatus.Where(i => i.BaseId == status.Id).ToList();
                                allStatus.Add(status);

                                if (customStatus?.Any() ?? false)
                                {
                                    allStatus.AddRange(customStatus);
                                }
                            }
                        }
                        customStatuses = allStatus;

                        foreach (var status in customStatuses)
                        {
                            if (status?.DisplayName != null)
                            {
                                string subStatusDisplayName = (status.DisplayName[0]) + status?.DisplayName?[1..];
                                headers.Add((subStatusDisplayName));
                            }

                        }
                        int slNo = 1;

                        foreach (var group in groupedResult)
                        {
                            ModifiedAllReportDto reportDto = new();
                            var baseStatusWithSubStatusCount = await GetGroupedStatusAsync(group, customStatuses);
                            reportDto.BaseStatusWithSubStatusCount = baseStatusWithSubStatusCount ?? new();
                            reportDto.BaseStatusWithSubStatusCount.Add("Sl No", slNo);
                            reportDto.BaseStatusWithSubStatusCount.Add("Sub Source", group.Key);
                            reportDto.BaseStatusWithSubStatusCount.Add("All", group.Value?.Sum(i => i.Count) ?? 0);
                            reportDto.BaseStatusWithSubStatusCount.Add("Active", group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel" && i.BaseStatus != "invoiced").Sum(i => i.Count) ?? 0);
                            reportDto.BaseStatusWithSubStatusCount.Add("Overdue", group.Value?.Sum(i => i.OverdueCount) ?? 0);
                            reportDto.BaseStatusWithSubStatusCount.Add("Meeting Done", group.Value?.Sum(i => i.MeetingDoneCount) ?? 0);
                            reportDto.BaseStatusWithSubStatusCount.Add("Meeting Done Unique", group.Value?.Sum(i => i.MeetingDoneUniqueCount) ?? 0);
                            reportDto.BaseStatusWithSubStatusCount.Add("Meeting Not Done", group.Value?.Sum(i => i.MeetingNotDoneCount) ?? 0);
                            reportDto.BaseStatusWithSubStatusCount.Add("Meeting Not Done Unique", group.Value?.Sum(i => i.MeetingNotDoneUniqueCount) ?? 0);
                            if (globalSetting?.ShouldRenameSiteVisitColumn == true)
                            {
                                reportDto.BaseStatusWithSubStatusCount.Add("Referral Taken", group.Value?.Sum(i => i.SiteVisitDoneCount) ?? 0);
                                reportDto.BaseStatusWithSubStatusCount.Add("Referral Taken Unique", group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0);
                                reportDto.BaseStatusWithSubStatusCount.Add("Referral Not Taken", group.Value?.Sum(i => i.SiteVisitNotDoneCount) ?? 0);
                                reportDto.BaseStatusWithSubStatusCount.Add("Referral Not Takne Unique", group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0);
                            }
                            else
                            {
                                reportDto.BaseStatusWithSubStatusCount.Add("Site Visit Done", group.Value?.Sum(i => i.SiteVisitDoneCount) ?? 0);
                                reportDto.BaseStatusWithSubStatusCount.Add("Site Visit Done Unique", group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0);
                                reportDto.BaseStatusWithSubStatusCount.Add("Site Visit Not Done", group.Value?.Sum(i => i.SiteVisitNotDoneCount) ?? 0);
                                reportDto.BaseStatusWithSubStatusCount.Add("Site Visit Not Done Unique", group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0);
                            }
                            modifiedSubStatusReportDtos.Add(reportDto);
                            slNo++;

                        }

                        var totalCount = (await _dapperRepository.QueryStoredProcedureCountFromReadReplicaAsync("LeadratBlack", "GetLeadSubStatusReportBySubSourceCount", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            userstatus = (request?.UserStatus ?? 0),
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize())
                        }));
                        var fileBytes = ExcelHelper.GenerateExcel(modifiedSubStatusReportDtos, headers, formattedFiltersDto, exportTracker, "Lead SubStatus_vs_SubSource Reports", allCount, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"SubStatus_vs_SubSource", $"Export_Leads_SubStatus_vs_SubSource_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Leads SubStatus vs SubSource Report");
                        isSent = true;
                        tracker.Count = res.Count();
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Leads_SubStatus_vs_SubSource_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }

            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadSubStatusReportBySubSourceHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }

        public async Task ExportLeadProjectReportBySubStatusHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    //CreateExcelForProjectReportBySubStatusRequest? request = JsonConvert.DeserializeObject<CreateExcelForProjectReportBySubStatusRequest>(tracker?.Request ?? string.Empty);
                    RunAWSBatchForProjectReportBySubStatusRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForProjectReportBySubStatusRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }
                            formattedFiltersDto.UserNames = userstodetails;
                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        var globalSetting = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request?.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadProjectBySubStatusDto>("LeadratBlack", "GetLeadProjectReportBySubStatus", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize())
                        })).ToList();
                        res.ForEach(i => i.StatusDtos = JsonConvert.DeserializeObject<List<StatusDto>>(i?.Status ?? string.Empty));
                        var groupedResult = res.GroupBy(i => i?.ProjectTitle ?? string.Empty).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.StatusDtos ?? new()).ToList());
                        var customStatuses = await _customMastereadStatus.ListAsync();
                        List<string> headers = new();
                        List<Dictionary<string, object>> subStatusByUserDtos = new();
                        headers.Add("Sl No");
                        headers.Add("Project Title");
                        headers.Add("All");
                        headers.Add("Active");
                        headers.Add("Overdue");
                        headers.Add("Meeting Done");
                        headers.Add("Meeting Done Unique");
                        headers.Add("Meeting Not Done");
                        headers.Add("Meeting Not Done Unique");
                        if (globalSetting?.ShouldRenameSiteVisitColumn == true)
                        {
                            headers.Add("Referral Taken");
                            headers.Add("Referral Taken Unique");
                            headers.Add("Referral Not Taken");
                            headers.Add("Referral Not Takne Unique");
                        }
                        else
                        {
                            headers.Add("Site Visit Done");
                            headers.Add("Site Visit Done Unique");
                            headers.Add("Site Visit Not Done");
                            headers.Add("Site Visit Not Done Unique");
                        }


                        var newLeadSTatus1 = customStatuses.Where(i => i.BaseId != null || i.BaseId != Guid.Empty);

                        var allCount = new List<Dictionary<string, int>>();

                        foreach (var status in customStatuses)
                        {
                            if (status != null)
                            {
                                if (status.Level == 0)
                                {
                                    int count = newLeadSTatus1.Count(i => i.BaseId == status.Id);

                                    var statusCountEntry = new Dictionary<string, int>
                                    {
                                            { status.DisplayName, count }
                                    };

                                    allCount.Add(statusCountEntry);
                                }
                                else
                                {
                                    var customStatus = newLeadSTatus1.Where(i => i.BaseId == status.Id).ToList();

                                    if (customStatus?.Any() ?? false)
                                    {
                                        foreach (var customStat in customStatus)
                                        {
                                            int count = newLeadSTatus1.Count(i => i.BaseId == customStat.Id);

                                            var subStatusCountEntry = new Dictionary<string, int>
                                            {
                                                { customStat.DisplayName, count }
                                            };

                                            allCount.Add(subStatusCountEntry);
                                        }
                                    }
                                }
                            }
                        }

                        var tempStatus = customStatuses.Where(i => i.BaseId == null || i.BaseId == Guid.Empty);
                        var newLeadSTatus = customStatuses.Where(i => i.BaseId != null || i.BaseId != Guid.Empty);
                        var allStatus = new List<CustomMasterLeadStatus>();


                        foreach (var status in tempStatus)
                        {
                            if (status != null)
                            {
                                var customStatus = newLeadSTatus.Where(i => i.BaseId == status.Id).ToList();
                                allStatus.Add(status);

                                if (customStatus?.Any() ?? false)
                                {
                                    allStatus.AddRange(customStatus);
                                }
                            }
                        }
                        customStatuses = allStatus;

                        foreach (var status in customStatuses)
                        {
                            if (status?.DisplayName != null)
                            {
                                string subStatusDisplayName = (status.DisplayName[0]) + status?.DisplayName?[1..];
                                headers.Add((subStatusDisplayName));
                            }
                        }
                        int slNo = 1;
                        List<ModifiedAllReportDto> modifiedSubStatusReportDtos = new();

                        foreach (var group in groupedResult)
                        {
                            ModifiedAllReportDto reportDto = new();

                            var baseStatusWithSubStatusCount = await GetGroupedStatusAsync(group, customStatuses);
                            reportDto.BaseStatusWithSubStatusCount = baseStatusWithSubStatusCount ?? new();
                            reportDto.BaseStatusWithSubStatusCount.Add("Sl No", slNo);
                            reportDto.BaseStatusWithSubStatusCount.Add("Project Title", group.Key);
                            reportDto.BaseStatusWithSubStatusCount.Add("All", group.Value?.Sum(i => i.Count) ?? 0);
                            reportDto.BaseStatusWithSubStatusCount.Add("Active", group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel" && i.BaseStatus != "invoiced").Sum(i => i.Count) ?? 0);
                            reportDto.BaseStatusWithSubStatusCount.Add("Overdue", group.Value?.Sum(i => i.OverdueCount) ?? 0);
                            reportDto.BaseStatusWithSubStatusCount.Add("Meeting Done", group.Value?.Sum(i => i.MeetingDoneCount) ?? 0);
                            reportDto.BaseStatusWithSubStatusCount.Add("Meeting Done Unique", group.Value?.Sum(i => i.MeetingDoneUniqueCount) ?? 0);
                            reportDto.BaseStatusWithSubStatusCount.Add("Meeting Not Done", group.Value?.Sum(i => i.MeetingNotDoneCount) ?? 0);
                            reportDto.BaseStatusWithSubStatusCount.Add("Meeting Not Done Unique", group.Value?.Sum(i => i.MeetingNotDoneUniqueCount) ?? 0);
                            if (globalSetting?.ShouldRenameSiteVisitColumn == true)
                            {
                                reportDto.BaseStatusWithSubStatusCount.Add("Referral Taken", group.Value?.Sum(i => i.SiteVisitDoneCount) ?? 0);
                                reportDto.BaseStatusWithSubStatusCount.Add("Referral Taken Unique", group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0);
                                reportDto.BaseStatusWithSubStatusCount.Add("Referral Not Taken", group.Value?.Sum(i => i.SiteVisitNotDoneCount) ?? 0);
                                reportDto.BaseStatusWithSubStatusCount.Add("Referral Not Takne Unique", group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0);
                            }
                            else
                            {
                                reportDto.BaseStatusWithSubStatusCount.Add("Site Visit Done", group.Value?.Sum(i => i.SiteVisitDoneCount) ?? 0);
                                reportDto.BaseStatusWithSubStatusCount.Add("Site Visit Done Unique", group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0);
                                reportDto.BaseStatusWithSubStatusCount.Add("Site Visit Not Done", group.Value?.Sum(i => i.SiteVisitNotDoneCount) ?? 0);
                                reportDto.BaseStatusWithSubStatusCount.Add("Site Visit Not Done Unique", group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0);
                            }
                            modifiedSubStatusReportDtos.Add(reportDto);
                            slNo++;
                        }
                        var totalCount = (await _dapperRepository.QueryStoredProcedureCountFromReadReplicaAsync("LeadratBlack", "GetLeadProjectReportBySubStatusCount", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),

                        }));
                        var fileBytes = ExcelHelper.GenerateExcel(modifiedSubStatusReportDtos, headers, formattedFiltersDto, exportTracker, "Project_vs_SubStatus Reports", allCount, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Project_vs_SubStatus", $"Export_Project_vs_SubStatus_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Project vs SubStatus Report");
                        isSent = true;
                        tracker.Count = res.Count();
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Project_vs_SubStatus_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadProjectReportBySubStatusHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }

        private async Task<Dictionary<string, object>?> GetGroupedStatusAsync(KeyValuePair<string, List<StatusDto>> group, List<CustomMasterLeadStatus> customStatuses)
        {
            var groupedValues = group.Value?.GroupBy(i => i.BaseStatus)?.ToDictionary(i => i.Key, j => j.ToList());
            Dictionary<string, object> baseStatusWithSubStatusCount = new();
            if (groupedValues == null)
            {
                return null;
            }
            foreach (var baseStatus in groupedValues)
            {
                Dictionary<string, int> subStatus = new();
                foreach (var status in baseStatus.Value)
                {
                    var customStatus = customStatuses.FirstOrDefault(i => i.Id == status.Id);
                    if (!string.IsNullOrEmpty(customStatus?.DisplayName ?? string.Empty))
                    {
                        string subStatusDisplayName = customStatus?.DisplayName ?? string.Empty;
                        subStatus.Add(subStatusDisplayName, status.Count);
                    }
                }
                if (!string.IsNullOrEmpty(baseStatus.Key ?? string.Empty))
                {
                    string baseKey = baseStatus.Key.Replace(" ", "").Replace("_", "").ToLower();
                    if (baseKey == subStatus.FirstOrDefault().Key)
                    {
                        baseStatusWithSubStatusCount.Add(baseStatus.Key, subStatus.FirstOrDefault().Value);
                    }
                    else
                    {
                        baseStatusWithSubStatusCount.Add(baseStatus.Key, subStatus);
                    }
                }
            }
            return baseStatusWithSubStatusCount;
        }

        public async Task ExportCallLogReportsHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails?.Result?.FirstName ?? string.Empty;
            string lastName = userDetails?.Result.LastName ?? string.Empty;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    RunAWSBatchForCallLogReportByUserReportRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForCallLogReportByUserReportRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds ?? new();

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName ?? string.Empty;
                                    string lastName1 = userDetailsDto.LastName ?? string.Empty;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }
                            formattedFiltersDto.UserNames = userstodetails;
                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request?.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        request.CallLogFromDate = request.CallLogFromDate.HasValue ? request.CallLogFromDate.Value.ConvertFromDateToUtc() : null;
                        request.CallLogToDate = request.CallLogToDate.HasValue ? request?.CallLogToDate.Value.ConvertToDateToUtc() : null;
                        var callLogReportDtos = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<CallLogReportDto>("LeadratBlack", "TempCallLogReport", new
                        {
                            fromdate = request?.FromDate,
                            todate = request?.ToDate,
                            datetype = request?.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrWhiteSpace(request?.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            userstatus = request?.UserStatus ?? 0,
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            agencies = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            withcalllogs = request?.IsWithCallLogsOnly ?? false,
                            calllogfromdate = request?.CallLogFromDate,
                            calllogtodate = request?.CallLogToDate,
                        }, 300)).ToList();
                        //callLogReportDtos = await UpdateTalkTimeAsync(callLogReportDtos);
                        var formattedDtos = callLogReportDtos?.Adapt<List<CallLogReportFormattedDto>>().ToList();
                        var fileBytes = ExcelGeneration<CallLogReportFormattedDto>.GenerateExcel(formattedDtos ?? new(), "Call-Log Report", formattedFiltersDto, exportTracker, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Call_Log", $"Export_Lead_Call_Log_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Lead Call Log Report");
                        isSent = true;
                        tracker.Count = callLogReportDtos?.Count();
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Lead_Call_Log_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadProjectReportBySubStatusHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
        private async Task<List<CallLogReportDto>> UpdateTalkTimeAsync(List<CallLogReportDto> callLogReportDtos)
        {
            //Converting Talk Time to Minutes
            callLogReportDtos.ForEach(i =>
            {
                i.AverageTalkTime = Math.Round(i.AverageTalkTime / 60, 2);
                i.MaxTalkTime = Math.Round(i.MaxTalkTime / 60, 2);
                i.MinTalkTime = Math.Round(i.MinTalkTime / 60, 2);
                i.TotalTalkTime = Math.Round(i.TotalTalkTime / 60, 2);
            });
            return callLogReportDtos;
        }
        private async Task SendExceptionEmailAsync(ExportReportsTracker tracker, MasterEmailTemplates errorEmailTemplate, MasterEmailServiceProvider serviceProvider)
        {
            if (errorEmailTemplate != null && serviceProvider != null)
            {
                EmailSenderDto emailSenderDto = new EmailSenderDto();
                List<string> toEmails = new();
                List<string> ccEamils = new();
                List<string> bccEamils = new();
                if (tracker?.ToRecipients?.Any() ?? false)
                {
                    toEmails.AddRange(tracker.ToRecipients);
                }
                if (tracker?.CcRecipients?.Any() ?? false)
                {
                    ccEamils.AddRange(tracker.CcRecipients);
                }
                if (tracker?.BccRecipients?.Any() ?? false)
                {
                    bccEamils.AddRange(tracker.BccRecipients);
                }
                emailSenderDto.To = toEmails;
                emailSenderDto.Cc = ccEamils;
                emailSenderDto.Bcc = bccEamils;
                emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                emailSenderDto.EmailBody = errorEmailTemplate.Body;
                emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                emailSenderDto.Subject = errorEmailTemplate?.Subject ?? string.Empty;
                await _graphEmailService.SendEmail(emailSenderDto);
            }
        }
        public async Task ExportUserActivityReportHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();

            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    //CreateExcelForActivityReportRequest? request = JsonConvert.DeserializeObject<CreateExcelForActivityReportRequest>(tracker?.Request ?? string.Empty);
                    RunAWSBatchForActivityReportRequest? requestforFileName = JsonConvert.DeserializeObject<RunAWSBatchForActivityReportRequest>(tracker?.Request ?? string.Empty);
                    if (requestforFileName != null)
                    {
                        FormattedFiltersDto? filtersDto = requestforFileName.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = requestforFileName.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();
                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);
                            string userstodetails = "";
                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;
                                    string userDetail = $"{firstName1} {lastName1}";
                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }
                            formattedFiltersDto.UserNames = userstodetails;
                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,
                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (requestforFileName.ReportPermission != null)
                        {
                            switch (requestforFileName.ReportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (requestforFileName?.UserIds?.Any() ?? false)
                        {
                            if (requestforFileName?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(requestforFileName.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = requestforFileName?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        List<UserActivityReportDto> userActivityReportDtos = new();
                        requestforFileName.FromDate = requestforFileName.FromDate.HasValue ? requestforFileName.FromDate.Value.ConvertFromDateToUtc() : DateTime.UtcNow.Date.ConvertFromDateToUtc();
                        requestforFileName.ToDate = requestforFileName.ToDate.HasValue ? requestforFileName.ToDate.Value.ConvertToDateToUtc() : DateTime.UtcNow.Date.ConvertToDateToUtc();

                        var dataFromHistory = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<UserActivityReportDto>("LeadratBlack", "Lead_GetActivityReportFromHistoryLevel1", new
                        {
                            pagesize = requestforFileName.PageSize,
                            pagenumber = requestforFileName.PageNumber,
                            from_date = requestforFileName.FromDate,
                            to_date = requestforFileName.ToDate,
                            tenant_id = tenantId,
                            user_ids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(requestforFileName.SearchText) ? null : requestforFileName.SearchText.Replace(" ", "").ToLower().Trim(),
                            userstatus = requestforFileName?.UserStatus ?? 0
                        }, 300)).ToList();
                        var task1 = _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<UserActivityReportDto>("LeadratBlack", "Lead_GetActivityReportFromHistoryLevel5", new
                        {
                            pagesize = requestforFileName.PageSize,
                            pagenumber = requestforFileName.PageNumber,
                            from_date = requestforFileName.FromDate,
                            to_date = requestforFileName.ToDate,
                            tenant_id = tenantId,
                            user_ids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(requestforFileName.SearchText) ? null : requestforFileName.SearchText.Replace(" ", "").ToLower().Trim(),
                            userstatus = requestforFileName?.UserStatus ?? 0
                        }, 300);
                        var task2 = _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<UserActivityReportDto>("LeadratBlack", "Lead_GetActivityReportFromHistoryLevel6", new
                        {
                            pagesize = requestforFileName.PageSize,
                            pagenumber = requestforFileName.PageNumber,
                            from_date = requestforFileName.FromDate,
                            to_date = requestforFileName.ToDate,
                            tenant_id = tenantId,
                            user_ids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(requestforFileName.SearchText) ? null : requestforFileName.SearchText.Replace(" ", "").ToLower().Trim(),
                            userstatus = requestforFileName?.UserStatus ?? 0
                        }, 300);
                        var task3 = _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<UserActivityReportDto>("LeadratBlack", "Lead_GetActivityReportFromHistoryLevel7", new
                        {
                            pagesize = requestforFileName.PageSize,
                            pagenumber = requestforFileName.PageNumber,
                            from_date = requestforFileName.FromDate,
                            to_date = requestforFileName.ToDate,
                            tenant_id = tenantId,
                            user_ids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(requestforFileName.SearchText) ? null : requestforFileName.SearchText.Replace(" ", "").ToLower().Trim(),
                            userstatus = requestforFileName?.UserStatus ?? 0
                        }, 300);
                        await Task.WhenAll(task1, task2, task3);
                        var userActivityReport = task1.Result.ToList();
                        var dataFromHistory2 = task2.Result.ToList();
                        var dataFromHistory3 = task3.Result.ToList();
                        userActivityReport.ForEach(data =>
                        {
                            data.NotesAddedCount = dataFromHistory2?.FirstOrDefault(i => i.UserId == data.UserId)?.NotesAddedCount ?? 0;
                            data.NotesAddedLeadsCount = dataFromHistory2?.FirstOrDefault(i => i.UserId == data.UserId)?.NotesAddedLeadsCount ?? 0;
                            data.StatusEditsCount = dataFromHistory3?.FirstOrDefault(i => i.UserId == data.UserId)?.StatusEditsCount ?? 0;
                            data.StatusEditsLeadsCount = dataFromHistory3?.FirstOrDefault(i => i.UserId == data.UserId)?.StatusEditsLeadsCount ?? 0;
                        });

                        var userCommunicationReport = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<UserActivityReportDto>("LeadratBlack", "Lead_GetActivityReportFromLeadCommunicationsLevel3", new
                        {
                            pagesize = requestforFileName.PageSize,
                            pagenumber = requestforFileName.PageNumber,
                            from_date = requestforFileName.FromDate,
                            to_date = requestforFileName.ToDate,
                            tenant_id = tenantId,
                            user_ids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(requestforFileName.SearchText) ? null : requestforFileName.SearchText.Replace(" ", "").ToLower().Trim(),
                            userstatus = requestforFileName?.UserStatus ?? 0
                        }, 300)).ToList();

                        dataFromHistory = dataFromHistory.Adapt(userCommunicationReport);
                        var userIdsToTake = dataFromHistory.Select(i => i.UserId).ToList();
                        var userIds = dataFromHistory.Select(i => i.UserId.ToString()).ToList();
                        var users = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
                        List<ActivityTotalCountFormatDto> totalCountFormatDtos = new();
                        var logDtos = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<AttendanceLogReportDto>("LeadratBlack", "Lead_GetActivityReportFromAttendanceLevel4", new
                        {
                            user_ids = userIdsToTake,
                            tenant_id = tenantId,
                            from_date = requestforFileName.FromDate,
                            to_date = requestforFileName.ToDate
                        }, 300)).ToList();

                        var dataFromLeadAppointment = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<UserActivityReportDto>("LeadratBlack", "Lead_GetActivityReportFromLeadAppointmentLevel4", new
                        {
                            pagesize = requestforFileName.PageSize,
                            pagenumber = requestforFileName.PageNumber,
                            user_ids = userIdsToTake,
                            tenant_id = tenantId,
                            from_date = requestforFileName.FromDate,
                            to_date = requestforFileName.ToDate
                        }, 300)).ToList();

                        dataFromHistory = dataFromHistory.Adapt(dataFromLeadAppointment);
                        dataFromHistory.ForEach(i =>
                        {
                            var logsForCurrentUser = logDtos.Where(log => log.UserId == i.UserId).OrderByDescending(l => l.CreatedOn).ToList();
                            var logsByDay = GetLogsByDateRange(logsForCurrentUser, requestforFileName.FromDate.Value, requestforFileName.ToDate.Value);
                            i.AverageWorkingHours = GetWorkingHoursByUser(logsByDay);
                            i.MeetingDoneCount = dataFromLeadAppointment.Where(app => app.UserId == i.UserId)?.FirstOrDefault()?.MeetingDoneCount ?? 0;
                            i.MeetingDoneUniqueCount = dataFromLeadAppointment.Where(app => app.UserId == i.UserId)?.FirstOrDefault()?.MeetingDoneUniqueCount ?? 0;
                            i.SiteVisitDoneUniqueCount = dataFromLeadAppointment.Where(app => app.UserId == i.UserId)?.FirstOrDefault()?.SiteVisitDoneUniqueCount ?? 0;
                            i.SiteVisitDoneCount = dataFromLeadAppointment.Where(app => app.UserId == i.UserId)?.FirstOrDefault()?.SiteVisitDoneCount ?? 0;
                            i.CallsInitiatedCount = userCommunicationReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.CallsInitiatedCount ?? 0;
                            i.CallsInitiatedLeadsCount = userCommunicationReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.CallsInitiatedLeadsCount ?? 0;
                            i.WhatsAppInitiatedCount = userCommunicationReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.WhatsAppInitiatedCount ?? 0;
                            i.WhatsAppInitiatedLeadsCount = userCommunicationReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.WhatsAppInitiatedLeadsCount ?? 0;
                            i.EmailsInitiatedCount = userCommunicationReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.EmailsInitiatedCount ?? 0;
                            i.EmailsInitiatedLeadsCount = userCommunicationReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.EmailsInitiatedLeadsCount ?? 0;
                            i.SMSInitiatedCount = userCommunicationReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.SMSInitiatedCount ?? 0;
                            i.SMSInitiatedLeadsCount = userCommunicationReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.SMSInitiatedLeadsCount ?? 0;
                            i.FormEditsCount = userActivityReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.FormEditsCount ?? 0;
                            i.FormEditsLeadsCount = userActivityReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.FormEditsLeadsCount ?? 0;
                            i.StatusEditsCount = userActivityReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.StatusEditsCount ?? 0;
                            i.StatusEditsLeadsCount = userActivityReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.StatusEditsLeadsCount ?? 0;
                            i.NotesAddedCount = userActivityReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.NotesAddedCount ?? 0;
                            i.NotesAddedLeadsCount = userActivityReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.NotesAddedLeadsCount ?? 0;
                        });
                       // var leadActivityDto = dataFromHistory.Adapt<List<ActivityFormattedDto>>();
                        List<DataActivityFormattedDto> dataActivityDto = new();
                        var globalSetting = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                        if (requestforFileName.ShouldShowDataReport == true)
                        {
                            var dataActivityFromHistory = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<DataActivityReportDto>("LeadratBlack", "GetActivityReportFromDataManagement", new
                            {
                                pagesize = requestforFileName.PageSize,
                                pagenumber = requestforFileName.PageNumber,
                                from_date = requestforFileName.FromDate,
                                to_date = requestforFileName.ToDate,
                                tenant_id = tenantId,
                                user_ids = teamUserIds,
                                searchtext = string.IsNullOrEmpty(requestforFileName.SearchText) ? null : requestforFileName.SearchText.Replace(" ", "").ToLower().Trim(),
                                userstatus = (requestforFileName?.UserStatus ?? 0),
                            }, 300)).ToList();
                            var dataFromCommunication = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<DataActivityReportDto>("LeadratBlack", "GetActivityReportFromProspectCommunication", new
                            {
                                pagesize = requestforFileName.PageSize,
                                pagenumber = requestforFileName.PageNumber,
                                from_date = requestforFileName.FromDate,
                                to_date = requestforFileName.ToDate,
                                tenant_id = tenantId,
                                user_ids = teamUserIds,
                                searchtext = string.IsNullOrEmpty(requestforFileName.SearchText) ? null : requestforFileName.SearchText.Replace(" ", "").ToLower().Trim(),
                                userstatus = requestforFileName?.UserStatus ?? 0,
                            }, 300)).ToList();

                            dataActivityFromHistory.ForEach(i =>
                            {
                                i.CallsInitiatedCount = dataFromCommunication.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.CallsInitiatedCount ?? 0;
                                i.CallsInitiatedDataCount = dataFromCommunication.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.CallsInitiatedDataCount ?? 0;
                                i.WhatsAppInitiatedCount = dataFromCommunication.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.WhatsAppInitiatedCount ?? 0;
                                i.WhatsAppInitiatedDataCount = dataFromCommunication.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.WhatsAppInitiatedDataCount ?? 0;
                            });
                            dataActivityDto = dataActivityFromHistory.Adapt<List<DataActivityFormattedDto>>();
                            userIdsToTake.ForEach(i =>
                            {
                                var dataActivity = dataActivityFromHistory.FirstOrDefault(j => j.UserId == i);
                                var leadActivity = dataFromHistory.FirstOrDefault(j => j.UserId == i);
                                ActivityTotalCountFormatDto dto = new();
                                dto.CallCount = (leadActivity?.CallsInitiatedCount ?? 0) + (dataActivity?.CallsInitiatedCount ?? 0);
                                dto.UniqueCallCount = (leadActivity?.CallsInitiatedLeadsCount ?? 0) + (dataActivity?.CallsInitiatedDataCount ?? 0);
                                dto.WhatsAppCount = (leadActivity?.WhatsAppInitiatedCount ?? 0) + (dataActivity?.WhatsAppInitiatedCount ?? 0);
                                dto.UniqueWhatsAppCount = (leadActivity?.WhatsAppInitiatedLeadsCount ?? 0) + (dataActivity?.WhatsAppInitiatedDataCount ?? 0);
                                totalCountFormatDtos.Add(dto);
                            });
                        }

                        byte[] fileBytes;
                        if (globalSetting?.ShouldRenameSiteVisitColumn == true)
                        {
                            var leadActivityDto = dataFromHistory.Adapt<List<ActivityNewFormattedDto>>();
                            if (requestforFileName.ShouldShowDataReport == true)
                            {
                                fileBytes = ExcelGeneration<ActivityNewFormattedDto, DataActivityFormattedDto, ActivityTotalCountFormatDto>.GenerateExcel(leadActivityDto, dataActivityDto, totalCountFormatDtos, "LeadActivity Report", "DataActivity Report","Total Counts", formattedFiltersDto, exportTracker, requestforFileName.TimeZoneId, requestforFileName.BaseUTcOffset);
                            }
                            else
                            {
                                fileBytes = ActivityExcelHelper<ActivityNewFormattedDto>.GenerateExcel(leadActivityDto, "Activity Reports", formattedFiltersDto, exportTracker, requestforFileName.TimeZoneId, requestforFileName.BaseUTcOffset);

                            }
                        }
                        else
                        {
                            var leadActivityDto = dataFromHistory.Adapt<List<ActivityFormattedDto>>();
                            if (requestforFileName.ShouldShowDataReport == true)
                            {
                                fileBytes = ExcelGeneration<ActivityFormattedDto, DataActivityFormattedDto, ActivityTotalCountFormatDto>.GenerateExcel(leadActivityDto, dataActivityDto, totalCountFormatDtos, "LeadActivity Report", "DataActivity Report","Total Counts", formattedFiltersDto, exportTracker, requestforFileName.TimeZoneId, requestforFileName.BaseUTcOffset);
                            }
                            else
                            {
                                fileBytes = ActivityExcelHelper<ActivityFormattedDto>.GenerateExcel(leadActivityDto, "Activity Reports", formattedFiltersDto, exportTracker, requestforFileName.TimeZoneId, requestforFileName.BaseUTcOffset);

                            }
                        }
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(requestforFileName.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Activity", $"Export_Lead_Activity_Reports_" + input.TenantId + requestforFileName.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Lead Activity Report");
                        isSent = true;
                        tracker.Count = dataFromHistory.Count();
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Lead_Activity_Reports_" + requestforFileName.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }

            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadStatusReportByUserHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
        private static TimeSpan GetWorkingHoursByUser(List<LogsByDayReportDto> logsByDayDtos)
        {
            var timeSpanList = logsByDayDtos.Where(i => i != null && i.WorkingHours != null).Select(i => i.WorkingHours).ToList();
            TimeSpan totalHours = new();

            int noOfDays = 0;
            timeSpanList.ForEach(i =>
            {
                if (i != null)
                {
                    totalHours += i.Value;
                    noOfDays++;
                }
            });
            return totalHours / noOfDays;
        }
        private static List<LogsByDayReportDto> GetLogsByDateRange(List<AttendanceLogReportDto> userEntries, DateTime fromDate, DateTime toDate)
        {
            List<LogsByDayReportDto> logsByDay = new();
            while (fromDate <= toDate)
            {
                var startDate = fromDate;
                var endDate = fromDate.AddDays(1).AddSeconds(-1);
                var entriesPerDay = userEntries.Where(i => (i.ClockInTime >= startDate && i.ClockInTime <= endDate)
                                                               || (i.ClockOutTime >= startDate && i.ClockOutTime <= endDate));
                logsByDay.Add(new LogsByDayReportDto()
                {
                    Day = endDate.Date,
                    logDtos = entriesPerDay.ToList(),
                    WorkingHours = entriesPerDay.Any(i => i.ClockOutTime == null) ? TimeSpan.Zero : GetWorkingHours(entriesPerDay.ToList()),
                    AreSwipesMissing = entriesPerDay.Any(i => i.ClockOutTime == null) ? true : false
                });
                fromDate = fromDate.AddDays(1);
            }
            return logsByDay;
        }
        private static TimeSpan GetWorkingHours(List<AttendanceLogReportDto> logDtos)
        {
            TimeSpan hours = new();
            foreach (var dto in logDtos)
            {
                if (dto.ClockInTime != null && dto.ClockOutTime != null)
                {
                    var time = (dto.ClockOutTime.Value) - (dto.ClockInTime.Value);
                    hours = hours + time;
                }
            }
            return hours;
        }

        public async Task ExportLeadDatewiseSourceCountHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    RunAWSBatchForDatewiseSourceReportRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForDatewiseSourceReportRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }

                            formattedFiltersDto.UserNames = userstodetails;

                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (!isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ReportPermission != null)
                        {
                            switch (request.ReportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {

                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }

                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertFromDateToUtc() : null;
                        request.FromDateForLeadReceived = request.FromDateForLeadReceived.HasValue ? request.FromDateForLeadReceived.Value.ConvertFromDateToUtc() : null;
                        request.ToDateForLeadReceived = request.ToDateForLeadReceived.HasValue ? request.ToDateForLeadReceived.Value.ConvertToDateToUtc() : null;
                        IEnumerable<LeadReceivedBySourceDto> result = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadReceivedBySourceDto>("LeadratBlack", "GetOut", new

                        {
                            lead_source = request?.LeadSources?.ConvertAll(i => (int)i),
                            user_id = teamUserIds,
                            date_type = request?.DateType,
                            from_date = request?.FromDate,
                            to_date = request?.ToDate,
                            from_date_for_lead_recived = request?.FromDateForLeadReceived,
                            to_date_for_lead_recived = request.ToDateForLeadReceived,
                            tenant_id = tenantId,
                            lead_generating_from = (request?.GeneratingFrom ?? 0),
                            projects = request?.Projects,
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "")),
                            searchtext = request?.SearchText,
                            pagesize = request?.PageSize,
                            pagenumber = 1,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize())

                        }));
                        Dictionary<string, int> topSources = GroupLeadsBySource(result);


                        var groupedLeadsByDay = GroupLeadsByDay(result, topSources);

                        int offset = (request.PageSize * (request.PageNumber - 1));

                        var pagedGroupedLeadsByDate = groupedLeadsByDay
                        .OrderByDescending(dictionary => dictionary["CreatedOn"])
                        .Skip(offset)
                        .Take(request.PageSize)
                        .ToList();

                        var convertedItems = pagedGroupedLeadsByDate.Select(dayGroup =>
                        {
                            var convertedGroup = new Dictionary<string, int>();

                            foreach (var kvp in dayGroup)
                            {
                                if (kvp.Key == "CreatedOn")
                                    continue;

                                convertedGroup.Add(kvp.Key, (int)kvp.Value);
                            }

                            var resultDictionary = new Dictionary<string, object>();
                            resultDictionary.Add("CreatedOn", (string)dayGroup["CreatedOn"]);

                            foreach (var kvp in convertedGroup)
                            {
                                resultDictionary.Add(kvp.Key, kvp.Value);
                            }

                            return resultDictionary;
                        });

                        var convertedDateWiseSourceList = pagedGroupedLeadsByDate.Select((dayGroup, index) =>
                        {
                            var dateWiseSourceDto = new DateWiseSourceDto();
                            dateWiseSourceDto.SlNo = (index + 1).ToString();

                            if (dayGroup.ContainsKey("CreatedOn") && dayGroup["CreatedOn"] != null)
                            {
                                if (DateTime.TryParseExact(dayGroup["CreatedOn"].ToString(), "yyyy/MM/dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime createdOn))
                                {
                                    dateWiseSourceDto.CreatedOn = createdOn;

                                    foreach (var propertyInfo in typeof(DateWiseSourceDto).GetProperties())
                                    {
                                        if (propertyInfo.Name != "SlNo" && propertyInfo.Name != "CreatedOn")
                                        {
                                            var leadSource = (LeadSource)Enum.Parse(typeof(LeadSource), propertyInfo.Name);
                                            var leadCount = GetValueOrDefault(dayGroup, leadSource.ToString());
                                            propertyInfo.SetValue(dateWiseSourceDto, leadCount);
                                        }
                                    }
                                }
                            }
                            return dateWiseSourceDto;

                        }).ToList();

                        var fileBytes = ExcelGeneration<DateWiseSourceDto>.GenerateExcel(convertedDateWiseSourceList, "Date vs Source", formattedFiltersDto, exportTracker, request.TimeZoneId, request.BaseUTcOffset);
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Date_vs_Source", $"Export_Lead_Date_vs_Source_" + input.TenantId + request.FileName + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Received Date By Source Report");
                        isSent = true;
                        tracker.Count = convertedDateWiseSourceList.Count();
                        tracker.S3BucketKey = presignedUrl;
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        tracker.FileName = $"Export_Lead_Date_vs_Source_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),

                    ErrorModule = "FunctionEntryPoint -> ExportLeadDatewiseSourceCountHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }

        }
        public static List<Dictionary<string, object>> GroupLeadsByDay(IEnumerable<LeadReceivedBySourceDto> leadReportByYear, Dictionary<string, int> topSources)
        {
            var groupedLeadsByDay = leadReportByYear
                .GroupBy(lead => lead.CreatedOn.Date.ToString("yyyy/MM/dd"))
                .Select(dayGroup =>
                {
                    var dayDictionary = new Dictionary<string, object>();
                    dayDictionary.Add("CreatedOn", dayGroup.Key);
                    foreach (var source in topSources)
                    {
                        dayDictionary.Add(source.Key, dayGroup.Sum(lead => lead.Source == (LeadSource)Enum.Parse(typeof(LeadSource), source.Key) ? lead.LeadCount : 0));
                    }
                    return dayDictionary;
                })
                .ToList();
            return groupedLeadsByDay;
        }
        private static Dictionary<string, int> GroupLeadsBySource(IEnumerable<LeadReceivedBySourceDto> leadReport)
        {
            return leadReport
                .Select(lead => new { Source = lead.Source.ToString(), LeadCount = lead.LeadCount })
                .GroupBy(item => item.Source)
                .ToDictionary(group => group.Key, group => group.Sum(item => item.LeadCount))
                .OrderByDescending(item => item.Value)
                .Take(Enum.GetValues(typeof(LeadSource)).Length)
                .ToDictionary(item => item.Key, item => item.Value);
        }
        private static int GetValueOrDefault(Dictionary<string, object> dictionary, string key)
        {
            return dictionary.TryGetValue(key, out var value) && value != null && int.TryParse(value.ToString(), out var intValue) ? intValue : 0;
        }

        public async Task ExportUserVsSourceReportHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();

            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    RunAWSBatchForUserVsSourceReportRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForUserVsSourceReportRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }
                            formattedFiltersDto.UserNames = userstodetails;


                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        List<UserSourceReportDto> userSourceReportDtos = new List<UserSourceReportDto>();
                        List<SourceReportByUserDto> newReportByUserDtos = new List<SourceReportByUserDto>();
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        var result = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadsReportDto>("LeadratBlack", "GetSourceByUserReport", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            userstatus = (request?.UserStatus ?? 0),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize())
                        })).ToList();
                        result.ForEach(i => userSourceReportDtos.Add(JsonConvert.DeserializeObject<UserSourceReportDto>(i.Report ?? string.Empty) ?? new UserSourceReportDto()));
                        var groupedResult = userSourceReportDtos.GroupBy(i => i?.User ?? new User()).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.Source).ToList());
                        foreach (var group in groupedResult)
                        {
                            newReportByUserDtos.Add(new()
                            {
                                UserId = group.Key.UserId,
                                FirstName = group.Key.FirstName,
                                LastName = group.Key.LastName,
                                Source = group.Value,

                            });
                        }
                        var masterLeadSource = await _masterLeadSource.ListAsync();
                        var source = masterLeadSource?.Where(i => !string.IsNullOrWhiteSpace(i.DisplayName)).Select(i => i.DisplayName)?.ToList();
                        var headers = new List<DataHeadersDto>();
                        headers.Add(new DataHeadersDto() { Header = "SlNo" });
                        headers.Add(new DataHeadersDto() { Header = "Name" });
                        if (source?.Any() ?? false)
                        {
                            source.ForEach(i =>
                            {
                                if (i == "Data")
                                {
                                    headers.Add(new DataHeadersDto()
                                    {
                                        Header = i + " "
                                    });
                                }
                                else
                                {
                                    headers.Add(new DataHeadersDto()
                                    {
                                        Header = i
                                    });
                                }
                            });
                        }
                        var dtos = newReportByUserDtos.Adapt<List<FormattedUserSourceReportDto>>();
                        var fileBytes = Lrb.Application.Data.Utils.DataExcelHelper.ExcelGeneration<FormattedUserSourceReportDto>.GenerateExcel(dtos, "User Vs Source", formattedFiltersDto, exportTracker, headers, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"User_Vs_Source", $"Export_Leads_User_vs_Source_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "User Vs Source Report");
                        isSent = true;
                        tracker.Count = result.Count();
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Leads_User_vs_Source_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportUserVsSourceReportHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
        public async Task ExportUserVsSubSourceReportHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;
            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    RunAWSBatchForUserVsSubSourceReportRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForUserVsSubSourceReportRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }

                            formattedFiltersDto.UserNames = userstodetails;

                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        List<UserSourceReportDto> userSourceReportDtos = new List<UserSourceReportDto>();
                        List<SourceReportByUserDto> newReportByUserDtos = new List<SourceReportByUserDto>();
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        var result = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadsReportDto>("LeadratBlack", "GetSourceByUserReportSubsource", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            userstatus = (request?.UserStatus ?? 0),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize())
                        }, 300)).ToList();
                        result.ForEach(i => userSourceReportDtos.Add(JsonConvert.DeserializeObject<UserSourceReportDto>(i.Report ?? string.Empty) ?? new UserSourceReportDto()));
                        var groupedResult = userSourceReportDtos.GroupBy(i => i?.User ?? new User()).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.Source).ToList());

                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<UserVsSourceReportDto>("LeadratBlack", "UserVsSourceReports", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            sources = request?.Sources?.ConvertAll(i => (int)i),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            userstatus = (request?.UserStatus ?? 0),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            pagenumber = request.PageNumber,
                            pagesize = request.PageSize,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize())
                        }, 300)).ToList();

                        res.ForEach(i => i.SourcesDtos = JsonConvert.DeserializeObject<List<SourceDtos>>(i?.Source ?? string.Empty));
                        var groupedResultSubSource = res.GroupBy(i => i?.UserId ?? Guid.Empty).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.SourcesDtos ?? new List<SourceDtos>()).ToList());

                        List<Dictionary<string, object>> subStatusByUserDtos = new();
                        List<string> headers = new();
                        headers.Add("Sl No");
                        headers.Add("User Name");

                        var distinctLeadSourcesData = groupedResultSubSource.Values.SelectMany(sources => sources)
                            .Where(e => !string.IsNullOrEmpty(e.SubSource))
                            .GroupBy(e => e.BaseSource)
                            .Select(g => new
                            {
                                LeadSource = g.Key,
                                SubSourceCount = g.Select(e => e.SubSource).Distinct().Count()
                            })
                            .ToList();

                        var allCount = new List<Dictionary<string, int>>();

                        foreach (var source in distinctLeadSourcesData)
                        {
                            string leadSourceAsString = source.LeadSource.ToString();
                            int count = source.SubSourceCount;
                            var sourceCountEntry = new Dictionary<string, int>
                            {
                              { leadSourceAsString, count }
                            };
                            allCount.Add(sourceCountEntry);
                        }

                        Dictionary<string, List<string>> leadSourceSubSources = new Dictionary<string, List<string>>();

                        foreach (var status in groupedResultSubSource.Values.SelectMany(sources => sources))
                        {
                            if (status?.BaseSource != null)
                            {
                                string leadSourceAsString = status.BaseSource.ToString();
                                string leadSourceKey = leadSourceAsString[0] + leadSourceAsString[1..];
                                string subSource = status.SubSource;

                                if (!leadSourceSubSources.ContainsKey(leadSourceKey))
                                {
                                    leadSourceSubSources[leadSourceKey] = new List<string>();
                                }

                                if (!string.IsNullOrEmpty(subSource) && !leadSourceSubSources[leadSourceKey].Contains(subSource))
                                {
                                    leadSourceSubSources[leadSourceKey].Add(subSource);
                                }
                            }
                        }

                        foreach (var sources in leadSourceSubSources)
                        {
                            string leadSource = sources.Key;
                            List<string> subSources = sources.Value;

                            headers.Add(leadSource);

                            bool concatenatedSubSourceAdded = false;
                            foreach (var subSource in subSources)
                            {
                                if (!concatenatedSubSourceAdded)
                                {
                                    string concatenatedSubSource = leadSource + "subSource" + "(" + leadSource + ")";
                                    headers.Add(concatenatedSubSource);
                                    concatenatedSubSourceAdded = true;
                                }
                                string name = subSource + "(" + leadSource + ")";
                                string sanitizedSubStatusDisplayName = name.Replace(" ", "");
                                headers.Add(sanitizedSubStatusDisplayName);
                            }
                        }

                        int slNo = 1;
                        List<ModifiedAllReportDto> modifiedSubStatusReportDtos = new();

                        foreach (var group in res.GroupBy(i => i?.UserId ?? Guid.Empty))
                        {
                            bool outerLoopContinue = false;
                            foreach (var groupSubSource in groupedResultSubSource)
                            {
                                if (group.Key == groupSubSource.Key)
                                {
                                    ModifiedAllReportDto reportDto = new();
                                    var baseSourceWithSubSourceCount = await GetGroupedStatusAsync1(groupSubSource, groupedResultSubSource.Values.SelectMany(sources => sources).ToList());
                                    reportDto.BaseStatusWithSubStatusCount = baseSourceWithSubSourceCount ?? new();
                                    reportDto.BaseStatusWithSubStatusCount.Add("Sl No", slNo);
                                    reportDto.BaseStatusWithSubStatusCount.Add("User Name", $"{group.First().UserName ?? string.Empty}");

                                    modifiedSubStatusReportDtos.Add(reportDto);
                                    outerLoopContinue = true;
                                    break;
                                }
                            }
                            if (!outerLoopContinue)
                            {
                            }
                            slNo++;
                        }


                        var fileBytes = ExcelHelper.GenerateExcel(modifiedSubStatusReportDtos, headers, formattedFiltersDto, exportTracker, "User Vs SubSource Report", allCount, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"User_vs_SubSource", $"Export_Leads_User_vs_SubSource_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "User Vs Sub Source Report");
                        isSent = true;
                        tracker.Count = modifiedSubStatusReportDtos.Count();
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Leads_User_vs_SubSource_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportUserVsSubSourceReportHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
        private async Task<Dictionary<string, object>?> GetGroupedStatusAsync1(KeyValuePair<Guid, List<SourceDtos>> group, List<SourceDtos> allSubSources)
        {
            var groupedValues = group.Value?.GroupBy(i => i.BaseSource)?.ToDictionary(i => i.Key, j => j.ToList());
            Dictionary<string, object> baseSourceWithSubSourceCount = new();

            if (groupedValues == null)
            {
                return null;
            }

            foreach (var source in groupedValues)
            {
                Dictionary<string, int> subSource = new();

                foreach (var subSources in source.Value)
                {
                    string subSourceName = string.IsNullOrEmpty(subSources.SubSource)
                        ? source.Key + "subSource" + "(" + source.Key + ")"
                        : subSources.SubSource + "(" + source.Key + ")";

                    var customStatus = allSubSources.FirstOrDefault(i => i.SubSource == subSourceName);
                    string subStatusDisplayName = customStatus != null ? customStatus.SubSource : subSourceName;
                    string sanitizedSubStatusDisplayName = subStatusDisplayName.Replace(" ", "");

                    if (subSource.ContainsKey(sanitizedSubStatusDisplayName))
                    {
                        subSource[sanitizedSubStatusDisplayName] += subSources.Count;
                    }
                    else
                    {
                        subSource.Add(sanitizedSubStatusDisplayName, subSources.Count);
                    }
                }

                if (source.Key != null)
                {
                    string sanitizedSourceKey = source.Key.Replace("_", "");

                    if (subSource.Count == 1 && subSource.ContainsKey(sanitizedSourceKey))
                    {
                        baseSourceWithSubSourceCount[source.Key] = subSource.FirstOrDefault().Value;
                    }
                    else
                    {
                        baseSourceWithSubSourceCount[source.Key] = subSource;
                    }
                }
            }

            return baseSourceWithSubSourceCount;
        }
        private static bool IsSimpleProperty(PropertyInfo property)
        {
            Type propertyType = property.PropertyType;
            return propertyType.IsPrimitive || propertyType == typeof(string) || propertyType == typeof(TimeOnly?);

        }
        private async Task ProcessReportsAutomationAsync(string presignedUrl, ExportReportsTracker tracker, ReportsConfigurationDto? reportsConfiguration = null, string? email = null, string? reportName = null)
        {
            GlobalSettings? globalSettings = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec());
            if (reportsConfiguration != null && (reportsConfiguration.ServiceType?.Any() ?? false) && reportsConfiguration.ServiceType.Contains(ServiceType.Email) && reportsConfiguration.ServiceType.Contains(ServiceType.WhatsApp))
            {
                //sending email
                try
                {
                    var serviceProvider = (await _masterEmailServiceProviderRepo.FirstOrDefaultAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None));
                    var errorEmailTemplate = (await _masterEmailTemplatesRepo.FirstOrDefaultAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ErrorMessage), CancellationToken.None));
                    var exportEmailTemplate = (await _masterEmailTemplatesRepo.FirstOrDefaultAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ExportLead), CancellationToken.None));
                    EmailSenderDto emailSenderDto = new EmailSenderDto();
                    List<string> toEmails = new();
                    List<string> ccEamils = new();
                    List<string> bccEamils = new();
                    if (tracker?.ToRecipients?.Any() ?? false)
                    {
                        toEmails.AddRange(tracker.ToRecipients);
                    }
                    if (tracker?.CcRecipients?.Any() ?? false)
                    {
                        ccEamils.AddRange(tracker.CcRecipients);
                    }
                    if (tracker?.BccRecipients?.Any() ?? false)
                    {
                        bccEamils.AddRange(tracker.BccRecipients);
                    }
                    var template = ExportLeadHelper.ReplaceVariables(exportEmailTemplate?.Body ?? string.Empty, new Dictionary<string, string>() { { string.Format("#PresignedUrl#"), presignedUrl } });
                    emailSenderDto.To = toEmails;
                    emailSenderDto.Cc = ccEamils;
                    emailSenderDto.Bcc = bccEamils;
                    emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                    emailSenderDto.EmailBody = template;
                    emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                    emailSenderDto.Subject = exportEmailTemplate?.Subject ?? string.Empty;
                    await _graphEmailService.SendEmail(emailSenderDto.SenderEmailAddress, emailSenderDto.Subject,
                        emailSenderDto.EmailBody, emailSenderDto.To, emailSenderDto.Cc, emailSenderDto.Bcc, null, emailSenderDto.BodyType);
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "ProcessSendExportEmailandWhatsAppAsync -> SendEmail()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
                //sending whatsapp template
                if (reportsConfiguration.WATemplateId != null)
                {
                    var waTemplate = await _masterWatemplateRepo.FirstOrDefaultAsync(new GetMasterWATemplateWithWAApiInfoByIdSpec(reportsConfiguration.WATemplateId ?? Guid.Empty), CancellationToken.None);
                    var obj = waTemplate?.MasterWAApiInfo;
                    if (obj != null)
                    {
                        WAApiInfoDto wrapperDto = obj.Adapt<WAApiInfoDto>();
                        await CallWhatsAppAPIAsync(wrapperDto, reportsConfiguration.PhoneNumbers, waTemplate, presignedUrl, reportName);
                    }
                }
            }
            else if (reportsConfiguration != null && (reportsConfiguration.ServiceType?.Any() ?? false) && reportsConfiguration.ServiceType.Contains(ServiceType.WhatsApp))
            {
                if (reportsConfiguration.WATemplateId != null)
                {
                    var waTemplate = await _masterWatemplateRepo.FirstOrDefaultAsync(new GetMasterWATemplateWithWAApiInfoByIdSpec(reportsConfiguration.WATemplateId ?? Guid.Empty), CancellationToken.None);
                    var obj = waTemplate?.MasterWAApiInfo;
                    if (obj != null)
                    {
                        WAApiInfoDto wrapperDto = obj.Adapt<WAApiInfoDto>();
                        await CallWhatsAppAPIAsync(wrapperDto, reportsConfiguration.PhoneNumbers, waTemplate, presignedUrl, reportName);
                    }
                }
            }
            else
            {
                tracker.ToRecipients = new List<string> { email };
                var serviceProvider = (await _masterEmailServiceProviderRepo.FirstOrDefaultAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None));
                var errorEmailTemplate = (await _masterEmailTemplatesRepo.FirstOrDefaultAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ErrorMessage), CancellationToken.None));
                var exportEmailTemplate = (await _masterEmailTemplatesRepo.FirstOrDefaultAsync(new GetMasterEmailTemplatesByEventSpec(Lrb.Domain.Enums.Event.ExportLead), CancellationToken.None));
                EmailSenderDto emailSenderDto = new EmailSenderDto();
                List<string> toEmails = new();
                List<string> ccEamils = new();
                List<string> bccEamils = new();
                if (tracker?.ToRecipients?.Any() ?? false)
                {
                    toEmails.AddRange(tracker.ToRecipients);
                }
                if (tracker?.CcRecipients?.Any() ?? false)
                {
                    ccEamils.AddRange(tracker.CcRecipients);
                }
                if (tracker?.BccRecipients?.Any() ?? false)
                {
                    bccEamils.AddRange(tracker.BccRecipients);
                }
                var template = ExportLeadHelper.ReplaceVariables(exportEmailTemplate?.Body ?? string.Empty, new Dictionary<string, string>() { { string.Format("#PresignedUrl#"), presignedUrl } });
                emailSenderDto.To = toEmails;
                emailSenderDto.Cc = ccEamils;
                emailSenderDto.Bcc = bccEamils;
                emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                emailSenderDto.EmailBody = template;
                emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                emailSenderDto.Subject = exportEmailTemplate?.Subject ?? string.Empty;
                await _graphEmailService.SendEmailAsync(emailSenderDto.SenderEmailAddress, emailSenderDto.Subject,
                    emailSenderDto.EmailBody, emailSenderDto.To, emailSenderDto.Cc, emailSenderDto.Bcc, null, emailSenderDto.BodyType);
            }
        }
        public async Task CallWhatsAppAPIAsync(WAApiInfoDto wAApiInfo, List<string>? phoneNumbers, MasterWATemplate template, string presignedUrl, string reportName)
        {
            (string, bool) result = new("", false);
            if (!string.IsNullOrEmpty(wAApiInfo.URL) && (phoneNumbers?.Any() ?? false))
            {
                phoneNumbers = phoneNumbers.Distinct().ToList();
                _logger.Information("PhoneNumbers " + string.Join(" ", phoneNumbers));
                foreach (var phoneNumber in phoneNumbers)
                {
                    if (wAApiInfo.MethodType?.Equals("Get", StringComparison.InvariantCultureIgnoreCase) ?? false)
                    {
                        var request = new RestRequest(wAApiInfo.URL, Method.Get);
                        if (wAApiInfo.Headers?.Any() ?? false)
                        {
                            foreach (var item in wAApiInfo.Headers)
                            {
                                request.AddHeader(item.Key, item.Value);
                            }
                        }
                        var jsonPayLoad = wAApiInfo.JsonPayload ?? string.Empty;
                        if (!string.IsNullOrEmpty(wAApiInfo.JsonPayload))
                        {
                            // Replace placeholders dynamically
                            jsonPayLoad = jsonPayLoad
                                .Replace("#UserFullContactNo#", phoneNumber)
                                .Replace("#TemplateName#", template.Name)
                                .Replace("#ReportName#", reportName)
                                .Replace("#ExportUrl#", EncodeUrl(presignedUrl));
                        }
                        var client = new RestClient();
                        request.AddJsonBody(jsonPayLoad ?? string.Empty);
                        var response = await client.ExecuteAsync(request);
                        result = new(response.Content ?? string.Empty, response.IsSuccessStatusCode);
                    }
                    else if (wAApiInfo.MethodType?.Equals("Post", StringComparison.InvariantCultureIgnoreCase) ?? false)
                    {
                        var request = new RestRequest(wAApiInfo.URL, Method.Post);
                        if (wAApiInfo.Headers?.Any() ?? false)
                        {
                            foreach (var item in wAApiInfo.Headers)
                            {
                                request.AddHeader(item.Key, item.Value);
                            }
                        }
                        var jsonPayLoad = wAApiInfo.JsonPayload ?? string.Empty;
                        if (!string.IsNullOrEmpty(wAApiInfo.JsonPayload))
                        {
                            // Replace placeholders dynamically
                            jsonPayLoad = jsonPayLoad
                                .Replace("#UserFullContactNo#", phoneNumber)
                                .Replace("#TemplateName#", template.Name)
                                .Replace("#ReportName#", reportName)
                                .Replace("#ExportUrl#", EncodeUrl(presignedUrl));
                        }
                        var client = new RestClient();
                        request.AddJsonBody(jsonPayLoad);
                        var response = await client.ExecuteAsync(request);
                        result = new(response.Content ?? string.Empty, response.IsSuccessStatusCode);
                        _logger.Information(phoneNumber + " " + " Api Responce : " + response.Content ?? string.Empty + " "  + wAApiInfo.JsonPayload);
                    }
                }
            }
        }
        public async Task<ReportsConfigurationDto?> GetReportConfiguration(ReportsConfigurationDto? reportConfiguration)
        {
            var config = await _reportsConfigurationRepo.FirstOrDefaultAsync(new GetAllReportConfigurationSpec(reportConfiguration?.Id ?? Guid.Empty, true), CancellationToken.None);
            if (config == null)
            {
                return null;
            }
            return config.Adapt<ReportsConfigurationDto>();
        }
        static string EncodeUrl(string url)
        {
            var uri = new Uri(url);
            string encodedPath = uri.AbsolutePath
                                    .Replace(" ", "%20")   // Encode spaces
                                    .Replace("(", "%28")   // Encode '('
                                    .Replace(")", "%29")   // Encode ')'
                                    .Replace(":", "%3A");  // Encode ':' (only in filenames)

            return $"{uri.Scheme}://{uri.Host}{encodedPath}";
        }

    }
}