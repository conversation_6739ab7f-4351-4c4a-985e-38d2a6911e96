﻿using Lrb.Application.Common.AWS_Batch;
using Lrb.Application.Common.ServiceBus;
using Newtonsoft.Json;
using Lrb.Application.DataManagement.Web.Export.Requests;
namespace Lrb.Application.DataManagement.Web.Request
{
    public class RunAWSBatchForExportProspectsByCustomFiltersRequest : GetAllProspectParameter, IRequest<Response<Guid>>
    {
        public List<string>? ToRecipients { get; set; } = new();
        public List<string>? CcRecipients { get; set; } = new();
        public List<string>? BccRecipients { get; set; } = new();
        public Guid? ExportTemplateId { get; set; }
        public string? FileName { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
        public bool? IsWithNotes { get; set; }
        public int? NotesCount { get; set; }

    }
    public class RunAWSBatchForExportProspectsByCustomFiltersRequestHandler : IRequestHandler<RunAWSBatchForExportProspectsByCustomFiltersRequest, Response<Guid>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<ExportProspectTracker> _exportProspectTracker;
        public const string TYPE = "exportprospectsbycustomfilters";
        private readonly IServiceBus _serviceBus;
        public RunAWSBatchForExportProspectsByCustomFiltersRequestHandler(
            ICurrentUser currentUser,
            IRepositoryWithEvents<ExportProspectTracker> exportProspectTracker,
            IServiceBus serviceBus)
        {
            _currentUser = currentUser;
            _exportProspectTracker = exportProspectTracker;
            _serviceBus = serviceBus;
        }
        public async Task<Response<Guid>> Handle(RunAWSBatchForExportProspectsByCustomFiltersRequest request, CancellationToken cancellationToken)
        {
            try
            {
                ExportProspectTracker exportTracker = new();
                exportTracker.Request = JsonConvert.SerializeObject(request);
                exportTracker.TemplateId = request.ExportTemplateId;
                exportTracker.ToRecipients = request.ToRecipients;
                exportTracker.CcRecipients = request.CcRecipients;
                exportTracker.BccRecipients = request.BccRecipients;
                var tenantId = _currentUser.GetTenant();
                var currentUserId = _currentUser.GetUserId();
                await _exportProspectTracker.AddAsync(exportTracker, cancellationToken);
                InputPayload input = new(exportTracker.Id, tenantId ?? string.Empty, currentUserId, TYPE, null);
                var stringArgument = JsonConvert.SerializeObject(input);
                var cmdArgs = new List<string>() { stringArgument };
                await _serviceBus.RunExcelUploadJobAsync(cmdArgs);
                return new(exportTracker.Id);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Something went wrong.");
            }
        }
    }
}
