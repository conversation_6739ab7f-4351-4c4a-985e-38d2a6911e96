﻿using Lrb.Application.Lead.Web.Dtos;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.Text.RegularExpressions;

namespace Lrb.Application.Lead.Web.Requests.GetRequests
{

    public class GetAllPropertiesModuleWiseRequest : IRequest<Response<List<PropertyListsDto>>>
    {
        public ModulePropertiesType ModuleType { get; set; }
    }

    public class GetAllPropertiesModuleWiseRequestHandler : IRequestHandler<GetAllPropertiesModuleWiseRequest, Response<List<PropertyListsDto>>>
    {
        public async Task<Response<List<PropertyListsDto>>> Handle(GetAllPropertiesModuleWiseRequest request, CancellationToken cancellationToken)
        {
            var moduleSearchableProperties = new Dictionary<ModulePropertiesType, List<string>>
            {
                [ModulePropertiesType.Lead] = new List<string>
            {
                "LeadName",
                "ContactNo",
                "AlternateContactNo",
                "Email",
                "Nationality",
                "PropertyName",
                "ProjectName",
                "SubSource",
                "Status",
                "Source",
                "Purpose",
                "EnquiredFor",
                "Location",
                "SerialNumber"
            },
                [ModulePropertiesType.Data] = new List<string>
            {
                "DataName",
                "ContactNo",
                "AlternateContactNo",
                "Email",
                "Nationality",
                "PropertyName",
                "ProjectName",
                "SubSource",
                "Status",
                "Source",
                "Purpose",
                "EnquiredFor",
                "Location"
            }
            };

            if (moduleSearchableProperties.TryGetValue(request.ModuleType, out var searchableProperties))
            {
                var result = searchableProperties
                    .Select(prop => new PropertyListsDto
                    {
                        PropertyName = prop,
                        DisplayName = GetFormattedDisplayName(prop) ?? prop
                    })
                    .ToList();

                return new Response<List<PropertyListsDto>>(result);
            }

            return new Response<List<PropertyListsDto>>(new List<PropertyListsDto>());
        }

        private static string? GetFormattedDisplayName(string? value)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                return value;
            }
            value = Regex.Replace(value, @"[-_ .#]", string.Empty);
            var words = Regex.Matches(value, @"([A-Z][a-z]+)")
                        .Cast<Match>()
                        .Select(m => m.Value);
            return string.Join(" ", words);
        }
    }
}
