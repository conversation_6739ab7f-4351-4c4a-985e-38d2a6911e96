﻿using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.LeadGenRequests;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.WebUtilities;
using Newtonsoft.Json;

namespace Lrb.Application.Integration.Web.Requests.Bayut
{
    public class DubizzleWAPushIntegrationRequest : IRequest<Response<bool>>
    {
        public string ApiKey { get; set; }
        public HttpRequest HttpRequest { get; set; }
        public string TenantId { get; set; }

        public DubizzleWAPushIntegrationRequest(HttpRequest request, string tenant, string base64)
        {
            HttpRequest = request;
            TenantId = tenant;
            ApiKey = base64;
        }
    }

    public class DubbileWAPushIntegrationRequestHandler : IRequestHandler<DubizzleWAPushIntegrationRequest, Response<bool>>
    {
        private readonly IMediator _mediator;
        private readonly Serilog.ILogger _logger;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _intAccRepo;
        public DubbileWAPushIntegrationRequestHandler(IMediator mediator, Serilog.ILogger logger, IRepositoryWithEvents<IntegrationAccountInfo> intAccRepo)
        {
            _mediator = mediator;
            _logger = logger;
            _intAccRepo = intAccRepo;
        }

        public async Task<Response<bool>> Handle(DubizzleWAPushIntegrationRequest request, CancellationToken cancellationToken)
        {
            var accountId = request.ApiKey.GetAccountId();
            var integrationAccount = await _intAccRepo.GetByIdAsync(accountId, cancellationToken);
            if (integrationAccount == null)
            {
                _logger.Error("DubbileWAPushIntegrationRequestHandler -> POST(Dubizzle) -> Integration Account not found for ApiKey: " + request.ApiKey);
                return new(false, "Integration Account not found");
            }

            var httpRequest = request.HttpRequest;
            var bodyInString = "";
            BayutWhatsappParamsDto? payload = null;
            BayutWhatsappDto? bodyPayload = null;
            if (request.HttpRequest.HasFormContentType)
            {
                var form = await httpRequest.ReadFormAsync();
                var formData = form.ToDictionary(x => x.Key, x => x.Value.ToString());
                bodyInString = JsonConvert.SerializeObject(formData);
                if (string.IsNullOrWhiteSpace(bodyInString))
                {
                    throw new ArgumentNullException("Payload Cannot be empty");
                }
                payload = JsonConvert.DeserializeObject<BayutWhatsappParamsDto>(bodyInString);
            }
            else if (httpRequest.QueryString.HasValue)
            {
                var queryParamsData = httpRequest.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
                bodyInString = JsonConvert.SerializeObject(queryParamsData);
                if (string.IsNullOrWhiteSpace(bodyInString))
                {
                    throw new ArgumentNullException("Payload Cannot be empty");
                }
                payload = JsonConvert.DeserializeObject<BayutWhatsappParamsDto>(bodyInString);
            }
            else
            {
                Stream stream = httpRequest.Body;
                HttpRequestStreamReader reader = new(stream, System.Text.Encoding.UTF8);
                bodyInString = await reader.ReadToEndAsync();
                if (string.IsNullOrWhiteSpace(bodyInString))
                {
                    throw new ArgumentNullException("Payload Cannot be empty");
                }
                bodyPayload = JsonConvert.DeserializeObject<BayutWhatsappDto>(bodyInString);
            }
            if (payload != null)
            {
                _logger.Information("DubbileWAPushIntegrationRequestHandler -> POST(Dubizzle) -> called, Dto: " + payload);
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Dubizzle, payload);

                await _mediator.Send(leadGenRequest);

                var lead = new WhatsAppListingSitesIntegrationRequest()
                {
                    LeadSource = Domain.Enums.LeadSource.Dubizzle,
                    ApiKey = request.ApiKey,
                    Name = payload?.name ?? "Unknown",
                    Mobile = payload?.phone_number ?? string.Empty,
                    Notes = $"{payload?.message ?? string.Empty} \nTracking Url: {payload?.contact_link ?? string.Empty} \n Property Url: {payload?.url ?? string.Empty} \n Reference: {payload?.reference ?? string.Empty}",
                    Link = payload?.contact_link ?? string.Empty,
                    ReferenceId = payload?.reference ?? string.Empty,
                };
                await _mediator.Send(lead);
            }
            else if (bodyPayload != null)
            {
                _logger.Information("DubbileWAPushIntegrationRequestHandler -> POST(Dubizzle) -> called, Dto: " + bodyPayload);
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Dubizzle, bodyPayload);

                await _mediator.Send(leadGenRequest);

                var lead = new WhatsAppListingSitesIntegrationRequest()
                {
                    LeadSource = Domain.Enums.LeadSource.Dubizzle,
                    ApiKey = request.ApiKey,
                    Name = bodyPayload?.enquirer?.name ?? "Unknown",
                    Mobile = bodyPayload?.enquirer?.phone_number ?? string.Empty,
                    Notes = $"{bodyPayload?.message ?? string.Empty} \nTracking Url: {bodyPayload?.enquirer?.contact_link ?? string.Empty} \n Property Url: {bodyPayload?.listing?.url ?? string.Empty} \n Reference: {bodyPayload?.listing?.reference ?? string.Empty}",
                    Link = bodyPayload?.enquirer?.contact_link ?? string.Empty,
                    ReferenceId = bodyPayload?.listing?.reference ?? string.Empty
                };
                await _mediator.Send(lead);
            }

            return new(true);
        }
    }
}
