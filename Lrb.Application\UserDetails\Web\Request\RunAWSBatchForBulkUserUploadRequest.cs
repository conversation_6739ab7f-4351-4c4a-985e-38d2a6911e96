﻿using DocumentFormat.OpenXml.Wordprocessing;
using Lrb.Application.Common.AWS_Batch;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Domain.Entities.User;
using Lrb.Domain.Enums;
using Newtonsoft.Json;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class RunAWSBatchForBulkUserUploadRequest : IRequest<Response<BulkUserUploadTrackerDto>>
    {
        public string? S3BucketKey { get; set; }
        public string? SheetName { get; set; }
        public string? Origin { get; set; }
        public RunAWSBatchForBulkUserUploadRequest(string s3BucketKey,string sheetName,string origin)
        {
            S3BucketKey=s3BucketKey;
            SheetName=sheetName;
            Origin=origin;
        }

    }
    public class RunAWSBatchForBulkUserUploadRequestHandler : IRequestHandler<RunAWSBatchForBulkUserUploadRequest, Response<BulkUserUploadTrackerDto>>
    {
        private readonly IAWSBatchService _aWSBatchService;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<BulkUserUploadTracker> _bulkUserUploadTracker;
        public const string Type = "userimport";
        private readonly IServiceBus _serviceBus;

        public RunAWSBatchForBulkUserUploadRequestHandler(
            IAWSBatchService aWSBatchService,
            ICurrentUser currentUser,
            IRepositoryWithEvents<BulkUserUploadTracker> bulkUserUploadTracker,
            IServiceBus serviceBus)
        {
            _currentUser = currentUser;
            _aWSBatchService = aWSBatchService;
            _bulkUserUploadTracker = bulkUserUploadTracker;
            _serviceBus = serviceBus;

        }
        public async Task<Response<BulkUserUploadTrackerDto>> Handle(RunAWSBatchForBulkUserUploadRequest request, CancellationToken cancellationToken)
        {
            try
            {
                BulkUserUploadTrackerDto bulkUserTracker = request.Adapt<BulkUserUploadTrackerDto>();
                var mappedColumnData = new Dictionary<UserDataColumn, string>
        {
            { UserDataColumn.FirstName, "FirstName" },
            { UserDataColumn.LastName, "LastName" },
            { UserDataColumn.Email, "Email" },
            { UserDataColumn.UserName, "UserName" },
            { UserDataColumn.CountryCode, "CountryCode" },
            { UserDataColumn.PhoneNumber, "PhoneNumber" },
            { UserDataColumn.Address, "Address" },
            { UserDataColumn.BloodGroopType, "BloodGroopType" },
            { UserDataColumn.Gender, "Gender" },
            { UserDataColumn.Description, "Description" },
            { UserDataColumn.AltConctNo, "AltConctNo" },
            { UserDataColumn.Designation, "Designation" },
            { UserDataColumn.Department, "Department" },
            { UserDataColumn.LicenseNo, "BrokerNumber" }
        };
                bulkUserTracker.MappedColumnData = mappedColumnData;
                bulkUserTracker.S3BucketKey = request.S3BucketKey;
                bulkUserTracker.Status = UploadStatus.Initiated;
                var bulkUserTrackers = bulkUserTracker.Adapt<BulkUserUploadTracker>();
                bulkUserTrackers.Origin = request.Origin;
                await _bulkUserUploadTracker.AddAsync(bulkUserTrackers);
                var tenantId = _currentUser.GetTenant();
                var userId = _currentUser.GetUserId();
                bulkUserTracker.Id = bulkUserTrackers.Id;
                InputPayload inputPayload = new(bulkUserTrackers.Id, tenantId, userId, Type);
                var stringArgument = JsonConvert.SerializeObject(inputPayload);
                var cmdArgs = new List<string>() { stringArgument };
                await _serviceBus.RunExcelUploadJobAsync(cmdArgs);
                return new(bulkUserTracker, "Started Processing.");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Something went wrong.");
            }
        }
    }

}
