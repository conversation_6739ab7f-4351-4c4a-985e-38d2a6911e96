﻿using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.UserDetails.Web.Specs;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GetUserDetailsClockInRequest : IRequest<Response<UserDetailsClockInDto>>
    {
        public Guid UserId { get; set; }
    }
    public class GetUserDetailsClockInRequestHandler : IRequestHandler<GetUserDetailsClockInRequest, Response<UserDetailsClockInDto>>
    {
        private readonly IReadRepository<Domain.Entities.Project> _projectRepo;
        private readonly IReadRepository<Domain.Entities.Property> _propertyRepo;
        private readonly IReadRepository<Domain.Entities.UserSettings> _userSettingsRepo;

        public GetUserDetailsClockInRequestHandler(
            IReadRepository<Domain.Entities.Project> projectRepo,
            IReadRepository<Domain.Entities.Property> propertyRepo,
            IReadRepository<Domain.Entities.UserSettings> userSettingsRepo)
        {
            _projectRepo = projectRepo;
            _propertyRepo = propertyRepo;
            _userSettingsRepo = userSettingsRepo;
        }
        public async Task<Response<UserDetailsClockInDto>> Handle(GetUserDetailsClockInRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var userSettings = await _userSettingsRepo.FirstOrDefaultAsync(new GetUserSettingsSpec(request.UserId), cancellationToken);
                if (userSettings is null)
                {
                    return new Response<UserDetailsClockInDto>("User settings not found.");
                }

                var result = new UserDetailsClockInDto
                {
                    Id = request.UserId,
                    GeoFenceRadius = userSettings.GeoFenceRadius,
                    RadiusUnit = userSettings.RadiusUnit ?? Radius.None,
                    Projects = new List<UserProjectDto>(),
                    Properties = new List<UserPropertyDto>()
                };

                if (userSettings.GeoFenceProjectIds?.Any() is true)
                {
                    List<Domain.Entities.Project> projects = await _projectRepo.ListAsync(new GetProjectsAddressByIdsSpec(userSettings.GeoFenceProjectIds), cancellationToken);
                    List<UserProjectDto> userProjects = projects.Adapt<List<UserProjectDto>>();
                    result.Projects.AddRange(userProjects);
                }

                if (userSettings.GeoFencePropertyIds?.Any() is true)
                {
                    List<Domain.Entities.Property> properties = await _propertyRepo.ListAsync(new GetPropertiesWithAddressByIdsSpec(userSettings.GeoFencePropertyIds), cancellationToken);
                    List<UserPropertyDto> userProperties = properties.Adapt<List<UserPropertyDto>>();
                    result.Properties.AddRange(userProperties);
                }

                return new Response<UserDetailsClockInDto>(result);
            }
            catch (Exception ex)
            {
                return new Response<UserDetailsClockInDto>($"An error occurred while retrieving user details for clock-in. {ex.Message}");
            }
        }
    }
}
