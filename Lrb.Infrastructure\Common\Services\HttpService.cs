﻿using Lrb.Application.Common.HttpService;
using Lrb.Shared.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Net;
using System.Net.Http.Json;
using System.Text;
using System.Text.RegularExpressions;

namespace Lrb.Infrastructure.Common.Services
{
    public class HttpService : IHttpService
    {
        public Uri BaseUri { get; set; }
        public HttpClient Client { get; set; } = new HttpClient();

        public async Task<TResponse> ExcuteAsync<TResponse>(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            try
            {
                HttpResponseMessage response = await Client.SendAsync(request, cancellationToken);
                if (response.StatusCode == HttpStatusCode.OK)
                {
                    TResponse? res;
                    try
                    {
                        res = await response.Content.ReadFromJsonAsync<TResponse>();
                    }
                    catch (Exception e)
                    {
                        var bytes = await response.Content.ReadAsByteArrayAsync(CancellationToken.None);
                        var jsonString = UTF8Encoding.UTF8.GetString(bytes);
                        res = JsonConvert.DeserializeObject<TResponse>(jsonString);
                    }
                    return res;
                }
                else if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    throw new UnauthorizedAccessException();
                }
                else if (response.StatusCode == HttpStatusCode.NotFound || response.StatusCode == HttpStatusCode.NoContent)
                {
                    throw new ArgumentNullException(JsonConvert.SerializeObject(request));
                }
                else
                {
                    throw new Exception();
                }
            }
            catch (Exception e)
            {
                throw;
            }
        }
        public async Task<TResponse> PostAsync<TBody, TResponse>(string baseUrl, string resource, TBody DTO, Dictionary<string, string> headers = null) where TBody : class
        {
            BaseUri = new Uri(baseUrl);
            Client = new()
            {
                BaseAddress = BaseUri
            };
            HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, resource);
            if (DTO != null)
            {
                var json = JsonConvert.SerializeObject(DTO);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
            }
            else
            {
                throw new ArgumentNullException("Request body can not be null.");
            }
            if (headers != null && headers.Any())
            {
                foreach (var item in headers)
                {
                    request.Headers.Add(item.Key, item.Value);
                }
            }
            try
            {
                var res = await Client.SendAsync(request);
                var bytes = await res.Content.ReadAsByteArrayAsync(CancellationToken.None);
                var jsonString = UTF8Encoding.UTF8.GetString(bytes);
                var body = JsonConvert.DeserializeObject<TResponse>(jsonString);
                return body;
            }
            catch (Exception e)
            {
                throw;
            }
        }

        #region IVR CURL Breakdown Function
        public async Task<Dictionary<string, string>> ExecuteCurlCommandAsync(string curlCommand)
        {
            var result = new Dictionary<string, string>();

            try
            {
                // Precompiled regexes
                var urlMatch = Regex.Match(curlCommand, @"(https?://[^\s'\\]+)", RegexOptions.IgnoreCase);
                if (!urlMatch.Success)
                {
                    result["error"] = "Invalid or missing URL in CURL command.";
                    return result;
                }

                string url = urlMatch.Value;

                var methodMatch = Regex.Match(curlCommand, @"(--request|-X)\s+(POST|PUT|DELETE|PATCH|GET)", RegexOptions.IgnoreCase);
                var method = methodMatch.Success ? methodMatch.Groups[2].Value.ToUpperInvariant() : "POST";
                var httpMethod = new HttpMethod(method);

                // Extract headers
                var headers = Regex.Matches(curlCommand, @"--header\s+(?:'([^']+)'|""([^""]+)"")", RegexOptions.IgnoreCase)
                    .Cast<Match>()
                    .Select(m => m.Groups[1].Success ? m.Groups[1].Value : m.Groups[2].Value)
                    .Select(h => h.Split(new[] { ':' }, 2))
                    .Where(parts => parts.Length == 2)
                    .ToDictionary(parts => parts[0].Trim(), parts => parts[1].Trim(), StringComparer.OrdinalIgnoreCase);

                // Extract body content
                var bodyMatch = Regex.Match(curlCommand, @"--data(?:-raw)?\s+(?:'([^']+)'|""([^""]+)"")", RegexOptions.IgnoreCase);
                string? bodyContent = bodyMatch.Success
                    ? (bodyMatch.Groups[1].Success ? bodyMatch.Groups[1].Value : bodyMatch.Groups[2].Value)
                    : null;

                var request = new HttpRequestMessage(httpMethod, url);

                // Add headers
                foreach (var (key, value) in headers)
                    request.Headers.TryAddWithoutValidation(key, value);

                // Add body if applicable
                if (!string.IsNullOrWhiteSpace(bodyContent))
                {
                    var contentType = headers.TryGetValue("Content-Type", out var ct) ? ct : "application/json";
                    request.Content = new StringContent(bodyContent, Encoding.UTF8, contentType);
                }

                #region TimeOut
                var cts = new CancellationTokenSource(TimeSpan.FromSeconds(1));

                HttpResponseMessage response;
                try
                {
                    response = await Client.SendAsync(request, cts.Token);
                }
                catch (TaskCanceledException ex)
                {
                    result["error"] = cts.IsCancellationRequested
                        ? "Request timed out after 1 second."
                        : $"Request was canceled. Details: {ex.Message}";
                    return result;
                }
                #endregion
                var content = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    result["status"] = response.StatusCode.ToString();
                    result["error"] = content;
                    return result;
                }

                var contentTypeHeader = response.Content.Headers.ContentType?.MediaType;
                if (string.IsNullOrWhiteSpace(contentTypeHeader) || !contentTypeHeader.Contains("json", StringComparison.OrdinalIgnoreCase))
                {
                    result["error"] = $"Response is not JSON. Raw content: {content[..Math.Min(200, content.Length)]}";
                    return result;
                }

                var token = JToken.Parse(content);

                return token switch
                {
                    JObject obj => FlattenJson(obj),
                    JArray arr => FlattenJsonArray(arr),
                    _ => new Dictionary<string, string> { ["error"] = "Unsupported JSON root type." }
                };
            }
            catch (Exception ex)
            {
                result["error"] = $"Failed to execute CURL command. Details: {ex.Message}";
                return result;
            }
        }

        private Dictionary<string, string> FlattenJson(JObject jObject)
        {
            var result = new Dictionary<string, string>();
            FlattenToken(jObject, result, string.Empty);
            return result;
        }

        private Dictionary<string, string> FlattenJsonArray(JArray array)
        {
            var result = new Dictionary<string, string>();
            for (int i = 0; i < array.Count; i++)
            {
                FlattenToken(array[i], result, $"[{i}]");
            }
            return result;
        }

        private void FlattenToken(JToken token, Dictionary<string, string> result, string prefix)
        {
            switch (token)
            {
                case JObject obj:
                    foreach (var prop in obj.Properties())
                        FlattenToken(prop.Value, result, string.IsNullOrEmpty(prefix) ? prop.Name : $"{prefix}.{prop.Name}");
                    break;

                case JArray arr:
                    for (int i = 0; i < arr.Count; i++)
                        FlattenToken(arr[i], result, $"{prefix}[{i}]");
                    break;

                default:
                    result[prefix] = token?.ToString() ?? string.Empty;
                    break;
            }
        }
        #endregion
    }
}
