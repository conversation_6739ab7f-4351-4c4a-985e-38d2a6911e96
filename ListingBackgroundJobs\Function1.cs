using DocumentFormat.OpenXml.Math;
using ListingBackgroundJobs.Repositories;
using Lrb.Application.Property.Web.V2.Dtos;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace ListingBackgroundJobs
{
    public class Function1
    {
        #region Publish Listing Function
        [FunctionName("PublishListing")]
        public async Task<IActionResult> PublishListingAsync([HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "PublishListing/{env}")] HttpRequest req, string env, Microsoft.Extensions.Logging.ILogger log)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"HTTP PublishListing trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(env);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, env);
                        IFunctionEntryPoint _entryPoint = provider.GetRequiredService<IFunctionEntryPoint>();
                        var dto = JsonConvert.DeserializeObject<ListAndDelistListingDtoV2>(payload.Entity.ToString() ?? string.Empty);
                        if (dto != null)
                        {
                            var result = await _entryPoint.PublishListingAsync(dto, payload.TenantId, payload.CurrentUserId);
                            _semaphoreOne.Release();
                            return new OkObjectResult(result ? "This HTTP triggered function executed successfully" : "PublishListing returned false");
                        }

                    }
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP PublishListing triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling PublishListing:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }
        #endregion

        #region Update Listing Function
        [FunctionName("UpdateListing")]
        public async Task<IActionResult> UpdateListingAsync([HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "UpdateListing/{env}")] HttpRequest req, string env, Microsoft.Extensions.Logging.ILogger log)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"HTTP UpdateListing trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(env);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, env);
                        IFunctionEntryPoint _entryPoint = provider.GetRequiredService<IFunctionEntryPoint>();
                        var dto = JsonConvert.DeserializeObject<ListAndDelistListingDtoV2>(payload.Entity.ToString() ?? string.Empty);
                        if (dto != null)
                        {
                            var result = await _entryPoint.UpdateListingAsync(dto, payload.TenantId, payload.CurrentUserId);
                            _semaphoreOne.Release();
                            return new OkObjectResult(result ? "This HTTP triggered function executed successfully" : "UpdateListing returned false");
                        }

                    }
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP UpdateListing triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling PublishListing:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }
        #endregion

        #region Delete Listing Function
        [FunctionName("DeleteListing")]
        public async Task<IActionResult> DeleteListingAsync([HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "DeleteListing/{env}")] HttpRequest req, string env, Microsoft.Extensions.Logging.ILogger log)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"HTTP DeleteListing trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(env);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, env);
                        IFunctionEntryPoint _entryPoint = provider.GetRequiredService<IFunctionEntryPoint>();
                        var dto = JsonConvert.DeserializeObject<ListAndDelistListingDtoV2>(payload.Entity.ToString() ?? string.Empty);
                        if (dto != null)
                        {
                            var result = await _entryPoint.DeleteListingAsync(dto, payload.TenantId, payload.CurrentUserId);
                            _semaphoreOne.Release();
                            return new OkObjectResult(result ? "This HTTP triggered function executed successfully" : "DeleteListing returned false");
                        }

                    }
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP DeleteListing triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling DeleteListing:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }
        #endregion

        #region Update Listing Status
        //[FunctionName("QaSetPropertyListingStatus")]
        //public async static Task QaSetPropertyListingStatus([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        //{
        //    try
        //    {
        //        log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
        //        Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
        //        var startup = new Startup("qa");
        //        IServiceProvider provider = startup.ConfigureServices("qa");
        //        IListingRepository _repo = provider.GetRequiredService<IListingRepository>();
        //        await _repo.SetPropertyListingStatus();
        //        log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
        //        Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
        //    }
        //    catch (Exception ex)
        //    {
        //        log.LogInformation("Exception details while calling QaSetPropertyListingStatus." + ex.Message);
        //    }
        //}

        [FunctionName("PrdSetPropertyListingStatus")]
        public async static Task PrdSetPropertyListingStatus([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("qa");
                IServiceProvider provider = startup.ConfigureServices("prd");
                IListingRepository _repo = provider.GetRequiredService<IListingRepository>();
                await _repo.SetPropertyListingStatus();
                log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling PrdSetPropertyListingStatus." + ex.Message);
            }
        }
        #endregion

        #region Permanent Delete Listing Function
        [FunctionName("PermanentDeleteListing")]
        public async Task<IActionResult> PermanentDeleteListingAsync([HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "PermanentDeleteListing/{env}")] HttpRequest req, string env, Microsoft.Extensions.Logging.ILogger log)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"HTTP PermanentDeleteListing trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(env);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, env);
                        IFunctionEntryPoint _entryPoint = provider.GetRequiredService<IFunctionEntryPoint>();
                        var dto = JsonConvert.DeserializeObject<ListAndDelistListingDtoV2>(payload.Entity.ToString() ?? string.Empty);
                        if (dto != null)
                        {
                            var result = await _entryPoint.PermanentDeleteListingAsync(dto, payload.TenantId, payload.CurrentUserId);
                            _semaphoreOne.Release();
                            return new OkObjectResult(result ? "This HTTP triggered function executed successfully" : "PermanentDeleteListing returned false");
                        }

                    }
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP PermanentDeleteListing triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling PermanentDeleteListing:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }
        #endregion

        #region Map Lead with Listing details
        [FunctionName("MapListingDetailsToLead")]
        public async Task<IActionResult> MapListingDetailsToLeadAsync([HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "MapListingDetailsToLead/{env}")] HttpRequest req, string env, Microsoft.Extensions.Logging.ILogger log)
        {
            Semaphore _semaphoreOne = new Semaphore(1, 1);
            try
            {
                _semaphoreOne.WaitOne();
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"HTTP MapListingDetailsToLead trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var payload = JsonConvert.DeserializeObject<InputPayload>(requestBody);
                    if (payload != null)
                    {
                        var startup = new Startup(env);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, env);
                        IFunctionEntryPoint _entryPoint = provider.GetRequiredService<IFunctionEntryPoint>();
                        var dto = JsonConvert.DeserializeObject<MapListingDetailsToLeads>(payload.Entity.ToString() ?? string.Empty);
                        if (dto != null)
                        {
                            var result = await _entryPoint.MapListingDetailsToLeadsAsync(dto, payload.TenantId);
                            _semaphoreOne.Release();
                            return new OkObjectResult(result ? "This HTTP triggered function executed successfully" : "MapListingDetailsToLead returned false");
                        }
                    }
                }
                _semaphoreOne.Release();
                return new OkObjectResult($"HTTP MapListingDetailsToLead triggered function executed successfully");
            }
            catch (Exception ex)
            {
                log.LogInformation($"Exception details while calling MapListingDetailsToLead:" + ex.Message);
                _semaphoreOne.Release();
                return new OkObjectResult(ex);
            }
        }
        #endregion

        #region Input Payload
        public record InputPayload(string TenantId, Guid CurrentUserId, object Entity);
        #endregion
    }
}
