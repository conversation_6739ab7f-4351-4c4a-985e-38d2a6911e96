﻿using LrbIntegrationBackgroundJobs.Dtos.Bayut;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace LrbIntegrationBackgroundJobs.Services
{
    public class BayutService : IBayutService
    {
        private readonly WorkingEndPoint _endpoints;
        private IDbRepositoryAsync _dbRepo = default!;
        private ILrbAPIService _lrbService = default!;
        public BayutService(IOptions<WorkingEndPoint> endpoints, IDbRepositoryAsync dbRepo, ILrbAPIService lrbService)
        {
            _endpoints = endpoints.Value;
            _dbRepo = dbRepo;
            _lrbService = lrbService;
        }

        #region Whatsapp Leads
        public async Task<List<BayutWhatsappResponseDto>> FetchLeadsFromBayutWAAsync(BayutCredentialDto cred)
        {
            try
            {
                var date = (DateTime.UtcNow.AddHours(4).AddMinutes(-5)).ToString("yyyy-MM-dd HH:mm:ss");
                string url = $"{_endpoints.BayutBaseUri}/website-client-leads?type={cred.RequestType}&timestamp={date}";
                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", cred.ApiKey);
                    HttpResponseMessage response = await client.GetAsync(url);
                    response.EnsureSuccessStatusCode();
                    string responseData = await response.Content.ReadAsStringAsync();
                    var waLeads = JsonConvert.DeserializeObject<List<BayutWhatsappResponseDto>>(responseData);
                    return waLeads;
                }
            }
            catch (Exception ex)
            {
            }
            return new();
        }

        private async Task<List<BayutWhatsappDto>> GetLeadsFromBayutWAAsync(IntegrationAccountDto bayutAccount)
        {
            var cred = bayutAccount.GetCreds<BayutCredentialDto>();

            if (cred != null)
            {

                List<BayutWhatsappDto> lrbLeads = new();
                var bayutWALeads = await FetchLeadsFromBayutWAAsync(cred);
                if (bayutWALeads != null)
                {
                    foreach (var lead in bayutWALeads)
                    {
                        lrbLeads.Add(lead.MapToLrbIngrDto());
                    }
                    return lrbLeads;
                }
            }
            return new();
        }

        private async Task<int> PostBayutAsLrbLeadsAsync(List<BayutWhatsappDto> lrbLeads, IntegrationAccountDto bayutLeadAccount)
        {
            int counter = 0;
            foreach (var lrbLead in lrbLeads)
            {
                try
                {
                    var res = await _lrbService.PostBayutAsync(lrbLead, bayutLeadAccount.GetApiKey());
                    bayutLeadAccount.SyncedCount++;
                    await _dbRepo.UpdateSyncedCountAsync(bayutLeadAccount);
                    counter++;
                }
                catch { }
            }
            return counter;
        }
        #endregion

        #region Call Logs Leads
        public async Task<List<BayutCallLogsResponseDto>> FetchLeadsFromBayutCallLogsAsync(BayutCredentialDto cred)
        {
            try
            {
                var date = (DateTime.UtcNow.AddHours(4).AddMinutes(-5)).ToString("yyyy-MM-dd HH:mm:ss");
                string url = $"{_endpoints.BayutBaseUri}/website-client-leads?type={cred.RequestType}&timestamp={date}";
                using( HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", cred.ApiKey);
                    HttpResponseMessage response = await client.GetAsync(url);
                    response.EnsureSuccessStatusCode();
                    string responseData = await response.Content.ReadAsStringAsync();
                    var callLeads = JsonConvert.DeserializeObject<List<BayutCallLogsResponseDto>>(responseData);
                    return callLeads;
                }
            }
            catch (Exception ex)
            {
                return new();
            }
        }

        private async Task<List<LrbBayutCallLogIntegrationPostDto>> GetLeadsFromBayutCallLogsAsync(IntegrationAccountDto bayutAccount)
        {
            var cred = bayutAccount.GetCreds<BayutCredentialDto>();
            if (cred != null)
            {
                List<LrbBayutCallLogIntegrationPostDto> bayutCallLeads = new();
                var bayutCallLogs = await FetchLeadsFromBayutCallLogsAsync(cred);
                if (bayutCallLogs != null)
                {
                    foreach (var lead in bayutCallLogs)
                    {
                        bayutCallLeads.Add(lead.MapToLrbIngrDto());
                    }
                }
                return bayutCallLeads;
            }
            return new();
        }

        private async Task<int> PostBayutCallAsLrbLeadsAsync(List<LrbBayutCallLogIntegrationPostDto> lrbLeads, IntegrationAccountDto bayutLeadAccount)
        {
            int counter = 0;
            foreach (var lrbLead in lrbLeads)
            {
                try
                {
                    var res = await _lrbService.PostBayutCallAsync(lrbLead, bayutLeadAccount.GetApiKey());
                    bayutLeadAccount.SyncedCount++;
                    await _dbRepo.UpdateSyncedCountAsync(bayutLeadAccount);
                    counter++;
                }
                catch { }
            }
            return counter;
        }
        #endregion

        #region Email
        public async Task<List<BayutEmailResponseDto>> FetchLeadsFromBayutEmailAsync(BayutCredentialDto cred)
        {
            try
            {
                var date = (DateTime.UtcNow.AddHours(4).AddMinutes(-5)).ToString("yyyy-MM-dd HH:mm:ss");
                string url = $"{_endpoints.BayutBaseUri}/website-client-leads?type={cred.RequestType}&timestamp={date}";
                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", cred.ApiKey);
                    HttpResponseMessage response = await client.GetAsync(url);
                    response.EnsureSuccessStatusCode();
                    string responseData = await response.Content.ReadAsStringAsync();
                    var callLeads = JsonConvert.DeserializeObject<List<BayutEmailResponseDto>>(responseData);
                    return callLeads;
                }
            }
            catch (Exception ex)
            {
                return new();
            }
        }

        private async Task<List<LrbIntegrationPostDto>> GetLeadsFromBayutEmailAsync(IntegrationAccountDto bayutAccount)
        {
            var cred = bayutAccount.GetCreds<BayutCredentialDto>();
            if (cred != null)
            {
                List<LrbIntegrationPostDto> bayutCallLeads = new();
                var bayutCallLogs = await FetchLeadsFromBayutEmailAsync(cred);
                var uniqueContactNumbers = bayutCallLogs.Select(i => i.client_phone).Distinct().ToList();
                var existingContactnumbers = await _dbRepo.IsLeadExistsAsync(uniqueContactNumbers, bayutAccount.TenantId, bayutAccount.ConnectionString);
                var uniqueLeads = bayutCallLogs.Where(i => !existingContactnumbers.Contains(i.client_phone)).ToList();
                var bayutLeads = uniqueLeads.Select(l => l.MapToLrbIngrDto()).ToList(); 
                return bayutLeads;
            }
            return new();
        }
           
        private async Task<int> PostBayutEmailAsLrbLeadsAsync(List<LrbIntegrationPostDto> lrbLeads, IntegrationAccountDto bayutLeadAccount, ILogger logger)
        {
            int counter = 0;
            logger.LogInformation($"Lrb Leads: {JsonConvert.SerializeObject(lrbLeads)}");
            foreach (var lrbLead in lrbLeads)
            {
                try
                {
                    logger.LogInformation($"Lrb Lead: {JsonConvert.SerializeObject(lrbLead)}");
                    var res = await _lrbService.PostBayutemailAsync(lrbLead, bayutLeadAccount.GetApiKey(), logger);
                    bayutLeadAccount.SyncedCount++;
                    await _dbRepo.UpdateSyncedCountAsync(bayutLeadAccount);
                    counter++;
                }
                catch { }
            }
            return counter;
        }
        #endregion


        public async Task ProcessBayutAccountsAsync(ILogger logger)
        {
            Console.WriteLine("ProcessBayutAccountsAsync() started.");
            var bayutLeadAccounts = await _dbRepo.GetAllBayutIntegrationsAsync();   
            var distinctAccount = bayutLeadAccounts.DistinctBy(i => i.Id).ToList();
            foreach (var account in distinctAccount)
            {
                var cred = account.GetCreds<BayutCredentialDto>();
                switch (cred.RequestType)
                {
                    case "whatsapp_leads":
                        var lrbLeads = await GetLeadsFromBayutWAAsync(account);
                        var postedLeadCount = await PostBayutAsLrbLeadsAsync(lrbLeads, account);
                        break;
                    case "call_logs":
                        var callLeads = await GetLeadsFromBayutCallLogsAsync(account);
                        var callLeadCount = await PostBayutCallAsLrbLeadsAsync(callLeads, account);
                        break;
                    case "leads":
                        var emailLeads = await GetLeadsFromBayutEmailAsync(account);
                        var emailLeadCount = await PostBayutEmailAsLrbLeadsAsync(emailLeads, account, logger);
                        break;
                }
            }
        }
    }
}
