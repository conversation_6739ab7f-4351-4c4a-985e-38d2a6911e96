﻿using Lrb.Application.Lead.Mobile;
using Lrb.Domain.Entities.MasterData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.CustomStatus.Mobile
{
    public static class StatusHelper
    {
        public static List<ViewCustomStatusDto> GetCustomStatus(List<ViewCustomStatusDto> filters)
        {
            var customStatus = filters.OrderBy(i => i.OrderRank).Where(i => i.BaseId == null || i.BaseId == Guid.Empty).ToList();
            GetChildData(customStatus, filters);
            return customStatus;
        }
        public static void GetChildData(List<ViewCustomStatusDto> childTypes, List<ViewCustomStatusDto> filters)
        {
            childTypes?.ForEach(child =>
            {
                if (TryGetChildData(child, filters, out List<ViewCustomStatusDto>? childType))
                {
                    child.ChildTypes = childType ?? new List<ViewCustomStatusDto>();
                    GetChildData(childType ?? new List<ViewCustomStatusDto>(), filters);
                }
            });
        }
        public static bool TryGetChildData(ViewCustomStatusDto filter, List<ViewCustomStatusDto> filters, out List<ViewCustomStatusDto>? childDatas)
        {
            var data = filters.Where(i => filter.Id == i.BaseId)?.OrderBy(i => i.OrderRank).ToList();
            childDatas = data;
            return data?.Any() ?? false;
        }
    }
}
