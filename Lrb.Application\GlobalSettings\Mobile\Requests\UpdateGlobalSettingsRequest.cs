﻿using Lrb.Domain.Entities;
using Mapster;
using Newtonsoft.Json;

namespace Lrb.Application.GlobalSettings.Mobile
{
    public class UpdateGlobalSettingsRequest : UpdateGlobalSettingsDto, IRequest<Response<bool>>
    {
    }
    public class UpdateGlobalSettingsRequestHandler : IRequestHandler<UpdateGlobalSettingsRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepository;
        public UpdateGlobalSettingsRequestHandler(IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepository)
        {
            _globalSettingsRepository = globalSettingsRepository;
        }

        public async Task<Response<bool>> Handle(UpdateGlobalSettingsRequest request, CancellationToken cancellationToken)
        {
            var existingGlobalSettings = (await _globalSettingsRepository.ListAsync(new GetGlobalSettingsSpec(), cancellationToken)).FirstOrDefault();
            string callSettings = null;
            string notificationSettings = null;
            string notesSetting = null;
            string propertySetting = null;
            string projectSetting = null;
            if (request.CallSettings != null)
            {
                callSettings = JsonConvert.SerializeObject(request.CallSettings);
            }
            if (request?.NotificationSettings?.ModuleSettings.Count > 0)
            {
                request.NotificationSettings.ModuleSettings = request.NotificationSettings.ModuleSettings.DistinctBy(i => i.Event).ToList();
                notificationSettings = JsonConvert.SerializeObject(request.NotificationSettings);
            }
            if (request?.LeadNotesSetting != null)
            {
                notesSetting = JsonConvert.SerializeObject(request.LeadNotesSetting);
            }
            if (request?.LeadPropertySetting != null)
            {
                propertySetting = JsonConvert.SerializeObject(request.LeadPropertySetting);
            }
            if (request?.LeadProjectSetting != null)
            {
                projectSetting = JsonConvert.SerializeObject(request.LeadProjectSetting);
            }
            if (existingGlobalSettings == null)
            {
                Domain.Entities.GlobalSettings globalSettings = new();
                globalSettings.CallSettings = callSettings ?? null;
                globalSettings.NotificationSettings = notificationSettings ?? null;
                globalSettings.HasInternationalSupport = request?.HasInternationalSupport ?? default;
                globalSettings.IsLeadsExportEnabled = request?.IsLeadsExportEnabled ?? default;
                globalSettings.IsLeadSourceEditable = request?.IsLeadSourceEditable ?? default;
                globalSettings.DayStartTime = request?.DayStartTime ?? null;
                globalSettings.DayEndTime = request?.DayEndTime ?? null;
                globalSettings.IsCallDetectionActivated = request?.IsCallDetectionActivated ?? default;
                globalSettings.LeadNotesSetting = notesSetting ?? null;
                globalSettings.IsWhatsAppEnabled = request.IsWhatsAppEnabled ?? default;
                globalSettings.IsMaskedLeadContactNo = request?.IsMaskedLeadContactNo ?? default;
                globalSettings.IsMicrositeFeatureEnabled = request?.IsMicrositeFeatureEnabled ?? default;
                globalSettings.IsDualOwnershipEnabled = request?.IsDualOwnershipEnabled ?? default;
                globalSettings.IsPropertiesExportEnabled = request?.IsPropertiesExportEnabled ?? default;
                globalSettings.IsExportDataEnabled = request?.IsExportDataEnabled ?? default;
                globalSettings.IsStickyAgentEnabled = request?.IsStickyAgentEnabled ?? default;
                globalSettings.LeadPropertySetting = propertySetting ?? null;
                globalSettings.LeadProjectSetting = projectSetting ?? null;
                globalSettings.IsTimeZoneEnabled = request?.IsTimeZoneEnabled ?? default;
                globalSettings.IsCopyPasteEnabled = request?.IsCopyPasteEnabled ?? default;
                globalSettings.IsScreenshotEnabled = request?.IsScreenshotEnabled ?? default;
                globalSettings.ShouldHideDashBoard = request?.ShouldHideDashBoard ?? default;
                globalSettings.IsWhatsAppDeepIntegration = request?.IsWhatsAppDeepIntegration ?? default;
                if (request?.Countries?.Any() ?? false)
                {
                    var countries = request.Countries.Adapt<List<CountryInfo>>();
                    globalSettings.Countries = countries;
                }

                globalSettings.IsLeadRotationEnabled = request?.IsLeadRotationEnabled ?? default;
                globalSettings.IsStickyAgentOverriddenEnabled = request?.IsStickyAgentOverriddenEnabled ?? default;
                globalSettings.DirectionOfLeadModification = request?.DirectionOfLeadModification ?? default;
                globalSettings.DirectionOfLeadCreation = request?.DirectionOfLeadCreation ?? default;
                globalSettings.IsCustomStatusEnabled = request?.IsCustomStatusEnabled ?? default;
                globalSettings.IsAssignedCallLogsEnabled = request?.IsAssignedCallLogsEnabled ?? default;
                globalSettings.ShouldEnablePropertyListing = request?.ShouldEnablePropertyListing ?? default;
                globalSettings.CanAccessAnonymousApis = request?.CanAccessAnonymousApis ?? default;
                globalSettings.IsCustomLeadFormEnabled = request?.IsCustomLeadFormEnabled ?? default;
                globalSettings.IsMobileCallEnabled = request?.IsMobileCallEnabled ?? default;
                globalSettings.DefaultValues = request?.DefaultValues ?? default;
                globalSettings.ShouldEnableEnquiryForm = request?.ShouldEnableEnquiryForm ?? default;
                globalSettings.ShowMoreMicrositeProperties=request?.ShowMoreMicrositeProperties ?? default;
                globalSettings.ShouldRenameSiteVisitColumn = request?.ShouldRenameSiteVisitColumn ?? default;
                globalSettings.IsPastDateSelectionEnabled = request?.IsPastDateSelectionEnabled ?? default;
                await _globalSettingsRepository.AddAsync(globalSettings, cancellationToken);

                return new(true);
            }
            else
            {
                existingGlobalSettings.NotificationSettings = notificationSettings ?? existingGlobalSettings.NotificationSettings;
                existingGlobalSettings.CallSettings = callSettings ?? existingGlobalSettings.CallSettings;
                existingGlobalSettings.HasInternationalSupport = request?.HasInternationalSupport ?? existingGlobalSettings.HasInternationalSupport;
                existingGlobalSettings.IsLeadsExportEnabled = request?.IsLeadsExportEnabled ?? existingGlobalSettings.IsLeadsExportEnabled;
                existingGlobalSettings.IsLeadSourceEditable = request?.IsLeadSourceEditable ?? existingGlobalSettings.IsLeadSourceEditable;
                existingGlobalSettings.DayStartTime = request?.DayStartTime ?? existingGlobalSettings.DayStartTime;
                existingGlobalSettings.DayEndTime = request?.DayEndTime ?? existingGlobalSettings.DayEndTime;
                existingGlobalSettings.IsCallDetectionActivated = request?.IsCallDetectionActivated ?? existingGlobalSettings.IsCallDetectionActivated;
                existingGlobalSettings.LeadNotesSetting = notesSetting ?? existingGlobalSettings.LeadNotesSetting;
                existingGlobalSettings.IsWhatsAppEnabled = request.IsWhatsAppEnabled ?? existingGlobalSettings.IsWhatsAppEnabled;
                existingGlobalSettings.IsMaskedLeadContactNo = request?.IsMaskedLeadContactNo ?? existingGlobalSettings.IsMaskedLeadContactNo;
                existingGlobalSettings.IsMicrositeFeatureEnabled = request?.IsMicrositeFeatureEnabled ?? existingGlobalSettings.IsMicrositeFeatureEnabled;
                existingGlobalSettings.IsDualOwnershipEnabled = request?.IsDualOwnershipEnabled ?? existingGlobalSettings.IsDualOwnershipEnabled;
                existingGlobalSettings.IsPropertiesExportEnabled = request?.IsPropertiesExportEnabled ?? existingGlobalSettings.IsPropertiesExportEnabled;
                existingGlobalSettings.IsExportDataEnabled = request?.IsExportDataEnabled ?? existingGlobalSettings.IsExportDataEnabled;
                existingGlobalSettings.IsStickyAgentEnabled = request?.IsStickyAgentEnabled ?? existingGlobalSettings.IsStickyAgentEnabled;
                existingGlobalSettings.LeadProjectSetting = projectSetting ?? existingGlobalSettings.LeadProjectSetting;
                existingGlobalSettings.LeadPropertySetting = propertySetting ?? existingGlobalSettings.LeadPropertySetting;
                existingGlobalSettings.IsLeadRotationEnabled = request?.IsLeadRotationEnabled ?? existingGlobalSettings.IsLeadRotationEnabled;
                existingGlobalSettings.IsStickyAgentOverriddenEnabled = request?.IsStickyAgentOverriddenEnabled ?? existingGlobalSettings.IsStickyAgentOverriddenEnabled;
                existingGlobalSettings.IsTimeZoneEnabled = request?.IsTimeZoneEnabled ?? existingGlobalSettings.IsTimeZoneEnabled;
                if (request?.Countries?.Any() ?? false)
                {
                    var countries = request.Countries.Adapt<List<CountryInfo>>();
                    existingGlobalSettings.Countries = countries;
                }
                existingGlobalSettings.IsCopyPasteEnabled = request?.IsCopyPasteEnabled ?? existingGlobalSettings.IsCopyPasteEnabled;
                existingGlobalSettings.ShouldHideDashBoard = request?.ShouldHideDashBoard ?? existingGlobalSettings.ShouldHideDashBoard;
                existingGlobalSettings.IsScreenshotEnabled = request?.IsScreenshotEnabled ?? existingGlobalSettings.IsScreenshotEnabled;
                existingGlobalSettings.IsCustomStatusEnabled = request?.IsCustomStatusEnabled ?? existingGlobalSettings.IsCustomStatusEnabled;
                existingGlobalSettings.IsWhatsAppDeepIntegration = request?.IsWhatsAppDeepIntegration ?? existingGlobalSettings.IsWhatsAppDeepIntegration;
                existingGlobalSettings.DirectionOfLeadCreation = request?.DirectionOfLeadCreation ?? existingGlobalSettings.DirectionOfLeadCreation;
                existingGlobalSettings.DirectionOfLeadModification = request?.DirectionOfLeadModification ?? existingGlobalSettings.DirectionOfLeadModification;
                existingGlobalSettings.IsAssignedCallLogsEnabled = request?.IsAssignedCallLogsEnabled ?? existingGlobalSettings.IsAssignedCallLogsEnabled;
                existingGlobalSettings.ShouldEnablePropertyListing = request?.ShouldEnablePropertyListing ?? existingGlobalSettings.ShouldEnablePropertyListing;
                existingGlobalSettings.CanAccessAnonymousApis = request?.CanAccessAnonymousApis ?? existingGlobalSettings.CanAccessAnonymousApis;
                existingGlobalSettings.IsCustomLeadFormEnabled = request?.IsCustomLeadFormEnabled ?? existingGlobalSettings.IsCustomLeadFormEnabled;
                existingGlobalSettings.IsMobileCallEnabled = request?.IsMobileCallEnabled ?? existingGlobalSettings.IsMobileCallEnabled;
                existingGlobalSettings.DefaultValues = request?.DefaultValues ?? existingGlobalSettings.DefaultValues;
                existingGlobalSettings.ShouldEnableEnquiryForm = request?.ShouldEnableEnquiryForm ?? existingGlobalSettings.ShouldEnableEnquiryForm;
                existingGlobalSettings.ShowMoreMicrositeProperties = request?.ShowMoreMicrositeProperties ?? existingGlobalSettings.ShowMoreMicrositeProperties;
                existingGlobalSettings.ShouldRenameSiteVisitColumn = request?.ShouldRenameSiteVisitColumn ?? existingGlobalSettings.ShouldRenameSiteVisitColumn;
                existingGlobalSettings.IsPastDateSelectionEnabled = request?.IsPastDateSelectionEnabled ?? existingGlobalSettings.IsPastDateSelectionEnabled;
                await _globalSettingsRepository.UpdateAsync(existingGlobalSettings, cancellationToken);
                return new(true);
            }
        }
    }
}
