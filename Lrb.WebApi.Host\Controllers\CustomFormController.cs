using Lrb.Application.CustomForm.Web.Dtos;
using Lrb.Application.CustomForm.Web.Requests;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class CustomFormController : VersionedApiController
    {

        [HttpGet]
        [TenantIdHeader]
        [OpenApiOperation("Get all custom forms.", "")]
        public async Task<PagedResponse<ViewCustomFormDto, string>> GetAllAsync([FromQuery] GetAllCustomFormRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Get custom form by ID.", "")]
        public async Task<Response<ViewCustomFormDto>> GetByIdAsync(Guid id)
        {
            return await Mediator.Send(new GetCustomFormByIdRequest(id));
        }
        [HttpPost]
        [TenantIdHeader]
        [OpenApiOperation("Create a new custom form.", "")]
        public async Task<Response<bool>> CreateAsync(CreateCustomFormRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut]
        [TenantIdHeader]
        [OpenApiOperation("Update an existing custom form.", "")]
        public async Task<Response<Guid>> UpdateAsync(UpdateCustomFormRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpDelete("{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Delete a custom form.", "")]
        public async Task<Response<bool>> DeleteAsync(Guid id)
        {
            return await Mediator.Send(new DeleteCustomFormRequest(id));
        }
    }
}
