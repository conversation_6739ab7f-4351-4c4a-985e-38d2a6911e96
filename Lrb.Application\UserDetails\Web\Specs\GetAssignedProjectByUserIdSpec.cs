﻿using Microsoft.EntityFrameworkCore;

namespace Lrb.Application.UserDetails.Web.Specs
{
    public class GetAssignedProjectByUserIdSpec : Specification<Domain.Entities.Project>
    {
        public GetAssignedProjectByUserIdSpec(Guid userId)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.UserAssignment != null && i.UserAssignment.UserIds != null && EF.Functions.JsonContains(i.UserAssignment.UserIds, $"\"{userId}\""))
                 .Include(i => i.Address)
                 .Include(i => i.UserAssignment);
        }
        public GetAssignedProjectByUserIdSpec(List<Guid> userIds)
        {
            Query.Where(i => !i.IsDeleted &&
                            !i.IsArchived && i.Address.PlaceId != null && i.Address.PlaceId != string.Empty &&
                            i.UserAssignment != null &&
                            i.UserAssignment.UserIds != null &&
                            userIds.Any(userId => EF.Functions.JsonContains(i.UserAssignment.UserIds, $"\"{userId}\"")))
                 .Include(i => i.Address)
                 .Include(i => i.UserAssignment);
        }
    }
}
