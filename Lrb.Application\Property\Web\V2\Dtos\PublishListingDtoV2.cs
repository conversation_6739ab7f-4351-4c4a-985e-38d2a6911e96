﻿namespace Lrb.Application.Property.Web.V2.Dtos
{
    public class ListAndDelistListingDtoV2
    {
        public List<Guid>? ListingIds { get; set; }
        public Guid? SourceId { get; set; }
    }

    public class MapListingDetailsToLeads
    {
        public Guid? LeadId { get; set; }
        public Guid? IntAccId { get; set; }
        public string? ListingId { get; set; }
        public string? ApiKey { get; set; }
        public string? SecretKey { get; set; }
    }
}
