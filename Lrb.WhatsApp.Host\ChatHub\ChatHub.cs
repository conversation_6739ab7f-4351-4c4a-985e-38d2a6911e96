﻿namespace Lrb.WhatsApp.ChatHub
{
    public sealed class ChatHub : Hub<IChatClient>
    {
        private readonly IWhatsAppService _whatsAppService;
        public ChatHub(IWhatsAppService whatsAppService)
        {
            _whatsAppService = whatsAppService;
        }

        #region Connection
        public override async Task OnConnectedAsync()
        {
            //await Clients.All.ReceiveMessageAsync($"{Context.ConnectionId} joined.");
        }
        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            //await _whatsAppService.StoreConnectionDetailsAsync(Context.ConnectionId, true, string.Empty, $"disconnected {exception?.Message}", false);
        }
        public async void RequestForDisconnectAsync(string tenant)
        {
            tenant = ValidateAndGetTenant(tenant);
            await LeaveGroup(tenant);
            //await _whatsAppService.StoreConnectionDetailsAsync(Context.ConnectionId, true, tenant, "disconnected", false);
            await Clients.Group(tenant).ReceiveMessageAsync($"{Context.ConnectionId} disconnected {tenant}");
            Context.Abort();
        }
        #endregion

        #region Tenant
        private string ValidateAndGetTenant(string tenant)
        {
            if (string.IsNullOrEmpty(tenant)) { throw new Exception("Tenant id is required."); };
            return tenant;
        }
        #endregion

        #region Group
        public async Task RequestForGroupJoinAsync(string tenant)
        {
            tenant = ValidateAndGetTenant(tenant);
            await JoinGroup(tenant);
            //await _whatsAppService.StoreConnectionDetailsAsync(Context.ConnectionId, false, tenant, string.Empty, true);
            await Clients.Group(tenant).ReceiveMessageAsync($"{Context.ConnectionId} joined in {tenant}");
        }

        Task JoinGroup(string tenant) => Groups.AddToGroupAsync(Context.ConnectionId, tenant);
        Task LeaveGroup(string tenant) => Groups.RemoveFromGroupAsync(Context.ConnectionId, tenant);

        public async Task SendMessageToGroupAsync(string tenant, WAWrapperDto wAWrapperDto)
        {
            tenant = ValidateAndGetTenant(tenant);
            try
            {
                if (wAWrapperDto != null)
                {
                    wAWrapperDto.TenantId = tenant;
                    var result = await _whatsAppService.ProcessPushOperationAsync(wAWrapperDto);
                    await Clients.Group(tenant).ReceiveMessageAsync(new Response<object>(result.Item2 != null ? result.Item2 : false, result.Item1));
                }
                else
                {
                    await Clients.Group(tenant).ReceiveMessageAsync(new Response<object>(false, "WAWrapperDto is null"));
                }
            }
            catch (Exception ex)
            {
                await Clients.Group(tenant).ReceiveMessageAsync(new Response<object>(false, ex.Message ?? ex.InnerException?.Message ?? string.Empty));
            }
        }
        #endregion

        #region Caller
        public async Task SendMessageToCallerAsync(object obj)
        {
            await Clients.Caller.ReceiveMessageAsync(new Response<object>(obj));
        }
        public async Task MessageToGroupAsync(string tenant, List<WAMessage> obj)
        {
            await Clients.Group(tenant).ReceiveMessageAsync(new Response<object>(obj));
        }
        #endregion

    }
}
