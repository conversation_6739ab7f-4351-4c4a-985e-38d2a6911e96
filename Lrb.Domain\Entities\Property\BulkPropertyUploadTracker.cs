﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Domain.Entities
{
    public class BulkPropertyUploadTracker : AuditableEntity, IAggregateRoot
    {
        public int TotalCount { get; set; }
        public int DistinctLeadCount { get; set; }
        public int TotalUploadedCount { get; set; }
        public int DuplicateCount { get; set; }
        public int InvalidCount { get; set; }
        public string? S3BucketKey { get; set; }
        public string? InvalidDataS3BucketKey { get; set; }
        public string? SheetName { get; set; }
        [Column(TypeName = "jsonb")]
        public Dictionary<PropertyDataColumns, string>? MappedColumnsData { get; set; }
        public UploadStatus Status { get; set; }
        public string? Message { get; set; }
    }
}
