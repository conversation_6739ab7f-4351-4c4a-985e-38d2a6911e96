﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Application.WhatsAppCloudApi.Web
{
    public class WhatsAppTemplateInfoDto : IDto
    {
        public string? Name { get; set; }
        public string? CodeName { get; set; }
        public WhatsAppTemplateLanguage? LanguageCode { get; set; }
        public WhatsAppHeaderTypes WhatsAppHeaderTypes { get; set; }
        public int HeaderValuesCount { get; set; }
        public int BodyValuesCount { get; set; }
        public int ButtonValuesCount { get; set; }
        public string? MediaUrl { get; set; }
        public WhatsAppServiceProvider WhatsAppServiceProvider { get; set; }
        public Event? Event { get; set; }
        public bool IsLeadSpecific { get; set; }
        public bool IsUserSpecific { get; set; }
        public IList<WhatsAppTemplateButtonsInfoDto>? WhatsAppTemplateButtonsInfos { get; set; }
        public bool ShouldSendWithFlow { get; set; }
        public string? SampleFileName { get; set; }
        public string? DefaultHeaderValue { get; set; }
        public List<string>? DefaultBodyValues { get; set; }
        public string? Message { get; set; }

    }
    public class WhatsAppTemplateButtonsInfoDto : IDto
    {
        public WhatsAppButtonType? Type { get; set; }
        public string? FlowId { get; set; }
        [Column(TypeName = "jsonb")]
        public IList<string>? NextIds { get; set; }
        public Guid? WhatsAppTemplateInfoId { get; set; }
        public int OrderRank { get; set; }
    }
}
