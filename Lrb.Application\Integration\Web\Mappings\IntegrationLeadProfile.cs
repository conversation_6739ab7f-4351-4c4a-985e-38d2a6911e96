﻿using Lrb.Application.Automation.Dtos;
using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.Gmail;
using Lrb.Application.Common.IVR;
using Lrb.Application.Common.IVR.Common.Dtos;
using Lrb.Application.Common.Knowlariry;
using Lrb.Application.Common.Servetel.FeederDto;
using Lrb.Application.Common.Servetel.ResponseDtos;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Requests;
using Lrb.Application.Integration.Web.Requests.IVR;
using Lrb.Application.Integration.Web.Requests.ListingSites;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.Utils;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.Integration;
using Lrb.Domain.Entities.Integration;
using Lrb.Domain.Entities.Integration;
using Lrb.Shared.Extensions;
using Mapster;
using Newtonsoft.Json;
using System.Configuration;

namespace Lrb.Application.Integration.Web
{
    public static class IntegrationMappings
    {
        public static void Configure()
        {
            TypeAdapterConfig<LeadData, IntegrationLeadInfo>
                .NewConfig()
                .TwoWays()
                .Map(dest => dest.Mobile, src => src.ContactNo);
            TypeAdapterConfig<IntegrationLeadInfo, Domain.Entities.Lead>
                .NewConfig()
                .TwoWays()
                .Map(dest => dest.ContactNo, src => src.Mobile);
            TypeAdapterConfig<IntegrationLeadInfo, LeadEnquiry>
                .NewConfig()
                .Ignore(dest => dest.PropertyType)
                .Map(dest => dest.UpperBudget, src => GetLongValue(src.Budget ?? "0"))
                .Map(dest => dest.LowerBudget, src => GetLongValue(src.Budget ?? "0"));
            TypeAdapterConfig<IntegrationLeadInfo, Address>
                .NewConfig()
                .Map(dest => dest.SubLocality, src => src.Location)
                .Map(dest => dest.State, src => src.State)
                .Map(dest => dest.City, src => src.City)
                .Map(dest => dest.Country, src => src.Country)
                .Ignore(src => src.Location);
            TypeAdapterConfig<IntegrationAccountInfo, IntegrationDto>
                .NewConfig()
                .Map(dest => dest.AccountId, src => src.Id)
                .Map(dest => dest.IVRServiceProvider, src => src.IVRApiConfiguration != null ? src.IVRApiConfiguration.IVRServiceProvider.ToString() : null)
                .Map(dest => dest.IVRType, src => src.LeadSource == LeadSource.IVR && src.Credentials != null && src.Credentials.Any() ? IVRType.Outbound : src.IVRCallType != null ? src.IVRCallType : IVRType.Inbound);
            TypeAdapterConfig<IVRCallLog, Domain.Entities.Lead>
                .NewConfig()
                .Map(dest => dest.ContactNo, src => src.CustomerNumber.Substring(src.CustomerNumber.Length - 10));
            TypeAdapterConfig<ServetelIntegrationOutboundUpdatedDto, ServetelCallLog>
                .NewConfig()
                .Map(dest => dest.CallerIdNumber, src => src.caller_id_number)
                .Map(dest => dest.Uuid, src => src.uuid)
                .Map(dest => dest.CallToNumber, src => src.call_to_number)
                .Map(dest => dest.CustomerNumber, src => (src.direction != null && src.direction.Contains("inbound", StringComparison.InvariantCultureIgnoreCase)) ? src.caller_id_number : src.call_to_number)
                .Map(dest => dest.StartStamp, src => src.start_stamp)
                .Map(dest => dest.AnswerStamp, src => src.answer_stamp)
                .Map(dest => dest.EndStamp, src => src.end_stamp)
                .Map(dest => dest.HangupCause, src => src.hangup_cause)
                .Map(dest => dest.Billsec, src => src.billsec)
                .Map(dest => dest.DigitsDialed, src => src.digits_dialed)
                .Map(dest => dest.Direction, src => src.direction)
                .Map(dest => dest.Duration, src => src.duration)
                .Map(dest => dest.AnsweredAgent, src => JsonConvert.SerializeObject(src.answered_agent))
                .Map(dest => dest.AnsweredAgentName, src => src.answered_agent_name != "_name" ? src.answered_agent_name : string.Empty)
                .Map(dest => dest.AnsweredAgentNumber, src => src.answered_agent_number != "_number" ? src.answered_agent_number : string.Empty)
                .Map(dest => dest.MissedAgent, src => JsonConvert.SerializeObject(src.missed_agent))
                .Map(dest => dest.CallFlow, src => JsonConvert.SerializeObject(src.call_flow))
                .Map(dest => dest.BroadcastLeadFields, src => src.broadcast_lead_fields)
                .Map(dest => dest.RecordingUrl, src => src.recording_url)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.OutboundSec, src => src.outbound_sec)
                .Map(dest => dest.AgentRingTime, src => src.agent_ring_time)
                .Map(dest => dest.BillingCircle, src => JsonConvert.SerializeObject(src.billing_circle))
                .Map(dest => dest.CallConnected, src => src.call_connected);

            TypeAdapterConfig<ServetelOutboundIntegrationRequest, ServetelIntegrationOutboundUpdatedDto>
                .NewConfig()
                .Ignore(dest => dest.answered_agent)
                .Ignore(dest => dest.missed_agent)
                .Ignore(dest => dest.call_flow);

            TypeAdapterConfig<ServetelIntegrationInboundUpdatedDto, ServetelCallLog>
                .NewConfig()
                .Map(dest => dest.CallerIdNumber, src => src.caller_id_number)
                .Map(dest => dest.Uuid, src => src.uuid)
                .Map(dest => dest.CallToNumber, src => src.call_to_number)
                .Map(dest => dest.CustomerNumber, src => (src.direction != null && src.direction.Contains("inbound", StringComparison.InvariantCultureIgnoreCase)) ? src.caller_id_number : src.call_to_number)
                .Map(dest => dest.StartStamp, src => src.start_stamp)
                .Map(dest => dest.AnswerStamp, src => src.answer_stamp)
                .Map(dest => dest.EndStamp, src => src.end_stamp)
                .Map(dest => dest.HangupCause, src => src.hangup_cause)
                .Map(dest => dest.Billsec, src => src.billsec)
                .Map(dest => dest.DigitsDialed, src => src.digits_dialed)
                .Map(dest => dest.Direction, src => src.direction)
                .Map(dest => dest.Duration, src => src.duration)
                .Map(dest => dest.AnsweredAgent, src => JsonConvert.SerializeObject(src.answered_agent))
                .Map(dest => dest.AnsweredAgentName, src => src.answered_agent_name != "_name" ? src.answered_agent_name : string.Empty)
                .Map(dest => dest.AnsweredAgentNumber, src => src.answered_agent_number != "_number" ? src.answered_agent_number : string.Empty)
                .Map(dest => dest.MissedAgent, src => JsonConvert.SerializeObject(src.missed_agent))
                .Map(dest => dest.CallFlow, src => JsonConvert.SerializeObject(src.call_flow))
                .Map(dest => dest.BroadcastLeadFields, src => src.broadcast_lead_fields)
                .Map(dest => dest.RecordingUrl, src => src.recording_url)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.OutboundSec, src => src.outbound_sec)
                .Map(dest => dest.AgentRingTime, src => src.agent_ring_time)
                .Map(dest => dest.BillingCircle, src => JsonConvert.SerializeObject(src.billing_circle))
                .Map(dest => dest.CallConnected, src => src.call_connected);

            TypeAdapterConfig<TataTeleBusinessIntegrationDto, TataTeleBusinessCallLog>
                .NewConfig()
                .Map(dest => dest.CallerIdNumber, src => src.caller_id_number)
                .Map(dest => dest.Uuid, src => src.uuid)
                .Map(dest => dest.CallToNumber, src => src.call_to_number)
                .Map(dest => dest.CustomerNumber, src => (src.direction != null && src.direction.Contains("inbound", StringComparison.InvariantCultureIgnoreCase)) ? src.caller_id_number : src.call_to_number)
                .Map(dest => dest.StartStamp, src => GetFormattedDateTime(src.start_stamp))
                .Map(dest => dest.EndStamp, src => GetFormattedDateTime(src.end_stamp))
                .Map(dest => dest.AnswerStamp, src => GetFormattedDateTime(src.answer_stamp))
                .Map(dest => dest.HangupCause, src => src.hangup_cause)
                .Map(dest => dest.Billsec, src => src.billsec)
                .Map(dest => dest.DigitsDialed, src => src.digits_dialed)
                .Map(dest => dest.Direction, src => src.direction)
                .Map(dest => dest.Duration, src => src.duration)
                .Map(dest => dest.AnsweredAgent, src => src.answered_agent != null ? JsonConvert.SerializeObject(src.answered_agent) : string.Empty)
                .Map(dest => dest.AnsweredAgentName, src => src.answered_agent_name != "_name" ? src.answered_agent_name : string.Empty)
                .Map(dest => dest.AnsweredAgentNumber, src => src.answered_agent_number != "_number" ? src.answered_agent_number : string.Empty)
                .Map(dest => dest.MissedAgent, src => src.missed_agent != null ? JsonConvert.SerializeObject(src.missed_agent) : string.Empty)
                .Map(dest => dest.CallFlow, src => src.call_flow != null ? JsonConvert.SerializeObject(src.call_flow) : string.Empty)
                .Map(dest => dest.BroadcastLeadFields, src => src.broadcast_lead_fields)
                .Map(dest => dest.RecordingUrl, src => src.recording_url)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.OutboundSec, src => src.outbound_sec)
                .Map(dest => dest.AgentRingTime, src => src.agent_ring_time)
                .Map(dest => dest.BillingCircle, src => src.billing_circle != null ? JsonConvert.SerializeObject(src.billing_circle) : string.Empty)
                .Map(dest => dest.CallConnected, src => src.call_connected);

            TypeAdapterConfig<TataTeleBusinessIntegrationUpdatedDto, TataTeleBusinessCallLog>
                .NewConfig()
                .Map(dest => dest.CallerIdNumber, src => src.caller_id_number)
                .Map(dest => dest.Uuid, src => src.uuid)
                .Map(dest => dest.CallToNumber, src => src.call_to_number)
                .Map(dest => dest.CustomerNumber, src => (src.direction != null && src.direction.Contains("inbound", StringComparison.InvariantCultureIgnoreCase)) ? src.caller_id_number : src.call_to_number)
                .Map(dest => dest.StartStamp, src => GetFormattedDateTime(src.start_stamp))
                .Map(dest => dest.EndStamp, src => GetFormattedDateTime(src.end_stamp))
                .Map(dest => dest.AnswerStamp, src => GetFormattedDateTime(src.answer_stamp))
                .Map(dest => dest.HangupCause, src => src.hangup_cause)
                .Map(dest => dest.Billsec, src => src.billsec)
                .Map(dest => dest.DigitsDialed, src => src.digits_dialed)
                .Map(dest => dest.Direction, src => src.direction)
                .Map(dest => dest.Duration, src => src.duration)
                .Map(dest => dest.AnsweredAgent, src => src.answered_agent != null ? JsonConvert.SerializeObject(src.answered_agent) : string.Empty)
                .Map(dest => dest.AnsweredAgentName, src => src.answered_agent_name != "_name" ? src.answered_agent_name : string.Empty)
                .Map(dest => dest.AnsweredAgentNumber, src => src.answered_agent_number != "_number" ? src.answered_agent_number : string.Empty)
                .Map(dest => dest.MissedAgent, src => src.missed_agent != null ? JsonConvert.SerializeObject(src.missed_agent) : string.Empty)
                .Map(dest => dest.CallFlow, src => src.call_flow != null ? JsonConvert.SerializeObject(src.call_flow) : string.Empty)
                .Map(dest => dest.BroadcastLeadFields, src => src.broadcast_lead_fields)
                .Map(dest => dest.RecordingUrl, src => src.recording_url)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.OutboundSec, src => src.outbound_sec)
                .Map(dest => dest.AgentRingTime, src => src.agent_ring_time)
                .Map(dest => dest.BillingCircle, src => src.billing_circle != null ? JsonConvert.SerializeObject(src.billing_circle) : string.Empty)
                .Map(dest => dest.CallConnected, src => src.call_connected);

            TypeAdapterConfig<TataTeleBusinessOutboundIntegrationRequest, IVRCommonCallLog>
                .NewConfig()
                .Map(dest => dest.CallId, src => src.caller_id_number)
                .Map(dest => dest.Uuid, src => src.uuid)
                .Map(dest => dest.CallToNumber, src => src.call_to_number)
                .Map(dest => dest.CustomerNumber, src => (src.direction != null && src.direction.Contains("inbound", StringComparison.InvariantCultureIgnoreCase)) ? src.caller_id_number : src.call_to_number)
                .Map(dest => dest.StartStamp, src => GetFormattedDateTime(src.start_stamp))
                .Map(dest => dest.EndStamp, src => GetFormattedDateTime(src.end_stamp))
                .Map(dest => dest.AnswerStamp, src => GetFormattedDateTime(src.answer_stamp))
                .Map(dest => dest.HangupCause, src => src.hangup_cause)
                .Map(dest => dest.Billsec, src => src.billsec)
                .Map(dest => dest.DigitsDialed, src => src.digits_dialed)
                .Map(dest => dest.Direction, src => src.direction)
                .Map(dest => dest.Duration, src => src.duration)
                .Map(dest => dest.AgentName, src => src.answered_agent_name != "_name" ? src.answered_agent_name : string.Empty)
                .Map(dest => dest.AgentNumber, src => src.answered_agent_number != "_number" ? src.answered_agent_number : string.Empty)
                .Map(dest => dest.MissedAgentName, src => JsonConvert.SerializeObject(src.missed_agent ?? string.Empty))
                .Map(dest => dest.CallFlow, src => JsonConvert.SerializeObject(src.call_flow ?? string.Empty))
                .Map(dest => dest.BroadcastLeadFields, src => src.broadcast_lead_fields)
                .Map(dest => dest.CallRecordingURL, src => src.recording_url)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.OutboundSec, src => src.outbound_sec)
                .Map(dest => dest.AgentRingTime, src => src.agent_ring_time)
                .Map(dest => dest.BillingCircle, src => JsonConvert.SerializeObject(src.billing_circle != null ? src.billing_circle : string.Empty))
                .Map(dest => dest.CallConnected, src => src.call_connected);

            TypeAdapterConfig<TataTeleBusinessInboundIntegrationRequest, IVRCommonCallLog>
                .NewConfig()
                .Map(dest => dest.CallId, src => src.caller_id_number)
                .Map(dest => dest.Uuid, src => src.uuid)
                .Map(dest => dest.CallToNumber, src => src.call_to_number)
                .Map(dest => dest.CustomerNumber, src => (src.direction != null && src.direction.Contains("inbound", StringComparison.InvariantCultureIgnoreCase)) ? src.caller_id_number : src.call_to_number)
                .Map(dest => dest.StartStamp, src => GetFormattedDateTime(src.start_stamp))
                .Map(dest => dest.EndStamp, src => GetFormattedDateTime(src.end_stamp))
                .Map(dest => dest.AnswerStamp, src => GetFormattedDateTime(src.answer_stamp))
                .Map(dest => dest.HangupCause, src => src.hangup_cause)
                .Map(dest => dest.Billsec, src => src.billsec)
                .Map(dest => dest.DigitsDialed, src => src.digits_dialed)
                .Map(dest => dest.Direction, src => src.direction)
                .Map(dest => dest.Duration, src => src.duration)
                .Map(dest => dest.AgentName, src => src.answered_agent_name != "_name" ? src.answered_agent_name : string.Empty)
                .Map(dest => dest.AgentNumber, src => src.answered_agent_number != "_number" ? src.answered_agent_number : string.Empty)
                .Map(dest => dest.MissedAgentName, src => JsonConvert.SerializeObject(src.missed_agent ?? string.Empty))
                .Map(dest => dest.CallFlow, src => JsonConvert.SerializeObject(src.call_flow ?? string.Empty))
                .Map(dest => dest.BroadcastLeadFields, src => src.broadcast_lead_fields)
                .Map(dest => dest.CallRecordingURL, src => src.recording_url)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.OutboundSec, src => src.outbound_sec)
                .Map(dest => dest.AgentRingTime, src => src.agent_ring_time)
                .Map(dest => dest.BillingCircle, src => JsonConvert.SerializeObject(src.billing_circle != null ? src.billing_circle : string.Empty))
                .Map(dest => dest.CallConnected, src => src.call_connected);


            TypeAdapterConfig<ServetelInboundIntegrationRequest, ServetelIntegrationInboundUpdatedDto>
                .NewConfig()
                .Ignore(dest => dest.answered_agent)
                .Ignore(dest => dest.missed_agent)
                .Ignore(dest => dest.call_flow);

            TypeAdapterConfig<AgentDto, ServetelIVRAgentDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.name)
                .Map(dest => dest.PhoneNumber, src => src.follow_me_number);

            TypeAdapterConfig<FbConnectedPageAccountData, FacebookConnectedPageAccount>
                .NewConfig()
                .Map(dest => dest.Name, src => src.name)
                .Map(dest => dest.AccessToken, src => src.access_token)
                .Map(dest => dest.PageToken, src => src.page_token)
                .Map(dest => dest.Category, src => src.category)
                .Map(dest => dest.FacebookId, src => src.id).TwoWays();

            TypeAdapterConfig<FbLeadGenFormInfoData, FacebookLeadGenForm>
                .NewConfig()
                .Map(dest => dest.Name, src => src.name)
                .Map(dest => dest.LeadsCount, src => src.leads_count)
                .Map(dest => dest.FacebookId, src => src.id)
                .Map(dest => dest.PageId, src => src.page_id)
                .Map(dest => dest.Status, src => src.status).TwoWays();

            TypeAdapterConfig<MyOperatorIVRInboundRequest, MyOperatorCallLog>
                .NewConfig()
                .Map(dest => dest.StartTimeOfCall, src => DateTimeOffset.FromUnixTimeSeconds(src.StartTimeOfCall).UtcDateTime)
                .Map(dest => dest.CallEndTime, src => DateTimeOffset.FromUnixTimeSeconds(src.CallEndTime).UtcDateTime)
                .Map(dest => dest.LogTimestamp, src => DateTimeOffset.FromUnixTimeSeconds(src.LogTimestamp).UtcDateTime)
                .Map(dest => dest.LogTimestampInMS, src => DateTimeOffset.FromUnixTimeMilliseconds(src.LogTimestampInMS).UtcDateTime);


            //related to user's assignments
            TypeAdapterConfig<FacebookAdsInfo, AssignmentEntityDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.AdName);
            TypeAdapterConfig<FacebookLeadGenForm, AssignmentEntityDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.Name);
            TypeAdapterConfig<IntegrationAccountInfo, IntegrationAssignmentEntityDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.AccountName)
                .Map(dest => dest.Source, src => src.LeadSource);
            TypeAdapterConfig<IntegrationAccountInfo, AssignmentEntityDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.AccountName);
            TypeAdapterConfig<CronberryIVRInboundRequest, CronberryCallLog>
                .NewConfig()
                .Map(dest => dest.VirtualNumber, src => src.virtual_number)
                .Map(dest => dest.CustomerNumber, src => src.customer_number)
                .Map(dest => dest.ReceiverNumber, src => src.receiver_number)
                .Map(dest => dest.CallDate, src => src.call_date)
                .Map(dest => dest.Status, src => src.status)
                .Map(dest => dest.RecordingPath, src => src.recording_path)
                .Map(dest => dest.UniqueId, src => src.unique_id);

            TypeAdapterConfig<ListingSitesIntegrationRequest, AddLocationRequest>
                .NewConfig()
                .Map(dest => dest.Locality, src => src.Location)
                .Map(dest => dest.Country, src => !string.IsNullOrWhiteSpace(src.CountryCode) && src.CountryCode.Contains("91") ? "India" : string.Empty);
            TypeAdapterConfig<WebsiteIntegrationDto, AddLocationRequest>
                .NewConfig()
                .Map(dest => dest.Locality, src => src.Location)
                .Map(dest => dest.Country, src => !string.IsNullOrWhiteSpace(src.CountryCode) && src.CountryCode.Contains("91") ? "India" : string.Empty);

            TypeAdapterConfig<FacebookLeadGenForm, FacebookLeadGenFormDto>
                .NewConfig()
                .Map(dest => dest.LeadCount, src => src.TotalLeadsCount)
                .Map(dest => dest.AgencyName, src => src.Agency != null ? src.Agency.Name : default);

            TypeAdapterConfig<FacebookAdsInfo, FacebookAdsInfoDto>
                .NewConfig()
                .Map(dest => dest.LeadCount, src => src.LeadsCount)
                .Map(dest => dest.AgencyName, src => src.Agency != null ? src.Agency.Name : default);   

            TypeAdapterConfig<IntegrationAccountInfo, BaseAssignedEntityDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.AccountName)
                .Map(dest => dest.Source, src => src.LeadSource);

            TypeAdapterConfig<IntegrationAccountInfo, AssignedFacebookIntegrationAccDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.AccountName)
                .Map(dest => dest.Source, src => src.LeadSource);

            TypeAdapterConfig<ExotelIVRInboundRequest, ExotelCallLog>
                .NewConfig()
                .Map(dest => dest.FlowId, src => src.flow_id)
                .Map(dest => dest.ExotelTenantId, src => src.tenant_id);

            TypeAdapterConfig<ExotelAppletIncomingDto, ExotelCallLog>
                .NewConfig()
                .Map(dest => dest.FlowId, src => src.flow_id)
                .Map(dest => dest.ExotelTenantId, src => src.tenant_id).TwoWays();
            TypeAdapterConfig<VoicePanelInboundIntegrationRequest, VoicePanelCallLog>
                .NewConfig()
                .Map(dest => dest.CallDuration, src => src.Call_Duration)
                .Map(dest => dest.Pulse, src => src.pulse)
                .Map(dest => dest.RemarkStatus, src => src.Remark_Status)
                .Map(dest => dest.RemarkPriority, src => src.Remark_Priority)
                .Map(dest => dest.FollowUp, src => src.Follow_Up)
                .Map(dest => dest.CustomerName, src => src.customer_name)
                .Map(dest => dest.CustomerAge, src => src.customer_age)
                .Map(dest => dest.AlternateNumber, src => src.alternate_number)
                .Map(dest => dest.CustomerAddress, src => src.customer_address)
                .Map(dest => dest.Landmark, src => src.landmark)
                .Map(dest => dest.City, src => src.city)
                .Map(dest => dest.State, src => src.state)
                .Map(dest => dest.Pincode, src => src.pincode)
                .Map(dest => dest.CustomerEmail, src => src.customer_email)
                .Map(dest => dest.Param1, src => src.param1)
                .Map(dest => dest.Param2, src => src.param2)
                .Map(dest => dest.AnsweredAgent1, src => src.answered_Agent1)
                .Map(dest => dest.DurationAgent1, src => src.duration_Agent1)
                .Map(dest => dest.AnsweredAgent2, src => src.answered_Agent2)
                .Map(dest => dest.DurationAgent2, src => src.duration_Agent2)
                ;
            TypeAdapterConfig<ListingSitesIntegrationRequest, CheckDuplicateLeadRequest>
                .NewConfig()
                .Map(dest => dest.ApiKey, src => src.ApiKey)
                .Map(dest => dest.LeadContactNo, src => src.Mobile)
                .Map(dest => dest.LeadSource, src => src.LeadSource)
                .Map(dest => dest.IntegrationAccountId, src => src.AccountId)
                .Map(dest => dest.CountryCode, src => src.CountryCode);
            TypeAdapterConfig<FacebookLrbWebhookRequest, CheckDuplicateLeadRequest>
                .NewConfig()
                .Map(dest => dest.ApiKey, src => src.ApiKey)
                .Map(dest => dest.LeadContactNo, src => src.Mobile)
                .Map(dest => dest.LeadSource, src => src.LeadSource)
                .Map(dest => dest.IntegrationAccountId, src => src.AccountId)
                .Map(dest => dest.FbFormId, src => src.FormId)
                .Map(dest => dest.FbAdId, src => src.AdId)
                .Map(dest => dest.CountryCode, src => src.CountryCode);
            TypeAdapterConfig<WebsiteIntegrationDto, CheckDuplicateLeadRequest>
                .NewConfig()
                .Map(dest => dest.LeadContactNo, src => src.Mobile)
                .Map(dest => dest.LeadSource, src => src.LeadSource)
                .Map(dest => dest.CountryCode, src => src.CountryCode);
            TypeAdapterConfig<RealStateIndiaListingSiteIntegrationRequest, CheckDuplicateLeadRequest>
                .NewConfig()
                .Map(dest => dest.ApiKey, src => src.ApiKey)
                .Map(dest => dest.LeadContactNo, src => src.Mobile)
                .Map(dest => dest.LeadSource, src => src.LeadSource)
                .Map(dest => dest.IntegrationAccountId, src => src.AccountId);
            TypeAdapterConfig<Domain.Entities.Lead, CheckDuplicateLeadRequest>
                .NewConfig()
                .Map(dest => dest.LeadContactNo, src => src.ContactNo)
                .Map(dest => dest.IntegrationAccountId, src => src.AccountId);
            TypeAdapterConfig<CheckDuplicateLeadRequest, DuplicateLeadSpecDto>
                .NewConfig()
                .Map(dest => dest.ContactNo, src => src.LeadContactNo)
                .Map(dest => dest.LeadSource, src => src.LeadSource)
                .Map(dest => dest.ProjectsList, src => src.Project != null ? new List<string> { src.Project } : default);

            TypeAdapterConfig<QKonnectOutboundRequest, IVRCommonCallLog> //Need to write complete mapping.
                .NewConfig();

            TypeAdapterConfig<QKonnectInboundRequest, DuplicateLeadSpecDto>
                .NewConfig()
                .Map(dest => dest.ContactNo, src => src.CallerNumber);

            TypeAdapterConfig<QKonnectOutboundRequest, DuplicateLeadSpecDto>
                .NewConfig()
                .Map(dest => dest.ContactNo, src => src.CallerNumber);

            TypeAdapterConfig<QKonnectOutboundRequest, IVRCommonCallLog>
                .NewConfig()
                .Map(dest => dest.AccountId, src => src.AccountId ?? default)
                .Map(dest => dest.CustomerNumber, src => src.CallerNumber)
                .Map(dest => dest.AgentNumber, src => src.AgentNumber)
                .Map(dest => dest.VirtualNumber, src => src.DestinationNumber)
                .Map(dest => dest.CallId, src => src.CallID)
                .Map(dest => dest.StartStamp, src => src.CallStartTime != null ? src.CallStartTime.Value.ConvertAndSetKindAsUtc() : default)
                .Map(dest => dest.EndStamp, src => src.CallEndTime != null ? src.CallEndTime.Value.ConvertAndSetKindAsUtc() : default)
                .Map(dest => dest.AnswerStamp, src => src.CallPickUpTime != null ? src.CallPickUpTime.Value.ConvertAndSetKindAsUtc() : default)
                .Map(dest => dest.Duration, src => src.TotalCallDuration)
                .Map(dest => dest.IVRDuration, src => src.IVRDuration)
                .Map(dest => dest.CallRecordingURL, src => src.CallRecordingURL)
                .Map(dest => dest.HangupCause, src => src.CallHangupCause)
                .Map(dest => dest.CallStatus, src => src.CallStatus)
                .Map(dest => dest.ServiceProviders, src => src.AccountId != null || src.AccountId == null ? Domain.Enums.IVRServiceProvider.QKonnect : IVRServiceProvider.QKonnect)
                .Map(dest => dest.Direction, src => src.AccountId != null || src.AccountId == null ? "Outbound" : "Outbound");

            TypeAdapterConfig<QKonnectInboundRequest, IVRCommonCallLog>
                .NewConfig()
                .Map(dest => dest.AccountId, src => src.AccountId ?? default)
                .Map(dest => dest.CustomerNumber, src => src.CallerNumber)
                .Map(dest => dest.AgentNumber, src => src.AgentNumber)
                .Map(dest => dest.VirtualNumber, src => src.DestinationNumber)
                .Map(dest => dest.CallId, src => src.CallID)
                .Map(dest => dest.StartStamp, src => src.CallStartTime != null ? src.CallStartTime.Value.ConvertAndSetKindAsUtc() : default)
                .Map(dest => dest.EndStamp, src => src.CallEndTime != null ? src.CallEndTime.Value.ConvertAndSetKindAsUtc() : default)
                .Map(dest => dest.AnswerStamp, src => src.CallPickUpTime != null ? src.CallPickUpTime.Value.ConvertAndSetKindAsUtc() : default)
                .Map(dest => dest.Duration, src => src.TotalCallDuration)
                .Map(dest => dest.IVRDuration, src => src.IVRDuration)
                .Map(dest => dest.CallRecordingURL, src => src.CallRecordingURL)
                .Map(dest => dest.HangupCause, src => src.CallHangupCause)
                .Map(dest => dest.CallStatus, src => src.CallStatus)
                .Map(dest => dest.ServiceProviders, src => src.AccountId != null || src.AccountId == null ? Domain.Enums.IVRServiceProvider.QKonnect : IVRServiceProvider.QKonnect)
                .Map(dest => dest.Direction, src => src.AccountId != null || src.AccountId == null ? "Inbound" : "Inbound");

            TypeAdapterConfig<IVRCommonCallLog, QKonnectCallLog>
                .NewConfig()
                .Map(dest => dest.AccountId, src => src.AccountId)
                .Map(dest => dest.CallerNumber, src => src.CustomerNumber)
                .Map(dest => dest.AgentNumber, src => src.AgentNumber)
                .Map(dest => dest.DestinationNumber, src => src.VirtualNumber)
                .Map(dest => dest.CallID, src => src.CallId)
                .Map(dest => dest.CallStartTime, src => src.StartStamp)
                .Map(dest => dest.CallEndTime, src => src.EndStamp)
                .Map(dest => dest.CallPickUpTime, src => src.AnswerStamp)
                .Map(dest => dest.TotalCallDuration, src => src.Duration)
                .Map(dest => dest.IVRDuration, src => src.IVRDuration)
                .Map(dest => dest.CallRecordingURL, src => src.CallRecordingURL)
                .Map(dest => dest.CallHangupCause, src => src.HangupCause)
                .Map(dest => dest.CallStatus, src => src.CallStatus);

            TypeAdapterConfig<IntegrationAccountInfo, IntegrationAccountInfoCommonDto>
                .NewConfig()
                .Map(dest => dest.AccountName, src => src.AccountName)
                .Map(dest => dest.LoginEmail, src => src.LoginEmail)
                .Map(dest => dest.LeadSource, src => src.LeadSource)
                .Map(dest => dest.VirtualNumbers, src => src.IVRAssignments != null ? src.IVRAssignments.Select(i => i.VirtualNumber).ToList() : new())
                .Map(dest => dest.IVRServiceProvider, src => src.IVRApiConfiguration != null && src.IVRApiConfiguration.IVRServiceProvider != default ? src.IVRApiConfiguration.IVRServiceProvider : default)
                .Map(dest => dest.AuthToken, src => src.IVRApiConfiguration != null && !string.IsNullOrWhiteSpace(src.IVRApiConfiguration.AuthToken) ? src.IVRApiConfiguration.AuthToken : default)
                .Map(dest => dest.ApiKey, src => src.IVRApiConfiguration != null && !string.IsNullOrWhiteSpace(src.IVRApiConfiguration.ApiKey) ? src.IVRApiConfiguration.ApiKey : default)
                .Map(dest => dest.BaseUri, src => src.IVRApiConfiguration != null && !string.IsNullOrWhiteSpace(src.IVRApiConfiguration.BaseUri) ? src.IVRApiConfiguration.BaseUri : default)
                .Map(dest => dest.IsPrimary, src => src.IsPrimary)
                .Map(dest => dest.SMEId, src => src.IVRApiConfiguration != null && !string.IsNullOrWhiteSpace(src.IVRApiConfiguration.SMEId) ? src.IVRApiConfiguration.SMEId : default)
                .Map(dest => dest.AccountSId, src => src.IVRApiConfiguration != null && !string.IsNullOrWhiteSpace(src.IVRApiConfiguration.AccountSId) ? src.IVRApiConfiguration.AccountSId : default);

            TypeAdapterConfig<ClickToCallCommonDto, ClickToCallDto>
                .NewConfig()
                .Map(dest => dest.destination_number, src => src.DestinationNumber)
                .Map(dest => dest.agent_number, src => src.AgentNumber)
                .Map(dest => dest.caller_id, src => src.CallerIdOrVirtualNumber);

            TypeAdapterConfig<IntegrationAccountInfo, ViewIntegrationAccountDto>
                .NewConfig()
                .Map(dest => dest.IVRAssignments, src => src.IVRAssignments != null ? src.IVRAssignments.Select(i => new IVRAssignmentDto()
                {
                    AgencyName = i.AgencyName,
                    UserIds = i.UserIds,
                    VirtualNumber = i.VirtualNumber,
                    ProjectId = i.Assignment != null && i.Assignment.Project != null ? i.Assignment.Project.Id : Guid.Empty,
                    LocationId = i.Assignment != null && i.Assignment.Location != null ? i.Assignment.Location.Id : Guid.Empty
                }) : new List<IVRAssignmentDto>())
                .Map(dest => dest.ServiceProviderName, src => src.IVRServiceProvider);

            TypeAdapterConfig<FreJunInboundRequest, IVRCommonCallLog>
                .NewConfig()
                .Map(dest => dest.AccountId, src => src.AccountId ?? default)
                .Map(dest => dest.CustomerNumber, src => src.candidate_number)
                .Map(dest => dest.AgentNumber, src => src.agent_number)
                .Map(dest => dest.VirtualNumber, src => src.virtual_number)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.StartStamp, src => GetFormattedDateTime(src.start_time))
                .Map(dest => dest.EndStamp, src => GetFormattedDateTime(src.end_time))
                //.Map(dest => dest.AnswerStamp, src => src.CallPickUpTime != null ? src.CallPickUpTime.Value.ConvertAndSetKindAsUtc() : default)
                //.Map(dest => dest.Duration, src => src.TotalCallDuration)
                //.Map(dest => dest.IVRDuration, src => src.IVRDuration)
                .Map(dest => dest.CallRecordingURL, src => src.recording_url)
                //.Map(dest => dest.HangupCause, src => src.CallHangupCause)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.ServiceProviders, src => src.AccountId != null || src.AccountId == null ? Domain.Enums.IVRServiceProvider.FreJun : IVRServiceProvider.FreJun)
                .Map(dest => dest.Direction, src => src.call_type);

            TypeAdapterConfig<FreJunWebhookRequest, IVRCommonCallLog>
                .NewConfig()
                .Map(dest => dest.AccountId, src => src.AccountId ?? default)
                .Map(dest => dest.CustomerNumber, src => src.candidate_number)
                .Map(dest => dest.AgentNumber, src => src.agent_number)
                .Map(dest => dest.VirtualNumber, src => src.virtual_number)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.StartStamp, src => GetFormattedDateTime(src.start_time))
                .Map(dest => dest.EndStamp, src => GetFormattedDateTime(src.end_time))
                //.Map(dest => dest.AnswerStamp, src => src.CallPickUpTime != null ? src.CallPickUpTime.Value.ConvertAndSetKindAsUtc() : default)
                //.Map(dest => dest.Duration, src => src.TotalCallDuration)
                //.Map(dest => dest.IVRDuration, src => src.IVRDuration)
                .Map(dest => dest.CallRecordingURL, src => src.recording_url)
                //.Map(dest => dest.HangupCause, src => src.CallHangupCause)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.ServiceProviders, src => src.AccountId != null || src.AccountId == null ? Domain.Enums.IVRServiceProvider.FreJun : IVRServiceProvider.FreJun)
                .Map(dest => dest.Direction, src => src.call_type);

            TypeAdapterConfig<ClickToCallCommonDto, FreJunC2CDto>
                .NewConfig()
                .Map(dest => dest.user_email, src => src.UserEmail)
                .Map(dest => dest.candidate_number, src => src.DestinationNumber);

            TypeAdapterConfig<FreJunWebhookRequest, FreJunCallLog>
                .NewConfig()
                .Map(dest => dest.Event, src => src.@event)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.CandidateNumber, src => src.candidate_number)
                .Map(dest => dest.VirtualNumber, src => src.virtual_number)
                .Map(dest => dest.AgentNumber, src => src.agent_number)
                .Map(dest => dest.CallCreator, src => src.call_creator)
                .Map(dest => dest.StartTime, src => src.start_time)
                .Map(dest => dest.EndTime, src => src.end_time)
                .Map(dest => dest.CallType, src => src.call_type)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.RecordingUrl, src => src.recording_url);
            TypeAdapterConfig<FreJunInboundRequest, FreJunCallLog>
                .NewConfig()
                .Map(dest => dest.Event, src => src.@event)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.CandidateNumber, src => src.candidate_number)
                .Map(dest => dest.VirtualNumber, src => src.virtual_number)
                .Map(dest => dest.AgentNumber, src => src.agent_number)
                .Map(dest => dest.CallCreator, src => src.call_creator)
                .Map(dest => dest.StartTime, src => src.start_time)
                .Map(dest => dest.EndTime, src => src.end_time)
                .Map(dest => dest.CallType, src => src.call_type)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.RecordingUrl, src => src.recording_url);
            TypeAdapterConfig<FreJunCallLog, IVRCommonCallLog>
                .NewConfig()
                .Map(dest => dest.CallId, src => src.CallId)
                .Map(dest => dest.CustomerNumber, src => src.CandidateNumber)
                .Map(dest => dest.VirtualNumber, src => src.VirtualNumber)
                .Map(dest => dest.AgentNumber, src => src.AgentNumber)
                .Map(dest => dest.StartStamp, src => src.StartTime)
                .Map(dest => dest.EndStamp, src => src.EndTime)
                .Map(dest => dest.Direction, src => src.CallType)
                .Map(dest => dest.CallStatus, src => src.CallStatus)
                .Map(dest => dest.CallRecordingURL, src => src.RecordingUrl)
                .Map(dest => dest.Duration, src => (!string.IsNullOrWhiteSpace(src.EndTime) && !string.IsNullOrWhiteSpace(src.StartTime)) ? (DateTime.Parse(src.EndTime) - DateTime.Parse(src.StartTime)).Seconds : 0);
            TypeAdapterConfig<IVRCommonCallLog, FreJunCallLog>
                .NewConfig()
                .Map(dest => dest.CallId, src => src.CallId)
                .Map(dest => dest.CandidateNumber, src => src.CustomerNumber)
                .Map(dest => dest.VirtualNumber, src => src.VirtualNumber)
                .Map(dest => dest.AgentNumber, src => src.AgentNumber)
                .Map(dest => dest.StartTime, src => src.StartStamp)
                .Map(dest => dest.EndTime, src => src.EndStamp)
                .Map(dest => dest.CallType, src => src.Direction)
                .Map(dest => dest.CallStatus, src => src.CallStatus)
                .Map(dest => dest.RecordingUrl, src => src.CallRecordingURL);

            TypeAdapterConfig<MCubeWebhookDto, MCubeWebhookRequest>
                .NewConfig()
                .Map(dest => dest.call_id, src => src.call_id)
                .Map(dest => dest.answered_agent_number, src => src.answered_agent_number)
                .Map(dest => dest.call_to_number, src => src.call_to_number)
                .Map(dest => dest.start_stamp, src => src.start_stamp)
                .Map(dest => dest.end_stamp, src => src.end_stamp)
                .Map(dest => dest.recording_url, src => src.recording_url)
                .Map(dest => dest.call_status, src => src.call_status)
                .Map(dest => dest.duration, src => src.duration)
                .Map(dest => dest.agent_ring_time, src => src.agent_ring_time)
                .Map(dest => dest.direction, src => src.direction);

            TypeAdapterConfig<MCubeWebhookRequest, MCubeCallLog>
                .NewConfig()
                .Map(dest => dest.Uuid, src => src.uuid)
                .Map(dest => dest.CustomerNumber, src => src.call_to_number)
                .Map(dest => dest.CallerIdNumber, src => src.caller_id_number)
                .Map(dest => dest.AnsweredAgentNumber, src => src.answered_agent_number)
                .Map(dest => dest.StartStamp, src => src.start_stamp)
                .Map(dest => dest.EndStamp, src => src.end_stamp)
                .Map(dest => dest.Direction, src => src.direction)
                .Map(dest => dest.Duration, src => src.duration)
                .Map(dest => dest.RecordingUrl, src => src.recording_url)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.VirtualNumber, src => src.virtual_number);

            TypeAdapterConfig<MCubeUpdatedDto, MCubeCallLog>
                .NewConfig()
                .Map(dest => dest.Uuid, src => src.uuid)
                .Map(dest => dest.CustomerNumber, src => src.call_to_number)
                .Map(dest => dest.CallerIdNumber, src => src.caller_id_number)
                .Map(dest => dest.AnsweredAgentNumber, src => src.answered_agent_number)
                .Map(dest => dest.StartStamp, src => src.start_stamp)
                .Map(dest => dest.EndStamp, src => src.end_stamp)
                .Map(dest => dest.Direction, src => src.direction)
                .Map(dest => dest.Duration, src => src.duration)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.RecordingUrl, src => src.recording_url)
                .Map(dest => dest.VirtualNumber, src => src.virtual_number);

            TypeAdapterConfig<MCubeCallLog, IVRCommonCallLog>
                .NewConfig()
                .Map(dest => dest.CallId, src => src.CallId)
                .Map(dest => dest.CustomerNumber, src => src.CustomerNumber)
                .Map(dest => dest.VirtualNumber, src => src.VirtualNumber)
                .Map(dest => dest.AgentNumber, src => src.AnsweredAgentNumber)
                .Map(dest => dest.StartStamp, src => GetFormattedDateTime(src.StartStamp))
                .Map(dest => dest.EndStamp, src => GetFormattedDateTime(src.EndStamp))
                .Map(dest => dest.Direction, src => src.Direction)
                .Map(dest => dest.CallStatus, src => src.CallStatus)
                .Map(dest => dest.CallRecordingURL, src => src.RecordingUrl)
                .Map(dest => dest.VirtualNumber, src => src.VirtualNumber);

            TypeAdapterConfig<ClickToCallCommonDto, KnowlarityMakeCallDto>
                .NewConfig()
                .Map(dest => dest.customer_number, src => src.DestinationNumber)
                .Map(dest => dest.agent_number, src => src.AgentNumber)
                .Map(dest => dest.k_number, src => src.CallerIdOrVirtualNumber)
                .Map(dest => dest.caller_id, src => src.CallerId);

            TypeAdapterConfig<IVRIntegrationRequest, DuplicateLeadSpecDto>
                .NewConfig()
                .Map(dest => dest.ContactNo, src => src.CustomerNumber);

            TypeAdapterConfig<IVRCallLog, IVRCommonCallLog>
                .NewConfig()
                .Map(dest => dest.AccountId, src => src.AccountId)
                .Map(dest => dest.VirtualNumber, src => src.CalledNumber)
                .Map(dest => dest.CallStatus, src => src.CallStatus)
                .Map(dest => dest.AgentNumber, src => src.AgentNumber)
                .Map(dest => dest.Direction, src => src.CallDirection)
                .Map(dest => dest.CustomerNumber, src => src.CustomerNumber)
                .Map(dest => dest.CallId, src => src.CallUuid)
                .Map(dest => dest.CallRecordingURL, src => src.RecordingUrl)
                .Map(dest => dest.Duration, src => GetSecondsFromTimeSpan(src.CallDuration));

            TypeAdapterConfig<IVRCommonCallLog, IVRCallLog>
                .NewConfig()
                .Map(dest => dest.AccountId, src => src.AccountId)
                .Map(dest => dest.CallTime, src => src.StartStamp.Value.Date)
                .Map(dest => dest.CallDate, src => src.StartStamp.Value.TimeOfDay)
                .Map(dest => dest.CalledNumber, src => src.VirtualNumber)
                .Map(dest => dest.CallStatus, src => src.CallStatus)
                .Map(dest => dest.AgentNumber, src => src.AgentNumber)
                .Map(dest => dest.CallDirection, src => src.Direction)
                .Map(dest => dest.CallDuration, src => src.Duration)
                .Map(dest => dest.CustomerNumber, src => src.CustomerNumber)
                .Map(dest => dest.CallUuid, src => src.CallId)
                .Map(dest => dest.RecordingUrl, src => src.CallRecordingURL);

            TypeAdapterConfig<IVROutboundConfigurationDto, IVROutboundConfiguration>
                .NewConfig()
                .Map(dest => dest.MethodType, src => src.MethodType.Replace(" ", "").ToUpper())
                .Map(dest => dest.BaseURL, src => src.BaseURL.Replace(" ", "").ToLower())
                .Map(dest => dest.Resources, src => src.Resources != null ? src.Resources.Replace(" ", "") : default);

            #region IVR Redirection Mappings

            TypeAdapterConfig<ServetelInboundIntegrationRequest, CommonIVRWebhookRequest>
                .NewConfig()
                .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
                .Map(dest => dest.AccountId, src => src.AccountId)
                .Map(dest => dest.ApiKey, src => src.ApiKey);

            TypeAdapterConfig<ServetelInboundIntegrationRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => src.caller_id_number)
                .Map(dest => dest.AgentNumber, src => !string.IsNullOrWhiteSpace(src.answered_agent_number) ? src.answered_agent_number : src.call_to_number)
                .Map(dest => dest.VirtualNumber, src => src.virtual_number)
                .Map(dest => dest.CallId, src => !string.IsNullOrWhiteSpace(src.call_id) ? src.call_id : src.uuid)
                .Map(dest => dest.HangupCause, src => src.hangup_cause)
                .Map(dest => dest.Billsec, src => src.billsec)
                .Map(dest => dest.DigitsDialed, src => src.digits_dialed)
                .Map(dest => dest.Direction, src => src.direction)
                .Map(dest => dest.AgentName, src => src.answered_agent_name)
                .Map(dest => dest.CallRecordingURL, src => src.recording_url)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.StartStamp, src => src.start_stamp)
                .Map(dest => dest.AnswerStamp, src => src.answer_stamp)
                .Map(dest => dest.EndStamp, src => src.end_stamp)
                .Map(dest => dest.Duration, src => src.duration);

            TypeAdapterConfig<ServetelOutboundIntegrationRequest, CommonIVRWebhookRequest>
                .NewConfig()
                .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
                .Map(dest => dest.AccountId, src => src.AccountId)
                .Map(dest => dest.ApiKey, src => src.ApiKey);

            //Missing Data - Virtual Number
            TypeAdapterConfig<ServetelOutboundIntegrationRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => src.call_to_number)
                .Map(dest => dest.AgentNumber, src => !string.IsNullOrWhiteSpace(src.answered_agent_number) ? src.answered_agent_number : src.caller_id_number)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.HangupCause, src => src.hangup_cause)
                .Map(dest => dest.Billsec, src => src.billsec)
                .Map(dest => dest.DigitsDialed, src => src.digits_dialed)
                .Map(dest => dest.Direction, src => src.direction)
                .Map(dest => dest.AgentName, src => src.answered_agent_name)
                .Map(dest => dest.CallRecordingURL, src => src.recording_url)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.StartStamp, src => src.start_stamp)
                .Map(dest => dest.AnswerStamp, src => src.answer_stamp)
                .Map(dest => dest.EndStamp, src => src.end_stamp)
                .Map(dest => dest.Duration, src => src.duration);

            TypeAdapterConfig<CronberryIVRInboundRequest, CommonIVRWebhookRequest>
                .NewConfig()
                .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
                .Map(dest => dest.AccountId, src => src.AccountId)
                .Map(dest => dest.ApiKey, src => src.ApiKey);

            TypeAdapterConfig<CronberryIVRInboundRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => src.customer_number)
                .Map(dest => dest.AgentNumber, src => src.receiver_number)
                .Map(dest => dest.VirtualNumber, src => src.virtual_number)
                .Map(dest => dest.CallId, src => src.unique_id)
                .Map(dest => dest.CallRecordingURL, src => src.recording_path)
                .Map(dest => dest.CallStatus, src => src.status)
                .Map(dest => dest.StartStamp, src => src.call_date);

            TypeAdapterConfig<ExotelIVRInboundRequest, CommonIVRWebhookRequest>
                .NewConfig()
                .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
                .Map(dest => dest.AccountId, src => src.AccountId)
                .Map(dest => dest.ApiKey, src => src.ApiKey);

            TypeAdapterConfig<ExotelIVRInboundRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => src.CallFrom)
                .Map(dest => dest.AgentNumber, src => src.DialWhomNumber)
                .Map(dest => dest.VirtualNumber, src => src.CallTo)
                .Map(dest => dest.CallId, src => src.CallSid)
                .Map(dest => dest.Direction, src => src.Direction)
                .Map(dest => dest.CallRecordingURL, src => src.RecordingUrl)
                .Map(dest => dest.CallStatus, src => src.ProcessStatus)
                .Map(dest => dest.StartStamp, src => src.StartTime)
                .Map(dest => dest.EndStamp, src => src.EndTime)
                .Map(dest => dest.Duration, src => src.DialCallDuration);

            TypeAdapterConfig<FreJunInboundRequest, CommonIVRWebhookRequest>
                .NewConfig()
                .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
                .Map(dest => dest.AccountId, src => src.AccountId);

            TypeAdapterConfig<FreJunInboundRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => src.candidate_number)
                .Map(dest => dest.AgentNumber, src => src.agent_number)
                .Map(dest => dest.VirtualNumber, src => src.virtual_number)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.Direction, src => src.call_type)
                .Map(dest => dest.CallRecordingURL, src => !string.IsNullOrWhiteSpace(src.recording_url) ? src.recording_url : src.recording)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.StartStamp, src => src.start_time)
                .Map(dest => dest.EndStamp, src => src.end_time)
                .Map(dest => dest.Duration, src => (GetFormattedDateTime(src.end_time) - GetFormattedDateTime(src.start_time)).Value.TotalSeconds);

            TypeAdapterConfig<FreJunWebhookRequest, CommonIVRWebhookRequest>
                .NewConfig()
                .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
                .Map(dest => dest.AccountId, src => src.AccountId);

            TypeAdapterConfig<FreJunWebhookRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => src.candidate_number)
                .Map(dest => dest.AgentNumber, src => src.agent_number)
                .Map(dest => dest.VirtualNumber, src => src.virtual_number)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.Direction, src => src.call_type)
                .Map(dest => dest.CallRecordingURL, src => !string.IsNullOrWhiteSpace(src.recording_url) ? src.recording_url : src.recording)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.StartStamp, src => src.start_time)
                .Map(dest => dest.EndStamp, src => src.end_time)
                .Map(dest => dest.Duration, src => (long)(GetFormattedDateTime(src.end_time) - GetFormattedDateTime(src.start_time)).Value.TotalSeconds);

            TypeAdapterConfig<IVRIntegrationRequest, CommonIVRWebhookRequest>
                .NewConfig()
                .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
                .Map(dest => dest.AccountId, src => src.AccountId)
                .Map(dest => dest.ApiKey, src => src.ApiKey);

            TypeAdapterConfig<IVRIntegrationRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => src.CustomerNumber)
                .Map(dest => dest.AgentNumber, src => src.AgentNumber)
                .Map(dest => dest.VirtualNumber, src => src.CalledNumber)
                .Map(dest => dest.Direction, src => src.CallDirection)
                .Map(dest => dest.CallRecordingURL, src => src.RecordingUrl)
                .Map(dest => dest.CallStatus, src => src.CallStatus)
                .Map(dest => dest.StartStamp, src => GetTimeStampFromDateAndTime(src.CallDate, src.CallTime))
                .Map(dest => dest.Duration, src => GetLongValueFromTimeFormat(src.CallDuration))
                .Map(dest => dest.CallId, src => src.CallUuid);

            TypeAdapterConfig<MCubeWebhookRequest, CommonIVRWebhookRequest>
               .NewConfig()
               .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
               .Map(dest => dest.AccountId, src => src.AccountId)
               .Map(dest => dest.ApiKey, src => src.ApiKey);

            TypeAdapterConfig<MCubeWebhookRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => src.caller_id_number)
                .Map(dest => dest.AgentNumber, src => src.answered_agent_number)
                .Map(dest => dest.VirtualNumber, src => src.virtual_number)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.Direction, src => src.direction)
                .Map(dest => dest.AgentName, src => src.answered_agent_name)
                .Map(dest => dest.CallRecordingURL, src => src.recording_url)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.StartStamp, src => GetFormattedDateTime(src.start_stamp))
                .Map(dest => dest.AnswerStamp, src => GetFormattedDateTime(src.answer_stamp))
                .Map(dest => dest.EndStamp, src => GetFormattedDateTime(src.end_stamp))
                .Map(dest => dest.Duration, src => GetLongValue(src.duration));

            TypeAdapterConfig<KommunoOutboundRequest, CommonIVRWebhookRequest>
               .NewConfig()
               .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
               .Map(dest => dest.AccountId, src => src.AccountId)
               .Map(dest => dest.ApiKey, src => src.ApiKey);

            //Missing fields - Virtual Number
            TypeAdapterConfig<KommunoOutboundRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => GetValidStringValue(src.CustomerNumber, src.To))
                .Map(dest => dest.AgentNumber, src => GetValidStringValue(src.AgentNumber, src.From))
                .Map(dest => dest.CallId, src => src.SessionId)
                .Map(dest => dest.Direction, src => src.CallMode != null ? src.CallMode.Contains("IN") ? "INBOUND" : "OUTBOUND" : null)
                .Map(dest => dest.CallRecordingURL, src => src.RecordingFile != "0" ? src.RecordingFile : null)
                .Map(dest => dest.CallStatus, src => src.CallStatus)
                .Map(dest => dest.StartStamp, src => GetFormattedDateTime(src.StartTime))
                .Map(dest => dest.EndStamp, src => GetFormattedDateTime(src.EndTime))
                .Map(dest => dest.Duration, src => GetLongValue(src.CallDuration ?? "0"))
                .Map(dest => dest.RawData, src => src.Serialize());

            TypeAdapterConfig<KommunoInboundRequest, CommonIVRWebhookRequest>
               .NewConfig()
               .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
               .Map(dest => dest.AccountId, src => src.AccountId)
               .Map(dest => dest.ApiKey, src => src.ApiKey);

            //Missing fields - Virtual Number
            TypeAdapterConfig<KommunoInboundRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => GetValidStringValue(src.CustomerNumber, src.From))
                .Map(dest => dest.AgentNumber, src => GetValidStringValue(src.AgentNumber, src.To))
                .Map(dest => dest.CallId, src => src.SessionId)
                .Map(dest => dest.Direction, src => src.CallMode != null ? src.CallMode.Contains("IN") ? "INBOUND" : "OUTBOUND" : null)
                .Map(dest => dest.CallRecordingURL, src => src.RecordingFile != "0" ? src.RecordingFile : null)
                .Map(dest => dest.CallStatus, src => src.CallStatus)
                .Map(dest => dest.StartStamp, src => GetFormattedDateTime(src.StartTime))
                .Map(dest => dest.EndStamp, src => GetFormattedDateTime(src.EndTime))
                .Map(dest => dest.Duration, src => GetLongValue(src.CallDuration ?? "0"))
                .Map(dest => dest.RawData, src => src.Serialize());

            TypeAdapterConfig<MyOperatorIVRInboundRequest, CommonIVRWebhookRequest>
                .NewConfig()
                .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
                .Map(dest => dest.AccountId, src => src.AccountId)
                .Map(dest => dest.ApiKey, src => src.ApiKey);

            TypeAdapterConfig<MyOperatorIVRInboundRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => src.CallerNumberFormatted)
                .Map(dest => dest.AgentNumber, src => src._ld.FirstOrDefault()._rr.FirstOrDefault().AgentContactNumber)
                .Map(dest => dest.CallId, src => src.UniqueCallerId)
                .Map(dest => dest.Direction, src => src != null ? "inbound" : "inbound")
                .Map(dest => dest.AgentName, src => src._ld.FirstOrDefault()._rr.FirstOrDefault().AgentName)
                .Map(dest => dest.CallRecordingURL, src => src.CallRecordingFilelink)
                .Map(dest => dest.CallStatus, src => src.StatusOfCall == 1 ? "connected" : src.StatusOfCall == 2 ? "missed" : src.StatusOfCall == 3 ? "voicemail" : src.StatusOfCall == 4 ? "success" : "none")
                .Map(dest => dest.Duration, src => GetLongValueFromTimeFormat(src.CallDuration))
                .Map(dest => dest.StartStamp, src => DateTimeOffset.FromUnixTimeSeconds(src.StartTimeOfCall).UtcDateTime)
                .Map(dest => dest.EndStamp, src => DateTimeOffset.FromUnixTimeSeconds(src.CallEndTime).UtcDateTime)
                .Map(dest => dest.VirtualNumber, src => src._ld != null && src._ld.Any() && src._ld.FirstOrDefault() != null && src._ld.FirstOrDefault().LastCallerId != null ? src._ld.FirstOrDefault().LastCallerId : string.Empty);

            //Missing Data - Virtual Number
            TypeAdapterConfig<MyOperatorCallLog, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => src.CallerNumberFormatted)
                .Map(dest => dest.AgentNumber, src => src._ld.FirstOrDefault()._rr.FirstOrDefault().AgentContactNumber)
                .Map(dest => dest.CallId, src => src.UniqueCallerId)
                .Map(dest => dest.Direction, src => src != null ? "inbound" : "inbound")
                .Map(dest => dest.AgentName, src => src._ld.FirstOrDefault()._rr.FirstOrDefault().AgentName)
                .Map(dest => dest.CallRecordingURL, src => src.CallRecordingFilelink)
                .Map(dest => dest.CallStatus, src => src.StatusOfCall == 1 ? "connected" : src.StatusOfCall == 2 ? "missed" : src.StatusOfCall == 3 ? "voicemail" : src.StatusOfCall == 4 ? "success" : "none")
                .Map(dest => dest.StartStamp, src => src.StartTimeOfCall)
                .Map(dest => dest.EndStamp, src => src.CallEndTime)
                .Map(dest => dest.Duration, src => GetLongValue(src.CallDuration));

            TypeAdapterConfig<QKonnectInboundRequest, CommonIVRWebhookRequest>
               .NewConfig()
               .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
               .Map(dest => dest.AccountId, src => src.AccountId)
               .Map(dest => dest.ApiKey, src => src.ApiKey);

            TypeAdapterConfig<QKonnectInboundRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => src.CallerNumber)
                .Map(dest => dest.AgentNumber, src => src.AgentNumber)
                .Map(dest => dest.VirtualNumber, src => src.DestinationNumber)
                .Map(dest => dest.CallId, src => src.CallID)
                .Map(dest => dest.Direction, src => src != null ? "incoming" : "incoming")
                .Map(dest => dest.CallRecordingURL, src => src.CallRecordingURL)
                .Map(dest => dest.CallStatus, src => src.CallStatus)
                .Map(dest => dest.StartStamp, src => src.CallStartTime)
                .Map(dest => dest.AnswerStamp, src => src.CallPickUpTime)
                .Map(dest => dest.EndStamp, src => src.CallEndTime)
                .Map(dest => dest.Duration, src => src.TotalCallDuration);

            TypeAdapterConfig<QKonnectOutboundRequest, CommonIVRWebhookRequest>
               .NewConfig()
               .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
               .Map(dest => dest.AccountId, src => src.AccountId)
               .Map(dest => dest.ApiKey, src => src.ApiKey);

            TypeAdapterConfig<QKonnectOutboundRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => src.CallerNumber)
                .Map(dest => dest.AgentNumber, src => src.AgentNumber)
                .Map(dest => dest.VirtualNumber, src => src.DestinationNumber)
                .Map(dest => dest.CallId, src => src.CallID)
                .Map(dest => dest.Direction, src => src != null ? "outgoing" : "outgoing")
                .Map(dest => dest.CallRecordingURL, src => src.CallRecordingURL)
                .Map(dest => dest.CallStatus, src => src.CallStatus)
                .Map(dest => dest.StartStamp, src => src.CallStartTime)
                .Map(dest => dest.AnswerStamp, src => src.CallPickUpTime)
                .Map(dest => dest.EndStamp, src => src.CallEndTime)
                .Map(dest => dest.Duration, src => src.TotalCallDuration);

            TypeAdapterConfig<TataTeleBusinessInboundIntegrationRequest, CommonIVRWebhookRequest>
                .NewConfig()
                .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
                .Map(dest => dest.AccountId, src => src.AccountId)
                .Map(dest => dest.ApiKey, src => src.ApiKey);

            //Missing Data - Virtual Number
            TypeAdapterConfig<TataTeleBusinessInboundIntegrationRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => src.caller_id_number)
                .Map(dest => dest.AgentNumber, src => !string.IsNullOrWhiteSpace(src.answered_agent_number) ? src.answered_agent_number : src.call_to_number)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.HangupCause, src => src.hangup_cause)
                .Map(dest => dest.Billsec, src => src.billsec)
                .Map(dest => dest.DigitsDialed, src => src.digits_dialed)
                .Map(dest => dest.Direction, src => src.direction)
                .Map(dest => dest.AgentName, src => src.answered_agent_name)
                .Map(dest => dest.CallRecordingURL, src => src.recording_url)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.StartStamp, src => src.start_stamp)
                .Map(dest => dest.AnswerStamp, src => src.answer_stamp)
                .Map(dest => dest.EndStamp, src => src.end_stamp)
                .Map(dest => dest.Duration, src => src.duration);

            TypeAdapterConfig<TataTeleBusinessOutboundIntegrationRequest, CommonIVRWebhookRequest>
               .NewConfig()
               .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
               .Map(dest => dest.AccountId, src => src.AccountId)
               .Map(dest => dest.ApiKey, src => src.ApiKey);

            //Missing Data - Virtual Number
            TypeAdapterConfig<TataTeleBusinessOutboundIntegrationRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => src.call_to_number)
                .Map(dest => dest.AgentNumber, src => src.caller_id_number)
                .Map(dest => dest.CallId, src => src.call_id)
                .Map(dest => dest.HangupCause, src => src.hangup_cause)
                .Map(dest => dest.Billsec, src => src.billsec)
                .Map(dest => dest.DigitsDialed, src => src.digits_dialed)
                .Map(dest => dest.Direction, src => src.direction)
                .Map(dest => dest.AgentName, src => src.answered_agent_name)
                .Map(dest => dest.CallRecordingURL, src => src.recording_url)
                .Map(dest => dest.CallStatus, src => src.call_status)
                .Map(dest => dest.StartStamp, src => src.start_stamp)
                .Map(dest => dest.AnswerStamp, src => src.answer_stamp)
                .Map(dest => dest.EndStamp, src => src.end_stamp)
                .Map(dest => dest.Duration, src => src.duration);

            TypeAdapterConfig<VoicePanelInboundIntegrationRequest, CommonIVRWebhookRequest>
               .NewConfig()
               .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
               .Map(dest => dest.AccountId, src => src.AccountId)
               .Map(dest => dest.ApiKey, src => src.ApiKey);

            //Missing Data - Call Direction
            TypeAdapterConfig<VoicePanelInboundIntegrationRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.CustomerNumber, src => src.CallingNo)
                .Map(dest => dest.AgentNumber, src => src.AgentNo)
                .Map(dest => dest.CallId, src => src.LogId)
                .Map(dest => dest.AgentName, src => src.AgentName)
                .Map(dest => dest.CallRecordingURL, src => src.RecordingLink)
                .Map(dest => dest.CallStatus, src => src.Status)
                .Map(dest => dest.StartStamp, src => GetTimeStampFromDateAndTime(src.Date, src.Time))
                .Map(dest => dest.Duration, src => src.Call_Duration);

            #endregion
            TypeAdapterConfig<UserAssignment, ViewUserAssignmentDto>
               .NewConfig()
                .Map(dest => dest.Team, src => src.Team != null ? src.Team : null)
               .Ignore(dest => dest.Module);

            TypeAdapterConfig<PropertyWalaPayloadDto, ListingSitesIntegrationRequest>
               .NewConfig()
               .Ignore(dest => dest.AdditionalProperties);
            TypeAdapterConfig<KommunoIntegrationDto, KommunoCallLog>
                .NewConfig()
                .Map(dest => dest.SMEId, src => src.SmeId);

            TypeAdapterConfig<KommunoInboundRequest, IVRCommonCallLog>
                .NewConfig()
                .Map(dest => dest.AccountId, src => src.AccountId)
                .Map(dest => dest.CallFromNumber, src => src.From)
                .Map(dest => dest.CallToNumber, src => src.To)
                .Map(dest => dest.CustomerNumber, src => src.From)
                .Map(dest => dest.VirtualNumber, src => src.To)
                .Map(dest => dest.AnswerStamp, src => GetFormattedDateTime(src.DateTime))
                .Map(dest => dest.StartStamp, src => GetFormattedDateTime(src.StartTime))
                .Map(dest => dest.EndStamp, src => GetFormattedDateTime(src.EndTime))
                .Map(dest => dest.Duration, src => src.CallDuration)
                .Map(dest => dest.AgentNumber, src => src.ConnectedAgentNumber)
                .Map(dest => dest.CallId, src => src.SmeId)
                .Map(dest => dest.CallStatus, src => src.LiveEvent)
                .Map(dest => dest.Direction, src => src.CallMode);

            TypeAdapterConfig<KommunoOutboundRequest, IVRCommonCallLog>
                .NewConfig()
                .Map(dest => dest.AccountId, src => src.AccountId)
                .Map(dest => dest.CallFromNumber, src => src.AgentNumber)
                .Map(dest => dest.CallToNumber, src => src.CustomerNumber)
                .Map(dest => dest.CustomerNumber, src => src.CustomerNumber)
                .Map(dest => dest.VirtualNumber, src => src.To)
                .Map(dest => dest.StartStamp, src => GetFormattedDateTime(src.StartTime))
                .Map(dest => dest.EndStamp, src => GetFormattedDateTime(src.EndTime))
                .Map(dest => dest.Duration, src => src.CallDuration)
                .Map(dest => dest.AgentNumber, src => src.AgentNumber)
                .Map(dest => dest.CallId, src => src.SmeId)
                .Map(dest => dest.CallStatus, src => src.CallStatus)
                .Map(dest => dest.Direction, src => src.CallMode);


            TypeAdapterConfig<ExotelIVROutboundRequest, CommonIVRWebhookRequest>
                .NewConfig()
                .Map(dest => dest.RequestBody, src => src.Adapt<IVRCommonCallLogDto>())
                .Map(dest => dest.AccountId, src => src.AccountId)
                .Map(dest => dest.ApiKey, src => src.ApiKey);

            //Virtual Number ID is sent but not the Virtual Number
            TypeAdapterConfig<ExotelIVROutboundRequest, IVRCommonCallLogDto>
                .NewConfig()
                .Map(dest => dest.AccountId, src => src.AccountId)
                .Map(dest => dest.CallFromNumber, src => src.From)
                .Map(dest => dest.CallToNumber, src => src.To)
                .Map(dest => dest.CustomerNumber, src => src.To)
                .Map(dest => dest.StartStamp, src => src.StartTime)
                .Map(dest => dest.EndStamp, src => src.EndTime)
                .Map(dest => dest.Duration, src => src.ConversationDuration)
                .Map(dest => dest.AgentNumber, src => src.From)
                .Map(dest => dest.CallId, src => src.CallSid)
                .Map(dest => dest.CallStatus, src => src.Status)
                .Map(dest => dest.Direction, src => src.Direction)
                .Map(dest => dest.RawData, src => src.Serialize());

        }
        public static string? GetValidStringValue(string? firstValue, string? secondValue)
        {
            return !string.IsNullOrWhiteSpace(firstValue) ? firstValue : !string.IsNullOrWhiteSpace(secondValue) ? secondValue : null;
        }
        public static DateTime? GetTimeStampFromDateAndTime(string? dateString, string? timeString)
        {
            var dateToUpdate = DateTime.TryParse(dateString, out var date) ? date : default;
            var timeToUpdate = DateTime.TryParse(timeString, out var time) ? time : default;
            if (dateToUpdate != default && timeToUpdate != default)
            {
                return dateToUpdate.Add(timeToUpdate.TimeOfDay);
            }
            return null;
        }

        public static long GetSecondsFromTimeSpan(string? timeSpan)
        {
            var tempValue = TimeSpan.TryParse(timeSpan, out var t) ? t : default;
            return ((long)tempValue.TotalSeconds);
        }

        public static DateTime? GetFormattedDateTime(string? unformattedDateTime)
        {
            DateTime? formattedDateTime = null;
            if (!string.IsNullOrWhiteSpace(unformattedDateTime))
            {
                formattedDateTime = DateTime.TryParse(unformattedDateTime, out DateTime t) ? t.ConvertAndSetKindAsUtc() : null;
            }
            return formattedDateTime;
        }

        public static long GetLongValue(string budget)
        {
            if (long.TryParse(budget, out long result))
                return result;
            else
                return 0;
        }
        public static long GetLongValueFromTimeFormat(string timeDuration)
        {
            if (TimeSpan.TryParse(timeDuration, out TimeSpan result))
                return (long)result.TotalSeconds;
            else
                return 0;
        }
    }
}
