﻿using Lrb.Application.DataManagement.Mobile.Requests;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Persistence.Context;
using Lrb.Shared.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Graph;
using System.Text.RegularExpressions;

namespace Lrb.Infrastructure.Persistence.Repository.New_Implementation
{
    public partial class ProspectRepository
    {
        public async Task<IEnumerable<Prospect>> GetAllProspectForMobile(GetAllProspectRequest request, Guid userId, IEnumerable<Guid> subIds, List<CustomProspectStatus>? prospectStatuses = null)
        {
            var query = BuildQuery(request, userId, subIds, prospectStatuses);
            query = query.OrderByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc));
            query = query.Skip(request.PageSize * (request.PageNumber - 1))
                .Take(request.PageSize)
                .AsQueryable();
            try
            {
                var prospects = await query.AsNoTracking().ToListAsync();
                return prospects;
            }
            catch (Exception e)
            {
                throw;
            }
        }

        public async Task<int> GetAllProspectCountForMobile(GetAllProspectRequest request, Guid userId, IEnumerable<Guid> subIds, List<CustomProspectStatus>? prospectStatuses = null)
        {
            var query = BuildQuery(request, userId, subIds, prospectStatuses);
            var count = await query.AsNoTracking().Select(i=> i.Id).CountAsync();
            return count;
        }

        private IQueryable<Prospect> BuildQuery(GetAllProspectRequest request, Guid userId, IEnumerable<Guid> subIds, List<CustomProspectStatus>? prospectStatuses = null)
        {
            var tenantId = _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var reporteeIds = subIds.Where(i => i != userId && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            var isAdmin = _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty).Result;
            IQueryable<Prospect> query = null;
            query = context.Prospects
                .Where(i => !i.IsDeleted)
                .Include(i => i.Address)
                .ThenInclude(i => i.Location)
                .ThenInclude(i => i.Zone)
                .ThenInclude(i => i.City)
                //.Include(i => i.Enquiries)
                //.ThenInclude(i => i.Address)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.Addresses)
                .ThenInclude(i => i.Location)
                .ThenInclude(i => i.Zone)
                .ThenInclude(i => i.City)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.Source)
                .Include(i => i.ChannelPartners)
                .Include(i => i.Campaigns)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.Status)
                .Include(i => i.Agencies)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                .Include(i => i.ProspectCallLogs).AsQueryable();


            /*  if (!string.IsNullOrWhiteSpace(request.ProspectSearch))
              {
                  request.ProspectSearch = request.ProspectSearch.ToLower().Trim();
                  query = query.Where(i => (i.Name.ToLower().Trim().Contains(request.ProspectSearch))
                 || (i.ContactNo.ToLower().Trim().Contains(request.ProspectSearch.Replace(" ", ""))) ||
                 (i.AlternateContactNo.ToLower().Trim().Contains(request.ProspectSearch.Replace(" ", ""))) ||
                 (i.Email.ToLower().Trim().Contains(request.ProspectSearch)));

              }*/
            if (!string.IsNullOrWhiteSpace(request.ProspectSearch) && request.PropertyToSearch?.Any() == true)
            {
                var searchTerm = request.ProspectSearch.ToLower().Trim().Replace(" ", "");
                var isPurposeValid = TryParseEnum<Purpose>(searchTerm, out var parsedPurpose);
                var isEnquiryTypeValid = TryParseEnum<EnquiryType>(searchTerm, out var parsedEnquiryType);
                var statusId = prospectStatuses.Where(i => i.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm)).Select(i => i.Id).ToList();
                query = query.Where(i =>
                   (request.PropertyToSearch.Contains("DataName") && i.Name != null && i.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("ContactNo") && i.ContactNo != null && i.ContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("AlternateContactNo") && i.AlternateContactNo != null && i.AlternateContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Email") && i.Email != null && i.Email.ToLower().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Nationality") && i.Nationality != null && i.Nationality.ToLower().Trim().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("PropertyName") && i.Properties.Any(a => a.Title.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("ProjectName") && i.Projects.Any(a => a.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Status") && (statusId.Contains(i.Status.BaseId ?? Guid.Empty) || statusId.Contains(i.Status.Id))) ||
                   (request.PropertyToSearch.Contains("Source") && i.Enquiries.Any(e => e.Source.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("SubSource") && i.Enquiries.Any(a => !string.IsNullOrEmpty(a.SubSource) && a.SubSource.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Purpose") && isPurposeValid && i.Enquiries.Any(e => e.Purpose == parsedPurpose)) ||
                   (request.PropertyToSearch.Contains("EnquiredFor") && isEnquiryTypeValid && i.Enquiries.Any(e => e.EnquiryTypes.Contains(parsedEnquiryType))) ||
                   (request.PropertyToSearch.Contains("Location") && i.Enquiries.Any(e => e.Addresses.Any(j =>
                     (j.SubLocality +
                      j.Locality +
                      j.Community +
                      j.SubCommunity +
                      j.TowerName +
                      j.District +
                      j.City +
                      j.State +
                      j.Country +
                      j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")
                     .Contains(searchTerm)))));
            }
            else if (!string.IsNullOrWhiteSpace(request.ProspectSearch))
            {
                request.ProspectSearch = request.ProspectSearch.ToLower().Trim();
                query = query.Where(i => (i.Name.ToLower().Trim().Contains(request.ProspectSearch))
               || (i.ContactNo.ToLower().Trim().Contains(request.ProspectSearch.Replace(" ", ""))));
            }
            switch (request.ProspectVisiblity)
            {
                case ProspectVisiblity.SelfWithReportee:
                    query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                    break;
                case ProspectVisiblity.Self:
                    query = query.Where(i => i.AssignTo == userId).Where(i => !i.IsArchived);
                    break;
                case ProspectVisiblity.Reportee:
                    query = query.Where(i => reporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                    break;
                case ProspectVisiblity.UnassignData:
                    query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                    break;
                case ProspectVisiblity.DeletedData:
                    if (isAdmin)
                    {
                        query = query.Where(i => i.IsArchived);
                        if (request.AssignTo?.Any() ?? false)
                        {
                            query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
                        }
                    }
                    else
                    {
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                    }
                    break;
                case ProspectVisiblity.ConvertedData:
                    if (isAdmin)
                    {
                        query = query.Where(i => i.IsConvertedToLead && i.IsQualified && !i.IsArchived);
                        if (request.AssignTo?.Any() ?? false)
                        {
                            query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
                        }
                    }
                    else
                    {
                        query = query.Where(i => i.IsConvertedToLead && i.IsQualified && !i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                    }
                    break;
            }

            if (request.DateType.HasValue && request.FromDate.HasValue && request.FromDate.Value != default && request.ToDate.HasValue && request.ToDate.Value != default)
            {
                _logger.Information("DateType in request: " + request.DateType);
                _logger.Information("FromDate in request: " + request.FromDate.Value);
                _logger.Information("ToDate in request: " + request.ToDate.Value);
                //Todo: Have to send correct date time in Utc
                DateTime? tempToDate = request.ToDate.Value.ConvertToDateToUtc();
                DateTime? tempFromDate = request.FromDate.Value.ConvertFromDateToUtc();

                _logger.Information("FromDate in Utc: " + tempFromDate.Value);
                _logger.Information("ToDate in Utc: " + tempToDate.Value);
                switch (request.DateType)
                {
                    case ProspectDateType.CreatedDate:
                        query = query.Where(i => i.CreatedOn >= tempFromDate && i.CreatedOn <= tempToDate);
                        break;
                    case ProspectDateType.ModifiedDate:
                        query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= tempFromDate && i.LastModifiedOn.Value <= tempToDate);
                        break;
                    case ProspectDateType.All:
                        query = query.Where(i => (i.CreatedOn >= tempFromDate && i.CreatedOn <= tempToDate) ||
                                           i.ScheduleDate != null && i.ScheduleDate.Value >= tempFromDate && i.ScheduleDate.Value <= tempToDate ||
                                          i.LastModifiedOn != null && i.LastModifiedOn.Value >= tempFromDate && i.LastModifiedOn.Value <= tempToDate);
                        break;
                    case ProspectDateType.ScheduleDate:
                        query = query.Where(i => i.ScheduleDate != null && i.ScheduleDate.Value >= tempFromDate && i.ScheduleDate.Value <= tempToDate);
                        break;
                    case ProspectDateType.QualifiedDate:
                        query = query.Where(i => i.QualifiedDate != null && i.QualifiedDate.Value >= tempFromDate && i.QualifiedDate.Value <= tempToDate);
                        break;
                    case ProspectDateType.ConvertedDate:
                        query = query.Where(i => i.ConvertedDate != null && i.ConvertedDate.Value >= tempFromDate && i.ConvertedDate.Value <= tempToDate);
                        break;
                    case ProspectDateType.PossessionDate:
                        query = query.Where(i => i.PossesionDate != null && i.PossesionDate.Value >= tempFromDate && i.PossesionDate.Value <= tempToDate);
                        break;
                    default:
                        break;
                }
            }
            var ScheduledStatus = (prospectStatuses?.Where(i => i.Status is "follow_ups" or "not_answered" or "not_reachable"))?.ToList() ?? new();
            var schuduledStatusIds = ScheduledStatus.Select(i => i.Id).ToList();
            var scheduledChildStatusesId = prospectStatuses?.Where(i => schuduledStatusIds.Contains(i.BaseId ?? Guid.NewGuid()))?.ToList()?.Select(i => i.Id)?.ToList() ?? new();
            switch (request.FilterType)
            {     
                case ProspectFilterType.New:
                  var  statuses = (prospectStatuses?.Where(i => i.Status is "new"))?.ToList() ?? new();
                    //childStatuses = prospectStatuses?.Where(i => statuses.Any(j => j.Id == i.BaseId)).ToList();
                  var  statusesIds = statuses?.Select(i => i.Id).ToList() ?? new();
                    // childStatusIds = childStatuses?.Select(i => i.Id).ToList() ?? new();
                    query = query.Where(i => statusesIds.Contains(i.StatusId ?? Guid.Empty)).Where(i => !i.IsConvertedToLead);
                    break; 

                case ProspectFilterType.Followups:
                    statuses = (prospectStatuses?.Where(i => i.Status is "follow_ups"))?.ToList() ?? new();
                   var childStatuses = prospectStatuses?.Where(i => statuses.Any(j => j.Id == i.BaseId)).ToList();
                    statusesIds = statuses.Select(i => i.Id).ToList();
                   var childStatusIds = childStatuses?.Select(i => i.Id).ToList() ?? new();
                    query = query.Where(i => (statusesIds.Contains(i.StatusId ?? Guid.Empty) || childStatusIds.Contains(i.StatusId ?? Guid.Empty))).Where(i => !i.IsConvertedToLead);
                    break;
                case ProspectFilterType.NotReachable:
                    statuses = (prospectStatuses?.Where(i => i.Status is "not_reachable"))?.ToList() ?? new();
                    childStatuses = prospectStatuses?.Where(i => statuses.Any(j => j.Id == i.BaseId)).ToList();
                    statusesIds = statuses.Select(i => i.Id).ToList();
                    childStatusIds = childStatuses?.Select(i => i.Id).ToList() ?? new();
                    query = query.Where(i => (statusesIds.Contains(i.StatusId ?? Guid.Empty) || childStatusIds.Contains(i.StatusId ?? Guid.Empty))).Where(i => !i.IsConvertedToLead);
                    break;
                case ProspectFilterType.All:
                    if (request.ProspectVisiblity != ProspectVisiblity.ConvertedData)
                    {
                        query = query.Where(i => !i.IsConvertedToLead);
                    }
                    else
                    {
                        query = query.Where(i => i.IsConvertedToLead);
                    }
                    break;
                case ProspectFilterType.Qualified:
                    statuses = (prospectStatuses?.Where(i => i.Status is "qualified"))?.ToList() ?? new();
                    childStatuses = prospectStatuses?.Where(i => statuses.Any(j => j.Id == i.BaseId)).ToList();
                    statusesIds = statuses.Select(i => i.Id).ToList();
                    childStatusIds = childStatuses?.Select(i => i.Id).ToList() ?? new();
                    query = query.Where(i => (statusesIds.Contains(i.StatusId ?? Guid.Empty) || childStatusIds.Contains(i.StatusId ?? Guid.Empty))).Where(i => !i.IsConvertedToLead);
                    break;
                case ProspectFilterType.Backlog:
                   query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddMinutes(-330))
                                .Where(i => schuduledStatusIds.Contains(i.StatusId ?? Guid.Empty) || scheduledChildStatusesId.Contains(i.StatusId ?? Guid.Empty)).Where(i => !i.IsConvertedToLead);
                    break;
                case ProspectFilterType.NotInterested:
                    statuses = (prospectStatuses?.Where(i => i.Status is "not_interested"))?.ToList() ?? new();
                    childStatuses = prospectStatuses?.Where(i => statuses.Any(j => j.Id == i.BaseId)).ToList();
                    statusesIds = statuses.Select(i => i.Id).ToList();
                    childStatusIds = childStatuses?.Select(i => i.Id).ToList() ?? new();
                    query = query.Where(i => (statusesIds.Contains(i.StatusId ?? Guid.Empty) || childStatusIds.Contains(i.StatusId ?? Guid.Empty))).Where(i => !i.IsConvertedToLead);
                    break;
                case ProspectFilterType.NotAnswered:
                    statuses = (prospectStatuses?.Where(i => i.Status is "not_answered"))?.ToList() ?? new();
                    childStatuses = prospectStatuses?.Where(i => statuses.Any(j => j.Id == i.BaseId)).ToList();
                    statusesIds = statuses.Select(i => i.Id).ToList();
                    childStatusIds = childStatuses?.Select(i => i.Id).ToList() ?? new();
                    query = query.Where(i => (statusesIds.Contains(i.StatusId ?? Guid.Empty) || childStatusIds.Contains(i.StatusId ?? Guid.Empty))).Where(i => !i.IsConvertedToLead);
                    break;
                case ProspectFilterType.InValid:
                    statuses = (prospectStatuses?.Where(i => i.Status is "invalid/wrong_number"))?.ToList() ?? new();
                    childStatuses = prospectStatuses?.Where(i => statuses.Any(j => j.Id == i.BaseId)).ToList();
                    statusesIds = statuses.Select(i => i.Id).ToList();
                    childStatusIds = childStatuses?.Select(i => i.Id).ToList() ?? new();
                    query = query.Where(i => (statusesIds.Contains(i.StatusId ?? Guid.Empty) || childStatusIds.Contains(i.StatusId ?? Guid.Empty))).Where(i => !i.IsConvertedToLead);
                    break;
                case ProspectFilterType.Converted:
                    query = query.Where(i => i.IsConvertedToLead);
                    break;
                default:
                    break;
            }


            if (request.CarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetArea == request.CarpetArea));
            }
            if (request.BuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpArea == request.BuiltUpArea));
            }
            if (request.SaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableArea == request.SaleableArea));
            }
            if (request.PropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea));
            }
            if (request.NetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea));
            }
            if (request.CarpetAreaUnitId != default && request.CarpetAreaUnitId != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetAreaUnitId == request.CarpetAreaUnitId));
            }
            if (request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId));
            }
            if (request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableAreaUnitId == request.SaleableAreaUnitId));
            }
            if (request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyAreaUnitId == request.PropertyAreaUnitId));
            }
            if (request.NetAreaUnitId != default && request.NetAreaUnitId != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetAreaUnitId == request.NetAreaUnitId));
            }
            if ((request.MinCarpetArea != null || request.MaxCarpetArea != null) && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.CarpetAreaUnitId == request.CarpetAreaUnitId &&
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea)));
            }
            else if (request.MinCarpetArea != null || request.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea ) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea )));
            }
            if ((request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null) && request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId &&
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea)));
            }
            else if (request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea ) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea )));
            }
            if ((request.MinSaleableArea != null || request.MaxSaleableArea != null) && request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.SaleableAreaUnitId == request.SaleableAreaUnitId &&
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea)));
            }
            else if (request.MinSaleableArea != null || request.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea ) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea )));
            }
            if ((request.MinPropertyArea != null || request.MaxPropertyArea != null) && request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.PropertyAreaUnitId == request.PropertyAreaUnitId &&
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea)));
            }
            else if (request.MinPropertyArea != null || request.MaxPropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea ) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea )));
            }

            if ((request.MinNetArea != null || request.MaxNetArea != null) && request.NetAreaUnitId != default && request.NetAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.NetAreaUnitId == request.NetAreaUnitId &&
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea)));
            }
            else if (request.MinNetArea != null || request.MaxNetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea ) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea )));
            }
            if (request.EnquiryTypes != null && request.EnquiryTypes.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.EnquiryTypes.Contains(i.EnquiryType)));
                query = query.Where(i =>
                                          i.Enquiries.Any(e => e.EnquiryTypes.Any(t => request.EnquiryTypes.Contains(t))));
            }
            if (request.NoOfBHKs != null && request.NoOfBHKs.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.NoOfBHKs.Contains(i.NoOfBhks)));
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(e => e.BHKs != null && request.NoOfBHKs.Any(r=>EF.Functions.JsonExists(e.BHKs,r.ToString()))));
                query = query.Where(i => i.Enquiries.Any(e => e.BHKs.Any(b => request.NoOfBHKs.Contains(b))));
            }
            if (request.SubSources != null && request.SubSources.Any())
            {
                request.SubSources = request.SubSources.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Enquiries.Any(i => request.SubSources.Contains(i.SubSource.ToLower().Trim())));
            }
            if (request.BHKTypes != null && request.BHKTypes.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.BHKTypes.Contains(i.BHKType)));
                query = query.Where(i => i.Enquiries.Any(e => e.BHKTypes.Any(b => request.BHKTypes.Contains(b))));
            }
            if (request.PropertyType != null && request.PropertyType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertyType.Contains(i.BaseId ?? Guid.Empty))));
            }
            if (request.PropertySubType != null && request.PropertySubType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertySubType.Contains(i.Id))));
            }
            if (request.Properties != null && request.Properties.Any())
            {
                var propertyNames = request.Properties.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Properties.Any(i => propertyNames.Contains(i.Title.ToLower())));
            }
            if (request.Projects?.Any() ?? false)
            {
                var projectNames = request.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Projects.Any(i => projectNames.Contains(i.Name.ToLower()))).AsQueryable();
            }
            if (request.Budget != null && request.Budget.Any())
            {
                foreach (var budget in request.Budget)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget <= 1000000));
                            break;
                        case Budget.TenToTwentyLakhs:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 1000000 && j.UpperBudget <= 2000000));
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 2000000 && j.UpperBudget <= 3000000));
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 3000000 && j.UpperBudget <= 4000000));
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 4000000 && j.UpperBudget <= 5000000));
                            break;
                        case Budget.FiftyToOneCrore:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 5000000 && j.UpperBudget <= 10000000));
                            break;
                        case Budget.MoreThanOneCrore:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 10000000));
                            break;
                    }
                }
            }

            if (request.Locations != null && request.Locations.Any())
            {
                request.Locations = request.Locations.ConvertAll(i => Uri.UnescapeDataString(i).Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Any(j =>
                request.Locations.Contains(
                    (j.SubLocality +
                    j.Locality +
                    j.Community +
                    j.SubCommunity +
                    j.TowerName +
                    j.District +
                    j.City +
                    j.State +
                    j.Country +
                    j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")))));

            }
            if (request.Currency != null)
            {
                query = query.Where(i => i.Enquiries.Any(e => e.Currency == request.Currency));
            }
            if (request.MinBudget != null || request.MaxBudget != null)
            {
                if (request.MinBudget != null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget && i.UpperBudget <= request.MaxBudget) || (i.LowerBudget >= request.MinBudget && i.LowerBudget <= request.MaxBudget)));
                }
                else if (request.MinBudget != null && request.MaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget) || (i.LowerBudget >= request.MinBudget)));
                }
                else if (request.MinBudget == null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget <= request.MaxBudget) || (i.LowerBudget <= request.MaxBudget)));
                }
            }
            if (request.FromMinBudget != null || request.ToMinBudget != null || request.FromMaxBudget != null || request.ToMaxBudget != null)
            {
                if (request.FromMinBudget != null && request.ToMinBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        (e.LowerBudget >= request.FromMinBudget && e.LowerBudget <= request.ToMinBudget) ||
                        (e.LowerBudget >= request.FromMinBudget && e.LowerBudget <= request.ToMinBudget)
                    ));
                }
                else if (request.FromMinBudget != null && request.ToMinBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.LowerBudget >= request.FromMinBudget ||
                        e.LowerBudget >= request.FromMinBudget
                    ));
                }
                else if (request.FromMinBudget == null && request.ToMinBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.LowerBudget <= request.ToMinBudget ||
                        e.LowerBudget <= request.ToMinBudget
                    ));
                }

                if (request.FromMaxBudget != null && request.ToMaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        (e.UpperBudget >= request.FromMaxBudget && e.UpperBudget <= request.ToMaxBudget) ||
                        (e.UpperBudget >= request.FromMaxBudget && e.UpperBudget <= request.ToMaxBudget)
                    ));
                }
                else if (request.FromMaxBudget != null && request.ToMaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.UpperBudget >= request.FromMaxBudget ||
                        e.UpperBudget >= request.FromMaxBudget
                    ));
                }
                else if (request.FromMaxBudget == null && request.ToMaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.UpperBudget <= request.ToMaxBudget ||
                        e.UpperBudget <= request.ToMaxBudget
                    ));
                }
            }

            if (request.AgencyNames != null && request.AgencyNames.Any())
            {
                request.AgencyNames = request.AgencyNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Agencies.Any(j => request.AgencyNames.Contains(j.Name.ToLower().Trim())));
            }
            if (request?.LastModifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.LastModifiedByIds.Contains(i.LastModifiedBy));
            }
            if (request?.CreatedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.CreatedByIds.Contains(i.CreatedBy));
            }
            if (request?.DeletedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.DeletedByIds.Contains(i.ArchivedBy.Value));
            }
            if (request?.RestoredByIds?.Any() ?? false)
            {
                query = query.Where(i => request.RestoredByIds.Contains(i.RestoredBy.Value));
            }
            if (request?.AssignedFromIds?.Any() ?? false)
            {
                query = query.Where(i => i.AssignedFrom != null && request.AssignedFromIds.Contains(i.AssignedFrom.Value));
            }
            if (request?.CompanyNames?.Any() ?? false)
            {
                request.CompanyNames = request.CompanyNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => request.CompanyNames.Contains(i.CompanyName.ToLower().Trim()));
            }
            if (request?.ProspectIds?.Any() ?? false)
            {
                query = query.Where(i => request.ProspectIds.Contains(i.Id));
            }
            if (request?.SourcingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.SourcingManagers.Contains(i.SourcingManager.Value));
            }
            if (request?.ClosingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.ClosingManagers.Contains(i.ClosingManager.Value));
            }
            if (request?.Profession != null && request.Profession.Any())
            {
                query = query.Where(i => request.Profession.Contains(i.Profession));
            }
            if (request?.Source?.Any() ?? false)
            {
                query = query.Where(i =>  i.Enquiries.Any(j => request.Source.Contains(j.Source.Id)));
            }
            if (request.CampaignNames != null && request.CampaignNames.Any())
            {
                request.CampaignNames = request.CampaignNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Campaigns.Any(j => request.CampaignNames.Contains(j.Name.ToLower().Trim())));
            }
            if (request.ChannelPartnerNames != null && request.ChannelPartnerNames.Any())
            {
                request.ChannelPartnerNames = request.ChannelPartnerNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.ChannelPartners.Any(j => request.ChannelPartnerNames.Contains(j.FirmName.ToLower().Trim())));
            }
            var statusIds = new List<Guid>();
            statusIds.AddRange(request?.StatusIds ?? new List<Guid>());
            statusIds.AddRange(request?.StatusIds ?? new List<Guid>());
            if (statusIds.Any())
            {
                query = query.Where(i => (statusIds.Contains(i.StatusId ?? Guid.Empty)));
            }
            if (request?.Cities?.Any() ?? false)
            {
                var normalizedCityNames = request.Cities.Select(i => i.LrbNormalize()).ToList();

                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedCityNames.Contains(i.City.ToLower().Trim())).Any()));

            }

            if (request?.Designations != null && request.Designations.Any())
            {
                request.Designations = request.Designations.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => request.Designations.Contains(i.Designation.ToLower().Trim()));
            }
            if (request?.QualifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.QualifiedByIds.Contains(i.QualifiedBy.Value));
            }
            if (request?.ConvertedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.ConvertedByIds.Contains(i.ConvertedBy.Value));
            }
            if (!string.IsNullOrEmpty(request.ReferralName))
            {
                query = query.Where(i => (i.ReferralName.ToLower().Trim().Contains(request.ReferralName.ToLower().Trim())));
            }
            if (!string.IsNullOrEmpty(request.UnitName))
            {
                query = query.Where(i => i.Enquiries.Any(e => e.UnitName != null && e.UnitName.ToLower().Trim() == request.UnitName.ToLower().Trim()));
            }
            if (request?.UnitNames?.Any() ?? false)
            {
                var unitnames = request.UnitNames.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Enquiries.Count > 0 && i.Enquiries.Any(i => unitnames.Contains(i.UnitName.ToLower()))).AsQueryable();
            }
            if (request?.Nationality?.Any() ?? false)
            {
                var nationality = request.Nationality.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Nationality != null && nationality.Contains(i.Nationality.ToLower())).AsQueryable();
            }

            if (request?.ClusterName?.Any() ?? false)
            {
                var ClusterName = request.ClusterName.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Enquiries.Count > 0 && i.Enquiries.Any(i => ClusterName.Contains(i.ClusterName.ToLower()))).AsQueryable();
            }
            if (!string.IsNullOrEmpty(request.ReferralContactNo))
            {
                query = query.Where(i => !string.IsNullOrEmpty(i.ReferralContactNo) && request.ReferralContactNo.Contains(i.ReferralContactNo.Substring(i.ReferralContactNo.Length - 10)));
            }
            if (!string.IsNullOrEmpty(request.ReferralEmail))
            {
                query = query.Where(i => (i.ReferralEmail.ToLower().Trim().Contains(request.ReferralEmail.ToLower().Trim())));
            }
            if (request?.PossesionType != null && request?.PossesionType != PossesionType.None)
            {
                switch (request?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.Enquiries.Any(j=>j.PossesionType == PossesionType.UnderConstruction));
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.Enquiries.Any(j => j.PossesionType == PossesionType.SixMonth));
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.Enquiries.Any(j => j.PossesionType == PossesionType.Year));
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.Enquiries.Any(j => j.PossesionType == PossesionType.TwoYears));
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(j =>j.PossesionDate != null && j.PossesionDate >= tempFrompossesionDate.Value && j.PossesionDate <= tempToPossesionDate.Value);

                        break;
                }
            }
            if (request?.LandLine != null && request.LandLine.Any())
            {
                query = query.Where(i => i.LandLine != null && request.LandLine.Contains(i.LandLine.Trim()));
            }
            if (request?.CountryCode?.Any() ?? false)
            {
                query = query.Where(i => i.CountryCode != null && request.CountryCode.Contains(i.CountryCode));
            }
            if (request?.AltCountryCode?.Any() ?? false)
            {
                query = query.Where(i => i.AltCountryCode != null && request.AltCountryCode.Contains(i.AltCountryCode));
            }
            if (request?.GenderTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Gender != null && request.GenderTypes.Contains(i.Gender.Value));
            }
            if (request?.MaritalStatuses?.Any() ?? false)
            {
                query = query.Where(i => i.MaritalStatus != null && request.MaritalStatuses.Contains(i.MaritalStatus.Value));
            }
            if (request?.DateOfBirth != null)
            {
                query = query.Where(i => i.DateOfBirth != null && i.DateOfBirth == request.DateOfBirth);
            }
            return query;
        }

        public async Task<IEnumerable<Prospect>> GetAllSearchProspectForMobile(GetAllProspectsBySearchRequest request, IEnumerable<Guid> subIds, List<CustomProspectStatus>? prospectStatuses = null)
        {
            var query = SearchBuildQuery(request, subIds, prospectStatuses);
            query = query.OrderByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc));
            query = query.Skip(request.PageSize * (request.PageNumber - 1))
                .Take(request.PageSize)
                .AsQueryable();
            try
            {
                var prospects = await query.ToListAsync();
                return prospects;
            }
            catch (Exception e)
            {
                throw;
            }
        }

        public async Task<int> GetAllSearchProspectCountForMobile(GetAllProspectsBySearchRequest request, IEnumerable<Guid> subIds, List<CustomProspectStatus>? prospectStatuses = null)
        {
            var query = SearchBuildQueryForCount(request, subIds, prospectStatuses);
            var count = await query.AsNoTracking().Select(i => i.Id).CountAsync();
            return count;
        }
        public IQueryable<Prospect> SearchBuildQuery(GetAllProspectsBySearchRequest request, IEnumerable<Guid> subIds, List<CustomProspectStatus>? prospectStatuses = null)
        {
            var tenantId = _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty).ToList();
            IQueryable<Prospect> query = null;
            query = context.Prospects
                .Where(i => !i.IsDeleted)
                .Include(i => i.Address)
                .ThenInclude(i => i.Location)
                .ThenInclude(i => i.Zone)
                .ThenInclude(i => i.City)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.Addresses)
                .ThenInclude(i => i.Location)
                .ThenInclude(i => i.Zone)
                .ThenInclude(i => i.City)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.Source)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.Status).AsQueryable();
            if (!string.IsNullOrWhiteSpace(request.ProspectSearch) && request.PropertyToSearch?.Any() == true)
            {
                var searchTerm = request.ProspectSearch.ToLower().Trim().Replace(" ", "");
                var isPurposeValid = TryParseEnum<Purpose>(searchTerm, out var parsedPurpose);
                var isEnquiryTypeValid = TryParseEnum<EnquiryType>(searchTerm, out var parsedEnquiryType);
                var statusId = prospectStatuses?.Where(i => i.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm)).Select(i => i.Id).ToList();
                query = query.Where(i =>
                   (request.PropertyToSearch.Contains("DataName") && i.Name != null && i.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("ContactNo") && i.ContactNo != null && i.ContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("AlternateContactNo") && i.AlternateContactNo != null && i.AlternateContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Email") && i.Email != null && i.Email.ToLower().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Nationality") && i.Nationality != null && i.Nationality.ToLower().Trim().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("PropertyName") && i.Properties.Any(a => a.Title.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("ProjectName") && i.Projects.Any(a => a.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Status") && (statusId.Contains(i.Status.BaseId ?? Guid.Empty) || statusId.Contains(i.Status.Id))) ||
                   (request.PropertyToSearch.Contains("Source") && i.Enquiries.Any(e => e.Source.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("SubSource") && i.Enquiries.Any(a => !string.IsNullOrEmpty(a.SubSource) && a.SubSource.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Purpose") && isPurposeValid && i.Enquiries.Any(e => e.Purpose == parsedPurpose)) ||
                   (request.PropertyToSearch.Contains("EnquiredFor") && isEnquiryTypeValid && i.Enquiries.Any(e => e.EnquiryTypes.Contains(parsedEnquiryType))) ||
                   (request.PropertyToSearch.Contains("Location") && i.Enquiries.Any(e => e.Addresses.Any(j =>
                     (j.SubLocality +
                      j.Locality +
                      j.Community +
                      j.SubCommunity +
                      j.TowerName +
                      j.District +
                      j.City +
                      j.State +
                      j.Country +
                      j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")
                     .Contains(searchTerm)))));
            }
            else if (!string.IsNullOrWhiteSpace(request.ProspectSearch))
            {
                request.ProspectSearch = request.ProspectSearch.ToLower().Trim();
                query = query.Where(i => (i.Name.ToLower().Trim().Contains(request.ProspectSearch))
               || (i.ContactNo.ToLower().Trim().Contains(request.ProspectSearch.Replace(" ", ""))));
            }
            if (subIds?.Any() ?? false)
            {
               query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
            }
            return query;
        }
        public IQueryable<Prospect> SearchBuildQueryForCount(GetAllProspectsBySearchRequest request, IEnumerable<Guid> subIds, List<CustomProspectStatus>? prospectStatuses = null)
        {
            var tenantId = _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty).ToList();
            IQueryable<Prospect> query = null;
            query = context.Prospects
                .Where(i => !i.IsDeleted).AsQueryable();
            if (!string.IsNullOrWhiteSpace(request.ProspectSearch) && request.PropertyToSearch?.Any() == true)
            {
                var searchTerm = request.ProspectSearch.ToLower().Trim().Replace(" ", "");
                var isPurposeValid = TryParseEnum<Purpose>(searchTerm, out var parsedPurpose);
                var isEnquiryTypeValid = TryParseEnum<EnquiryType>(searchTerm, out var parsedEnquiryType);
                var statusId = prospectStatuses?.Where(i => i.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm)).Select(i => i.Id).ToList();
                query = query.Where(i =>
                   (request.PropertyToSearch.Contains("DataName") && i.Name != null && i.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("ContactNo") && i.ContactNo != null && i.ContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("AlternateContactNo") && i.AlternateContactNo != null && i.AlternateContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Email") && i.Email != null && i.Email.ToLower().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Nationality") && i.Nationality != null && i.Nationality.ToLower().Trim().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("PropertyName") && i.Properties.Any(a => a.Title.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("ProjectName") && i.Projects.Any(a => a.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Status") && (statusId.Contains(i.Status.BaseId ?? Guid.Empty) || statusId.Contains(i.Status.Id))) ||
                   (request.PropertyToSearch.Contains("Source") && i.Enquiries.Any(e => e.Source.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("SubSource") && i.Enquiries.Any(a => !string.IsNullOrEmpty(a.SubSource) && a.SubSource.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Purpose") && isPurposeValid && i.Enquiries.Any(e => e.Purpose == parsedPurpose)) ||
                   (request.PropertyToSearch.Contains("EnquiredFor") && isEnquiryTypeValid && i.Enquiries.Any(e => e.EnquiryTypes.Contains(parsedEnquiryType))) ||
                   (request.PropertyToSearch.Contains("Location") && i.Enquiries.Any(e => e.Addresses.Any(j =>
                     (j.SubLocality +
                      j.Locality +
                      j.Community +
                      j.SubCommunity +
                      j.TowerName +
                      j.District +
                      j.City +
                      j.State +
                      j.Country +
                      j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")
                     .Contains(searchTerm)))));
            }
            else if (!string.IsNullOrWhiteSpace(request.ProspectSearch))
            {
                request.ProspectSearch = request.ProspectSearch.ToLower().Trim();
                query = query.Where(i => (i.Name.ToLower().Trim().Contains(request.ProspectSearch))
               || (i.ContactNo.ToLower().Trim().Contains(request.ProspectSearch.Replace(" ", ""))));
            }
            if (subIds?.Any() ?? false)
            {
               query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
            }
            return query;
        }

        public async Task<int> GetProspectsCountByCustomFiltersForMobileAsync(CustomFilter filter, GetAllProspectFilterParameter request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<CustomProspectStatus>? prospectStatuses = null)
        {
            var query = CustomFilterBuildQueryForCount(filter, request, subIds, userId, isAdmin, prospectStatuses);
            var count = await query.AsNoTracking().Select(i => i.Id).CountAsync();
            return count;
        }
        public async Task<IEnumerable<Prospect>> GetAllProspectsByCustomFiltersForMobileAsync(CustomFilter filter, GetAllProspectFilterParameter request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<CustomProspectStatus>? prospectStatuses = null)
        {
            var query = CustomFilterBuildQuery(filter, request, subIds, userId, isAdmin, prospectStatuses);
            query = query.OrderByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc));
            query = query.Skip(request.PageSize * (request.PageNumber - 1))
                .Take(request.PageSize)
                .AsQueryable();
            try
            {
                var prospects = await query.AsNoTracking().ToListAsync();
                return prospects;
            }
            catch (Exception e)
            {
                throw;
            }
        }
        private IQueryable<Prospect> CustomFilterBuildQuery(CustomFilter filter, GetAllProspectFilterParameter request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<CustomProspectStatus>? prospectStatuses = null)
        {
            var tenantId = _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var reporteeIds = (subIds.Where(i => i != userId && i != Guid.Empty)).ToList();
            var selfWithReporteeIds = (subIds.Where(i => i != Guid.Empty)).ToList();
            IQueryable<Prospect>? query = null;
            query = context.Prospects
                .Where(i => !i.IsDeleted)
                .Include(i => i.Address)
                .ThenInclude(i => i.Location)
                .ThenInclude(i => i.Zone)
                .ThenInclude(i => i.City)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.Addresses)
                .ThenInclude(i => i.Location)
                .ThenInclude(i => i.Zone)
                .ThenInclude(i => i.City)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.Source)
                .Include(i => i.ChannelPartners)
                .Include(i => i.Campaigns)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.Status)
                .Include(i => i.Agencies).Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes).AsQueryable();
            if (!string.IsNullOrWhiteSpace(request.ProspectSearch) && request.PropertyToSearch?.Any() == true)
            {
                var searchTerm = request.ProspectSearch.ToLower().Trim().Replace(" ", "");
                var isPurposeValid = TryParseEnum<Purpose>(searchTerm, out var parsedPurpose);
                var isEnquiryTypeValid = TryParseEnum<EnquiryType>(searchTerm, out var parsedEnquiryType);
                var statusId = prospectStatuses?.Where(i => i.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm)).Select(i => i.Id).ToList();
                query = query.Where(i =>
                   (request.PropertyToSearch.Contains("DataName") && i.Name != null && i.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("ContactNo") && i.ContactNo != null && i.ContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("AlternateContactNo") && i.AlternateContactNo != null && i.AlternateContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Email") && i.Email != null && i.Email.ToLower().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Nationality") && i.Nationality != null && i.Nationality.ToLower().Trim().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("PropertyName") && i.Properties.Any(a => a.Title.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("ProjectName") && i.Projects.Any(a => a.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Status") && (statusId.Contains(i.Status.BaseId ?? Guid.Empty) || statusId.Contains(i.Status.Id))) ||
                   (request.PropertyToSearch.Contains("Source") && i.Enquiries.Any(e => e.Source.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("SubSource") && i.Enquiries.Any(a => !string.IsNullOrEmpty(a.SubSource) && a.SubSource.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Purpose") && isPurposeValid && i.Enquiries.Any(e => e.Purpose == parsedPurpose)) ||
                   (request.PropertyToSearch.Contains("EnquiredFor") && isEnquiryTypeValid && i.Enquiries.Any(e => e.EnquiryTypes.Contains(parsedEnquiryType))) ||
                   (request.PropertyToSearch.Contains("Location") && i.Enquiries.Any(e => e.Addresses.Any(j =>
                     (j.SubLocality +
                      j.Locality +
                      j.Community +
                      j.SubCommunity +
                      j.TowerName +
                      j.District +
                      j.City +
                      j.State +
                      j.Country +
                      j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")
                     .Contains(searchTerm)))));
            }
            else if (!string.IsNullOrWhiteSpace(request.ProspectSearch))
            {
                request.ProspectSearch = request.ProspectSearch.ToLower().Trim();
                query = query.Where(i => (i.Name.ToLower().Trim().Contains(request.ProspectSearch))
               || (i.ContactNo.ToLower().Trim().Contains(request.ProspectSearch.Replace(" ", ""))));
            }
            switch (request.ProspectVisiblity)
            {
                case ProspectVisiblity.SelfWithReportee:
                    query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                    break;
                case ProspectVisiblity.Self:
                    query = query.Where(i => i.AssignTo == userId).Where(i => !i.IsArchived);
                    break;
                case ProspectVisiblity.Reportee:
                    query = query.Where(i => reporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                    break;
                case ProspectVisiblity.UnassignData:
                    query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                    break;
                case ProspectVisiblity.DeletedData:
                    if (isAdmin)
                    {
                        query = query.Where(i => i.IsArchived);
                        if (request.AssignTo?.Any() ?? false)
                        {
                            query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
                        }
                    }
                    else
                    {
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                    }
                    break;
                case ProspectVisiblity.ConvertedData:
                    if (isAdmin)
                    {
                        query = query.Where(i => i.IsConvertedToLead && i.IsQualified && !i.IsArchived && i.AssignTo != Guid.Empty);
                        if (request.AssignTo?.Any() ?? false)
                        {
                            query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
                        }
                    }
                    else
                    {
                        query = query.Where(i => i.IsConvertedToLead && i.IsQualified && !i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                    }
                    break;
            }

            if (request.DateType.HasValue && request.FromDate.HasValue && request.FromDate.Value != default && request.ToDate.HasValue && request.ToDate.Value != default)
            {
                _logger.Information("DateType in request: " + request.DateType);
                _logger.Information("FromDate in request: " + request.FromDate.Value);
                _logger.Information("ToDate in request: " + request.ToDate.Value);
                //Todo: Have to send correct date time in Utc
                DateTime? tempToDate = request.ToDate.Value.ConvertToDateToUtc();
                DateTime? tempFromDate = request.FromDate.Value.ConvertFromDateToUtc();

                _logger.Information("FromDate in Utc: " + tempFromDate.Value);
                _logger.Information("ToDate in Utc: " + tempToDate.Value);
                switch (request.DateType)
                {
                    case ProspectDateType.CreatedDate:
                        query = query.Where(i => i.CreatedOn >= tempFromDate && i.CreatedOn <= tempToDate);
                        break;
                    case ProspectDateType.ModifiedDate:
                        query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= tempFromDate && i.LastModifiedOn.Value <= tempToDate);
                        break;
                    case ProspectDateType.All:
                        query = query.Where(i => (i.CreatedOn >= tempFromDate && i.CreatedOn <= tempToDate) ||
                                            i.ScheduleDate != null && i.ScheduleDate.Value >= tempFromDate && i.ScheduleDate.Value <= tempToDate ||
                                          i.LastModifiedOn != null && i.LastModifiedOn.Value >= tempFromDate && i.LastModifiedOn.Value <= tempToDate);
                        break;
                    case ProspectDateType.ScheduleDate:
                        query = query.Where(i => i.ScheduleDate != null && i.ScheduleDate.Value >= tempFromDate && i.ScheduleDate.Value <= tempToDate);
                        break;
                    case ProspectDateType.QualifiedDate:
                        query = query.Where(i => i.QualifiedDate != null && i.QualifiedDate.Value >= tempFromDate && i.QualifiedDate.Value <= tempToDate);
                        break;
                    case ProspectDateType.ConvertedDate:
                        query = query.Where(i => i.ConvertedDate != null && i.ConvertedDate.Value >= tempFromDate && i.ConvertedDate.Value <= tempToDate);
                        break;
                    case ProspectDateType.PossessionDate:
                        query = query.Where(i => i.PossesionDate != null && i.PossesionDate.Value >= tempFromDate && i.PossesionDate.Value <= tempToDate);
                        break;
                    default:
                        break;
                }
            }


            #region Custom Filters
            if (filter != null)
            {
                if (filter.IsConvertedFilter ?? false)
                {
                    query = query.Where(i => i.IsConvertedToLead && i.IsQualified);
                }
                else
                {
                    query = query.Where(i => !i.IsConvertedToLead);
                }
                if (!filter.CustomProspectStatuses?.Any() ?? true)
                {
                    query = query.Where(i => i.StatusId != null);
                }
                else if (filter.IsForward != null)
                {
                    if (filter.IsForward ?? false)
                    {
                        if (filter.FromNoOfDays == 0 && filter.ToNoOfDays == 0)
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddMinutes(-330) && (i.ScheduleDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddDays(1).AddMinutes(-330));
                        }
                        else if (filter.FromNoOfDays >= 0 && filter.ToNoOfDays > 0)
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddDays(filter.FromNoOfDays ?? 0).AddMinutes(-330) && (i.ScheduleDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddDays(filter.ToNoOfDays ?? 0).AddMinutes(-330));
                        }
                        else if (filter.FromNoOfDays >= 0)
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddDays(filter.FromNoOfDays ?? 0).AddMinutes(-330));
                        }
                        else if (filter.ToNoOfDays >= 0)
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddMinutes(-330) && (i.ScheduleDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddDays(filter.ToNoOfDays ?? 0).AddMinutes(-330));
                        }
                        else
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddMinutes(-330));
                        }
                        var statusId = filter.CustomProspectStatuses?.Select(i => i.Id)?.ToList() ?? new List<Guid>();
                        if (statusId?.Any() ?? false)
                        {
                            query = query.Where(i =>  statusId.Contains(i.StatusId ?? Guid.Empty));
                        }
                    }
                    else
                    {
                        if (filter.FromNoOfDays >= 0 && filter.ToNoOfDays > 0)
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) <= DateTime.UtcNow.Date.AddDays((-filter.FromNoOfDays) ?? 0).AddMinutes(-330) && (i.ScheduleDate ?? DateTime.MinValue) > DateTime.UtcNow.Date.AddDays((-filter.ToNoOfDays) ?? 0).AddMinutes(-330));
                        }
                        else if (filter.FromNoOfDays >= 0)
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) <= DateTime.UtcNow.Date.AddDays((-filter.FromNoOfDays) ?? 0).AddMinutes(-330));
                        }
                        else if (filter.ToNoOfDays >= 0)
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) <= DateTime.UtcNow.Date.AddMinutes(-330) && (i.ScheduleDate ?? DateTime.MinValue) > DateTime.UtcNow.Date.AddDays((-filter.ToNoOfDays) ?? 0).AddMinutes(-330));
                        }
                        else
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddMinutes(-330));
                        }
                        var statusId = filter.CustomProspectStatuses?.Select(i => i.Id)?.ToList() ?? new List<Guid>();
                        if (statusId?.Any() ?? false)
                        {
                            query = query.Where(i => statusId.Contains(i.StatusId ?? Guid.Empty));
                        }
                    }
                }
                else if (filter.CustomProspectStatuses?.Any() ?? false)
                {
                    var statusId = filter.CustomProspectStatuses.Select(i => i.Id).ToList();
                    query = query.Where(i => statusId.Contains(i.StatusId ?? Guid.Empty));
                }
            }
            #endregion


            if (request.CarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetArea == request.CarpetArea));
            }
            if (request.BuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpArea == request.BuiltUpArea));
            }
            if (request.SaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableArea == request.SaleableArea));
            }
            if (request.NetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea));
            }
            if (request.PropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea));
            }
            if (request.CarpetAreaUnitId != default && request.CarpetAreaUnitId != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetAreaUnitId == request.CarpetAreaUnitId));
            }
            if (request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId));
            }
            if (request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableAreaUnitId == request.SaleableAreaUnitId));
            }
            if (request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyAreaUnitId == request.PropertyAreaUnitId));
            }
            if (request.NetAreaUnitId != default && request.NetAreaUnitId != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetAreaUnitId == request.NetAreaUnitId));
            }
            if ((request.MinCarpetArea != null || request.MaxCarpetArea != null) && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.CarpetAreaUnitId == request.CarpetAreaUnitId &&
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea)));
            }
            else if (request.MinCarpetArea != null || request.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea ) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea )));
            }
            if ((request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null) && request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId &&
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea)));
            }
            else if (request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea ) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea )));
            }
            if ((request.MinSaleableArea != null || request.MaxSaleableArea != null) && request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.SaleableAreaUnitId == request.SaleableAreaUnitId &&
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea)));
            }
            else if (request.MinSaleableArea != null || request.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea ) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea )));
            }
            if ((request.MinPropertyArea != null || request.MaxPropertyArea != null) && request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.PropertyAreaUnitId == request.PropertyAreaUnitId &&
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea)));
            }
            else if (request.MinPropertyArea != null || request.MaxPropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea ) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea )));
            }

            if ((request.MinNetArea != null || request.MaxNetArea != null) && request.NetAreaUnitId != default && request.NetAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.NetAreaUnitId == request.NetAreaUnitId &&
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea)));
            }
            else if (request.MinNetArea != null || request.MaxNetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea ) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea )));
            }
            if (request.EnquiryTypes != null && request.EnquiryTypes.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.EnquiryTypes.Contains(i.EnquiryType)));
                query = query.Where(i =>
                                          i.Enquiries.Any(e => e.EnquiryTypes.Any(t => request.EnquiryTypes.Contains(t))));
            }
            if (request.NoOfBHKs != null && request.NoOfBHKs.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.NoOfBHKs.Contains(i.NoOfBhks)));
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(e => e.BHKs != null && request.NoOfBHKs.Any(r=>EF.Functions.JsonExists(e.BHKs,r.ToString()))));
                query = query.Where(i => i.Enquiries.Any(e => e.BHKs.Any(b => request.NoOfBHKs.Contains(b))));
            }
            if (request.SubSources != null && request.SubSources.Any())
            {
                request.SubSources = request.SubSources.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Enquiries.Any(i => request.SubSources.Contains(i.SubSource.ToLower().Trim())));
            }
            if (request.BHKTypes != null && request.BHKTypes.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.BHKTypes.Contains(i.BHKType)));
                query = query.Where(i => i.Enquiries.Any(e => e.BHKTypes.Any(b => request.BHKTypes.Contains(b))));
            }
            if (request.PropertyType != null && request.PropertyType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertyType.Contains(i.BaseId ?? Guid.Empty))));
            }
            if (request.PropertySubType != null && request.PropertySubType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertySubType.Contains(i.Id))));
            }
            if (request.Properties != null && request.Properties.Any())
            {
                var propertyNames = request.Properties.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Properties.Any(i => propertyNames.Contains(i.Title.ToLower())));
            }
            if (request.Projects?.Any() ?? false)
            {
                var projectNames = request.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Projects.Any(i => projectNames.Contains(i.Name.ToLower()))).AsQueryable();
            }
            if (request.Budget != null && request.Budget.Any())
            {
                foreach (var budget in request.Budget)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget <= 1000000));
                            break;
                        case Budget.TenToTwentyLakhs:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 1000000 && j.UpperBudget <= 2000000));
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 2000000 && j.UpperBudget <= 3000000));
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 3000000 && j.UpperBudget <= 4000000));
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 4000000 && j.UpperBudget <= 5000000));
                            break;
                        case Budget.FiftyToOneCrore:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 5000000 && j.UpperBudget <= 10000000));
                            break;
                        case Budget.MoreThanOneCrore:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 10000000));
                            break;
                    }
                }
            }

            if (request.Locations != null && request.Locations.Any())
            {
                request.Locations = request.Locations.ConvertAll(i => Uri.UnescapeDataString(i).Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Any(j =>
                request.Locations.Contains(
                    (j.SubLocality +
                    j.Locality +
                    j.Community +
                    j.SubCommunity +
                    j.TowerName +
                    j.District +
                    j.City +
                    j.State +
                    j.Country +
                    j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")))));

            }
            if (request.Localities?.Any() ?? false)
            {
                var normalizedLocalities = request.Localities
                    .Select(loc => loc.Replace(",", "").ToLower().Trim().Replace(" ", ""))
                    .ToList();

                query = query.Where(enquiry => enquiry.Enquiries.Any(e =>
                    e.Addresses.Any(address =>
                        normalizedLocalities.Contains((address.Location.Locality ?? "").ToLower().Trim().Replace(" ", ""))
                    )
                ));
            }
            if (request.Currency != null)
            {
                query = query.Where(i => i.Enquiries.Any(e => e.Currency == request.Currency));
            }
            if (request.MinBudget != null || request.MaxBudget != null)
            {
                if (request.MinBudget != null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget && i.UpperBudget <= request.MaxBudget) || (i.LowerBudget >= request.MinBudget && i.LowerBudget <= request.MaxBudget)));
                }
                else if (request.MinBudget != null && request.MaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget) || (i.LowerBudget >= request.MinBudget)));
                }
                else if (request.MinBudget == null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget <= request.MaxBudget) || (i.LowerBudget <= request.MaxBudget)));
                }
            }
            if (request.FromMinBudget != null || request.ToMinBudget != null || request.FromMaxBudget != null || request.ToMaxBudget != null)
            {
                if (request.FromMinBudget != null && request.ToMinBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        (e.LowerBudget >= request.FromMinBudget && e.LowerBudget <= request.ToMinBudget) ||
                        (e.LowerBudget >= request.FromMinBudget && e.LowerBudget <= request.ToMinBudget)
                    ));
                }
                else if (request.FromMinBudget != null && request.ToMinBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.LowerBudget >= request.FromMinBudget ||
                        e.LowerBudget >= request.FromMinBudget
                    ));
                }
                else if (request.FromMinBudget == null && request.ToMinBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.LowerBudget <= request.ToMinBudget ||
                        e.LowerBudget <= request.ToMinBudget
                    ));
                }

                if (request.FromMaxBudget != null && request.ToMaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        (e.UpperBudget >= request.FromMaxBudget && e.UpperBudget <= request.ToMaxBudget) ||
                        (e.UpperBudget >= request.FromMaxBudget && e.UpperBudget <= request.ToMaxBudget)
                    ));
                }
                else if (request.FromMaxBudget != null && request.ToMaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.UpperBudget >= request.FromMaxBudget ||
                        e.UpperBudget >= request.FromMaxBudget
                    ));
                }
                else if (request.FromMaxBudget == null && request.ToMaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.UpperBudget <= request.ToMaxBudget ||
                        e.UpperBudget <= request.ToMaxBudget
                    ));
                }
            }
            if (request.AgencyNames != null && request.AgencyNames.Any())
            {
                request.AgencyNames = request.AgencyNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Agencies.Any(j => request.AgencyNames.Contains(j.Name.ToLower().Trim())));
            }
            if (request?.LastModifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.LastModifiedByIds.Contains(i.LastModifiedBy));
            }
            if (request?.CreatedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.CreatedByIds.Contains(i.CreatedBy));
            }
            if (request?.DeletedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.DeletedByIds.Contains(i.ArchivedBy.Value));
            }
            if (request?.RestoredByIds?.Any() ?? false)
            {
                query = query.Where(i => request.RestoredByIds.Contains(i.RestoredBy.Value));
            }
            if (request?.AssignedFromIds?.Any() ?? false)
            {
                query = query.Where(i => i.AssignedFrom != null && request.AssignedFromIds.Contains(i.AssignedFrom.Value));
            }
            if (request?.CompanyNames?.Any() ?? false)
            {
                request.CompanyNames = request.CompanyNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => request.CompanyNames.Contains(i.CompanyName.ToLower().Trim()));
            }
            if (request?.ProspectIds?.Any() ?? false)
            {
                query = query.Where(i => request.ProspectIds.Contains(i.Id));
            }
            if (request?.SourcingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.SourcingManagers.Contains(i.SourcingManager.Value));
            }
            if (request?.ClosingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.ClosingManagers.Contains(i.ClosingManager.Value));
            }
            if (request?.Profession != null && request.Profession.Any())
            {
                query = query.Where(i => request.Profession.Contains(i.Profession));
            }
            if (request?.Source?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => request.Source.Contains(j.Source.Id)));
            }
            if (request.AgencyNames != null && request.AgencyNames.Any())
            {
                request.AgencyNames = request.AgencyNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Agencies.Any(j => request.AgencyNames.Contains(j.Name.ToLower().Trim())));
            }
            if (request.CampaignNames != null && request.CampaignNames.Any())
            {
                request.CampaignNames = request.CampaignNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Campaigns.Any(j => request.CampaignNames.Contains(j.Name.ToLower().Trim())));
            }
            if (request.ChannelPartnerNames != null && request.ChannelPartnerNames.Any())
            {
                request.ChannelPartnerNames = request.ChannelPartnerNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.ChannelPartners.Any(j => request.ChannelPartnerNames.Contains(j.FirmName.ToLower().Trim())));
            }
            var statusIds = new List<Guid>();
            statusIds.AddRange(request?.StatusIds ?? new List<Guid>());
            statusIds.AddRange(request?.StatusIds ?? new List<Guid>());
            if (statusIds.Any())
            {
                query = query.Where(i => (statusIds.Contains(i.StatusId ?? Guid.Empty)));
            }
            if (request?.Cities?.Any() ?? false)
            {
                var normalizedCityNames = request.Cities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedCityNames.Contains(i.City.ToLower().Trim().Replace(" ", ""))).Any()));
            }

            if (request?.Designations != null && request.Designations.Any())
            {
                request.Designations = request.Designations.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => request.Designations.Contains(i.Designation.ToLower().Trim()));
            }
            if (request?.QualifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.QualifiedByIds.Contains(i.QualifiedBy.Value));
            }
            if (request?.ConvertedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.ConvertedByIds.Contains(i.ConvertedBy.Value));
            }
            if (!string.IsNullOrEmpty(request.ReferralName))
            {
                query = query.Where(i => (i.ReferralName.ToLower().Trim().Contains(request.ReferralName.ToLower().Trim())));
            }
            if (!string.IsNullOrEmpty(request.UnitName))
            {
                query = query.Where(i => i.Enquiries.Any(e => e.UnitName != null && e.UnitName.ToLower().Trim() == request.UnitName.ToLower().Trim()));
            }
            if (request?.UnitNames?.Any() ?? false)
            {
                var unitnames = request.UnitNames.ConvertAll(i => i.ToLower());
                query = query.Where(i =>  i.Enquiries.Any(i => unitnames.Contains(i.UnitName.ToLower()))).AsQueryable();
            }
            if (request?.Nationality?.Any() ?? false)
            {
                var nationality = request.Nationality.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Nationality != null && nationality.Contains(i.Nationality.ToLower())).AsQueryable();
            }

            if (request?.ClusterName?.Any() ?? false)
            {
                var ClusterName = request.ClusterName.ConvertAll(i => i.ToLower());
                query = query.Where(i =>  i.Enquiries.Any(i => ClusterName.Contains(i.ClusterName.ToLower()))).AsQueryable();
            }
            if (!string.IsNullOrEmpty(request.ReferralContactNo))
            {
                query = query.Where(i => !string.IsNullOrEmpty(i.ReferralContactNo) && request.ReferralContactNo.Contains(i.ReferralContactNo.Substring(i.ReferralContactNo.Length - 10)));
            }
            if (!string.IsNullOrEmpty(request.ReferralEmail))
            {
                query = query.Where(i => (i.ReferralEmail.ToLower().Trim().Contains(request.ReferralEmail.ToLower().Trim())));
            }
            if (request.Beds?.Any() ?? false)
            {
                query = query.Where(i =>  i.Enquiries.Any(i => i.Beds != null && i.Beds.Any(j => request.Beds.Contains(j))));
            }
            if (request.Baths?.Any() ?? false)
            {
                query = query.Where(i =>  i.Enquiries.Any(i => i.Baths != null && i.Baths.Any(j => request.Baths.Contains(j))));
            }
            if (request.Floors?.Any() ?? false)
            {
                query = query.Where(i =>  i.Enquiries.Any(i => i.Floors != null && i.Floors.Any(j => request.Floors.Contains(j))));
            }
            if (request.OfferTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.OfferType != null && request.OfferTypes.Contains(i.OfferType.Value)));
            }
            if (request.Purposes?.Any() ?? false)
            {
                query = query.Where(i =>  i.Enquiries.Any(i => i.Purpose != null && request.Purposes.Contains(i.Purpose.Value)));
            }
            if (request.Furnished?.Any() ?? false)
            {
                query = query.Where(i =>  i.Enquiries.Any(i => i.Furnished != null && request.Furnished.Contains(i.Furnished.Value)));
            }
            if (request.Communities?.Any() ?? false)
            {
                var normalizedStateNames = request.Communities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Community.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.SubCommunities?.Any() ?? false)
            {
                var normalizedStateNames = request.SubCommunities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.SubCommunity.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.TowerNames?.Any() ?? false)
            {
                var normalizedStateNames = request.TowerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.TowerName.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Countries?.Any() ?? false)
            {
                var normalizedStateNames = request.Countries.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Country.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (!string.IsNullOrEmpty(request?.UploadTypeName))
            {
                query = query.Where(i => i.UploadTypeName != null && i.UploadTypeName.ToLower().Trim().Contains(request.UploadTypeName.ToLower().Trim()));
            }
            if (request?.PossesionType != null && request?.PossesionType != PossesionType.None)
            {
                switch (request?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.Enquiries.Any(j => j.PossesionType == PossesionType.UnderConstruction));
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.Enquiries.Any(j => j.PossesionType == PossesionType.SixMonth));
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.Enquiries.Any(j => j.PossesionType == PossesionType.Year));
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.Enquiries.Any(j => j.PossesionType == PossesionType.TwoYears));
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(j => j.PossesionDate != null && j.PossesionDate >= tempFrompossesionDate.Value && j.PossesionDate <= tempToPossesionDate.Value);

                        break;
                }
            }
            if (request?.LandLine != null && request.LandLine.Any())
            {
                query = query.Where(i => i.LandLine != null && request.LandLine.Contains(i.LandLine.Trim()));
            }
            if (request?.CountryCode?.Any() ?? false)
            {
                query = query.Where(i => i.CountryCode != null && request.CountryCode.Contains(i.CountryCode));
            }
            if (request?.AltCountryCode?.Any() ?? false)
            {
                query = query.Where(i => i.AltCountryCode != null && request.AltCountryCode.Contains(i.AltCountryCode));
            }
            if (request?.GenderTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Gender != null && request.GenderTypes.Contains(i.Gender.Value));
            }
            if (request?.MaritalStatuses?.Any() ?? false)
            {
                query = query.Where(i => i.MaritalStatus != null && request.MaritalStatuses.Contains(i.MaritalStatus.Value));
            }
            if (request?.DateOfBirth != null)
            {
                query = query.Where(i => i.DateOfBirth != null && i.DateOfBirth == request.DateOfBirth);
            }
            return query;
        }
        private IQueryable<Prospect> CustomFilterBuildQueryForCount(CustomFilter filter, GetAllProspectFilterParameter request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<CustomProspectStatus>? prospectStatuses = null)
        {
            var tenantId = _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var reporteeIds = (subIds.Where(i => i != userId && i != Guid.Empty)).ToList();
            var selfWithReporteeIds = (subIds.Where(i => i != Guid.Empty)).ToList();
            IQueryable<Prospect>? query = null;
            query = context.Prospects.Where(i => !i.IsDeleted).AsQueryable();
            if (!string.IsNullOrWhiteSpace(request.ProspectSearch) && request.PropertyToSearch?.Any() == true)
            {
                var searchTerm = request.ProspectSearch.ToLower().Trim().Replace(" ", "");
                var isPurposeValid = TryParseEnum<Purpose>(searchTerm, out var parsedPurpose);
                var isEnquiryTypeValid = TryParseEnum<EnquiryType>(searchTerm, out var parsedEnquiryType);
                var statusId = prospectStatuses?.Where(i => i.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm)).Select(i => i.Id).ToList();
                query = query.Where(i =>
                   (request.PropertyToSearch.Contains("DataName") && i.Name != null && i.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("ContactNo") && i.ContactNo != null && i.ContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("AlternateContactNo") && i.AlternateContactNo != null && i.AlternateContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Email") && i.Email != null && i.Email.ToLower().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Nationality") && i.Nationality != null && i.Nationality.ToLower().Trim().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("PropertyName") && i.Properties.Any(a => a.Title.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("ProjectName") && i.Projects.Any(a => a.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Status") && (statusId.Contains(i.Status.BaseId ?? Guid.Empty) || statusId.Contains(i.Status.Id))) ||
                   (request.PropertyToSearch.Contains("Source") && i.Enquiries.Any(e => e.Source.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("SubSource") && i.Enquiries.Any(a => !string.IsNullOrEmpty(a.SubSource) && a.SubSource.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Purpose") && isPurposeValid && i.Enquiries.Any(e => e.Purpose == parsedPurpose)) ||
                   (request.PropertyToSearch.Contains("EnquiredFor") && isEnquiryTypeValid && i.Enquiries.Any(e => e.EnquiryTypes.Contains(parsedEnquiryType))) ||
                   (request.PropertyToSearch.Contains("Location") && i.Enquiries.Any(e => e.Addresses.Any(j =>
                     (j.SubLocality +
                      j.Locality +
                      j.Community +
                      j.SubCommunity +
                      j.TowerName +
                      j.District +
                      j.City +
                      j.State +
                      j.Country +
                      j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")
                     .Contains(searchTerm)))));
            }
            else if (!string.IsNullOrWhiteSpace(request.ProspectSearch))
            {
                request.ProspectSearch = request.ProspectSearch.ToLower().Trim();
                query = query.Where(i => (i.Name.ToLower().Trim().Contains(request.ProspectSearch))
               || (i.ContactNo.ToLower().Trim().Contains(request.ProspectSearch.Replace(" ", ""))));
            }
            switch (request.ProspectVisiblity)
            {
                case ProspectVisiblity.SelfWithReportee:
                    query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                    break;
                case ProspectVisiblity.Self:
                    query = query.Where(i => i.AssignTo == userId).Where(i => !i.IsArchived);
                    break;
                case ProspectVisiblity.Reportee:
                    query = query.Where(i => reporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                    break;
                case ProspectVisiblity.UnassignData:
                    query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                    break;
                case ProspectVisiblity.DeletedData:
                    if (isAdmin)
                    {
                        query = query.Where(i => i.IsArchived);
                        if (request.AssignTo?.Any() ?? false)
                        {
                            query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
                        }
                    }
                    else
                    {
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                    }
                    break;
                case ProspectVisiblity.ConvertedData:
                    if (isAdmin)
                    {
                        query = query.Where(i => i.IsConvertedToLead && i.IsQualified && !i.IsArchived && i.AssignTo != Guid.Empty);
                        if (request.AssignTo?.Any() ?? false)
                        {
                            query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
                        }
                    }
                    else
                    {
                        query = query.Where(i => i.IsConvertedToLead && i.IsQualified && !i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                    }
                    break;
            }

            if (request.DateType.HasValue && request.FromDate.HasValue && request.FromDate.Value != default && request.ToDate.HasValue && request.ToDate.Value != default)
            {
                DateTime? tempToDate = request.ToDate.Value.ConvertToDateToUtc();
                DateTime? tempFromDate = request.FromDate.Value.ConvertFromDateToUtc();
                switch (request.DateType)
                {
                    case ProspectDateType.CreatedDate:
                        query = query.Where(i => i.CreatedOn >= tempFromDate && i.CreatedOn <= tempToDate);
                        break;
                    case ProspectDateType.ModifiedDate:
                        query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= tempFromDate && i.LastModifiedOn.Value <= tempToDate);
                        break;
                    case ProspectDateType.All:
                        query = query.Where(i => (i.CreatedOn >= tempFromDate && i.CreatedOn <= tempToDate) ||
                                            i.ScheduleDate != null && i.ScheduleDate.Value >= tempFromDate && i.ScheduleDate.Value <= tempToDate ||
                                          i.LastModifiedOn != null && i.LastModifiedOn.Value >= tempFromDate && i.LastModifiedOn.Value <= tempToDate);
                        break;
                    case ProspectDateType.ScheduleDate:
                        query = query.Where(i => i.ScheduleDate != null && i.ScheduleDate.Value >= tempFromDate && i.ScheduleDate.Value <= tempToDate);
                        break;
                    case ProspectDateType.QualifiedDate:
                        query = query.Where(i => i.QualifiedDate != null && i.QualifiedDate.Value >= tempFromDate && i.QualifiedDate.Value <= tempToDate);
                        break;
                    case ProspectDateType.ConvertedDate:
                        query = query.Where(i => i.ConvertedDate != null && i.ConvertedDate.Value >= tempFromDate && i.ConvertedDate.Value <= tempToDate);
                        break;
                    case ProspectDateType.PossessionDate:
                        query = query.Where(i => i.PossesionDate != null && i.PossesionDate.Value >= tempFromDate && i.PossesionDate.Value <= tempToDate);
                        break;
                    default:
                        break;
                }
            }


            #region Custom Filters
            if (filter != null)
            {
                if (filter.IsConvertedFilter ?? false)
                {
                    query = query.Where(i => i.IsConvertedToLead && i.IsQualified);
                }
                else
                {
                    query = query.Where(i => !i.IsConvertedToLead);
                }
                if (!filter.CustomProspectStatuses?.Any() ?? true)
                {
                    query = query.Where(i => i.StatusId != null);
                }
                else if (filter.IsForward != null)
                {
                    if (filter.IsForward ?? false)
                    {
                        if (filter.FromNoOfDays == 0 && filter.ToNoOfDays == 0)
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddMinutes(-330) && (i.ScheduleDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddDays(1).AddMinutes(-330));
                        }
                        else if (filter.FromNoOfDays >= 0 && filter.ToNoOfDays > 0)
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddDays(filter.FromNoOfDays ?? 0).AddMinutes(-330) && (i.ScheduleDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddDays(filter.ToNoOfDays ?? 0).AddMinutes(-330));
                        }
                        else if (filter.FromNoOfDays >= 0)
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddDays(filter.FromNoOfDays ?? 0).AddMinutes(-330));
                        }
                        else if (filter.ToNoOfDays >= 0)
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddMinutes(-330) && (i.ScheduleDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddDays(filter.ToNoOfDays ?? 0).AddMinutes(-330));
                        }
                        else
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddMinutes(-330));
                        }
                        var statusId = filter.CustomProspectStatuses?.Select(i => i.Id)?.ToList() ?? new List<Guid>();
                        if (statusId?.Any() ?? false)
                        {
                            query = query.Where(i =>  statusId.Contains(i.StatusId ?? Guid.Empty));
                        }
                    }
                    else
                    {
                        if (filter.FromNoOfDays >= 0 && filter.ToNoOfDays > 0)
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) <= DateTime.UtcNow.Date.AddDays((-filter.FromNoOfDays) ?? 0).AddMinutes(-330) && (i.ScheduleDate ?? DateTime.MinValue) > DateTime.UtcNow.Date.AddDays((-filter.ToNoOfDays) ?? 0).AddMinutes(-330));
                        }
                        else if (filter.FromNoOfDays >= 0)
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) <= DateTime.UtcNow.Date.AddDays((-filter.FromNoOfDays) ?? 0).AddMinutes(-330));
                        }
                        else if (filter.ToNoOfDays >= 0)
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) <= DateTime.UtcNow.Date.AddMinutes(-330) && (i.ScheduleDate ?? DateTime.MinValue) > DateTime.UtcNow.Date.AddDays((-filter.ToNoOfDays) ?? 0).AddMinutes(-330));
                        }
                        else
                        {
                            query = query.Where(i => (i.ScheduleDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddMinutes(-330));
                        }
                        var statusId = filter.CustomProspectStatuses?.Select(i => i.Id)?.ToList() ?? new List<Guid>();
                        if (statusId?.Any() ?? false)
                        {
                            query = query.Where(i => statusId.Contains(i.StatusId ?? Guid.Empty));
                        }
                    }
                }
                else if (filter.CustomProspectStatuses?.Any() ?? false)
                {
                    var statusId = filter.CustomProspectStatuses.Select(i => i.Id).ToList();
                    query = query.Where(i => statusId.Contains(i.StatusId ?? Guid.Empty));
                }
            }
            #endregion


            if (request.CarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetArea == request.CarpetArea));
            }
            if (request.BuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpArea == request.BuiltUpArea));
            }
            if (request.SaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableArea == request.SaleableArea));
            }
            if (request.NetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea));
            }
            if (request.PropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea));
            }
            if (request.CarpetAreaUnitId != default && request.CarpetAreaUnitId != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetAreaUnitId == request.CarpetAreaUnitId));
            }
            if (request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId));
            }
            if (request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableAreaUnitId == request.SaleableAreaUnitId));
            }
            if (request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyAreaUnitId == request.PropertyAreaUnitId));
            }
            if (request.NetAreaUnitId != default && request.NetAreaUnitId != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetAreaUnitId == request.NetAreaUnitId));
            }
            if ((request.MinCarpetArea != null || request.MaxCarpetArea != null) && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.CarpetAreaUnitId == request.CarpetAreaUnitId &&
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea)));
            }
            else if (request.MinCarpetArea != null || request.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea ) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea )));
            }
            if ((request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null) && request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId &&
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea)));
            }
            else if (request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea ) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea )));
            }
            if ((request.MinSaleableArea != null || request.MaxSaleableArea != null) && request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.SaleableAreaUnitId == request.SaleableAreaUnitId &&
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea)));
            }
            else if (request.MinSaleableArea != null || request.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea ) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea )));
            }
            if ((request.MinPropertyArea != null || request.MaxPropertyArea != null) && request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.PropertyAreaUnitId == request.PropertyAreaUnitId &&
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea)));
            }
            else if (request.MinPropertyArea != null || request.MaxPropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea ) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea )));
            }

            if ((request.MinNetArea != null || request.MaxNetArea != null) && request.NetAreaUnitId != default && request.NetAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.NetAreaUnitId == request.NetAreaUnitId &&
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea)));
            }
            else if (request.MinNetArea != null || request.MaxNetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea ) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea )));
            }
            if (request.EnquiryTypes != null && request.EnquiryTypes.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.EnquiryTypes.Any(t => request.EnquiryTypes.Contains(t))));
            }
            if (request.NoOfBHKs != null && request.NoOfBHKs.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.BHKs.Any(b => request.NoOfBHKs.Contains(b))));
            }
            if (request.SubSources != null && request.SubSources.Any())
            {
                request.SubSources = request.SubSources.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Enquiries.Any(i => request.SubSources.Contains(i.SubSource.ToLower().Trim())));
            }
            if (request.BHKTypes != null && request.BHKTypes.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.BHKTypes.Any(b => request.BHKTypes.Contains(b))));
            }
            if (request.PropertyType != null && request.PropertyType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertyType.Contains(i.BaseId ?? Guid.Empty))));
            }
            if (request.PropertySubType != null && request.PropertySubType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertySubType.Contains(i.Id))));
            }
            if (request.Properties != null && request.Properties.Any())
            {
                var propertyNames = request.Properties.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Properties.Any(i => propertyNames.Contains(i.Title.ToLower())));
            }
            if (request.Projects?.Any() ?? false)
            {
                var projectNames = request.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Projects.Any(i => projectNames.Contains(i.Name.ToLower()))).AsQueryable();
            }
            if (request.Budget != null && request.Budget.Any())
            {
                foreach (var budget in request.Budget)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget <= 1000000));
                            break;
                        case Budget.TenToTwentyLakhs:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 1000000 && j.UpperBudget <= 2000000));
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 2000000 && j.UpperBudget <= 3000000));
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 3000000 && j.UpperBudget <= 4000000));
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 4000000 && j.UpperBudget <= 5000000));
                            break;
                        case Budget.FiftyToOneCrore:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 5000000 && j.UpperBudget <= 10000000));
                            break;
                        case Budget.MoreThanOneCrore:
                            query = query.Where(i => i.Enquiries.Any(j => j.UpperBudget > 10000000));
                            break;
                    }
                }
            }

            if (request.Locations != null && request.Locations.Any())
            {
                request.Locations = request.Locations.ConvertAll(i => Uri.UnescapeDataString(i).Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Any(j =>
                request.Locations.Contains(
                    (j.SubLocality +
                    j.Locality +
                    j.Community +
                    j.SubCommunity +
                    j.TowerName +
                    j.District +
                    j.City +
                    j.State +
                    j.Country +
                    j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")))));

            }
            if (request.Localities?.Any() ?? false)
            {
                var normalizedLocalities = request.Localities
                    .Select(loc => loc.Replace(",", "").ToLower().Trim().Replace(" ", ""))
                    .ToList();

                query = query.Where(enquiry => enquiry.Enquiries.Any(e =>
                    e.Addresses.Any(address =>
                        normalizedLocalities.Contains((address.Location.Locality ?? "").ToLower().Trim().Replace(" ", ""))
                    )
                ));
            }
            if (request.Currency != null)
            {
                query = query.Where(i => i.Enquiries.Any(e => e.Currency == request.Currency));
            }
            if (request.MinBudget != null || request.MaxBudget != null)
            {
                if (request.MinBudget != null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget && i.UpperBudget <= request.MaxBudget) || (i.LowerBudget >= request.MinBudget && i.LowerBudget <= request.MaxBudget)));
                }
                else if (request.MinBudget != null && request.MaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget) || (i.LowerBudget >= request.MinBudget)));
                }
                else if (request.MinBudget == null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget <= request.MaxBudget) || (i.LowerBudget <= request.MaxBudget)));
                }
            }
            if (request.FromMinBudget != null || request.ToMinBudget != null || request.FromMaxBudget != null || request.ToMaxBudget != null)
            {
                if (request.FromMinBudget != null && request.ToMinBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        (e.LowerBudget >= request.FromMinBudget && e.LowerBudget <= request.ToMinBudget) ||
                        (e.LowerBudget >= request.FromMinBudget && e.LowerBudget <= request.ToMinBudget)
                    ));
                }
                else if (request.FromMinBudget != null && request.ToMinBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.LowerBudget >= request.FromMinBudget ||
                        e.LowerBudget >= request.FromMinBudget
                    ));
                }
                else if (request.FromMinBudget == null && request.ToMinBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.LowerBudget <= request.ToMinBudget ||
                        e.LowerBudget <= request.ToMinBudget
                    ));
                }

                if (request.FromMaxBudget != null && request.ToMaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        (e.UpperBudget >= request.FromMaxBudget && e.UpperBudget <= request.ToMaxBudget) ||
                        (e.UpperBudget >= request.FromMaxBudget && e.UpperBudget <= request.ToMaxBudget)
                    ));
                }
                else if (request.FromMaxBudget != null && request.ToMaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.UpperBudget >= request.FromMaxBudget ||
                        e.UpperBudget >= request.FromMaxBudget
                    ));
                }
                else if (request.FromMaxBudget == null && request.ToMaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.UpperBudget <= request.ToMaxBudget ||
                        e.UpperBudget <= request.ToMaxBudget
                    ));
                }
            }
            if (request.AgencyNames != null && request.AgencyNames.Any())
            {
                request.AgencyNames = request.AgencyNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Agencies.Any(j => request.AgencyNames.Contains(j.Name.ToLower().Trim())));
            }
            if (request?.LastModifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.LastModifiedByIds.Contains(i.LastModifiedBy));
            }
            if (request?.CreatedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.CreatedByIds.Contains(i.CreatedBy));
            }
            if (request?.DeletedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.DeletedByIds.Contains(i.ArchivedBy.Value));
            }
            if (request?.RestoredByIds?.Any() ?? false)
            {
                query = query.Where(i => request.RestoredByIds.Contains(i.RestoredBy.Value));
            }
            if (request?.AssignedFromIds?.Any() ?? false)
            {
                query = query.Where(i => i.AssignedFrom != null && request.AssignedFromIds.Contains(i.AssignedFrom.Value));
            }
            if (request?.CompanyNames?.Any() ?? false)
            {
                request.CompanyNames = request.CompanyNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => request.CompanyNames.Contains(i.CompanyName.ToLower().Trim()));
            }
            if (request?.ProspectIds?.Any() ?? false)
            {
                query = query.Where(i => request.ProspectIds.Contains(i.Id));
            }
            if (request?.SourcingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.SourcingManagers.Contains(i.SourcingManager.Value));
            }
            if (request?.ClosingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.ClosingManagers.Contains(i.ClosingManager.Value));
            }
            if (request?.Profession != null && request.Profession.Any())
            {
                query = query.Where(i => request.Profession.Contains(i.Profession));
            }
            if (request?.Source?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => request.Source.Contains(j.Source.Id)));
            }
            if (request.AgencyNames != null && request.AgencyNames.Any())
            {
                request.AgencyNames = request.AgencyNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Agencies.Any(j => request.AgencyNames.Contains(j.Name.ToLower().Trim())));
            }
            if (request.CampaignNames != null && request.CampaignNames.Any())
            {
                request.CampaignNames = request.CampaignNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Campaigns.Any(j => request.CampaignNames.Contains(j.Name.ToLower().Trim())));
            }
            if (request.ChannelPartnerNames != null && request.ChannelPartnerNames.Any())
            {
                request.ChannelPartnerNames = request.ChannelPartnerNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.ChannelPartners.Any(j => request.ChannelPartnerNames.Contains(j.FirmName.ToLower().Trim())));
            }
            var statusIds = new List<Guid>();
            statusIds.AddRange(request?.StatusIds ?? new List<Guid>());
            statusIds.AddRange(request?.StatusIds ?? new List<Guid>());
            if (statusIds.Any())
            {
                query = query.Where(i => (statusIds.Contains(i.StatusId ?? Guid.Empty)));
            }
            if (request?.Cities?.Any() ?? false)
            {
                var normalizedCityNames = request.Cities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedCityNames.Contains(i.City.ToLower().Trim().Replace(" ", ""))).Any()));
            }

            if (request?.Designations != null && request.Designations.Any())
            {
                request.Designations = request.Designations.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => request.Designations.Contains(i.Designation.ToLower().Trim()));
            }
            if (request?.QualifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.QualifiedByIds.Contains(i.QualifiedBy.Value));
            }
            if (request?.ConvertedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.ConvertedByIds.Contains(i.ConvertedBy.Value));
            }
            if (!string.IsNullOrEmpty(request.ReferralName))
            {
                query = query.Where(i => (i.ReferralName.ToLower().Trim().Contains(request.ReferralName.ToLower().Trim())));
            }
            if (!string.IsNullOrEmpty(request.UnitName))
            {
                query = query.Where(i => i.Enquiries.Any(e => e.UnitName != null && e.UnitName.ToLower().Trim() == request.UnitName.ToLower().Trim()));
            }
            if (request?.UnitNames?.Any() ?? false)
            {
                var unitnames = request.UnitNames.ConvertAll(i => i.ToLower());
                query = query.Where(i =>  i.Enquiries.Any(i => unitnames.Contains(i.UnitName.ToLower()))).AsQueryable();
            }
            if (request?.Nationality?.Any() ?? false)
            {
                var nationality = request.Nationality.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Nationality != null && nationality.Contains(i.Nationality.ToLower())).AsQueryable();
            }

            if (request?.ClusterName?.Any() ?? false)
            {
                var ClusterName = request.ClusterName.ConvertAll(i => i.ToLower());
                query = query.Where(i =>  i.Enquiries.Any(i => ClusterName.Contains(i.ClusterName.ToLower()))).AsQueryable();
            }
            if (!string.IsNullOrEmpty(request.ReferralContactNo))
            {
                query = query.Where(i => !string.IsNullOrEmpty(i.ReferralContactNo) && request.ReferralContactNo.Contains(i.ReferralContactNo.Substring(i.ReferralContactNo.Length - 10)));
            }
            if (!string.IsNullOrEmpty(request.ReferralEmail))
            {
                query = query.Where(i => (i.ReferralEmail.ToLower().Trim().Contains(request.ReferralEmail.ToLower().Trim())));
            }
            if (request.Beds?.Any() ?? false)
            {
                query = query.Where(i =>  i.Enquiries.Any(i => i.Beds != null && i.Beds.Any(j => request.Beds.Contains(j))));
            }
            if (request.Baths?.Any() ?? false)
            {
                query = query.Where(i =>  i.Enquiries.Any(i => i.Baths != null && i.Baths.Any(j => request.Baths.Contains(j))));
            }
            if (request.Floors?.Any() ?? false)
            {
                query = query.Where(i =>  i.Enquiries.Any(i => i.Floors != null && i.Floors.Any(j => request.Floors.Contains(j))));
            }
            if (request.OfferTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.OfferType != null && request.OfferTypes.Contains(i.OfferType.Value)));
            }
            if (request.Purposes?.Any() ?? false)
            {
                query = query.Where(i =>  i.Enquiries.Any(i => i.Purpose != null && request.Purposes.Contains(i.Purpose.Value)));
            }
            if (request.Furnished?.Any() ?? false)
            {
                query = query.Where(i =>  i.Enquiries.Any(i => i.Furnished != null && request.Furnished.Contains(i.Furnished.Value)));
            }
            if (request.Communities?.Any() ?? false)
            {
                var normalizedStateNames = request.Communities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Community.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.SubCommunities?.Any() ?? false)
            {
                var normalizedStateNames = request.SubCommunities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.SubCommunity.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.TowerNames?.Any() ?? false)
            {
                var normalizedStateNames = request.TowerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.TowerName.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Countries?.Any() ?? false)
            {
                var normalizedStateNames = request.Countries.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Country.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (!string.IsNullOrEmpty(request?.UploadTypeName))
            {
                query = query.Where(i => i.UploadTypeName != null && i.UploadTypeName.ToLower().Trim().Contains(request.UploadTypeName.ToLower().Trim()));
            }
            if (request?.PossesionType != null && request?.PossesionType != PossesionType.None)
            {
                switch (request?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.Enquiries.Any(j => j.PossesionType == PossesionType.UnderConstruction));
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.Enquiries.Any(j => j.PossesionType == PossesionType.SixMonth));
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.Enquiries.Any(j => j.PossesionType == PossesionType.Year));
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.Enquiries.Any(j => j.PossesionType == PossesionType.TwoYears));
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(j => j.PossesionDate != null && j.PossesionDate >= tempFrompossesionDate.Value && j.PossesionDate <= tempToPossesionDate.Value);

                        break;
                }
            }
            if (request?.LandLine != null && request.LandLine.Any())
            {
                query = query.Where(i => i.LandLine != null && request.LandLine.Contains(i.LandLine.Trim()));
            }
            if (request?.CountryCode?.Any() ?? false)
            {
                query = query.Where(i => i.CountryCode != null && request.CountryCode.Contains(i.CountryCode));
            }
            if (request?.AltCountryCode?.Any() ?? false)
            {
                query = query.Where(i => i.AltCountryCode != null && request.AltCountryCode.Contains(i.AltCountryCode));
            }
            if (request?.GenderTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Gender != null && request.GenderTypes.Contains(i.Gender.Value));
            }
            if (request?.MaritalStatuses?.Any() ?? false)
            {
                query = query.Where(i => i.MaritalStatus != null && request.MaritalStatuses.Contains(i.MaritalStatus.Value));
            }
            if (request?.DateOfBirth != null)
            {
                query = query.Where(i => i.DateOfBirth != null && i.DateOfBirth == request.DateOfBirth);
            }
            return query;
        }
    }
}
