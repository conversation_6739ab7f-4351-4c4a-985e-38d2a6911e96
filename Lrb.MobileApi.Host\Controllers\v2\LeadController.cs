﻿using Lrb.Application.Lead.Mobile.Requests.v2;
using Lrb.Application.Lead.Mobile.v2;
using Lrb.Domain.Enums;

namespace Lrb.MobileApi.Host.Controllers.v2
{
    [Authorize]
    [Route("api/v2/[controller]")]
    [ApiVersionNeutral]
    public class LeadController : BaseApiController
    {
        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search leads using available filters.", "")]
        public async Task<Response<GetAllLeadsWrapperDto>> SearchAsync([FromQuery] V2GetAllLeadsRequest request) 
        {
            return await Mediator.Send(request);
        }
        [HttpGet("enquiries/{leadId:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get enquiries by lead id.", "")]
        public async Task<Response<List<V2ViewLeadEnquiryDto>>> GetLeadEnquiriesAsync(Guid leadId)
        {
            return await Mediator.Send(new V2GetLeadEnquiriesByIdRequest(leadId));
        }
        [HttpGet("basicInfo/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get lead basic info by lead id", "")]
        public async Task<Response<V2BasicLeadInfoDto>> GetLeadBasicInfoAsync(Guid id)
        {
            return await Mediator.Send(new V2GetLeadBasicInfoByIdRequest(id));
        }
        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get lead details.", "")]
        public Task<Response<V2ViewLeadDto>> GetLeadAsync(Guid id)
        {
            return Mediator.Send(new V2GetLeadByIdRequest(id));
        }
        [HttpGet("getAllLeadCategory")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leadCategory details.", "")]
        public Task<Response<LeadCategoryDto>> GetAsync([FromQuery] V2GetLeadCategoryRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("SearchLeads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search lead details.", "")]
        public Task<Response<LeadCategoryDto>> GetAsync([FromQuery] V2SearchLeadRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("leadSearch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("lead global search.", "")]
        public async Task<PagedResponse<SearchLeadDto, string>> GetAsync([FromQuery] V2LeadGlobalSearchRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("scheduledleads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search scheduled leads.", "")]
        public async Task<PagedResponse<SearchLeadDto, string>> GetAsync([FromQuery] V2GetLeadsByScheduledDateRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("subsource")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads subsource.", "")]
        public async Task<Response<Dictionary<LeadSource, List<string?>?>>> GetAllSubSourceAsync()
        {
            return await Mediator.Send(new V2GetAllLeadsSubSourceRequest());
        }
        [HttpPut("source/bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Update leads source.", "")]
        public async Task<Response<bool>> UpdateBulkSourceAsync(V2UpdateBulkLeadSourceRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("{contactNumber}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Lead id by contact numbar.", "")]
        public async Task<Response<Application.Lead.Mobile.LeadContactDto>> GetAsync(string contactNumber)
        {
            var response = await Mediator.Send(new V2GetLeadIdByContactNoRequest(contactNumber));
            return response;
        }

        [HttpPost("message")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("save lead message.", "")]
        public async Task<Response<bool>> SaveLeadMessage(SaveLeadCommunicationRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("basicInfo/anonymous/{id:guid}")]
        [AllowAnonymous]
        [OpenApiOperation("Get lead basic info by lead id anonymously", "")]
        public async Task<Response<V2BasicLeadInfoDto>> GetLeadBasicInfoAnonymousAsync(Guid id)
        {
            return await Mediator.Send(new GetLeadByIdAnonymousRequest(id));
        }
        [HttpGet("basicInfo/anonymous")]
        [AllowAnonymous]
        [OpenApiOperation("Get lead basic info by lead id anonymously - Updated", "")]
        public async Task<Response<V2BasicLeadInfoDto>> GetLeadBasicInfoAnonymousAsync([FromQuery] GetLeadByIdAnonymouslyRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("counts")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get leads count using available filters.", "")]
        public async Task<Response<LeadCountDto>> GetCountAsync([FromQuery] GetAllLeadCountsRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }

        [HttpGet("communications")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get lead communications details.", "")]
        public async Task<Response<Dictionary<Guid, Dictionary<ContactType, int>>>?> GetLeadCommunicationsAsync([FromQuery] GetLeadCommunicationsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut("assign")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Leads)]
        [OpenApiOperation("Assign leads to a user.", "")]
        public async Task<Response<bool>> AssignLeadsAsync(AssignLeadRequest request)
        {
            var res = await Mediator.Send(request);
            return res;
        }

        [HttpPut("assign/new")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Leads)]
        [OpenApiOperation("Assign leads to a user.", "")]
        public async Task<Response<bool>> AssignLeadsAsync(V2AssignLeadRequest request)
        {
            var res = await Mediator.Send(request);
            return res;
        }
        [HttpPut("customflag/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Update custom flag of a lead.", "")]
        public async Task<ActionResult<bool>> UpdateCustomFlagAsync(UpdateLeadCustomFlagRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpGet("Currency")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get All Currency.", "")]
        public async Task<Response<List<string>>> GetAllCuurencyy()
        {
            return await Mediator.Send(new GetAllCurrencyRequest());
        }
        [HttpPut("status/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.UpdateLeadStatus, LrbResource.Leads)]
        [OpenApiOperation("Update status of a lead.", "")]
        public async Task<ActionResult<Response<bool>>> UpdateStatusAsync(V2UpdateLeadStatusRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
    }
}
