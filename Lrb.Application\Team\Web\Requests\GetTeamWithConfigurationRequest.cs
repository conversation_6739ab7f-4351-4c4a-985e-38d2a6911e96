﻿using Lrb.Application.Team.Web.Dtos;
using Lrb.Domain.Entities.MasterData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Team.Web.Requests
{
    public class GetTeamWithConfigurationRequest : IRequest<Response<List<ViewTeamLeadRotationInfoDto>>>
    {
    }
    public class GetTeamWithConfigurationRequestHandler : IRequestHandler<GetTeamWithConfigurationRequest, Response<List<ViewTeamLeadRotationInfoDto>>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Team> _teamRepo;
        public GetTeamWithConfigurationRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Team> teamRepo)
        {
            _teamRepo = teamRepo;
        }

        public async Task<Response<List<ViewTeamLeadRotationInfoDto>>> Handle(GetTeamWithConfigurationRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var teams = await _teamRepo.ListAsync(new GetTeamsWithConfigurationSpec(), cancellationToken);
                if (!teams.Any()) { return new(); }
                ;
                return new(teams.Adapt<List<ViewTeamLeadRotationInfoDto>>());
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
