﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class ListingNewFieldAdditionsMigration : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<double>(
                name: "Downpayment",
                schema: "LeadratBlack",
                table: "PropertyMonetaryInfo",
                type: "double precision",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Age",
                schema: "LeadratBlack",
                table: "Properties",
                type: "double precision",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "FinishingType",
                schema: "LeadratBlack",
                table: "Properties",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "PublishedBy",
                schema: "LeadratBlack",
                table: "Properties",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "PublishedOn",
                schema: "LeadratBlack",
                table: "Properties",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UaeEmirate",
                schema: "LeadratBlack",
                table: "Properties",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "ListingCompliance",
                schema: "LeadratBlack",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    LicenseIssuanceDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ListingAdvertisementNumber = table.Column<string>(type: "text", nullable: true),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    UserConfirmedDataIsCorrect = table.Column<bool>(type: "boolean", nullable: false),
                    PropertyId = table.Column<Guid>(type: "uuid", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ListingCompliance", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ListingCompliance_Properties_PropertyId",
                        column: x => x.PropertyId,
                        principalSchema: "LeadratBlack",
                        principalTable: "Properties",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_ListingCompliance_PropertyId",
                schema: "LeadratBlack",
                table: "ListingCompliance",
                column: "PropertyId",
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ListingCompliance",
                schema: "LeadratBlack");

            migrationBuilder.DropColumn(
                name: "Downpayment",
                schema: "LeadratBlack",
                table: "PropertyMonetaryInfo");

            migrationBuilder.DropColumn(
                name: "Age",
                schema: "LeadratBlack",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "FinishingType",
                schema: "LeadratBlack",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "PublishedBy",
                schema: "LeadratBlack",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "PublishedOn",
                schema: "LeadratBlack",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "UaeEmirate",
                schema: "LeadratBlack",
                table: "Properties");
        }
    }
}
