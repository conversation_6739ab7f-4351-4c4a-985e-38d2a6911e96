using Lrb.Application.CustomForm.Web.Specs;

namespace Lrb.Application.CustomForm.Web.Requests
{
    public class DeleteCustomFormRequest : IRequest<Response<bool>>
    {
        public Guid Id { get; set; }

        public DeleteCustomFormRequest(Guid id)
        {
            Id = id;
        }
    }

    public class DeleteCustomFormRequestHandler : IRequestHandler<DeleteCustomFormRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.CustomFormFields> _customFormRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.CustomFieldValue> _customFormValueRepo;

        public DeleteCustomFormRequestHandler(IRepositoryWithEvents<Domain.Entities.CustomFormFields> customFormRepo, IRepositoryWithEvents<Domain.Entities.CustomFieldValue> customFormValueRepo)
        {
            _customFormRepo = customFormRepo;
            _customFormValueRepo = customFormValueRepo;
        }

        public async Task<Response<bool>> Handle(DeleteCustomFormRequest request, CancellationToken cancellationToken)
        {
            var customForm = await _customFormRepo.GetByIdAsync(request.Id, cancellationToken);
            if (customForm == null)
                throw new NotFoundException($"CustomForm with ID {request.Id} not found");
            var customFormValue = await _customFormValueRepo.FirstOrDefaultAsync(new GetCustomFormValueByIdSpecs(request.Id), cancellationToken);
            if (customFormValue!=null)
            {
                throw new NotFoundException($"the field  having the data cont't delete");
            }
            await _customFormRepo.SoftDeleteAsync(customForm, cancellationToken);
            return new(true);
        }
    }
}
