﻿
namespace Lrb.Application.Integration.Web.Requests.Bayut
{
    public class CreatePropertyFinderIntegrationRequest : IRequest<Response<Guid>>
    {
        public string? Name { get; set; }
        public string? ApiKey { get; set; }
        public string? SecretKey { get; set; }
        public PFRequestType? RequestType { get; set; }
        public LeadSource Source { get; set; }
        public List<string>? ToRecipients { get; set; }
        public List<string>? CcRecipients { get; set; }
        public List<string>? BccRecipients { get; set; }
        public string? LoginEmail { get; set; }
    }

    public class CreatePropertyFinderIntegrationRequestHandler : IRequestHandler<CreatePropertyFinderIntegrationRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccountInfoRepositoryAsync;
        private readonly ICurrentUser _currentUser;

        public CreatePropertyFinderIntegrationRequestHandler(IRepositoryWithEvents<IntegrationAccountInfo> integrationAccountInfoRepositoryAsync, ICurrentUser currentUser)
        {
            _integrationAccountInfoRepositoryAsync = integrationAccountInfoRepositoryAsync;
            _currentUser = currentUser;
        }

        public async Task<Response<Guid>> Handle(CreatePropertyFinderIntegrationRequest request, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(request.ApiKey))
            {
                throw new InvalidDataException("ApiKey connot be empty");
            }
            if (string.IsNullOrWhiteSpace(request.SecretKey))
            {
                throw new InvalidDataException("SecretKey connot be empty");
            }
            var userId = _currentUser.GetUserId();
            var cred = GetCredential(request);
            var integrationAccount = CreateIntegrationEntity(request, userId, cred);
            integrationAccount.ApiKey = ApiKeyHelper.GenerateApiKey(integrationAccount.Id);
            integrationAccount.LoginEmail = request.LoginEmail;
            integrationAccount.ToRecipients = request.ToRecipients;
            integrationAccount.CcRecipients = request.CcRecipients;
            integrationAccount.BccRecipients = request.BccRecipients;
            await _integrationAccountInfoRepositoryAsync.AddAsync(integrationAccount);
            return new(integrationAccount.Id);
        }

        private IntegrationAccountInfo CreateIntegrationEntity(CreatePropertyFinderIntegrationRequest command, Guid userId, Dictionary<string, string> credentials)
        {
            return new IntegrationAccountInfo()
            {
                Id = Guid.NewGuid(),
                AccountName = command.Name,
                LoginEmail = command.LoginEmail,
                LeadSource = command.Source,
                LicenseId = Guid.NewGuid(),
                CreatedBy = userId,
                Credentials = credentials
            };
        }

        private Dictionary<string, string> GetCredential(CreatePropertyFinderIntegrationRequest request)
        {
            Dictionary<string, string> credentials = new()
            {
                { "apiKey", request?.ApiKey ?? string.Empty },
                { "secretKey", request?.SecretKey ?? string.Empty },
                { "requestType", request?.RequestType.ToString() ?? string.Empty },
            };
            return credentials;
        }
    }

    public enum PFRequestType
    {
        None = 0,
        Whatsapp,
        Call,
        Email
    }
}
