﻿using LrbIntegrationBackgroundJobs.Dtos;
using LrbIntegrationBackgroundJobs.Dtos.Bayut;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace LrbIntegrationBackgroundJobs
{
    public interface ILrbAPIService
    {
        /// <summary>
        /// Post a Lrb Justlead request
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="apiKey"></param>
        /// <returns></returns>
        Task<Response<bool>> PostJustLeadAsync(LrbIntegrationPostDto dto, string apiKey);

        /// <summary>
        /// Post a Lrb CommonFloor request
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="apiKey"></param>
        /// <returns></returns>
        Task<Response<bool>> PostCommonFloorAsync(LrbIntegrationPostDto dto, string apiKey);

        /// <summary>
        /// Post a Lrb Bayut Whatsapp request
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="apiKey"></param>
        /// <returns></returns>
        Task<Response<bool>> PostBayutAsync(BayutWhatsappDto dto, string apiKey);

        /// <summary>
        /// Post a Lrb Bayut call Logs request
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="apiKey"></param>
        /// <returns></returns>
        Task<Response<bool>> PostBayutCallAsync(LrbBayutCallLogIntegrationPostDto dto, string apiKey);

        /// <summary>
        /// Post a Lrb Bayut email Logs request
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="apiKey"></param>
        /// <returns></returns>
        Task<Response<bool>> PostBayutemailAsync(LrbIntegrationPostDto dto, string apiKey, ILogger logger);

        /// <summary>
        /// Post a Lrb Property Finder request
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="apiKey"></param>
        /// <returns></returns>
        Task<Response<bool>> PostProperyFinderAsync(LrbIntegrationPostDto dto, string apiKey);

        /// <summary>
        /// Post a Lrb Property Finder call Logs request
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="apiKey"></param>
        /// <returns></returns>
        Task<Response<bool>> PostPropertyFinderCallAsync(LrbIntegrationIVRDto dto, string apiKey);

        /// <summary>
        /// Post a Lrb Dubizzle Whatsapp request
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="apiKey"></param>
        /// <returns></returns>
        Task<Response<bool>> PostDubbizleAsync(BayutWhatsappDto dto, string apiKey);

        /// <summary>
        /// Post a Lrb Dubizzle call Logs request
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="apiKey"></param>
        /// <returns></returns>
        Task<Response<bool>> PostDubizzleCallAsync(LrbBayutCallLogIntegrationPostDto dto, string apiKey);

        /// <summary>
        /// Post a Lrb Dubizzle email Logs request
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="apiKey"></param>
        /// <returns></returns>
        Task<Response<bool>> PostDubizzleEmailAsync(LrbIntegrationPostDto dto, string apiKey);

        /// <summary>
        /// Post a Lrb Property Finder V2 request
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="apiKey"></param>
        /// <returns></returns>
        Task<Response<bool>> V2PostProperyFinderAsync(LrbIntegrationPostDtoV2 dto, string apiKey);
    }
}
