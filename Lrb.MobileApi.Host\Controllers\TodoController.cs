﻿using Lrb.Application.Dashboard.Mobile;
using Lrb.Application.Todo.Mobile;
using Lrb.Application.Todo.Mobile.Requests;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class TodoController : VersionedApiController
    {
        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Todos)]
        [OpenApiOperation("Get all todo details.", "")]
        public Task<PagedResponse<BaseViewTodoDto, TodoCountDto>> SearchAsync([FromQuery] GetAllTodoRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("all/basicinfo")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Todos)]
        [OpenApiOperation("Get all todo basic details.", "")]
        public Task<PagedResponse<BaseViewTodoDto, string>> GetAsync([FromQuery] GetAllTodoBasicInfoRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Todos)]
        [OpenApiOperation("Get task details.", "")]
        public async Task<Response<Application.Todo.Mobile.ViewTodoDto>> GetAsync(Guid id)
        {
            return await Mediator.Send(new GetTodoByIdRequest(id));
        }
        [HttpGet("history/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Get todoHistory of a todo.", "")]
        public async Task<Response<Dictionary<DateTime, List<TodoHistoryDto>>>> GetHistoriesAsync(Guid id)
        {
            var res = await Mediator.Send(new GetTodoHsitoryByIdRequest(id));
            return res;
        }

        [HttpPost]
        [Authorize]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Todos)]
        [OpenApiOperation("Create a new task.", "")]
        public Task<Response<Guid>> CreateAsync(CreateTodoRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("multiple")]
        [Authorize]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Todos)]
        [OpenApiOperation("Create a multiple tasks for users.", "")]
        public Task<Response<bool>> CreateAsync(CreateMultipleTodoRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPut("{id:guid}")]
        [MustHavePermission(LrbAction.Update, LrbResource.Todos)]
        [TenantIdHeader]
        [OpenApiOperation("Update a task.", "")]
        public async Task<ActionResult<Guid>> UpdateAsync(UpdateTodoRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }

        [HttpDelete("{id:guid}")]
        [MustHavePermission(LrbAction.Delete, LrbResource.Todos)]
        [TenantIdHeader]
        [OpenApiOperation("Delete a task.", "")]
        public Task<Response<bool>> DeleteAsync(Guid id)
        {
            return Mediator.Send(new DeleteTodoRequest(id));
        }

        [HttpPost("assign")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Todos)]
        [OpenApiOperation("Assign todos to a user.", "")]
        public async Task<Response<bool>> AssignTodosAsync(AssignMultipleTodoRequest request)
        {
            var res = await Mediator.Send(request);
            return res;
        }

        [HttpGet("SheduledDate")]
        [TenantIdHeader]
        [OpenApiOperation("Get todo details by scheduled date.", "")]
        public async Task<PagedResponse<BaseViewTodoDto, TodoCountDto>> AssignTodosAsync([FromQuery] GetTodobySheduledDateRequest request)
        {
            var res = await Mediator.Send(request);
            return res;
        }
        [HttpGet("scheduled-dates")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Todos)]
        [OpenApiOperation("Get all scheduled dates within a date range.", "")]  
        public async Task<Response<List<DateTime>>> GetScheduledDatesAsync([FromQuery] GetScheduledDatesRequest request)  
        {
            return await Mediator.Send(request);
        }
    }
}
