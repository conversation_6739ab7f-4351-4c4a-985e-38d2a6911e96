﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class AddingAppointmentDateOnFieldInLeadsAndAppointmentTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "AppointmentDoneOn",
                schema: "LeadratBlack",
                table: "Leads",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<IDictionary<int, DateTime?>>(
                name: "AppointmentDoneOn",
                schema: "LeadratBlack",
                table: "LeadHistories",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "AppointmentDoneOn",
                schema: "LeadratBlack",
                table: "LeadAppointments",
                type: "timestamp with time zone",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AppointmentDoneOn",
                schema: "LeadratBlack",
                table: "Leads");

            migrationBuilder.DropColumn(
                name: "AppointmentDoneOn",
                schema: "LeadratBlack",
                table: "LeadHistories");

            migrationBuilder.DropColumn(
                name: "AppointmentDoneOn",
                schema: "LeadratBlack",
                table: "LeadAppointments");
        }
    }
}
