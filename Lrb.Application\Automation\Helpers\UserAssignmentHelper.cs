﻿using DocumentFormat.OpenXml.Office2010.Excel;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.UserDetails.Web;
using Lrb.Domain.Entities;
using Lrb.Shared.Extensions;

namespace Lrb.Application.Automation.Helpers
{
    public static class UserAssignmentHelper
    {
        public static async Task<(Guid AssignTo, bool IsDupicateUnassigned)> GetUserIdAsync(this UserAssignment userAssignment, IRepositoryWithEvents<UserAssignment> userAssignmentRepo, IRepositoryWithEvents<Domain.Entities.UserDetails> userRepo, IUserService userService, List<Domain.Entities.Lead> duplicateLeads)
        {
            var assignToUserId = Guid.Empty;
            var isDupicateUnassigned = false;
            try
            {
                if (userAssignment == null || (userAssignment?.UserIds ?? new()).Count <= 0)
                {
                    return (Guid.Empty, false);
                }
                var userDetails = (await userService.GetUserBasicDetailsAsync(userAssignment?.UserIds ?? new(), CancellationToken.None));

                //  var users = await userService.GetListOfUsersByIdsAsync(userAssignment?.UserIds?.Select(i => i.ToString())?.ToList() ?? new(), default);
                //inactive and disabled users to remove from assignment
                // var inactiveUserIds = userDetails.Where(u => !u.IsActive).Select(i => i.Id).ToList();
                // var userDetails = await userRepo.ListAsync(new GetUsersSpec(userAssignment?.UserIds ?? new()));
                //  var disabledUserIds = userDetails?.Where(j => !j?.IsAutomationEnabled ?? false)?.Select(i => i.UserId)?.ToList() ?? new();
                //user ids after removing inactive and disabled users
                var userIds = userAssignment?.UserIds ?? new List<Guid>();
                if (userIds.Count <=0)
                {
                    return (Guid.Empty, false);
                }
                var assignedUserIds = (userDetails != null && userDetails.Count > 0)
                    ? userDetails
                        .Select(i => i.UserId)
                        .OrderBy(id => userIds.IndexOf(id))
                        .ToList()
                    : new List<Guid>();
                if (userAssignment == null || assignedUserIds.Count <= 0)
                {
                    return (Guid.Empty, false);
                }
                else if (assignedUserIds.Count > 0)
                {
                    if (duplicateLeads.Any())
                    {
                        var allMatched = assignedUserIds.All(i => duplicateLeads.Select(j => j.AssignTo).Any(j => j == i));
                        if (!allMatched)
                        {
                            foreach (var id in assignedUserIds)
                            {
                                assignToUserId = ChooseUserId(userAssignment, assignedUserIds);
                                if (!duplicateLeads?.Any(i => i.AssignTo == assignToUserId) ?? false)
                                {
                                    break;
                                }
                            }
                            if (duplicateLeads?.Any(i => i.AssignTo == assignToUserId) ?? false)
                            {
                                assignToUserId = Guid.Empty;
                                isDupicateUnassigned = true;
                            }
                        }
                        else
                        {
                            isDupicateUnassigned = true;
                        }
                    }
                    else
                    {
                        assignToUserId = ChooseUserId(userAssignment, assignedUserIds);
                    }
                    await userAssignmentRepo.UpdateAsync(userAssignment);
                    return (assignToUserId, isDupicateUnassigned);
                }
                else
                {
                    return (Guid.Empty, false);
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("PK_FacebookLeadInfo"))
                {
                    return (assignToUserId, isDupicateUnassigned);
                }
                return (Guid.Empty, false);
            }
        }
       
        public static async Task<(Guid AssignTo, bool IsDupicateUnassigned)> GetUserIdAsync(this UserAssignment userAssignment, IRepositoryWithEvents<UserAssignment> userAssignmentRepo, List<Domain.Entities.UserDetails> newUserDetails, List<Lrb.Application.Identity.Users.UserDetailsDto> allUser, List<Domain.Entities.Lead> duplicateLeads)
        {
            var assignToUserId = Guid.Empty;
            var isDupicateUnassigned = false;
            try
            {
                if (userAssignment == null || (userAssignment?.UserIds ?? new()).Count <= 0)
                {
                    return (Guid.Empty, false);
                }
                var users = allUser?.Where(i => (userAssignment?.UserIds ?? new List<Guid>()).Contains(i.Id))?.ToList();
                //inactive and disabled users to remove from assignment
                var inactiveUserIds = users?.Where(u => !u.IsActive)?.Select(i => i.Id)?.ToList() ?? new List<Guid>();
                var userDetails = newUserDetails?.Where(i => (userAssignment?.UserIds ?? new()).Contains(i.Id));
                var disabledUserIds = userDetails?.Where(j => !j?.IsAutomationEnabled ?? false)?.Select(i => i.UserId)?.ToList() ?? new();
                //user ids after removing inactive and disabled users
                List<Guid> assignedUserIds = userAssignment?.UserIds?.Except(inactiveUserIds)?.Except(disabledUserIds)?.ToList() ?? new();
                if (userAssignment == null || assignedUserIds.Count <= 0)
                {
                    return (Guid.Empty, false);
                }
                else if (assignedUserIds.Count > 0)
                {
                    if (duplicateLeads.Any())
                    {
                        var allMatched = assignedUserIds.All(i => duplicateLeads.Select(j => j.AssignTo).Any(j => j == i));
                        if (!allMatched)
                        {
                            foreach (var id in assignedUserIds)
                            {
                                assignToUserId = ChooseUserId(userAssignment, assignedUserIds);
                                if (!duplicateLeads?.Any(i => i.AssignTo == assignToUserId) ?? false)
                                {
                                    break;
                                }
                            }
                            if (duplicateLeads?.Any(i => i.AssignTo == assignToUserId) ?? false)
                            {
                                assignToUserId = Guid.Empty;
                                isDupicateUnassigned = true;
                            }
                        }
                        else
                        {
                            isDupicateUnassigned = true;
                        }
                    }
                    else
                    {
                        assignToUserId = ChooseUserId(userAssignment, assignedUserIds);
                    }
                    await userAssignmentRepo.UpdateAsync(userAssignment);
                    return (assignToUserId, isDupicateUnassigned);
                }
                else
                {
                    return (Guid.Empty, false);
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("PK_FacebookLeadInfo"))
                {
                    return (assignToUserId, isDupicateUnassigned);
                }
                return (Guid.Empty, false);
            }
        }


        private static Guid ChooseUserId(UserAssignment userAssignment, List<Guid> assignedUserIds)
        {
            var assignToUserId = Guid.Empty;
            //if there is only 1 userId, then assign that userId all time
            if (assignedUserIds.Count == 1)
            {
                userAssignment.LastAssignedUser = assignedUserIds[0];
                userAssignment.PreviousAssignedUser = userAssignment.NextUserToBeAssigned = null;
                //take the userId to be assigned to the lead
                assignToUserId = assignedUserIds[0];
            }
            //if there are only 2 userIds then assign them alternatively
            else if (assignedUserIds.Count == 2)
            {
                // taking the userId which was not assigned to the last lead
                userAssignment.LastAssignedUser = assignedUserIds.FirstOrDefault(i => !(i == userAssignment.LastAssignedUser));
                userAssignment.PreviousAssignedUser = userAssignment.NextUserToBeAssigned = null;
                //take the userId to be assigned to the lead
                assignToUserId = userAssignment.LastAssignedUser ?? assignedUserIds[0];
            }
            //else if there are more than 2 userIds in the list
            else if (assignedUserIds.Count > 2)
            {
                //if the last assigned user is null there, then start with position 0
                if (userAssignment.LastAssignedUser == null)
                {
                    userAssignment.LastAssignedUser = assignedUserIds[0];
                    //shift the other two field's value
                    userAssignment.NextUserToBeAssigned = assignedUserIds[(0 + 1) % assignedUserIds.Count];
                    userAssignment.PreviousAssignedUser = null;
                    //take the userId to be assigned to the lead
                    assignToUserId = assignedUserIds[0];
                }
                else
                {
                    //Get the index of the last assigned user
                    var currentIndex = assignedUserIds.LastIndexOf(userAssignment.LastAssignedUser ?? Guid.Empty);
                    //if the index is greter or equal to 0, means the last assigned user is present in the list
                    if (currentIndex >= 0)
                    {
                        //assign the userId of last assigned user to PreviousAssignedUser field
                        userAssignment.PreviousAssignedUser = assignedUserIds[currentIndex];
                        //shift the other two field's value
                        userAssignment.LastAssignedUser = assignedUserIds[(currentIndex + 1) % assignedUserIds.Count];
                        userAssignment.NextUserToBeAssigned = assignedUserIds[(currentIndex + 2) % assignedUserIds.Count];
                        //take the userId to be assigned to the lead
                        assignToUserId = assignedUserIds[(currentIndex + 1) % assignedUserIds.Count];
                    }
                    //when there is no LastAssignedUser found
                    else
                    {
                        //check for the value of NextUserToBeAssigned field is present
                        var nextIndex = assignedUserIds.LastIndexOf(userAssignment.NextUserToBeAssigned ?? Guid.Empty);
                        if (nextIndex >= 0)
                        {
                            //if value of NextUserToBeAssigned is there, then assign that userId to the LastAssignedUser field
                            userAssignment.LastAssignedUser = assignedUserIds[nextIndex];
                            //shift the other two field's value
                            userAssignment.NextUserToBeAssigned = assignedUserIds[(nextIndex + 1) % assignedUserIds.Count];
                            userAssignment.PreviousAssignedUser = nextIndex > 0 ? assignedUserIds[nextIndex - 1] : null;
                            //take the userId to be assigned to the lead
                            assignToUserId = assignedUserIds[nextIndex];
                        }
                        //when there is no NextUserToBeAssigned found
                        else
                        {
                            //check for the value of PreviousAssignedUser field is present
                            var previousIndex = assignedUserIds.LastIndexOf(userAssignment.PreviousAssignedUser ?? Guid.Empty);
                            if (previousIndex >= 0)
                            {
                                //if value of PreviousAssignedUser is there,
                                //then assign that userId to the PreviousAssignedUser field
                                //(for safety, assign the value of the LastIndex found again)
                                userAssignment.PreviousAssignedUser = assignedUserIds[previousIndex];
                                //shift the other two field's value
                                userAssignment.LastAssignedUser = assignedUserIds[(previousIndex + 1) % assignedUserIds.Count];
                                userAssignment.NextUserToBeAssigned = assignedUserIds[(previousIndex + 2) % assignedUserIds.Count];
                                //take the userId to be assigned to the lead
                                assignToUserId = assignedUserIds[(previousIndex + 1) % assignedUserIds.Count];
                            }
                            //if values of all the 3 fields are not present there, then start from position 0
                            else
                            {
                                userAssignment.LastAssignedUser = assignedUserIds[0];
                                //shift the other two field's value
                                userAssignment.NextUserToBeAssigned = assignedUserIds[(0 + 1) % assignedUserIds.Count];
                                userAssignment.PreviousAssignedUser = null;
                                //take the userId to be assigned to the lead
                                assignToUserId = assignedUserIds[0];
                            }
                        }
                    }
                }
            }
            userAssignment.PreviousAssignedUserIds ??= new List<Guid>();
            userAssignment.PreviousAssignedUserIds.Add(assignToUserId);
            return assignToUserId;
        }
        public static async Task<List<Guid>> GetUserIdListAsync(this UserAssignment userAssignment, IRepositoryWithEvents<UserAssignment> userAssignmentRepo, IRepositoryWithEvents<Domain.Entities.UserDetails> userRepo, IUserService userService, Domain.Entities.Lead? lead)
        {
            var duplicateUserIds = new List<Guid>();
            var isDupicateUnassigned = false;
            try
            {
                if (userAssignment == null || (userAssignment?.DuplicateUserIds ?? new()).Count <= 0)
                {
                    return (duplicateUserIds);
                }
                var users = await userService.GetUserBasicDetailsAsync(userAssignment?.DuplicateUserIds ?? new(), default);
                //inactive and disabled users to remove from assignment
               // var inactiveUserIds = users.Where(u => !u.IsActive).Select(i => i.Id).ToList();
               // var userDetails = await userRepo.ListAsync(new GetUsersSpec(userAssignment?.DuplicateUserIds ?? new()));
               // var disabledUserIds = userDetails?.Where(j => !j?.IsAutomationEnabled ?? false)?.Select(i => i.UserId)?.ToList() ?? new();
                //user ids after removing inactive and disabled users
                List<Guid> assignedUserIds = users?.Select(i => i.UserId)?.ToList() ?? new();
                if (userAssignment == null || assignedUserIds.Count <= 0)
                {
                    return (duplicateUserIds);
                }
                else if (assignedUserIds.Count > 0 && lead.AssignTo != null)
                {
                        var allNotMatchedIds = assignedUserIds.Where(i => lead.AssignTo != i).Select(i => i).ToList();
                        return (allNotMatchedIds);                   
                }
                else
                {
                    return (assignedUserIds);
                }
            }
            catch (Exception ex)
            {
                return duplicateUserIds;
            }
        }
        public static async Task<(Guid AssignTo, bool IsDupicateUnassigned)> GetSecondaryUserIdAsync(this UserAssignment userAssignment, IRepositoryWithEvents<UserAssignment> userAssignmentRepo, IRepositoryWithEvents<Domain.Entities.UserDetails> userRepo, IUserService userService, IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo, Domain.Entities.Lead lead, string? mobileWithCode = null)
        {
            var assignToUserId = Guid.Empty;
            var isDupicateUnassigned = false;
            try
            {
                if (userAssignment == null || (userAssignment?.SecondaryUserIds ?? new()).Count <= 0)
                {
                    return (Guid.Empty, false);
                }
                var users = await userService.GetUserBasicDetailsAsync(userAssignment?.SecondaryUserIds ?? new(), default);
                //inactive and disabled users to remove from assignment
             //   var inactiveUserIds = users.Where(u => !u.IsActive).Select(i => i.Id).ToList();
              //  var userDetails = await userRepo.ListAsync(new GetUsersSpec(userAssignment?.SecondaryUserIds ?? new()));
              //  var disabledUserIds = userDetails?.Where(j => !j?.IsAutomationEnabled ?? false)?.Select(i => i.UserId)?.ToList() ?? new();
                //user ids after removing inactive and disabled users
                List<Guid> UserIds = users?.Select(i => i.UserId).ToList() ?? new();
                List<Guid> notAssignedUserIds = new();
                foreach(var id in UserIds)
                {
                    Domain.Entities.Lead? assignedLeads = null;
                    if (!string.IsNullOrWhiteSpace(mobileWithCode))
                    {
                         assignedLeads = await _leadRepo.FirstOrDefaultAsync(new GetAssignedLeadsSpec(mobileWithCode, lead.ContactNo, id));
                    }
                    else
                    {
                        assignedLeads = await _leadRepo.FirstOrDefaultAsync(new GetAssignedLeadsWithoutCountryCodeSpec( lead.ContactNo, id));
                    }
                    if (assignedLeads == null && lead?.AssignTo != id)
                    {
                        notAssignedUserIds.Add(id); 
                    }
                }
                if (userAssignment == null || notAssignedUserIds.Count <= 0)
                {
                    return (Guid.Empty, false);
                }
                else if (notAssignedUserIds.Count > 0)
                {
                    assignToUserId = await ChooseSecondaryUserId(userAssignment, notAssignedUserIds, lead,_leadRepo);
                    isDupicateUnassigned = true;
                    return (assignToUserId, isDupicateUnassigned);

                }
                else
                {
                    return (Guid.Empty, false);
                }
            }
            catch (Exception ex)
            {
                return (Guid.Empty, false);
            }
        }
        private static async Task<Guid> ChooseSecondaryUserId(UserAssignment userAssignment, List<Guid> assignedUserIds, Domain.Entities.Lead lead, IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo)
        {
            var assignToUserId = Guid.Empty;

            bool isSecondaryAssigned = false;
            userAssignment.PreviousAssignedUserIds = userAssignment.PreviousAssignedUserIds.DistinctBy(i => i).ToList();
            foreach (var assignedUser in assignedUserIds)
            {
                    if (userAssignment?.PreviousAssignedUserIds != null)
                    {
                        var LastUser = userAssignment.PreviousAssignedUserIds.LastOrDefault();
                        var index = assignedUserIds.IndexOf(LastUser);
                        var index1 = assignedUserIds.IndexOf(assignedUser);
                        if (index1 > index || !(userAssignment.PreviousAssignedUserIds.Contains(assignedUser)))
                        {
                            if ( !(userAssignment.PreviousAssignedUserIds.Contains(assignedUser)) && assignedUser != lead.AssignTo )
                            {

                                userAssignment.PreviousAssignedUserIds ??= new List<Guid>();
                                userAssignment.PreviousAssignedUserIds.Add(assignedUser);
                                isSecondaryAssigned = true;
                                return assignedUser;
                            }
                         
                        }
                    }
                    else
                    {
                        if (userAssignment?.PreviousAssignedUserIds == null && assignedUser != lead.AssignTo )
                        {

                            userAssignment.PreviousAssignedUserIds ??= new List<Guid>();
                            userAssignment.PreviousAssignedUserIds.Add(assignedUser);
                            isSecondaryAssigned = true;
                            return assignedUser;
                        }

                    }
                }
            
            if (!isSecondaryAssigned && (assignedUserIds?.Any() ?? false))
            {
                Random random = new Random();
                int index = random.Next(assignedUserIds.Count); // Get a random index
                var assignedUser = assignedUserIds[index];
                userAssignment.PreviousAssignedUserIds.Clear();
                userAssignment.PreviousAssignedUserIds ??= new List<Guid>();// Get the ID at the random index
                userAssignment?.PreviousAssignedUserIds.Add(assignedUser);
                return assignedUser;
            }

            return assignToUserId;
        }
        public static async Task AssignSecondaryUserIdsToDuplicateLeadsAsync(UserAssignment? userAssignment, IRepositoryWithEvents<UserAssignment> userAssignmentRepo, IRepositoryWithEvents<Domain.Entities.UserDetails> userRepo, IUserService userService, IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo ,List<Domain.Entities.Lead> duplicateLeads, string? mobileWithCode = null)
        {
            if (userAssignment != null && (userAssignment?.SecondaryUserIds.Any() ?? false))
            {
                var users = await userService.GetListOfUsersByIdsAsync(userAssignment?.SecondaryUserIds?.Select(i => i.ToString())?.ToList() ?? new(), default);
                //inactive and disabled users to remove from assignment
                var inactiveUserIds = users.Where(u => !u.IsActive).Select(i => i.Id).ToList();
                var userDetails = await userRepo.ListAsync(new GetUsersSpec(userAssignment?.SecondaryUserIds ?? new()));
                var disabledUserIds = userDetails?.Where(j => !j?.IsAutomationEnabled ?? false)?.Select(i => i.UserId)?.ToList() ?? new();
                //user ids after removing inactive and disabled users
                List<Guid> UserIds = userAssignment?.SecondaryUserIds?.Except(inactiveUserIds)?.Except(disabledUserIds)?.ToList() ?? new();
                List<Guid> notAssignedUserIds = new();
                foreach (var id in UserIds)
                {
                    Domain.Entities.Lead? assignedLeads = null;
                    if (!string.IsNullOrWhiteSpace(mobileWithCode))
                    {
                         assignedLeads = await _leadRepo.GetBySpecAsync(new GetAssignedLeadsSpec(mobileWithCode, duplicateLeads.FirstOrDefault().ContactNo, id));
                    }
                    else
                    {
                        assignedLeads = await _leadRepo.GetBySpecAsync(new GetAssignedLeadsWithoutCountryCodeSpec(duplicateLeads.FirstOrDefault().ContactNo, id));

                    }
                    if (assignedLeads == null)
                    {
                        notAssignedUserIds.Add(id);
                    }
                }
                foreach (var lead in duplicateLeads)
                {
                    if (notAssignedUserIds?.Any() ?? false)
                    {
                        var secondaryId = await ChooseSecondaryUserId(userAssignment, notAssignedUserIds, lead, _leadRepo);
                        lead.SecondaryUserId = secondaryId;
                        if (secondaryId != Guid.Empty)
                        {
                            notAssignedUserIds.Remove(secondaryId);
                        }
                    }
                }
            }
        }
        public static async Task<Guid?> ChooseUserIdForDuplicateLead(UserAssignment userAssignment, Domain.Entities.Lead lead, List<Guid> userIds, IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo,CancellationToken cancellationToken,string? mobileWithCode = null)
        {
            List<Guid> notAssignedUserIds = new();
            foreach (var id in userIds)
            {
                Domain.Entities.Lead? assignedLeads = null;
                if (!string.IsNullOrWhiteSpace(mobileWithCode))
                {
                    assignedLeads = await _leadRepo.GetBySpecAsync(new GetAssignedLeadsSpec(mobileWithCode, lead?.ContactNo, id));
                }
                else
                {
                    assignedLeads = await _leadRepo.GetBySpecAsync(new GetAssignedLeadsWithoutCountryCodeSpec(lead?.ContactNo, id));

                }
                if (assignedLeads == null)
                {
                    notAssignedUserIds.Add(id);
                }
            }
            bool isSequentiallyAssigned = true;
            foreach (var userId in notAssignedUserIds)
            {
                if (userAssignment?.DuplicateAssignedUserIds != null)
                {
                    var lastUserId = userAssignment.DuplicateAssignedUserIds.LastOrDefault();
                    var firstIndex = notAssignedUserIds.IndexOf(lastUserId);
                    var secondIndex = notAssignedUserIds.IndexOf(userId);
                    if (secondIndex > firstIndex || !(userAssignment.DuplicateAssignedUserIds.Contains(userId)))
                    {
                        if (!(userAssignment.DuplicateAssignedUserIds.Contains(userId)) && userId != lead.AssignTo)
                        {
                            userAssignment.DuplicateAssignedUserIds ??= new List<Guid>();
                            userAssignment.DuplicateAssignedUserIds.Add(userId);
                            isSequentiallyAssigned = false;
                            return userId;
                        }
                    }
                }
                else
                {
                    if (userId != lead.AssignTo)
                    {
                        userAssignment.DuplicateAssignedUserIds ??= new List<Guid>();
                        userAssignment.DuplicateAssignedUserIds.Add(userId);
                        isSequentiallyAssigned = false;
                        return userId;
                    }
                }
            }
            if (isSequentiallyAssigned && (notAssignedUserIds?.Any() ?? false))
            {
                var assignedUser = notAssignedUserIds[0];
                userAssignment.DuplicateAssignedUserIds.Clear();
                userAssignment.DuplicateAssignedUserIds ??= new List<Guid>();// Get the ID at the random index
                userAssignment?.DuplicateAssignedUserIds.Add(assignedUser);
                return assignedUser;
            }
            return null;
        }
    }
}
