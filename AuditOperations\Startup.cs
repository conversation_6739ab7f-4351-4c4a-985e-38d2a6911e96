﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.IO;

namespace AuditOperation
{
    public class Startup
    {
        private readonly IConfigurationRoot Configuration;

        public Startup(string env)
        {
            Configuration = new ConfigurationBuilder() // ConfigurationBuilder() method requires Microsoft.Extensions.Configuration NuGet package
                .SetBasePath(Directory.GetCurrentDirectory())  // SetBasePath() method requires Microsoft.Extensions.Configuration.FileExtensions NuGet package
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true) // AddJsonFile() method requires Microsoft.Extensions.Configuration.Json NuGet package
                .AddJsonFile($"appsettings.{env}.json", optional: true, reloadOnChange: true)
                .AddEnvironmentVariables() // AddEnvironmentVariables() method requires Microsoft.Extensions.Configuration.EnvironmentVariables NuGet package
                .Build();
        }

        public IServiceProvider ConfigureServices()
        {
            var services = new ServiceCollection(); // ServiceCollection require Microsoft.Extensions.DependencyInjection NuGet package

            ConfigureLoggingAndConfigurations(services);

            ConfigureApplicationServices(services);

            IServiceProvider provider = services.BuildServiceProvider();

            return provider;
        }


        private void ConfigureLoggingAndConfigurations(ServiceCollection services)
        {

            // Add configuration service
            services.AddSingleton<IConfiguration>(Configuration);

            // Add logging service
            services.AddLogging(loggingBuilder =>  // AddLogging() requires Microsoft.Extensions.Logging NuGet package
            {
                loggingBuilder.ClearProviders();
                loggingBuilder.AddConsole();
                // AddConsole() requires Microsoft.Extensions.Logging.Console NuGet package
            });

            // Cosmos db keys
            services.AddOptions<CosmosSettings>()
                .BindConfiguration(nameof(CosmosSettings));
        }

        private void ConfigureApplicationServices(ServiceCollection services)
        {
            services.AddTransient<ICosmosService, CosmosService>();
        }
    }
}
