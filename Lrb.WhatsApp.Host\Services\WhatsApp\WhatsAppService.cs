﻿using Lrb.WhatsApp.Host.DTOs;
using Lrb.WhatsApp.Host.Entities;
using Lrb.WhatsApp.Host.Enums;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using RestSharp;
using System.Reflection;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace Lrb.WhatsApp.Services
{
    public class WhatsAppService : IWhatsAppService
    {
        private readonly ILRService _lrService;
        private readonly ICosmosService _cosmosService;
        private readonly LeadratApis _leadratApis;
        private readonly Serilog.ILogger _logger;
        private readonly DatabaseSettings _connectionStrings;

        public WhatsAppService(IOptions<DatabaseSettings> settings, ILRService lrService, ICosmosService cosmosService, IOptions<LeadratApis> options, Serilog.ILogger logger)
        {
            _leadratApis = options.Value;
            _lrService = lrService;
            _cosmosService = cosmosService;
            _logger = logger;
            _connectionStrings = settings.Value;
        }

        public async Task<bool> UpdateOrCreateIntegrationAccountAsync(ViewWAIntegrationAccountDto dto)
        {
            var connection = await _cosmosService.InitCosmosDbAsync();
            if(dto.Id != Guid.Empty)
            {
                var existingItem = await _cosmosService.ReadItemAsync<IntegrationAccountInfo>(dto.Id.ToString(), connection);
                if (existingItem != null)
                {
                    existingItem.AccountName = dto.AccountName;
                    existingItem.ApiKey = dto.ApiKey;
                    existingItem.WAPayloadMapping = dto.WAPayloadMapping;
                    existingItem.TenantId = dto.TenantId;
                    await _cosmosService.ReplaceItemAsync<IntegrationAccountInfo>(existingItem, connection);
                    return true;
                }
            }
            var newItem = new IntegrationAccountInfo();
            newItem.AccountName = dto.AccountName;
            newItem.ApiKey = dto.ApiKey;
            newItem.Id = dto.Id.ToString();
            newItem.WAPayloadMapping = dto.WAPayloadMapping;
            newItem.TenantId = dto.TenantId;
            await _cosmosService.AddItemsToContainerAsync<IntegrationAccountInfo>(newItem, connection);
            return true;
        }
        public async Task<IntegrationAccountInfo> GetOrCreateIntegrationAccountAsync(Guid accId, string tenantId)
        {
            var connection = await _cosmosService.InitCosmosDbAsync();
            var existingItem = await _cosmosService.ReadItemAsync<IntegrationAccountInfo>(accId.ToString(), connection);
            if (existingItem != null)
            {
                return existingItem;
            }
            else
            {
                var apiItem = await _lrService.GetIntegrationAccountAsync(accId, tenantId);
                var newItem = new IntegrationAccountInfo();
                newItem.AccountName = apiItem.AccountName;
                newItem.ApiKey = apiItem.ApiKey;
                newItem.Id = apiItem.Id.ToString();
                newItem.WAPayloadMapping = apiItem.WAPayloadMapping;
                newItem.TenantId = tenantId;
                return await _cosmosService.AddItemsToContainerAsync<IntegrationAccountInfo>(newItem, connection);
            }
        }
        public async Task<DTOs.Response<List<WAMessage>>> HandleWebhookPayloadAsync(HttpRequest httpRequest, string tenantId, string apiKey, CancellationToken cancellationToken = default)
        {
            var connection = await _cosmosService.InitCosmosDbAsync();
            Guid accountId = apiKey.GetAccountId();
            var accInc = await _cosmosService.ReadItemAsync<IntegrationAccountInfo>(accountId.ToString(), connection);
            var connectionString = (await _cosmosService.ReadItemAsync<TenantInfo>(tenantId, connection))?.ConnectionString;
            connectionString = string.IsNullOrWhiteSpace(connectionString) ? _connectionStrings.DefaultConnection : connectionString;
            var resultMessages = new List<WAMessage>();
            var waWebhookMapping = accInc?.WAPayloadMapping?.WebhookMapping;
            var statusMapping = accInc?.WAPayloadMapping?.StatusMapping;
            //Get object from the http request
            Stream stream = httpRequest.Body;
            HttpRequestStreamReader reader = new(stream, System.Text.Encoding.UTF8);
            var bodyInString = await reader.ReadToEndAsync();
            var parsedObj = JObject.Parse(bodyInString);

            if (waWebhookMapping == null || statusMapping == null)
            {
                return new(resultMessages)
                {
                    Succeeded = true,
                    Message = "No Payload Info was found for the given account"
                };
            }
            var mappings = await GetMappedValues(parsedObj, waWebhookMapping, statusMapping, tenantId, accountId);

            if (mappings.Item1.TryGetValue("#Field#", out var field) && field.ToLowerInvariant().Contains("templates"))
            {
                await HandleWebhookTemplatesAsync(parsedObj, accInc?.WAPayloadMapping?.TemplateMapping, tenantId, connectionString, cancellationToken);
                return new();
            }
            Dictionary<string, string> mappedValues = mappings.Item1;
            Dictionary<WAEvent, string> mappedEvents = mappings.Item2;
            WAWebhookDto waWebhookDto = GetWebhookDto(mappedValues);
            waWebhookDto.WAEvent = mappedEvents.FirstOrDefault().Key;
            var customerNo = waWebhookDto.CustomerNo ?? string.Empty;
            if (string.IsNullOrEmpty(customerNo))
            {
                return new(resultMessages)
                {
                    Message = "Contact Number cant be null",
                    Succeeded = true,
                };
            }
            if (string.IsNullOrWhiteSpace(waWebhookDto.MessageId) || string.IsNullOrWhiteSpace(customerNo))
            {
                return new(resultMessages)
                {
                    Message = "MessageId can't be null or empty",
                    Succeeded = true,
                };
            }
            string defaultCountryCode = accInc?.DefaultCountryCode ?? string.Empty;
            List<string> contactNos = new List<string>() { customerNo };
            contactNos = contactNos.Distinct().ToList();
            var waMessages = await _lrService.GetWAMessagesAsync(waWebhookDto.MessageId, tenantId, connectionString);
            var existingLeads = await _lrService.GetLeadsCountAsync(contactNos, tenantId, connectionString);
            if (waMessages?.Any() ?? false)
            {
                waMessages.ForEach(async i =>
                {
                    if (waWebhookDto.WAEvent >= i.WAEvent)
                    {
                        i.WAEvent = waWebhookDto.WAEvent;
                    }
                    i.Error = waWebhookDto.ErrorCode + " " + waWebhookDto.Error;
                    await _lrService.UpdateWAMessageAsync(i, connectionString);
                });
                resultMessages.AddRange(waMessages);
                if (existingLeads?.Any() ?? false)
                {
                    foreach (var lead in existingLeads)
                    {
                        if ((!waMessages?.Any(j => j.CustomerId == lead)) ?? false)
                        {
                            var message = waMessages?.FirstOrDefault();
                            if (message != null)
                            {
                                message.CustomerId = lead;
                                message.Id = Guid.NewGuid();
                                await _lrService.CreateWAMessageAsync(message, connectionString);
                                resultMessages.Add(message);
                            }
                        }
                    };
                }
            }
            else
            {
                if (existingLeads?.Any() ?? false)
                {
                    existingLeads.ForEach(async i =>
                    {
                        if (!IsValidJson(waWebhookDto.Message ?? string.Empty))
                        {
                            var newMessage = new WAMessage()
                            {
                                WAEvent = waWebhookDto.WAEvent,
                                CustomerNo = waWebhookDto.CustomerNo,
                                CustomerId = i,
                                MessageId = waWebhookDto.MessageId,
                                Message = waWebhookDto.Message ?? string.Empty,
                                TenantId = tenantId ?? string.Empty,
                                MediaUrl = waWebhookDto.MediaUrl,
                                MediaType = waWebhookDto.MediaType,
                                MediaName = waWebhookDto.MediaName,
                                Error = waWebhookDto.ErrorCode + " " + waWebhookDto.Error,
                            };
                            resultMessages.Add(newMessage);
                            await _lrService.CreateWAMessageAsync(newMessage, connectionString);
                        }
                    });
                }
                else
                {
                    WhatsAppListingSitesIntegrationDto lead = new()
                    {
                        ApiKey = apiKey,
                        Mobile = waWebhookDto.CustomerNo,
                        Name = !string.IsNullOrEmpty(waWebhookDto.Name) ? waWebhookDto.Name : "WA" + TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")).ToString("dd-MM-yyyy HH:mm:ss")
                    };

                    RestClient client = new RestClient(_leadratApis.WhatsAppApi);
                    RestRequest? restRequest = new RestRequest();
                    RestResponse res = new();
                    if (tenantId == "black")
                    {
                        if (!IsValidUserIntent(waWebhookDto.Message))
                        {
                            restRequest.Method = Method.Patch;
                            restRequest.AddHeader("accept", "application/json");
                            restRequest.AddHeader("API-Key", apiKey ?? string.Empty);
                            restRequest.AddHeader("Content-Type", "application/json");
                            restRequest.AddBody(lead);
                            res = await client.ExecuteAsync(restRequest);
                        }
                    }
                    else
                    {
                        restRequest.Method = Method.Post;
                        restRequest.AddHeader("accept", "application/json");
                        restRequest.AddHeader("API-Key", apiKey ?? string.Empty);
                        restRequest.AddHeader("Content-Type", "application/json");
                        restRequest.AddBody(lead);
                        res = await client.ExecuteAsync(restRequest);
                    }
                    if (res.IsSuccessStatusCode)
                    {
                        var newLeads = await _lrService.GetLeadsCountAsync(contactNos, tenantId, connectionString);
                        if (newLeads?.Any() ?? false)
                        {
                            newLeads.ForEach(async i =>
                            {
                                if (!IsValidJson(waWebhookDto.Message ?? string.Empty))
                                {
                                    var newMessage = new WAMessage()
                                    {
                                        WAEvent = waWebhookDto.WAEvent,
                                        CustomerNo = waWebhookDto.CustomerNo,
                                        CustomerId = i,
                                        MessageId = waWebhookDto.MessageId,
                                        Message = waWebhookDto.Message ?? string.Empty,
                                        TenantId = tenantId ?? string.Empty,
                                        MediaUrl = waWebhookDto.MediaUrl,
                                        MediaType = waWebhookDto.MediaType,
                                        MediaName = waWebhookDto.MediaName,
                                        Error = waWebhookDto.ErrorCode + " " + waWebhookDto.Error,
                                    };
                                    resultMessages.Add(newMessage);
                                    await _lrService.CreateWAMessageAsync(newMessage, connectionString);
                                }
                            });
                        }
                    }
                    else
                    {
                        return new(resultMessages)
                        {
                            Succeeded = true,
                            Message = "Lead Not Created"
                        };
                    }
                }
            }
            if (resultMessages.Any() && (existingLeads?.Any() ?? false) && waWebhookDto.WAEvent == WAEvent.Receive &&
                (!string.IsNullOrWhiteSpace(waWebhookDto.Message) || !string.IsNullOrWhiteSpace(waWebhookDto.MediaType)))
            {
                if (tenantId == "black")
                {
                    return new(resultMessages)
                    {
                        Succeeded = true,
                        Message = "Request Processed Successfully"
                    };
                }
                else
                {
                    await SendWaNotificationAsync(existingLeads, waWebhookDto.MediaType ?? string.Empty, waWebhookDto.Message ?? string.Empty, tenantId);
                }
            }
            return new(resultMessages)
            {
                Succeeded = true,
                Message = "Request Processed Successfully"
            };
        }
        public static bool IsValidJson(string strInput)
        {
            if (string.IsNullOrWhiteSpace(strInput)) return false;

            strInput = strInput.Trim();
            if ((strInput.StartsWith("{") && strInput.EndsWith("}")) || // For object
                (strInput.StartsWith("[") && strInput.EndsWith("]")))   // For array
            {
                try
                {
                    var obj = JsonSerializer.Deserialize<object>(strInput);
                    return true;
                }
                catch (JsonException)
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }
        public async Task<bool> SendWaNotificationAsync(List<Guid> leadIds, string mediaType, string message, string tenant)
        {
            try
            {

                RestClient client = new RestClient(_leadratApis.WaPushNotification);
                RestRequest? restRequest = new RestRequest();
                restRequest.Method = Method.Post;
                restRequest.AddHeader("accept", "application/json");
                restRequest.AddHeader("tenant", tenant);
                restRequest.AddHeader("Content-Type", "application/json");
                restRequest.AddBody(new WAPushNotificationDto() { LeadIds = leadIds, Message = message, MediaType = mediaType });
                Task.Run(async () => client.ExecuteAsync(restRequest));
                return true;
            }
            catch (Exception ex)
            {
                _logger.Information("Exception info while calling SendWaNotificationAsync " + ex.Message ?? ex.InnerException?.Message ?? string.Empty);
                return true;
            }
        }
        public async Task<(string, List<WAMessage>?)> ProcessPushOperationAsync(WAWrapperDto obj)
        {
            if (obj.WAApiInfo != null)
            {
                var result = await CallWhatsAppAPIAsync(obj.WAApiInfo);
                if (result.Item2)
                {
                    var connection = await _cosmosService.InitCosmosDbAsync();
                    var connectionString = (await _cosmosService.ReadItemAsync<TenantInfo>(obj.TenantId, connection))?.ConnectionString;
                    connectionString = string.IsNullOrWhiteSpace(connectionString) ? _connectionStrings.DefaultConnection : connectionString;
                    var messageId = GetMessageId(GetUnMappedParameters(GetKeysAndValues(JObject.Parse(result.Item1)), obj.WAPayloadMapping?.ResponseMapping ?? new()));
                    var existingLeads = await _lrService.GetLeadsCountAsync(new List<string>() { obj.CustomerNo }, obj.TenantId, connectionString);
                    var messages = GetWAMessage(obj, messageId ?? string.Empty, result.Item1, existingLeads);
                    var data = await _lrService.CreateWAMessageInDapperAsync(messages, connectionString);
                    return (data.Item2 ? "Success" : "Failed", data.Item1);
                }
                return (result.Item1, null);
            }
            return ("WAApiInfo cant be null", null);
        }
        public async Task<bool> StoreConnectionDetailsAsync(string connectionId, bool isConnected, string? tenantId, string? errorInfo, bool shouldCreate)
        {
            try
            {
                var connection = await _cosmosService.InitCosmosDbAsync();
                var existingItem = await _cosmosService.ReadItemAsync<ConnectionDetails>(connectionId, connection);
                if (existingItem != null)
                {
                    existingItem.IsDeleted = isConnected;
                    existingItem.TenantId = tenantId ?? existingItem.TenantId;
                    existingItem.LastModifiedOn = DateTime.UtcNow;
                    existingItem.ErrorInfo = errorInfo ?? existingItem.ErrorInfo;
                    await _cosmosService.ReplaceItemAsync<ConnectionDetails>(existingItem, connection);
                }
                else if (shouldCreate)
                {
                    var newItem = new ConnectionDetails();
                    newItem.Id = connectionId;
                    newItem.IsDeleted = isConnected;
                    newItem.TenantId = tenantId ?? string.Empty;
                    newItem.LastModifiedOn = DateTime.UtcNow;
                    newItem.CreatedOn = DateTime.UtcNow;
                    newItem.ErrorInfo = errorInfo;
                    await _cosmosService.AddItemsToContainerAsync<ConnectionDetails>(newItem, connection);
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        #region Private functions
        public WAWebhookDto GetWebhookDto(Dictionary<string, string> mappedValues)
        {
            WAWebhookDto waWebhookDto = new()
            {
                CustomerNo = (mappedValues.GetValueOrDefault("#CountryCode#") + "" + mappedValues.GetValueOrDefault("#CustomerNo#"))?.Trim(),
                DeliveryStatus = mappedValues.GetValueOrDefault("#DeliveryStatus#"),
                MediaUrl = mappedValues.GetValueOrDefault("#MediaUrl#"),
                Message = mappedValues.GetValueOrDefault("#Message#"),
                MessageId = mappedValues.GetValueOrDefault("#MessageId#"),
                Error = mappedValues.GetValueOrDefault("#Error#"),
                ErrorCode = mappedValues.GetValueOrDefault("#ErrorCode#"),
                MediaName = mappedValues.GetValueOrDefault("#MediaName#"),
                Name = mappedValues.GetValueOrDefault("#Name#"),
                MediaType = mappedValues.GetValueOrDefault("#MediaType#")?.ToLower()
            };
            if (!string.IsNullOrEmpty(waWebhookDto.MediaType))
            {
                waWebhookDto.MediaType = char.ToUpper(waWebhookDto.MediaType[0]) + waWebhookDto.MediaType.Substring(1);
            }
            if (!string.IsNullOrEmpty(waWebhookDto.CustomerNo) && !waWebhookDto.CustomerNo.StartsWith("+"))
            {
                waWebhookDto.CustomerNo = "+" + waWebhookDto.CustomerNo;
            }
            return waWebhookDto;
        }
        public async Task<(Dictionary<string, string>, Dictionary<WAEvent, string>)> GetMappedValues(JObject parsedObj, IDictionary<string, string> waWebhookMapping, IDictionary<WAEvent, string> waStatusMapping, string tenant, Guid accountId)
        {
            Console.WriteLine("Payload : " + parsedObj + " " + tenant + " " + accountId + " " + DateTime.UtcNow);
            _logger.Information("Payload : " + parsedObj + " " + tenant + " " + accountId + " " + DateTime.UtcNow);
            var keyValues = GetKeysAndValues(parsedObj);
            Dictionary<string, string> mappedBody = GetUnMappedParameters(keyValues, waWebhookMapping);
            //Dictionary<WAEvent, string> statusBody = GetUnMappedStatusParameters(keyValues, waStatusMapping);
            Dictionary<WAEvent, string> statusBody = [];
            var direction = mappedBody?.FirstOrDefault(i => i.Key == "#Direction#").Value;
            var result = waStatusMapping?.FirstOrDefault(i => i.Value == direction);
            if (result != null && result.Value.Key != WAEvent.None)
            {
                statusBody.Add(result.Value.Key, result.Value.Value);
            }
            if (statusBody.Count <= 0)
            {
                var status = mappedBody?.FirstOrDefault(i => i.Key == "#DeliveryStatus#").Value;
                if (!string.IsNullOrEmpty(status))
                {
                    var statusResult = waStatusMapping?.FirstOrDefault(i => i.Value == status);
                    if (statusResult != null)
                    {
                        statusBody.Add(statusResult.Value.Key, statusResult.Value.Value);
                    }
                }
            }
            return (mappedBody ?? new(), statusBody);
        }
        public List<WAMessage> GetWAMessage(WAWrapperDto obj, string messageId, string result, List<Guid> leadIds)
        {
            var messages = new List<WAMessage>();
            leadIds.ForEach(i =>
            {
                messages.Add(new()
                {
                    WAEvent = WAEvent.None,
                    CustomerNo = obj.CustomerNo,
                    CustomerId = i,
                    MessageId = messageId ?? string.Empty,
                    Message = obj.Message ?? string.Empty,
                    TenantId = obj.TenantId ?? string.Empty,
                    RawJson = result,
                    TemplateName = obj.TemplateName,
                    UserId = obj.UserId,
                    MediaUrl = obj.MediaUrl,
                    MediaType = obj.MediaType,
                    MediaName = obj.MediaName,
                    JsonButton = JsonSerializer.Serialize(obj.WAButtons)
                });
            });
            return messages;
        }
        public Dictionary<string, string> GetUnMappedParameters(IDictionary<string, string> requestPayload, IDictionary<string, string> waWebhookMapping)
        {
            Dictionary<string, string> mappedBody = new();
            string? val = string.Empty;
            foreach (var map in waWebhookMapping)
            {
                if (map.Value.Contains(","))
                {                    
                    var splitValues = new List<string>(map.Value.Split(','));
                    List<string> values = new();
                    foreach (var sv in splitValues)
                    {
                        var value = requestPayload.TryGetValue(sv, out val) ? val?.ToString() : string.Empty;
                        if (!string.IsNullOrWhiteSpace(value))
                        {
                            values.Add(value);
                        }
                    }
                    string concateValues = string.Join(",", values);
                    mappedBody.Add(map.Key, concateValues ?? string.Empty);
                }
                else
                {
                    var value = requestPayload.TryGetValue(map.Value, out val) ? val?.ToString() : string.Empty;
                    mappedBody.Add(map.Key, value ?? string.Empty);
                }
                
            }
            mappedBody = mappedBody.Where(i => !string.IsNullOrWhiteSpace(i.Value)).ToDictionary(i => i.Key, j => j.Value);
            return mappedBody;
        }
        public string? GetMessageId(Dictionary<string, string>? mappedValues)
        {
            string? messageId = string.Empty;
            if (mappedValues != null)
            {
                mappedValues.TryGetValue("#MessageId#", out messageId);
            }
            return messageId;
        }
        public static Dictionary<string, string> GetKeysAndValues(JObject obj, string prefix = "")
        {
            Dictionary<string, string> keyValues = new Dictionary<string, string>();
            foreach (var property in obj.Properties())
            {
                if (property.Value.Type == JTokenType.Object)
                {
                    var values = GetKeysAndValues((JObject)property.Value, prefix + property.Name + ".");
                    foreach (var value in values)
                    {
                        keyValues.Add(value.Key, value.Value);
                    }
                }
                else if (property.Value.Type == JTokenType.Array)
                {
                    int index = 0;
                    foreach (var item in property.Value)
                    {
                        if (item.Type == JTokenType.Object)
                        {
                            var values = GetKeysAndValues((JObject)item, prefix + property.Name + "[" + index + "].");
                            foreach (var value in values)
                            {
                                keyValues.Add(value.Key, value.Value);
                            }
                        }
                        else
                        {
                            keyValues.Add(prefix + property.Name + "[" + index + "]", item.ToString());
                        }
                        index++;
                    }
                }
                else
                {
                    keyValues.Add(prefix + property.Name, property.Value.ToString());
                }
            }
            return keyValues;
        }
        public static List<string> GetKeys(JObject obj, string prefix = "")
        {
            List<string> keys = new List<string>();
            foreach (var property in obj.Properties())
            {
                if (property.Value.Type == JTokenType.Object)
                {
                    keys.AddRange(GetKeys((JObject)property.Value, prefix + property.Name + "."));
                }
                else
                {
                    keys.Add(prefix + property.Name);
                }
            }
            return keys;
        }
        public async Task<(string, bool)> CallWhatsAppAPIAsync(WAApiInfoDto wAApiInfo)
        {
            (string, bool) result = new("", false);
            if (!string.IsNullOrEmpty(wAApiInfo.URL))
            {
                if (wAApiInfo.MethodType?.Equals("Get", StringComparison.InvariantCultureIgnoreCase) ?? false)
                {
                    var request = new RestRequest(wAApiInfo.URL, Method.Get);
                    if (wAApiInfo.Headers?.Any() ?? false)
                    {
                        foreach (var item in wAApiInfo.Headers)
                        {
                            request.AddHeader(item.Key, item.Value);
                        }
                    }
                    var client = new RestClient();
                    request.AddJsonBody(wAApiInfo.JsonPayload ?? string.Empty);
                    var response = await client.ExecuteAsync(request);
                    result = new(response.Content ?? string.Empty, response.IsSuccessStatusCode);
                }
                else if (wAApiInfo.MethodType?.Equals("Post", StringComparison.InvariantCultureIgnoreCase) ?? false)
                {
                    var request = new RestRequest(wAApiInfo.URL, Method.Post);
                    if (wAApiInfo.Headers?.Any() ?? false)
                    {
                        foreach (var item in wAApiInfo.Headers)
                        {
                            request.AddHeader(item.Key, item.Value);
                        }
                    }
                    var client = new RestClient();
                    request.AddJsonBody(wAApiInfo.JsonPayload ?? string.Empty);
                    var response = await client.ExecuteAsync(request);
                    result = new(response.Content ?? string.Empty, response.IsSuccessStatusCode);
                    _logger.Information("Api Responce : " + response.Content ?? string.Empty + " " + wAApiInfo.Serialize());
                }
            }
            return result;
        }

        public async Task<bool> HandleWebhookTemplatesAsync(JObject parsedObj, IDictionary<WAApiAction, IList<PropertyMapping>>? payloadMappings, string tenantId, string connectionString, CancellationToken cancellationToken = default)
        {
            try
            {
                var id = await _lrService.GetWATemplateApiInfoId(tenantId, connectionString);
                if (payloadMappings?.Any() == false)
                {
                    _logger.Information("HandleWebhookTemplatesAsync ->Not Found WAApiInfos or PayloadMapping: " + parsedObj.Serialize() ?? string.Empty + " " + "tenantId" + tenantId);
                    return false;
                }
                var templates = MapWATemplates(parsedObj, payloadMappings);
                SynchronizeWATemplates(templates);

                templates.ForEach(x =>
                {
                    x.WAApiInfoId = id;
                    x.TenantId = tenantId;
                    x.Id = Guid.NewGuid();
                    if (x.WAButtons?.Any() == true)
                    {
                        x.WAButtons.ForEach(i =>
                        {
                            i.Id = Guid.NewGuid();
                            i.WATemplateId = x.Id;
                            i.TenantId = tenantId;
                        });
                    }
                });
                return await _lrService.CreateOrUpdateWATemplateAsync(tenantId, templates, connectionString);
            }
            catch (Exception ex)
            {
                _logger.Information("Error:HandleWebhookTemplatesAsync ->Not Found WAApiInfos or PayloadMapping: " + parsedObj?.Serialize() ?? string.Empty + " " + "tenantId" + tenantId);
                return false;
            }
        }
        private List<WATemplate> MapWATemplates(JToken result, IDictionary<WAApiAction, IList<PropertyMapping>> payloadMapping)
        {
            var PropertyMapping = payloadMapping.First(x => x.Key == WAApiAction.GetTemplates).Value;

            var templateResponseMapping = PropertyMapping.Where(x => x.TargetObject == "Result").ToDictionary(d => d.SourceProperty, d => d.TargetProperty);

            var templateMapping = PropertyMapping.Where(x => x.TargetObject == "TemplateProperty").ToDictionary(d => d.SourceProperty, d => d.TargetProperty);

            var buttonPropertyMapping = PropertyMapping.Where(x => x.TargetObject == "ButtonProperty").ToDictionary(d => d.SourceProperty, d => d.TargetProperty);

            var mediaTypeMapping = PropertyMapping.Where(x => x.TargetObject == "MediaTypeMapping").ToDictionary(d => d.SourceProperty, d => d.TargetProperty);

            var templates = new List<WATemplate>();

            JToken dataArray = new JArray();
            bool isSuccess = false;

            var templateProperties = GetPropertyKeyValuePairs<WATemplate>(new WATemplate());
            var templateButtonProperties = GetPropertyKeyValuePairs<WAButton>(new WAButton());

            if (result.Type == JTokenType.Object)
            {
                string succeededKey = string.Empty;
                string dataKey = string.Empty;

                templateResponseMapping?.TryGetValue("Data", out dataKey);
                templateResponseMapping?.TryGetValue("Succeeded", out succeededKey);


                var success = result[succeededKey ?? string.Empty];

                isSuccess = success switch
                {
                    JValue jValue when jValue.Type == JTokenType.Boolean => (bool)jValue,
                    JValue jValue when jValue.Type == JTokenType.String =>
                        ((string)jValue).ToLowerInvariant().Contains("success") ||
                        ((string)jValue).ToLowerInvariant().Contains("true"),
                    _ => false
                };
                dataArray = result[dataKey ?? string.Empty] as JArray ?? new JArray();
            }
            else if (result.Type == JTokenType.Array)
            {
                isSuccess = true;
                dataArray = result;
            }

            if (isSuccess)
            {
                foreach (var data in dataArray)
                {
                    var dataJObject = JObject.FromObject(data);
                    var mappedObject = new JObject();

                    var buttonKeyName = templateResponseMapping["Buttons"];
                    var buttonArray = dataJObject[buttonKeyName] as JArray ?? new JArray();
                    int orderRank = 1;
                    var waButtons = new List<WAButton>();
                    foreach (var button in buttonArray)
                    {
                        string buttonKeyNmae = buttonPropertyMapping["Name"];
                        string buttonKeyType = buttonPropertyMapping["Type"];
                        string buttonKeyValue = buttonPropertyMapping["Value"];
                        //string buttonCountryCode = buttonPropertyMapping["CountryCode"];
                        buttonPropertyMapping.TryGetValue("CountryCode", out string? buttonCountryCode);

                        var type = Enum.TryParse<WAButtonType>(button[buttonKeyType]?.ToString(), out var parsedType)
                        ? parsedType : WAButtonType.None;

                        waButtons.Add(new WAButton
                        {
                            OrderRank = orderRank,
                            Name = button[buttonKeyNmae]?.ToString(),
                            Value = (string.IsNullOrEmpty(buttonCountryCode)
                                ? button[buttonKeyValue]?.ToString()
                                : (button[buttonCountryCode] + button[buttonKeyValue]?.ToString()))
                                ?? string.Empty,
                            Type = type,
                        });
                        orderRank++;

                    }
                    foreach (var property in dataJObject.Properties())
                    {
                        var originalKey = property.Name;
                        var value = property.Value;

                        var mappedKey = templateMapping.FirstOrDefault(x => x.Value == originalKey).Key;
                        if (!string.IsNullOrEmpty(mappedKey))
                        {
                            var propertyName = templateProperties.FirstOrDefault(x => x.Value == mappedKey).Key;

                            if (!string.IsNullOrEmpty(propertyName))
                            {
                                if (mediaTypeMapping != null)
                                {
                                    if (mappedKey == "MediaType")
                                    {
                                        var mediaType = mediaTypeMapping?.FirstOrDefault(x => x.Value == value?.ToString()).Key;
                                        mappedObject[propertyName] = mediaType ?? null;
                                    }
                                    else
                                    {
                                        mappedObject[propertyName] = value;
                                    }
                                }
                                else
                                {
                                    mappedObject[propertyName] = value;
                                }
                            }
                        }
                    }
                    var template = mappedObject.ToObject<WATemplate>();
                    if (template != null && !string.IsNullOrEmpty(template.Name))
                    {
                        template.WAButtons = waButtons;
                        templates.Add(template);
                    }
                }
            }

            return templates;
        }
        private void SynchronizeWATemplates(List<WATemplate> templates)
        {
            string pattern = @"\{\{\d+\}\}";
            foreach (var template in templates)
            {
                template.IsEnabled = !(Regex.IsMatch(template.Message, pattern) || Regex.IsMatch(template.Header ?? string.Empty, pattern));

                if (!string.IsNullOrEmpty(template.MediaURL))
                {
                    template.HeaderValues = new Dictionary<int, string>
                    {
                        { 1, template.MediaURL }
                    };
                }
                else if (string.IsNullOrEmpty(template.MediaType))
                {
                    template.MediaType = "Text";
                }
                template.BodyValues = ExtractVariablesAsDictionary(template.Message);
            }
        }
        private Dictionary<int, string> ExtractVariablesAsDictionary(string message)
        {
            string variablePattern = @"#([^\s#][\w\s]*[^\s#])#";
            var variablesDict = new Dictionary<int, string>();
            int index = 1;

            // Use Regex to find matches of variables
            var matches = System.Text.RegularExpressions.Regex.Matches(message, variablePattern);
            foreach (System.Text.RegularExpressions.Match match in matches)
            {
                string variable = match.Value;
                if (!variablesDict.ContainsValue(variable)) // Ensure unique variables
                {
                    variablesDict.Add(index++, variable);
                }
            }

            return variablesDict;
        }

        private Dictionary<string, string> GetPropertyKeyValuePairs<T>(object model)
        {
            var keyValuePairs = new Dictionary<string, string>();
            Type type = typeof(T);
            PropertyInfo[] properties = type.GetProperties();

            foreach (PropertyInfo property in properties)
            {
                string key = property.Name;
                keyValuePairs[key] = $"{key}";
            }

            return keyValuePairs;
        }

        #region Automation messages
        public static bool IsValidUserIntent(string? message)
        {
            if (string.IsNullOrWhiteSpace(message))
                return false;

            message = message.ToLowerInvariant();

            var automatedMessagePhrases = new List<string>
            {
                "thanks",
                "thank you",
                "we appreciate your interest",
                "great to hear from you",
                "great to have you here",
                "good morning",
                "good afternoon",
                "good evening",
                "hey! thanks",
                "hi there",
                "greetings",
                "greetings from",
                "welcome",
                "good day",
                "hello",
                "thanks for reaching out",
                "we’ve received your message",
                "this is",
                "for the right guidance",
                "discover your perfect space",
                "available properties",
                "your message is important to us",
                "our team will get back to you shortly",
                "please feel free to contact us",
                "congratulations",
                "dear",
                "wishing you well",
                "residential",
                "commercial",
                "land",
                "2bhk",
                "bhk",
                "your session has timed out",
                "job opportunity"
            };

            var positiveKeywords = new List<string>
            {
                "interested",
                "schedule",
                "demo",
                "call",
                "meeting",
                "site visit",
                "property visit",
                "connect",
                "today",
                "tomorrow",
                "day after",
                "share details",
                "send info",
                "available",
                "book",
                "visit",
                "need help",
                "discuss",
                "ready",
                "let's talk",
                "can we talk",
                "price",
                "cost",
                "rent",
                "buy",
                "sell",
                "investment",
                "follow up",
                "follow-up",
                "requirement",
                "details please",
                "more info",
                "share brochure",
                "2bhk",
                "bhk" // moved here too to treat as positive intent as well
            };

            // Check if message has any positive user intent
            bool isPositive = positiveKeywords.Any(keyword => message.Contains(keyword));

            // Return true if there is any positive intent (even inside an automated message)
            return isPositive;
        }
        #endregion

        #endregion
    }
}
