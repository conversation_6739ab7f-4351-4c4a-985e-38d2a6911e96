﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Web
{
    public class GetLeadCountsByFlagsRequest : GetAllLeadsParametersNewFilters, IRequest<LeadCountsByFlagDto>
    {

    }
    public class GetLeadCountsByFlagsRequestHandler : GetAllLeadsCommonHandler, IRequestHandler<GetLeadCountsByFlagsRequest, LeadCountsByFlagDto>
    {
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        public GetLeadCountsByFlagsRequestHandler(
            ICurrentUser currentuser,
            IDapperRepository dapperRepository,
            ILeadRepository leadRepository,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customMasterLeadStatusRepo)
            : base(currentuser,
                  dapperRepository,
                  leadRepository, customMasterLeadStatusRepo)
        {
            _leadRepositoryAsync = leadRepositoryAsync;
        }
        public async Task<LeadCountsByFlagDto> Handle(GetLeadCountsByFlagsRequest request, CancellationToken cancellationToken)
        {
            if (request?.LeadTags?.Any() ?? false)
            {
                request.TagFilterDto = GetLeadTagFilter(request.Adapt<GetAllLeadsByNewFiltersRequest>());
                request.LeadTags = null;
            }
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var isAdmin = _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty).Result;
            //request.UserType = request?.UserType ?? UserType.None;
            List<Guid> leadHistoryIds = new();
            List<Guid> subIds = new();
            try
            {
                if (request?.AssignTo?.Any() ?? false)
                {
                    if (request?.IsWithTeam ?? false)
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, tenantId ?? string.Empty)).ToList();
                    }
                    else
                    {
                        subIds = request?.AssignTo ?? new List<Guid>();
                    }
                }
                else
                {
                    if (request?.IsOnlyReportees ?? false)
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty))?.ToList() ?? new();
                    }
                    else
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllLeads, isAdmin))?.ToList() ?? new();
                    }
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetLeadCountsByFlagsRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            var customStatus = await _customMasterLeadStatusRepo.ListAsync(cancellationToken);
            LeadCountsByNewFilterDto leadCounts = new();
            var flags = typeof(LeadCountsByFlagDto).GetProperties().ToList();
            var query = await _efLeadRepository.BuildQueryForLeadsCount(request.Adapt<GetAllLeadsByNewFiltersRequest>(), subIds, userId, leadHistoryIds, customStatus, tenantId, isAdmin);
            foreach (var flag in flags)
            {
                leadCounts = AddLeadsCount(leadCounts, flag, request.Adapt<GetAllLeadsByNewFiltersRequest>(), subIds, userId, leadHistoryIds, customStatus, query,isAdmin: isAdmin).Result;
            }
            return leadCounts.Adapt<LeadCountsByFlagDto>();
        }
    }
}
