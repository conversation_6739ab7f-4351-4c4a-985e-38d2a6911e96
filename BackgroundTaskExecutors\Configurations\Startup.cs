using Microsoft.Extensions.Configuration;
public static class Startup
{
    public static IConfigurationBuilder AddConfigurations(this IConfigurationBuilder config, string env)
    {
        const string configurationsDirectory = "Configurations";
        config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/logger.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/logger.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/hangfire.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/hangfire.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/cache.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/cache.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/cors.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/cors.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/database.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/database.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/mail.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/mail.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/middleware.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/middleware.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/security.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/security.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/openapi.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/openapi.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/signalr.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/signalr.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/securityheaders.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/securityheaders.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/localization.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/localization.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/google.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/google.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/aws.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/aws.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/facebook.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/facebook.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/azure.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/azure.{env}.json", optional: true, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/domain.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{configurationsDirectory}/domain.{env}.json", optional: true, reloadOnChange: true)
            .AddEnvironmentVariables();
        return config;
    }
}