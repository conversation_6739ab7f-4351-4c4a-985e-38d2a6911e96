﻿namespace Lrb.Application.DataManagement.Web.Dtos
{
    public class MasterItems
    {
        public List<Domain.Entities.Property>? Properties { get; set; }
        public List<Domain.Entities.Project>? Projects { get; set; }
        public List<Domain.Entities.MasterData.MasterPropertyType>? PropetyTypes { get; set; }
        public List<CustomProspectStatus>? ProspectStatuses { get; set; }
        public List<Domain.Entities.Agency>? Agencies { get; set; }
        public List<Domain.Entities.ChannelPartner>? ChannelPartners { get; set; }
        public List<UserView>? Users { get; set; }
        public List<UserView>? HistoryUsers { get; set; }
        public Domain.Entities.GlobalSettings? GlobalSettings { get; set; }
        public List<Domain.Entities.MasterProspectSource>? ProspectSources { get; set; }
        public string? TenantDisplayPrefix { get; set; }
        public List<Domain.Entities.MasterData.MasterAreaUnit>? AreaUnits { get; set; }
        public List<Domain.Entities.Campaign>? Campaigns { get; set; }
    }
}
