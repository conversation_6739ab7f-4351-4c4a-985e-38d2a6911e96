﻿using Lrb.Application.Utils;
using Lrb.Shared.Extensions;

namespace Lrb.Application.Reports.Web
{
    public class GetVisitAndMeetingReportByUserRequest : IRequest<PagedResponse<LeadAppointmentByUserDto, string>>
    {
        public string? SearchText { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public DateTime? ToDateForMeetingOrVisit { get; set; }
        public DateTime? FromDateForMeetingOrVisit { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<string>? Projects { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<string>? SubSources { get; set; }
        public UserStatus? UserStatus { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; } = int.MaxValue;
        public OwnerSelectionType? OwnerSelection { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
    }
    public class GetVisitAndMeetingReportByUserRequestHandler : IRequestHandler<GetVisitAndMeetingReportByUserRequest, PagedResponse<LeadAppointmentByUserDto, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public GetVisitAndMeetingReportByUserRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<PagedResponse<LeadAppointmentByUserDto, string>> Handle(GetVisitAndMeetingReportByUserRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ReportPermission != null)
            {
                switch (request.ReportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    //request.UserIds = new List<Guid>() { userId };
                    //teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();

                    teamUserIds = permittedUserIds;
                }
            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            request.FromDateForMeetingOrVisit = request.FromDateForMeetingOrVisit.HasValue ? request.FromDateForMeetingOrVisit.Value.ConvertFromDateToUtc() : null;
            request.ToDateForMeetingOrVisit = request.ToDateForMeetingOrVisit.HasValue ? request.ToDateForMeetingOrVisit.Value.ConvertToDateToUtc() : null;
            var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadAppointmentByUserDto>("LeadratBlack", "GetMeetingAndSitevisitReportByUser", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                fromdateformeetingorvisit = request.FromDateForMeetingOrVisit,
                todateformeetingorvisit = request.ToDateForMeetingOrVisit,
                datetype = request.DateType,
                userids = teamUserIds,
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                tenantid = tenantId,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                userstatus = request?.UserStatus ?? 0,
                pagesize = request.PageSize,
                pagenumber = request.PageNumber
            }))?.ToList();
            res?.ForEach(src => src.TotalCount =  src.MeetingScheduledCount + src.MeetingDoneCount + src.MeetingNotDoneCount +
                             src.SiteVisitScheduledCount + src.SiteVisitDoneCount + src.SiteVisitNotDoneCount + src.NotInterestedAfterMeetingDone + src.NotInterestedAfterSiteVisitDone + src.DroppedAfterMeetingDone + src.DroppedAfterSiteVisitDone + src.BookedCount + src.BookingCancelCount);
            var totalCount = (await _dapperRepository.QueryStoredProcedureCountFromReadReplicaAsync("LeadratBlack", "GetMeetingAndSitevisitReportCountByUser", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                fromdateformeetingorvisit = request.FromDateForMeetingOrVisit,
                todateformeetingorvisit = request.ToDateForMeetingOrVisit,
                datetype = request.DateType,
                userids = teamUserIds,
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                tenantid = tenantId,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                userstatus = request?.UserStatus ?? 0,
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                ownerselection = request?.OwnerSelection ?? OwnerSelectionType.PrimaryOwner,
            }));
            return new(res, totalCount);

        }
    }
}
