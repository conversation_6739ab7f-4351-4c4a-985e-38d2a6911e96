﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class PropertyNewFieldFOrLIstingMigration : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ListingCompliance_Properties_PropertyId",
                schema: "LeadratBlack",
                table: "ListingCompliance");

            migrationBuilder.DropPrimaryKey(
                name: "PK_ListingCompliance",
                schema: "LeadratBlack",
                table: "ListingCompliance");

            migrationBuilder.RenameTable(
                name: "ListingCompliance",
                schema: "LeadratBlack",
                newName: "ListingCompliances",
                newSchema: "LeadratBlack");

            migrationBuilder.RenameIndex(
                name: "IX_ListingCompliance_PropertyId",
                schema: "LeadratBlack",
                table: "ListingCompliances",
                newName: "IX_ListingCompliances_PropertyId");

            migrationBuilder.AddColumn<int>(
                name: "Height",
                schema: "LeadratBlack",
                table: "PropertyGalleries",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Width",
                schema: "LeadratBlack",
                table: "PropertyGalleries",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Message",
                schema: "LeadratBlack",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PFListingId",
                schema: "LeadratBlack",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_ListingCompliances",
                schema: "LeadratBlack",
                table: "ListingCompliances",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_ListingCompliances_Properties_PropertyId",
                schema: "LeadratBlack",
                table: "ListingCompliances",
                column: "PropertyId",
                principalSchema: "LeadratBlack",
                principalTable: "Properties",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ListingCompliances_Properties_PropertyId",
                schema: "LeadratBlack",
                table: "ListingCompliances");

            migrationBuilder.DropPrimaryKey(
                name: "PK_ListingCompliances",
                schema: "LeadratBlack",
                table: "ListingCompliances");

            migrationBuilder.DropColumn(
                name: "Height",
                schema: "LeadratBlack",
                table: "PropertyGalleries");

            migrationBuilder.DropColumn(
                name: "Width",
                schema: "LeadratBlack",
                table: "PropertyGalleries");

            migrationBuilder.DropColumn(
                name: "Message",
                schema: "LeadratBlack",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "PFListingId",
                schema: "LeadratBlack",
                table: "Properties");

            migrationBuilder.RenameTable(
                name: "ListingCompliances",
                schema: "LeadratBlack",
                newName: "ListingCompliance",
                newSchema: "LeadratBlack");

            migrationBuilder.RenameIndex(
                name: "IX_ListingCompliances_PropertyId",
                schema: "LeadratBlack",
                table: "ListingCompliance",
                newName: "IX_ListingCompliance_PropertyId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_ListingCompliance",
                schema: "LeadratBlack",
                table: "ListingCompliance",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_ListingCompliance_Properties_PropertyId",
                schema: "LeadratBlack",
                table: "ListingCompliance",
                column: "PropertyId",
                principalSchema: "LeadratBlack",
                principalTable: "Properties",
                principalColumn: "Id");
        }
    }
}
