﻿using Microsoft.AspNetCore.Http.Connections;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.WhatsApp.Host
{
    public static class Startup
    {
        public static IHostBuilder AddConfigurations(this IHostBuilder hostBuilder)
        {
            hostBuilder.ConfigureAppConfiguration((context, config) =>
            {
                var env = context.HostingEnvironment;
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                      .AddJsonFile($"appsettings.{env?.EnvironmentName ?? "dev"}.json", optional: true, reloadOnChange: true);
            });

            return hostBuilder;
        }
        private const string CorsPolicy = nameof(CorsPolicy);
        public static IServiceCollection AddAppServices(this IServiceCollection services)
        {
            services.AddTransient<TenantHubFilter>();
            services.AddTransient<IWhatsAppService, WhatsAppService>();
            services.AddTransient<ILRService, LRService>();
            services.AddTransient<IRestSharpService, RestSharpService>();
            services.AddSingleton<ICosmosService, CosmosService>();
            services.AddOptions<CosmosSettings>()
                .BindConfiguration(nameof(CosmosSettings));
            services.AddOptions<DatabaseSettings>()
                .BindConfiguration(nameof(DatabaseSettings));
            services.AddOptions<LeadratApis>()
                .BindConfiguration(nameof(LeadratApis));
            return services;
        }
        public static void ConfigureHub(this WebApplication app)
        {
            app.UseRouting();
            app.MapHub<ChatHub.ChatHub>("/chat-hub", options =>
            {
                options.Transports =
                    HttpTransportType.WebSockets |
                    HttpTransportType.LongPolling;
            });
            app.UseAuthorization();
        }
        public static IEndpointRouteBuilder MapEndpoints(this IEndpointRouteBuilder builder)
        {
            #region Health Check

            // Map / to check it is alive or not
            builder.MapGet("/", context =>
            {
                // Perform health checks and return appropriate status
                return context.Response.WriteAsync("API is live!");
            }).AllowAnonymous();

            // Map /api/health to HealthCheck
            builder.MapHealthCheck();

            // Map /health-check to HealthCheck
            builder.MapGet("/health-check", context =>
            {
                // Perform health checks and return appropriate status
                return context.Response.WriteAsync("API is healthy!");
            }).AllowAnonymous();

            // Map /admin/host/ping
            builder.MapGet("/admin/host/ping", async context =>
            {
                // Perform admin/host/ping logic and return appropriate response
                await context.Response.WriteAsync("Ping successful!");
            }).AllowAnonymous();

            builder.MapPost("/admin/host/ping", async context =>
            {
                // Perform admin/host/ping logic and return appropriate response
                await context.Response.WriteAsync("Ping successful!");
            }).AllowAnonymous();
            #endregion

            return builder;
        }
        public static IServiceCollection AddHealthCheck(this IServiceCollection services) =>
        services.AddHealthChecks().AddCheck<WhatsAppHealthCheck>("WhatsApp").Services;

        private static IEndpointConventionBuilder MapHealthCheck(this IEndpointRouteBuilder endpoints) =>
            endpoints.MapHealthChecks("/api/health").AllowAnonymous();

        internal static IServiceCollection AddCorsPolicy(this IServiceCollection services, IConfiguration config)
        {
            var corsSettings = config.GetSection(nameof(CorsSettings)).Get<CorsSettings>();
            var origins = new List<string>();
            if (corsSettings.Angular is not null)
                origins.AddRange(corsSettings.Angular.Split(';', StringSplitOptions.RemoveEmptyEntries));
            if (corsSettings.Blazor is not null)
                origins.AddRange(corsSettings.Blazor.Split(';', StringSplitOptions.RemoveEmptyEntries));
            if (corsSettings.React is not null)
                origins.AddRange(corsSettings.React.Split(';', StringSplitOptions.RemoveEmptyEntries));

            return services.AddCors(opt =>
                opt.AddPolicy(CorsPolicy, policy =>
                    policy.AllowAnyHeader()
                        .AllowAnyOrigin()
                        .AllowAnyMethod()));
            //.WithOrigins(origins.ToArray())));
        }

        internal static IApplicationBuilder UseCorsPolicy(this IApplicationBuilder app) =>
            app.UseCors(CorsPolicy);
    }
}
