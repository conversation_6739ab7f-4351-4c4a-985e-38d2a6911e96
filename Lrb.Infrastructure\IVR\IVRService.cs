﻿using Amazon.Auth.AccessControlPolicy;
using Amazon.DynamoDBv2.Model;
using DocumentFormat.OpenXml.Wordprocessing;
using Hangfire.Client;
using Lrb.Application.Common.HttpService;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.IVR;
using Lrb.Application.Common.IVR.Common.Dtos;
using Lrb.Application.Common.IVR.Kommuno;
using Lrb.Application.Common.Knowlariry;
using Lrb.Application.Common.Models;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.Servetel.FeederDto;
using Lrb.Application.Common.Servetel.ResponseDtos;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Infrastructure.Auth;
using Lrb.Shared.Extensions;
using Mapster;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using Serilog;
using System.Security.AccessControl;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.RegularExpressions;
using System.Web;
using System.Xml;
using System.Xml.Linq;
using System.Xml.Serialization;
using Formatting = Newtonsoft.Json.Formatting;

namespace Lrb.Infrastructure.IVR
{
    public class IVRService : IIVRService
    {
        private readonly IServetelService _servetelService;
        private readonly IQKonnectService _iQKonnectService;
        private readonly ITataTeleBusinessService _iTataTeleBusinessService;
        private readonly IFreJunService _iFreJunService;
        private readonly IMCubeService _mCubeService;
        private readonly IKnowlarityService _knowlarityService;
        private readonly IKommunoService _kommunoService;
        private readonly ILogger _logger;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly ICurrentUser _currentUser;
        private readonly IHttpService _httpService;

        public IVRService(IServetelService servetelService,
            IQKonnectService iQKonnectService,
            ITataTeleBusinessService iTataTeleBusinessService,
            ILogger logger,
            IFreJunService iFreJunService,
            IMCubeService mCubeService,
            IKnowlarityService knowlarityService,
            IKommunoService kommunoService,
            ILeadRepositoryAsync leadRepositoryAsync,
            ICurrentUser currentUser,
            IHttpService httpService)
        {
            _servetelService = servetelService;
            _iQKonnectService = iQKonnectService;
            _logger = logger;
            _iFreJunService = iFreJunService;
            _iTataTeleBusinessService = iTataTeleBusinessService;
            _mCubeService = mCubeService;
            _knowlarityService = knowlarityService;
            _kommunoService = kommunoService;
            _leadRepositoryAsync = leadRepositoryAsync;
            _currentUser = currentUser;
            _httpService = httpService;
        }
        public async Task<Response<ClickToCallCommonResponseDto>> ClickToCall(ClickToCallCommonDto dto, IntegrationAccountInfoCommonDto integrationAccountInfoDto)
        {

            try
            {
                switch (integrationAccountInfoDto.IVRServiceProvider)
                {
                    case Domain.Enums.IVRServiceProvider.Servetel:
                        var clickToCallResponseDto = await _servetelService.ClickToCall(dto.Adapt<ClickToCallDto>(), integrationAccountInfoDto.AuthToken ?? string.Empty);
                        var result = clickToCallResponseDto.Adapt<Response<ClickToCallCommonResponseV2Dto>>();
                        return new() { Data = new ClickToCallCommonResponseDto() { Message = result.Data.message, Success = result.Data.success }, Succeeded = result.Succeeded, Message = result.Message, Errors = result.Errors };
                    case Domain.Enums.IVRServiceProvider.MyOperator:
                        break;
                    case Domain.Enums.IVRServiceProvider.Cronberry:
                        break;
                    case Domain.Enums.IVRServiceProvider.Exotel:
                        break;
                    case Domain.Enums.IVRServiceProvider.VoicePanel:
                        break;
                    case Domain.Enums.IVRServiceProvider.QKonnect:
                        var qKonnectResponse = await _iQKonnectService.ClickToCall(dto, integrationAccountInfoDto);
                        return qKonnectResponse;
                    case Domain.Enums.IVRServiceProvider.FreJun:
                        var freJunResponse = await _iFreJunService.ClickToCall(dto, integrationAccountInfoDto);
                        return freJunResponse;
                    case Domain.Enums.IVRServiceProvider.TataTeleBusiness:
                        var tataTeleBusiness = await _iTataTeleBusinessService.ClickToCall(dto, integrationAccountInfoDto);
                        return tataTeleBusiness;
                    case Domain.Enums.IVRServiceProvider.MCube:
                        var mCubeResponse = await _mCubeService.ClickToCallV2(dto, integrationAccountInfoDto);
                        return mCubeResponse;
                    case Domain.Enums.IVRServiceProvider.MCubeClassic:
                        var mCubeClassicResponse = await _mCubeService.ClickToCall(dto, integrationAccountInfoDto);
                        return mCubeClassicResponse;
                    case Domain.Enums.IVRServiceProvider.Knowlarity:
                        var knowlarityResponse = await _knowlarityService.ClickToCallAsync(dto.Adapt<KnowlarityMakeCallDto>(), integrationAccountInfoDto);
                        return knowlarityResponse;
                    case Domain.Enums.IVRServiceProvider.Kommuno:
                        var kummunoResponse = await _kommunoService.ClickToCall(dto, integrationAccountInfoDto);
                        return kummunoResponse;
                    default:
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Information($"IVRService -> ClickToCall -> Exception : {JsonConvert.SerializeObject(ex)}");
                throw;
            }
            return new()
            {

            };
        }

        public Task<Response<ClickToCallCommonResponseDto>> BuildRequest(ClickToCallCommonDto dto, IntegrationAccountInfoCommonDto integrationAccountInfoDto)
        {
            throw new NotImplementedException();
        }
        public async Task<Response<ClickToCallCommonResponseDto>> ClickToCallConfig(ClickToCallCommonDto dto, IntegrationAccountInfoCommonDto integrationAccountInfoDto, IVROutboundConfiguration ivrOutboundConfig, TempVariable? tempVariable)
        {
            string? authvalue = null;
            if (ivrOutboundConfig == null)
            {
                return new Response<ClickToCallCommonResponseDto>()
                {
                    Message = "IVR Outbound Config can't be null"
                };
            }
            if (!string.IsNullOrWhiteSpace(ivrOutboundConfig.CurlForAuthentication))
            {
                if (ivrOutboundConfig.CurlForAuthentication.Contains('#'))
                {
                    ivrOutboundConfig.CurlForAuthentication = ReplaceCurlVariables(dto, ivrOutboundConfig.CurlForAuthentication);
                }
                var data = await _httpService.ExecuteCurlCommandAsync(ivrOutboundConfig.CurlForAuthentication);
                authvalue = data[$"{ivrOutboundConfig.ResponseParameterKey}"];
                ivrOutboundConfig.Resources = ReplaceIVRResourceVariables(dto, ivrOutboundConfig, authvalue);
            }
            //Creating a RestClient
            RestClient client = new RestClient(ivrOutboundConfig.BaseURL ?? string.Empty);
            RestRequest? request = null;

            //Adding resource Parameters
            if (!string.IsNullOrWhiteSpace(ivrOutboundConfig.Resources) && ivrOutboundConfig.Resources.Contains('#'))
            {
                ivrOutboundConfig.Resources = ReplaceIVRResourceVariables(dto, ivrOutboundConfig);
            }

            //Creating a new Rest Request with the given Method Type
            if (ivrOutboundConfig.MethodType?.Contains("POST", StringComparison.InvariantCultureIgnoreCase) ?? false)
            {
                request = new RestRequest(ivrOutboundConfig.Resources, Method.Post);
            }
            else if (ivrOutboundConfig.MethodType?.Contains("GET", StringComparison.InvariantCultureIgnoreCase) ?? false)
            {
                request = new RestRequest(ivrOutboundConfig.Resources, Method.Get);
            }
            else
            {
                return new Response<ClickToCallCommonResponseDto>()
                {
                    Message = "Method Type is invalid"
                };
            }



            //Updating Country Code
            dto = UpdateCountryCode(dto);

            //Adding Query Parameters
            if (ivrOutboundConfig.QueryParameters?.Any() ?? false)
            {
                var queryParams = await ReplaceIVRParamVariables(dto, new Dictionary<string, string>(ivrOutboundConfig.QueryParameters), ivrOutboundConfig);
                foreach (var queryParam in queryParams)
                {
                    request.AddQueryParameter(queryParam.Key, queryParam.Value);
                }
            }

            //Adding Header Variables
            if (ivrOutboundConfig.HeaderVariables?.Any() ?? false)
            {
                foreach (var headVar in ivrOutboundConfig.HeaderVariables)
                {
                    request.AddHeader(headVar.Key, headVar.Value);
                }
            }

            //Adding Body Variables
            if (ivrOutboundConfig.BodyVariables?.Any() ?? false)
            {
                if (!string.IsNullOrWhiteSpace(ivrOutboundConfig.ContentType) && ivrOutboundConfig.ContentType.Contains("urlencoded"))
                {
                    request = GetFormattedRequestBodyAsync(request, ivrOutboundConfig.BodyVariables, ivrOutboundConfig.ContentType, dto);
                }
                else
                {
                    try
                    {
                        Dictionary<string, object> temp = new();
                        var bodyInStringFormat = ivrOutboundConfig.BodyVariables.Serialize();
                        var requestBody = ReplaceIVRBodyVariables(dto, bodyInStringFormat, ivrOutboundConfig, authvalue);
                        temp = JsonConvert.DeserializeObject<Dictionary<string, object>>(requestBody) ?? new();

                        if (temp.Any())
                        {
                            foreach (var item in temp)
                            {
                                try
                                {
                                    if (item.Value is JObject jsonObject)
                                    {
                                        temp[item.Key] = jsonObject.ToObject<object>() ?? string.Empty;
                                    }
                                    else if (item.Value is JArray jsonArray)
                                    {
                                        temp[item.Key] = jsonArray.ToObject<object[]>() ?? Array.Empty<object>();
                                    }
                                    else if (item.Value is string str && IsJson(str))
                                    {
                                        temp[item.Key] = JsonConvert.DeserializeObject<object>(str) ?? string.Empty;
                                    }
                                }
                                catch (Exception ex)
                                {
                                }
                            }
                        }
                        request.AddBody(temp);
                    }
                    catch (Exception ex)
                    {
                    }

                }
            }

            RestResponse response = new();
            try
            {
                _logger.Information("ClickToCallConfig -> Request = " + request.Serialize());
                if (ivrOutboundConfig.MethodType?.Equals("POST", StringComparison.InvariantCultureIgnoreCase) ?? false)
                {
                    response = await client.ExecuteAsync(request);
                }
                else if (ivrOutboundConfig.MethodType?.Equals("GET", StringComparison.InvariantCultureIgnoreCase) ?? false)
                {
                    response = await client.GetAsync(request);
                }
                _logger.Information("ClickToCallConfig -> Response = " + response.Serialize());
            }
            catch (Exception ex)
            {
                _logger.Error("Error while executing ClickToCallConfig ClickToCall" + JsonConvert.SerializeObject(ex));
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Newtonsoft.Json.Formatting.Indented }),
                    ErrorModule = "ClickToCallConfig -> ClickToCall()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                string jsonExceptionResponse = GetResponseMessage(response.Content ?? string.Empty, response.ContentType ?? string.Empty);
                return new()
                {
                    Data = new ClickToCallCommonResponseDto() { Message = jsonExceptionResponse, Success = false },
                    Message = jsonExceptionResponse ?? string.Empty,
                    Succeeded = false,
                };
            }
            //ClickToCallResponseDto? clickToCallResponse = JsonConvert.DeserializeObject<ClickToCallResponseDto>(response?.Content ?? string.Empty);
            string jsonResponse = GetResponseMessage(response.Content ?? string.Empty, response.ContentType ?? string.Empty);
            if (response.IsSuccessful && response.IsSuccessStatusCode)
            {
                return new()
                {
                    Data = new ClickToCallCommonResponseDto()
                    {
                        Message = "Call Queued Successfully.",
                        Success = response.IsSuccessful,
                        ClickToCallCommonDto = dto
                    },
                    Succeeded = response.IsSuccessful,
                    Message = jsonResponse,
                };
            }
            else
            {
                return new()
                {
                    Data = new ClickToCallCommonResponseDto()
                    {
                        Message = jsonResponse,
                        Success = false,
                        ClickToCallCommonDto = dto
                    },
                    Succeeded = false,
                    Message = jsonResponse,
                };
            }
        }
        private static bool IsJson(string str)
        {
            str = str.Trim();
            return (str.StartsWith("{") && str.EndsWith("}")) || (str.StartsWith("[") && str.EndsWith("]"));
        }

        private string GetResponseMessage(string responseContent, string contentType)
        {
            if (!string.IsNullOrWhiteSpace(responseContent) && !string.IsNullOrWhiteSpace(contentType))
            {
                switch (contentType)
                {
                    case "application/xml":

                        Dictionary<string, string> responseDic = new();

                        var xdoc = new XmlDocument();
                        xdoc.LoadXml(responseContent);
                        XmlNodeList nodeList = xdoc.GetElementsByTagName("Message");
                        var entity = nodeList[0];

                        if (!string.IsNullOrWhiteSpace(entity?.Name) && !string.IsNullOrWhiteSpace(entity?.FirstChild?.Value))
                        {
                            responseDic.Add(entity.Name, entity.FirstChild.Value);
                        }

                        if (!responseDic.Any())
                        {
                            nodeList = xdoc.GetElementsByTagName("message");
                            entity = nodeList[0];
                            if (!string.IsNullOrWhiteSpace(entity?.Name) && !string.IsNullOrWhiteSpace(entity?.FirstChild?.Value))
                            {
                                responseDic.Add(entity.Name, entity.FirstChild.Value);
                            }
                        }

                        if (!responseDic.Any())
                        {
                            nodeList = xdoc.GetElementsByTagName("msg");
                            entity = nodeList[0];
                            if (!string.IsNullOrWhiteSpace(entity?.Name) && !string.IsNullOrWhiteSpace(entity?.FirstChild?.Value))
                            {
                                responseDic.Add(entity.Name, entity.FirstChild.Value);
                            }
                        }

                        if (!responseDic.Any())
                        {
                            nodeList = xdoc.GetElementsByTagName("RestException");
                            entity = nodeList[0];
                            if (!string.IsNullOrWhiteSpace(entity?.Name) && !string.IsNullOrWhiteSpace(entity?.FirstChild?.Value))
                            {
                                responseDic.Add(entity.Name, entity.FirstChild.Value);
                            }
                        }

                        if (!responseDic.Any())
                        {
                            nodeList = xdoc.GetElementsByTagName("Exception");
                            entity = nodeList[0];
                            if (!string.IsNullOrWhiteSpace(entity?.Name) && !string.IsNullOrWhiteSpace(entity?.FirstChild?.Value))
                            {
                                responseDic.Add(entity.Name, entity.FirstChild.Value);
                            }
                        }

                        if (!responseDic.Any())
                        {
                            nodeList = xdoc.GetElementsByTagName("Status");
                            entity = nodeList[0];
                            if (!string.IsNullOrWhiteSpace(entity?.Name) && !string.IsNullOrWhiteSpace(entity?.FirstChild?.Value))
                            {
                                responseDic.Add(entity.Name, entity.FirstChild.Value);
                            }
                        }

                        return responseDic.Values?.FirstOrDefault() ?? "Something Went Wrong!";

                    case "application/json":
                        break;
                    default:
                        return responseContent;
                }
            }
            return responseContent;
        }
        private RestRequest GetFormattedRequestBodyAsync(RestRequest restRequest, IDictionary<string, string> bodyVariables, string contentType, ClickToCallCommonDto ivrCommonDto)
        {
            switch (contentType)
            {
                case "application/x-www-form-urlencoded":
                    restRequest = GetUrlEncodedDataAsync(bodyVariables, ivrCommonDto, restRequest);
                    return restRequest;

                default:
                    return restRequest;
            }
        }
        private RestRequest GetUrlEncodedDataAsync(IDictionary<string, string> bodyVariables, ClickToCallCommonDto ivrCommonDto, RestRequest restRequest)
        {
            foreach (KeyValuePair<string, string> kvp in bodyVariables)
            {
                if (!string.IsNullOrEmpty(kvp.Key) && !string.IsNullOrEmpty(kvp.Value))
                {
                    var result = ReplaceIVRBodyVariables(ivrCommonDto, kvp.Value);
                    restRequest.AddParameter(kvp.Key, result, ParameterType.GetOrPost);
                }
            }
            return restRequest;
        }
        public string ReplaceIVRBodyVariables(ClickToCallCommonDto ivrCommonDto, string? stringToReplace, IVROutboundConfiguration? ivrOutboundConfig = null, string? authvalue = null)
        {
            if (string.IsNullOrWhiteSpace(stringToReplace))
            {
                return string.Empty;
            }
            stringToReplace = stringToReplace.Replace("#CustomerNumber#", ivrCommonDto.DestinationNumber)
                .Replace("#VirtualNumber#", ivrCommonDto.CallerIdOrVirtualNumber)
                .Replace("#AgentNumber#", ivrCommonDto.AgentNumber)
                .Replace("#UserEmail#", ivrCommonDto.UserEmail)
                .Replace("#CurrentTime#", DateTimeOffset.UtcNow.ToString())
                .Replace("#NewSixteenDigitUniqueId#", GenerateSixteenDigitNUmber())
                .Replace("#AuthKey#", authvalue);
            return stringToReplace;
        }
        private string GenerateSixteenDigitNUmber()
        {
            long timestamp = DateTime.UtcNow.Ticks;
            Random random = new Random();
            int randomNumber = random.Next(100000, 999999);
            string uniqueNumber = $"{timestamp}{randomNumber}";
            uniqueNumber = uniqueNumber.Substring(0, 16).PadLeft(16, '0');
            return uniqueNumber;
        }
        public async Task<Dictionary<string, string>> ReplaceIVRParamVariables(ClickToCallCommonDto ivrCommonDto, Dictionary<string, string> queryParams, IVROutboundConfiguration ivrOutboundConfig)
        {
            if (!queryParams.Any())
            {
                return new();
            }
            Dictionary<string, string> updatedQueryParams = new();
            foreach (var queryParam in queryParams)
            {
                updatedQueryParams.Add(queryParam.Key, queryParam.Value.Replace("#CustomerNumber#", ivrCommonDto.DestinationNumber)
                .Replace("#VirtualNumber#", ivrCommonDto.CallerIdOrVirtualNumber)
                .Replace("#AgentNumber#", ivrCommonDto.AgentNumber)
                .Replace("#LeadId#", ivrCommonDto.LeadId?.ToString() ?? string.Empty)
                .Replace("#ProspectId#", ivrCommonDto.ProspectId?.ToString() ?? string.Empty)
                .Replace("#UserEmail#", ivrCommonDto.UserEmail));
            }
            return updatedQueryParams;
        }
        public ClickToCallCommonDto UpdateCountryCode(ClickToCallCommonDto dto)
        {
            if (dto == null)
            {
                return null;
            }
            var tenantId = _currentUser.GetTenant();
            if (new List<string>() { "denzrealtors", "kumari" }.Contains(tenantId ?? string.Empty))
            {
                dto.AgentNumber = (dto.AgentNumber?.Length >= 10) ? dto.AgentNumber[^10..] : dto.AgentNumber;
                dto.DestinationNumber = (dto.DestinationNumber?.Length >= 10) ? dto.DestinationNumber[^10..] : dto.DestinationNumber;
            }
            return dto;
        }
        public string ReplaceIVRResourceVariables(ClickToCallCommonDto ivrCommonDto, IVROutboundConfiguration ivrOutboundConfig, string? authResponseData = null)
        {
            if (ivrOutboundConfig != null)
            {
                ivrOutboundConfig.Resources = ivrOutboundConfig?.Resources?.Replace("#CustomerNumber#", Regex.Replace(ivrCommonDto.DestinationNumber ?? string.Empty, "[^0-9]", ""));
                ivrOutboundConfig.Resources = ivrOutboundConfig?.Resources?.Replace("#VirtualNumber#", Regex.Replace(ivrCommonDto.CallerIdOrVirtualNumber ?? string.Empty, "[^0-9]", ""));
                ivrOutboundConfig.Resources = ivrOutboundConfig?.Resources?.Replace("#AgentNumber#", Regex.Replace(ivrCommonDto.AgentNumber ?? string.Empty, "[^0-9]", ""));
                ivrOutboundConfig.Resources = ivrOutboundConfig?.Resources?.Replace("#UserEmail#", Regex.Replace(ivrCommonDto.UserEmail ?? string.Empty, "[^0-9]", ""));
                ivrOutboundConfig.Resources = ivrOutboundConfig?.Resources?.Replace("#AuthKey#", Regex.Replace(authResponseData ?? string.Empty, "", ""));
            }
            return ivrOutboundConfig?.Resources ?? string.Empty;
        }
        public string ReplaceCurlVariables(ClickToCallCommonDto dto, string curlString)
        {
            if (curlString != null)
            {
                curlString = curlString.Replace("#CustomerNumber#", Regex.Replace(dto.DestinationNumber ?? string.Empty, "[^0-9]", ""));
                curlString = curlString.Replace("#VirtualNumber#", Regex.Replace(dto.CallerIdOrVirtualNumber ?? string.Empty, "[^0-9]", ""));
                curlString = curlString.Replace("#agentnumber#", Regex.Replace(dto.AgentNumber ?? string.Empty, "[^0-9]", ""));
                curlString = curlString.Replace("#UserEmail#", Regex.Replace(dto.UserEmail ?? string.Empty, "[^0-9]", ""));
            }
            return curlString ?? string.Empty;
        }
    }
}
