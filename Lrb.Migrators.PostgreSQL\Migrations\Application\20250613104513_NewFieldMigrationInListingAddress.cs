﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class NewFieldMigrationInListingAddress : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Latitude",
                schema: "LeadratBlack",
                table: "ListingSourceAddresses",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LocationId",
                schema: "LeadratBlack",
                table: "ListingSourceAddresses",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Longitude",
                schema: "LeadratBlack",
                table: "ListingSourceAddresses",
                type: "text",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Latitude",
                schema: "LeadratBlack",
                table: "ListingSourceAddresses");

            migrationBuilder.DropColumn(
                name: "LocationId",
                schema: "LeadratBlack",
                table: "ListingSourceAddresses");

            migrationBuilder.DropColumn(
                name: "Longitude",
                schema: "LeadratBlack",
                table: "ListingSourceAddresses");
        }
    }
}
