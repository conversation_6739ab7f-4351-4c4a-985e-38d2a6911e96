﻿using Lrb.Application.Agency.Web.Dtos;
using Lrb.Application.Campaigns.Dto;
using Lrb.Application.Campaigns.Spec;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Marketing.Web.Dtos;
using Lrb.Application.Marketing.Web.Mapping;
using Lrb.Application.Marketing.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Marketing;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using PhoneNumbers;
using System.Collections.Concurrent;
using System.Data;
using System.Text.RegularExpressions;
namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task MarketingCampaignHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            BulkMarketingAgencyUploadTracker? bulkCampaignUpload = (await _bulkMarketingUploadRepo.ListAsync(new GetBulkMarketingAgencyByTrackerIdSpec(input.TrackerId))).FirstOrDefault();
            try
            {
                if (bulkCampaignUpload != null)
                {
                    try
                    {
                        bulkCampaignUpload.MappedColumnData = bulkCampaignUpload.MappedColumnData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                        bulkCampaignUpload.Status = Lrb.Domain.Enums.UploadStatus.Started;
                        bulkCampaignUpload.LastModifiedBy = input.CurrentUserId;
                        bulkCampaignUpload.CreatedBy = input.CurrentUserId;
                        bulkCampaignUpload.SheetName = bulkCampaignUpload.S3BucketKey.Split('/').Last() + "/" + bulkCampaignUpload.SheetName.Split('/').Last();
                        await _bulkMarketingUploadRepo.UpdateAsync(bulkCampaignUpload);
                        Console.WriteLine($"handler() -> MarketingCampaignHandler Updated Status: {bulkCampaignUpload.Status} \n {JsonConvert.SerializeObject(bulkCampaignUpload)}");
                        #region fetch all required data
                        var existingCamapigns = await _campaignRepo.ListAsync(new GetAllBulkCampaignsSpecs(), cancellationToken);
                        var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(cancellationToken));
                        #endregion

                        #region Convert To DataTable
                        Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", bulkCampaignUpload.S3BucketKey);
                        DataTable dataTable = new();
                        if (bulkCampaignUpload.S3BucketKey.Split('.').LastOrDefault() == "csv")
                        {
                            using MemoryStream memoryStream = new();
                            fileStream.CopyTo(memoryStream);
                            dataTable = CSVHelper.CSVToDataTable(memoryStream);
                        }
                        else
                        {
                            dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, bulkCampaignUpload.SheetName);
                        }

                        List<InvalidAgencyDto> invalids = new();
                        int totalRows = dataTable.Rows.Count;
                        for (int i = totalRows - 1; i >= 0; i--)
                        {
                            var row = dataTable.Rows[i];
                            var data1 = row[bulkCampaignUpload.MappedColumnData[MarketingDataColumns.Name]].ToString();
                            if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                            {
                                row.Delete();
                            }
                            else if (string.IsNullOrEmpty(row[bulkCampaignUpload.MappedColumnData[MarketingDataColumns.Name]].ToString()))
                            {
                                var notes = string.Join(",", row.ItemArray.Where(i => !string.IsNullOrEmpty(i.ToString())));
                                var invalidCampaign = new InvalidAgencyDto
                                {
                                    Errors = "contact number and name are empty",
                                    //Notes = notes
                                };

                                row.Delete();
                            }
                        }
                        if (dataTable.Rows.Count <= 0)
                        {
                            throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                        }
                        totalRows = dataTable.Rows.Count;
                        Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
                        #endregion

                        #region Checking For new Prosperties and Project and Agencies

                        List<Property> newProperties = new();
                        List<Lrb.Domain.Entities.Project> newProjects = new();

                        #endregion

                        var unMappedColumn = dataTable.GetUnmappedAgencyColumnNames(bulkCampaignUpload?.MappedColumnData);
                        var globalSettingInfoList = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec(), cancellationToken);

                        var toCampaigns = dataTable.ConvertToCampaign(bulkCampaignUpload.MappedColumnData, unMappedColumn, globalSettingInfoList, bulkCampaignUpload);


                        string callingCode = globalSettingInfoList?.Countries?.FirstOrDefault().DefaultCallingCode;

                        List<Task<(string ContactNo, string? AlternateContactNo)>> validationTasks = new List<Task<(string, string?)>>();

                        toCampaigns = toCampaigns
                           .Where(age => age.Name != string.Empty)
                           .DistinctBy(prospect => prospect.Name)
                           .ToList();
                        validationTasks = validationTasks
                        .Where(t => !t.IsFaulted && !t.IsCanceled && !string.IsNullOrWhiteSpace(t.Result.ContactNo) && t.Result.ContactNo.Length <= 25)
                        .DistinctBy(t => t.Result.ContactNo)
                        .ToList();
                        var distinctCount = toCampaigns.Count();
                        Console.WriteLine($"handler() -> Total Distinct Campaign: {distinctCount}");
                        var existingContactNos = existingCamapigns.Select(i => i.Name).ToList();
                        List<Campaign> campaignsToUpdate = new();
                        foreach (Campaign campaign in toCampaigns)
                        {
                            if (existingContactNos.Any(i => !string.IsNullOrWhiteSpace(campaign.Name)))
                            {
                                var invalidcampaign = campaign.Adapt<InvalidAgencyDto>();

                                invalidcampaign.Errors = "Duplicate Campaign";
                                var duplicateAgency = existingCamapigns.FirstOrDefault(i => !string.IsNullOrWhiteSpace(campaign.Name) && i.Name != null && i.Name.Equals(campaign.Name));
                                if (duplicateAgency != null)
                                {
                                    invalids.Add(invalidcampaign);
                                }
                            }
                        }
                        toCampaigns.RemoveAll(i => invalids.Select(i => i.Name).Contains(i.Name));

                        bulkCampaignUpload.Status = UploadStatus.InProgress;
                        bulkCampaignUpload.TotalCount = totalRows;
                        bulkCampaignUpload.DistinctCount = distinctCount;
                        bulkCampaignUpload.LastModifiedBy = input.CurrentUserId;
                        bulkCampaignUpload.CreatedBy = input.CurrentUserId;
                        if (invalids.Any())
                        {
                            bulkCampaignUpload.DuplicateCount = invalids.Where(i => i.Errors == "Duplicate Campaign").Count();
                            bulkCampaignUpload.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty").Count();
                            bulkCampaignUpload.Count = campaignsToUpdate.Count();
                            byte[] bytes = MarketingAgencyHelper.CreateExcelData(invalids).ToArray();
                            string fileName = $"InvalidCampaign-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                            string folder = "Campaigns";
                            var key = await _blobStorageService?.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                            bulkCampaignUpload.InvalidS3BucketKey = key;
                        }
                        await _bulkMarketingUploadRepo.UpdateAsync(bulkCampaignUpload);
                        BulkCampaignUploadBackgroundDto backgroundDto = new();
                        if (toCampaigns.Count > 0)
                        {
                            int agencyPerchunk = toCampaigns.Count > 5000 ? 5000 : toCampaigns.Count;
                            var chunks = toCampaigns.Chunk(agencyPerchunk).Select(i => new ConcurrentBag<Campaign>(i));
                            List<Task> tasks = new();
                            var currentUserId = _currentUser.GetUserId();
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                            if (currentUserId == Guid.Empty)
                            {
                                currentUserId = input.CurrentUserId;
                            }
                            Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                            var chunkIndex = 1;
                            foreach (var chunk in chunks.ToList())
                            {
                                backgroundDto = new BulkCampaignUploadBackgroundDto()
                                {
                                    CurrentUserId = currentUserId,
                                    TrackerId = bulkCampaignUpload.Id,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Campaigns = new(chunk),
                                    UserIds = new(bulkCampaignUpload.UserIds ?? new()),
                                    Users = users.ToList()
                                };
                                await ExecuteDBOperationsAsync(backgroundDto);
                                chunkIndex++;
                            }
                        }
                        bulkCampaignUpload.Status = UploadStatus.Completed;
                        bulkCampaignUpload.LastModifiedBy = input.CurrentUserId;
                        bulkCampaignUpload.CreatedBy = input.CurrentUserId;
                        await _bulkMarketingUploadRepo.UpdateAsync(bulkCampaignUpload);
                    }

                    catch (Exception ex)
                    {
                        bulkCampaignUpload = await _bulkMarketingUploadRepo.GetByIdAsync(bulkCampaignUpload.Id);
                        bulkCampaignUpload.Status = UploadStatus.Failed;
                        bulkCampaignUpload.Message = ex.Message;
                        bulkCampaignUpload.LastModifiedBy = input.CurrentUserId;
                        bulkCampaignUpload.CreatedBy = input.CurrentUserId;
                        await _bulkMarketingUploadRepo.UpdateAsync(bulkCampaignUpload);
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CreateBulkProspectleadUploadTrackerUsingEPPlus -> MarketingCampaignHandler()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkProspectleadUploadTrackerUsingEPPlus -> MarketingCampaignHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                Console.WriteLine($"handler() -> Exception:  {JsonConvert.SerializeObject(ex, settings: new() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;
            }
        }

        public async Task ExecuteDBOperationsAsync(BulkCampaignUploadBackgroundDto dto)
        {
            BulkChannelPartnerUploadTracker bulkChannelPartner = new();
            var tracker = (await _bulkMarketingUploadRepo.ListAsync(new GetBulkMarketingCampaignByTrackerIdSpec(dto.TrackerId))).FirstOrDefault();
            try
            {
                try
                {
                    await _campaignRepo.AddRangeAsync(dto.Campaigns);
                    await _dapperRepository.UpdateLatModifiedDateAsync(dto?.TenantInfoDto?.Id, (int)EntityType.Campaign);


                }
                catch (Exception e)
                {

                }
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.Campaigns.Count;
                    if (dto.IsLastChunk)
                    {
                        tracker.Status = UploadStatus.Completed;
                    }
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _bulkMarketingUploadRepo.UpdateAsync(tracker);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == tracker.InvalidCount)
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _bulkMarketingUploadRepo.UpdateAsync(tracker);
            }
        }
    }
}
