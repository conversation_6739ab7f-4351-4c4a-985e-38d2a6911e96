﻿using Dapper;
using Finbuckle.MultiTenant;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Models;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Requests;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.ErrorModule;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Npgsql;

namespace Lrb.Infrastructure.Persistence.Repository
{
    public class LeadRepositoryAsync : ILeadRepositoryAsync
    {
        private ICurrentUser _currentUser;
        private readonly DatabaseSettings _settings;

        public LeadRepositoryAsync(IOptions<DatabaseSettings> options,
            ICurrentUser currentUser)
        {
            _settings = options.Value;
            _currentUser = currentUser;
        }

        public async Task<List<LeadWithEnquiryModel>> GetAllLeadsAsync(GetAllLeadRequestDapper filter, string tenantId)
        {
            int pageSize = filter.PageSize == default ? 10 : filter.PageSize;
            int pageNumber = filter.PageNumber == default ? 1 : filter.PageNumber;
            int skip = ((pageNumber - 1) * pageSize);
            string query = $"create Temp TABLE LeadEnquiries as select \"LeadEnquiries\".\"LeadId\",\"LeadEnquiries\".\"AddressId\",\"LeadEnquiries\".\"EnquiredFor\" ,\"LeadEnquiries\".\"PropertyTypeId\"," +
                $"\r\njson_agg(\"LeadEnquiries\") as Enquiries , row_to_json(\"Addresses\".*) as Address ,row_to_json(\"MasterPropertyTypes\".*) as PropertyType\r\n" +
                $"from \"LeadratBlack\".\"LeadEnquiries\" \r\nLeft join \"LeadratBlack\".\"Addresses\" on \"Addresses\".\"Id\" = \"LeadEnquiries\".\"AddressId\"\r\n" +
                $"Left join \"LeadratBlack\".\"MasterPropertyTypes\" on \"MasterPropertyTypes\".\"Id\" = \"LeadEnquiries\".\"PropertyTypeId\"\r\n" +
                $"group by \"LeadEnquiries\".\"LeadId\",\"LeadEnquiries\".\"EnquiredFor\",\"LeadEnquiries\".\"AddressId\",\"LeadEnquiries\".\"PropertyTypeId\",\"Addresses\".\"Id\",\"MasterPropertyTypes\".\"Id\";" +
                $"\r\nselect   \"Leads\".* , row_to_json(\"LeadTags\") as TagInfo , row_to_json(\"MasterLeadStatuses\") as Status, T.Enquiries,T.Address,T.PropertyType " +
                $"from \"LeadratBlack\".\"Leads\" \r\nLeft join \"LeadratBlack\".\"LeadTags\" on \"LeadTags\".\"LeadId\" = \"Leads\".\"Id\"\r\n" +
                $"Left join \"LeadratBlack\".\"MasterLeadStatuses\" ON \"MasterLeadStatuses\".\"Id\" = \"Leads\".\"StatusId\" Left join LeadEnquiries T On T.\"LeadId\" = \"Leads\".\"Id\"" +
                $" where \"Leads\".\"TenantId\" = @tenantId and \"Leads\".\"IsDeleted\" = @isDeleted ";

            query = await BuildQueryString(filter, query);
            var filters = await BuildDynamicParameters(filter);
            filters.Add("skip", skip);
            filters.Add("pagesize", pageSize);
            await using var connection = new NpgsqlConnection(_settings.ConnectionString);
            try
            {
                query += $"ORDER BY \"Leads\".\"LastModifiedOn\" DESC OFFSET @skip LIMIT @pagesize";
                await connection.OpenAsync();
                var result = await connection.QueryAsync<LeadWithEnquiryModel>(query, new DynamicParameters(filters));
                await connection.CloseAsync();
                return result.ToList();
            }
            catch (Exception ex) {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "LeadRepositoryAsync -> GetAllLeadsAsync()"
                };
                await AddErrorAsync(error);
                throw new Exception($"{ex.Message}");
            }
            finally { await connection.CloseAsync(); }
        }
        public async Task<int> GetAllLeadsCountAsync(GetAllLeadRequestDapper filter, string tenantId)
        {
            var currentUserId = _currentUser.GetUserId();
            string query = $"create Temp TABLE LeadEnquiries as select \"LeadEnquiries\".\"LeadId\",\"LeadEnquiries\".\"AddressId\" ,\"LeadEnquiries\".\"EnquiredFor\" ,\"LeadEnquiries\".\"PropertyTypeId\"," +
                $"\r\njson_agg(\"LeadEnquiries\") as Enquiries , row_to_json(\"Addresses\".*) as Address ,row_to_json(\"MasterPropertyTypes\".*) as PropertyType\r\n" +
                $"from \"LeadratBlack\".\"LeadEnquiries\" \r\nLeft join \"LeadratBlack\".\"Addresses\" on \"Addresses\".\"Id\" = \"LeadEnquiries\".\"AddressId\"\r\n" +
                $"Left join \"LeadratBlack\".\"MasterPropertyTypes\" on \"MasterPropertyTypes\".\"Id\" = \"LeadEnquiries\".\"PropertyTypeId\"\r\n" +
                $"group by \"LeadEnquiries\".\"LeadId\",\"LeadEnquiries\".\"AddressId\",\"LeadEnquiries\".\"EnquiredFor\",\"LeadEnquiries\".\"PropertyTypeId\",\"Addresses\".\"Id\",\"MasterPropertyTypes\".\"Id\";" +
                $"\r\nselect  count(\"Leads\".*)" +
                $"from \"LeadratBlack\".\"Leads\" \r\nLeft join \"LeadratBlack\".\"LeadTags\" on \"LeadTags\".\"LeadId\" = \"Leads\".\"Id\"\r\n" +
                $"Left join \"LeadratBlack\".\"MasterLeadStatuses\" ON \"MasterLeadStatuses\".\"Id\" = \"Leads\".\"StatusId\" Left join LeadEnquiries T On T.\"LeadId\" = \"Leads\".\"Id\"" +
                $"where  \"Leads\".\"TenantId\" = @tenantId and \"Leads\".\"IsDeleted\" = @isDeleted ";

            query = await BuildQueryString(filter, query);
            var filters = await BuildDynamicParameters(filter);
            await using var connection = new NpgsqlConnection(_settings.ConnectionString);
            try
            {
                connection.Open();
                var result = connection.ExecuteScalar<int>(query, new DynamicParameters(filters));
                return result;
            }
            catch (Exception ex) 
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "LeadRepositoryAsync -> GetAllLeadsCountAsync()"
                };
                await AddErrorAsync(error);
                throw new Exception($"{ex.Message}"); 
            }
            finally { await connection.CloseAsync(); }
        }

        public async Task<string> BuildQueryString(GetAllLeadRequestDapper filter, string query)
        {

            switch (filter.LeadVisibility)
            {
                case BaseLeadVisibility.SelfWithReportee:
                    query += "and \"Leads\".\"AssignTo\" = Any(@selfWithReporteeIds) ";
                    break;
                case BaseLeadVisibility.Self:
                    query += $"and \"Leads\".\"AssignTo\" = @currentUser ";
                    break;
                case BaseLeadVisibility.Reportee:
                    query += $"and \"Leads\".\"AssignTo\" = Any(@reporties) ";
                    break;
                case BaseLeadVisibility.UnassignLead:
                    query += $"and \"Leads\".\"AssignTo\" = @emptyGuid ";
                    break;
                default:
                    break;
            }
            if (filter.EnquiredFor != null)
            {
                query += $"and t.\"EnquiredFor\" = @enquiryFor ";
            }
            if (filter.DateType != null && filter.FromDate != null && filter.FromDate.Value != default && filter.ToDate != null && filter.ToDate.Value != default)
            {
                filter.FromDate = filter.FromDate.HasValue ? filter.FromDate.Value.ConvertFromDateToUtc() : null;
                filter.ToDate = filter.ToDate.HasValue ? filter.ToDate.Value.ConvertToDateToUtc() : null;
                switch (filter.DateType)
                {
                    case DateType.ReceivedDate:
                        query += $"and Date(\"Leads\".\"CreatedOn\") >= Date(@fromDate)  and Date(\"Leads\".\"CreatedOn\") <= Date(@todate) ";
                        break;
                    case DateType.ScheduledDate:
                        query += $"and Date(\"Leads\".\"ScheduledDate\") >= Date(@fromDate)  and Date(\"Leads\".\"ScheduledDate\") <= Date(@toDate) ";
                        break;
                    case DateType.ModifiedDate:
                        query += $"and Date(\"Leads\".\"LastModifiedOn\") >= Date(@fromDate)  and Date(\"Leads\".\"LastModifiedOn\") <= Date(@todate) ";
                        break;
                    case DateType.All:
                        query += "and (Date(\"Leads\".\"CreatedOn\") >= Date(@fromDate)  and Date(\"Leads\".\"CreatedOn\") <= Date(@todate)" +
                                    "or Date(\"Leads\".\"ScheduledDate\") >= Date(@fromDate)  and Date(\"Leads\".\"ScheduledDate\") <= Date(@toDate)" +
                                    "or Date(\"Leads\".\"LastModifiedOn\") >= Date(@fromDate)  and Date(\"Leads\".\"LastModifiedOn\") <= Date(@todate))";
                        break;
                    default:
                        break;
                }
            }
            if (filter.AssignTo != null && filter.AssignTo != Guid.Empty)
            {
                query += $"and \"Leads\".\"AssignTo\" =@assignTo ";
            }
            if (!string.IsNullOrWhiteSpace(filter.SearchByNameOrNumber))
            {
                query += $"and lower(\"Leads\".\"Name\") LIKE CONCAT('%',@name,'%') ";
            }
            switch (filter.FilterType)
            {
                case LeadFilterTypeWeb.New:
                    query += $"and \"MasterLeadStatuses\".\"Status\" =@new ";
                    break;
                case LeadFilterTypeWeb.Today:
                    query += $"and Date(\"Leads\".\"ScheduledDate\") >= Date(@sheduledDate)  and Date(\"Leads\".\"ScheduledDate\") < Date(@addOneDay) ";
                    break;
                case LeadFilterTypeWeb.Overdue:
                    query += $"and not \"MasterLeadStatuses\".\"Status\" = Any(@dCNStatuses) and not \"MasterLeadStatuses\".\"BaseId\" = Any(@dCNStatusesIds) and Date(\"Leads\".\"ScheduledDate\") < Date(@dateTime) ";
                    break;
                case LeadFilterTypeWeb.NotInterested:
                    query += $"and \"MasterLeadStatuses\".\"BaseId\" =@notInterestedId ";
                    break;
                case LeadFilterTypeWeb.Dropped:
                    query += $"and \"MasterLeadStatuses\".\"BaseId\" =@droppedId ";
                    break;
                case LeadFilterTypeWeb.Escalated:
                    query += $"and \"LeadTags\".\"IsEscalated\" = @isEscalated ";
                    break;
                case LeadFilterTypeWeb.Pending:
                    query += $"and \"MasterLeadStatuses\".\"Status\" =@pending ";
                    break;
                case LeadFilterTypeWeb.Booked:
                    query += $"and \"MasterLeadStatuses\".\"Status\" =@booked ";
                    break;
                case LeadFilterTypeWeb.All:
                default:
                    break;
            }
            return query;
        }
        public async Task<Dictionary<string, object>> BuildDynamicParameters(GetAllLeadRequestDapper filter)
        {
            var dCNStatuses = new List<string>() { "not_interested", "dropped", "booked" };
            var dCNStatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };
            var subIds = _currentUser.GetSubordinateIds() ?? new();
            var tenantId = _currentUser.GetTenant();
            var reporteeIds = subIds.Where(i => i != _currentUser.GetUserId() && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            var currentUserId = _currentUser.GetUserId();
            var filters = new Dictionary<string, object>();
            filters.Add("@tenantId", tenantId);
            filters.Add("@reporties", reporteeIds.ToList());
            filters.Add("@selfWithReporteeIds", selfWithReporteeIds.ToList());
            filters.Add("@currentUser", currentUserId);
            filters.Add("@emptyGuid", Guid.Empty);
            filters.Add("@isDeleted", false);
            if (filter.EnquiredFor != null)
            {
                filters.Add("@enquiryFor", filter.EnquiredFor);
            }
            if (filter.DateType != null && filter.FromDate != null && filter.FromDate.Value != default && filter.ToDate != null && filter.ToDate.Value != default)
            {
                filters.Add("@fromDate", filter.FromDate.Value.ToUniversalTime().Date);
                filters.Add("@toDate", filter.ToDate.Value.ToUniversalTime().Date);
            }
            if (filter.AssignTo != null && filter.AssignTo != default)
            {
                filters.Add("@assignTo", filter.AssignTo);
            }
            if (filter.SearchByNameOrNumber != null && filter.SearchByNameOrNumber != null)
            {
                filters.Add("@name", filter.SearchByNameOrNumber);
            }
            switch (filter.FilterType)
            {
                case LeadFilterTypeWeb.New:
                    filters.Add("@new", "new");
                    break;
                case LeadFilterTypeWeb.Today:
                    filters.Add("@sheduledDate", DateTime.UtcNow.Date);
                    filters.Add("@addOneDay", DateTime.UtcNow.Date.AddDays(1));
                    break;
                case LeadFilterTypeWeb.Overdue:
                    filters.Add("@dCNStatuses", dCNStatuses);
                    filters.Add("@dCNStatusesIds", dCNStatusesIds);
                    filters.Add("@dateTime", DateTime.UtcNow);
                    break;
                case LeadFilterTypeWeb.NotInterested:
                    filters.Add("@notInterestedId", Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeWeb.Dropped:
                    filters.Add("@droppedId", Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"));
                    break;
                case LeadFilterTypeWeb.Escalated:
                    filters.Add("@isEscalated", true);
                    break;
                case LeadFilterTypeWeb.Pending:
                    filters.Add("@pending", "pending");
                    break;
                case LeadFilterTypeWeb.Booked:
                    filters.Add("@booked", "booked");
                    break;
                case LeadFilterTypeWeb.All:
                default:
                    break;
            }
            return filters;
        }

        public async Task<Response<bool>> AddErrorAsync(LrbError error)
        {
            return new(true);
            //NpgsqlConnection? connection = null;
            //try
            //{
            //    connection = new NpgsqlConnection(_settings.ConnectionString);
            //    await connection.OpenAsync();
            //    error.Id = Guid.NewGuid();
            //    error.IsDeleted = false;
            //    var tenantId = _currentUser.GetTenant();
            //    var userId = _currentUser.GetUserId();
            //    error.CreatedBy = error.UserId;
            //    error.LastModifiedBy = error.CreatedBy;
            //    error.CreatedOn = DateTime.UtcNow;
            //    error.LastModifiedOn = error.CreatedOn;
            //    var query = $" Insert into \"LeadratBlack\".\"Errors\"\r\n(\"Id\", \"InnerException\", \"StackTrace\", \"ErrorMessage\", \"IsDeleted\", \r\n \"CreatedBy\", " +
            //        $"\"CreatedOn\", \"LastModifiedBy\", \"LastModifiedOn\",\r\n \"UserId\", \"ErrorSource\",\"TenantId\",\"ErrorModule\")" +
            //        $"Values ( @Id, @InnerException, @StackTrace, @ErrorMessage, @IsDeleted, @CreatedBy," +
            //        $" @CreatedOn, @LastModifiedBy, @LastModifiedOn, '{userId}', @ErrorSource, '{tenantId}', @ErrorModule)";
            //    var affectedRow = await connection.ExecuteAsync(query, error);
            //    await connection.CloseAsync();
            //    return new Response<bool>(affectedRow > 0);
            //}
            //catch { if (connection != null) { await connection.CloseAsync(); } return new Response<bool>(false); }
            //finally { if (connection != null) { await connection.CloseAsync(); } }
        }
    }
}



