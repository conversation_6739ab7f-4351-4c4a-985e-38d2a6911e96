﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application
{
    public class PropertyConfig : IEntityTypeConfiguration<Lrb.Domain.Entities.Property>
    {
        public void Configure(EntityTypeBuilder<Lrb.Domain.Entities.Property> builder)
        {
            builder.IsMultiTenant();
            builder.HasOne(p => p.Dimension)
                .WithOne(p => p.Property)
                .HasForeignKey<PropertyDimension>(p => p.PropertyId);
            builder.HasOne(p => p.OwnerDetails)
                .WithOne(p => p.Property)
                .HasForeignKey<PropertyOwnerDetails>(p => p.PropertyId);
            builder.HasOne(p => p.MonetaryInfo)
                .WithOne(p => p.Property)
                .HasForeignKey<PropertyMonetaryInfo>(p => p.PropertyId);
            builder.HasOne(p => p.TagInfo)
                .WithOne(p => p.Property)
                .HasForeignKey<PropertyTagInfo>(p => p.PropertyId);
            builder.HasMany(i => i.Leads)
               .WithMany(i => i.Properties);
            builder.Property(i => i.Links).Metadata.SetProviderClrType(null);
            builder.HasMany(i => i.Prospects)
                .WithMany(i => i.Properties);
            builder.HasMany(i => i.BookedDetails)
                .WithMany(i => i.Properties);
            builder.Property(i => i.NoOfFloorsOccupied).Metadata.SetProviderClrType(null);
            builder.HasOne(p => p.TenantContactInfo)
                .WithOne(p => p.Property);
            builder.HasMany(i => i.ListingSources)
                .WithMany(i => i.Properties);
            builder.HasMany(i => i.ListingSourceAddresses)
                .WithMany(i => i.Properties);
            builder.HasOne(p => p.UserAssignment) // one-to-one
                 .WithOne()
                 .HasForeignKey<Domain.Entities.Property>(p => p.UserAssignmentId);
            builder.HasMany(p => p.UserAssignments) // one-to-many
                .WithOne(ua => ua.Property)
                .HasForeignKey(ua => ua.PropertyId);
            builder.HasMany(i => i.PropertyOwnerDetails)
               .WithMany(i => i.Properties);
            builder.Property(i => i.SourceReferenceIds).Metadata.SetProviderClrType(null);
        }
    }
}
