﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Domain.Entities
{
    public class BulkChannelPartnerUploadTracker : AuditableEntity, IAggregateRoot
    {
        public int TotalCount { get; set; }
        public int DistictLeadCount { get; set; }
        public int TotalUploadedCount { get; set; }
        public int DuplicateCount { get; set; }
        public int ChannelPartnerCount { get; set; }
        public int InvalidCount { get; set; }
        public string? S3BucketKey { get; set; }
        public string? InvalidDataS3BucketKey { get; set; }
        public string? SheetName { get; set; }
        [Column(TypeName = "jsonb")]
        public Dictionary<ChannelPartnerDataColumn, string>? MappedColumnsData { get; set; }
        public UploadStatus Status { get; set; }
        public string? Message { get; set; }
    }
}
