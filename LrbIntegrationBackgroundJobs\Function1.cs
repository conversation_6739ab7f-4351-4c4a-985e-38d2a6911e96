using Azure.Core;
using LrbIntegrationBackgroundJobs.Repositories;
using LrbIntegrationBackgroundJobs.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace LrbIntegrationBackgroundJobs
{
    // need to change the qa and prd function dubug check while deploying
    public class Function1
    {
        //qa functions make sure that, need to change this while deploying in qa
#if DEBUG

        #region Qa & Dev

        #region SetLeadStatusToPending
        [FunctionName("QaSetLeadStatusToPending")]
        public async static Task QaSetLeadStatusToPending([TimerTrigger("0 30 18 * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                //log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("qa");
                IServiceProvider provider = startup.ConfigureServices();
                ITenantIndependentRepository _repo = provider.GetRequiredService<ITenantIndependentRepository>();
                await _repo.SetLeadStatusToPending();
                //log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling QaSetLeadStatusToPending." + ex.Message);
            }
        }

        [FunctionName("DevSetLeadStatusToPending")]
        public async static Task DevSetLeadStatusToPending([TimerTrigger("0 30 18 * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                //log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("dev");
                IServiceProvider provider = startup.ConfigureServices();
                ITenantIndependentRepository _repo = provider.GetRequiredService<ITenantIndependentRepository>();
                await _repo.SetLeadStatusToPending();
                //log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling DevSetLeadStatusToPending." + ex.Message);
            }
        }
        #endregion

        #region JustLead
        [FunctionName("DevJustLeadTrigger")]
        public async static Task DevJustLeadTrigger([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                //log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("dev");
                IServiceProvider provider = startup.ConfigureServices();
                IJustLeadService _repo = provider.GetRequiredService<IJustLeadService>();
                await _repo.ProcessJustLeadAccountsAsync();
                //log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling DevJustLeadTrigger." + ex.Message);
            }
        }

        [FunctionName("QaJustLeadTrigger")]
        public async static Task QaJustLeadTrigger([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                //log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("qa");
                IServiceProvider provider = startup.ConfigureServices();
                IJustLeadService _repo = provider.GetRequiredService<IJustLeadService>();
                await _repo.ProcessJustLeadAccountsAsync();
                //log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling QaJustLeadTrigger." + ex.Message);
            }
        }
        #endregion

        #region CommonFloor
        [FunctionName("DevCommonFloorTrigger")]
        public async static Task DevCommonFloorTrigger([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                //log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("dev");
                IServiceProvider provider = startup.ConfigureServices();
                ICommonFloorService _repo = provider.GetRequiredService<ICommonFloorService>();
                await _repo.ProcessCommonFloorAccountsAsync();
                //log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling DevCommonFloorTrigger." + ex.Message);
            }
        }

        [FunctionName("QaCommonFloorTrigger")]
        public async static Task QaCommonFloorTrigger([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                //log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("qa");
                IServiceProvider provider = startup.ConfigureServices();
                ICommonFloorService _repo = provider.GetRequiredService<ICommonFloorService>();
                await _repo.ProcessCommonFloorAccountsAsync();
                //log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling QaCommonFloorTrigger." + ex.Message);
            }
        }
        #endregion

        #region Property Finder

        ////[FunctionName("QaPropertyFInder")]
        ////public async static Task QABayutTrigger([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        ////{
        ////    try
        ////    {
        //////        log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
        //////        Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
        ////        var startup = new Startup("qa");
        ////        IServiceProvider provider = startup.ConfigureServices();
        ////        IPropertyFinderService _repo = provider.GetRequiredService<IPropertyFinderService>();
        ////        await _repo.ProcessPropertyFinderAccountsAsync();
        //////        log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
        //////        Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
        ////    }
        ////    catch (Exception ex)
        ////    {
        ////        log.LogInformation("Exception details while calling QaPropertyFInder." + ex.Message);
        ////    }
        ////}

        [FunctionName("QaPropertyFInderV2")]
        public async static Task QABayutTriggerV2([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                //        log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                //        Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("qa");
                IServiceProvider provider = startup.ConfigureServices();
                IPropertyFinderServiceV2 _repo = provider.GetRequiredService<IPropertyFinderServiceV2>();
                await _repo.V2ProcessPropertyFinderAccountsAsync();
                //        log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                //        Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling QaPropertyFInderV2." + ex.Message);
            }
        }
        #endregion

        #region Bayut

        //[FunctionName("QaBayutTrigger")]
        //public async static Task QaBayutTrigger([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        //{
        //    try
        //    {
        //        //        log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
        //        //        Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
        //        var startup = new Startup("Qa");
        //        IServiceProvider provider = startup.ConfigureServices();
        //        IBayutService _repo = provider.GetRequiredService<IBayutService>();
        //        await _repo.ProcessBayutAccountsAsync();
        //        //        log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
        //        //        Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
        //    }
        //    catch (Exception ex)
        //    {
        //        log.LogInformation("Exception details while calling QaBayutTrigger." + ex.Message);
        //    }
        //}

        #endregion

        #region Set Property Listing Status
        [FunctionName("QaSetPropertyListingStatus")]
        public async static Task QaSetPropertyListingStatus([TimerTrigger("0 */30 * * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("qa");
                IServiceProvider provider = startup.ConfigureServices();
                IPropertyListingRepository _repo = provider.GetRequiredService<IPropertyListingRepository>();
                await _repo.SetPropertyListingStatus();
                log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling QaSetPropertyListingStatus." + ex.Message);
            }
        }
        #endregion

        #region Dubizzle

        [FunctionName("QaDubizzleTrigger")]
        public async static Task PrdDubizzleTrigger([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                //log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("qa");
                IServiceProvider provider = startup.ConfigureServices();
                IDubizzleService _repo = provider.GetRequiredService<IDubizzleService>();
                await _repo.ProcessDubizzleAccountsAsync();
                //log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling QaDubizzleTrigger." + ex.Message);
            }
        }

        #endregion

        #region Gmail
        //[FunctionName("GmailWebhook")]
        //public static async Task<IActionResult> RunGmailJob(
        //    [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "{env}")] HttpRequest req, string env,
        //    ILogger log)
        //{
        //    try
        //    {
        //        //log.LogInformation("C# HTTP trigger function processed a request.");
        //        var startup = new Startup(env);
        //        IServiceProvider provider = startup.ConfigureServices();
        //        IGmailService _repo = provider.GetRequiredService<IGmailService>();
        //        var result = await _repo.ProcessGmailMessageAsync(req, env, log);
        //        //log.LogInformation($"C# GmailWebhook trigger function ended at: {DateTime.Now}" + result);
        //        return new OkObjectResult(result);
        //    }
        //    catch (Exception ex)
        //    {
        //        log.LogInformation("Exception details while calling FaceBookWebhook." + ex.Message);
        //        return new OkObjectResult(ex.Message);
        //    }
        //}
        #endregion

        #endregion

#endif

        //prd functions make sure that, need to change this while deploying in prd
#if !DEBUG

        #region Prd

        #region SetLeadStatusToPending
        [FunctionName("PrdSetLeadStatusToPending")]
        public async static Task PrdSetLeadStatusToPending([TimerTrigger("0 30 18 * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                //log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("prd");
                IServiceProvider provider = startup.ConfigureServices();
                ITenantIndependentRepository _repo = provider.GetRequiredService<ITenantIndependentRepository>();
                await _repo.SetLeadStatusToPending();
                //log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling PrdSetLeadStatusToPending." + ex.Message);
            }
        }

        #endregion

        #region JustLead

        [FunctionName("PrdJustLeadTrigger")]
        public async static Task PrdJustLeadTrigger([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                //log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("prd");
                IServiceProvider provider = startup.ConfigureServices();
                IJustLeadService _repo = provider.GetRequiredService<IJustLeadService>();
                await _repo.ProcessJustLeadAccountsAsync();
                //log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling QaJustLeadTrigger." + ex.Message);
            }
        }

        #endregion

        #region CommonFloor

        [FunctionName("PrdCommonFloorTrigger")]
        public async static Task PrdCommonFloorTrigger([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                //log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("prd");
                IServiceProvider provider = startup.ConfigureServices();
                ICommonFloorService _repo = provider.GetRequiredService<ICommonFloorService>();
                await _repo.ProcessCommonFloorAccountsAsync();
                //log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling QaCommonFloorTrigger." + ex.Message);
            }
        }
        #endregion

        #region Gmail
        [FunctionName("GmailTrigger")]
        public async static Task GmailTrigger([TimerTrigger("0 30 19 */3 * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                //log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                string apiGatewayUrl = $"https://prd-lrb-webapi.leadrat.com/api/v1/integration/gmail/watch/all";
                RestClient client = new(apiGatewayUrl);
                RestRequest request = new();
                var res = await client.GetAsync(request);
                //log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now} " + res);
                //Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now} " + res);
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling GmailTrigger." + ex.Message);
            }
        }
        #endregion

        #region FaceBook 
        [FunctionName("FaceBookWebhook")]
        public static async Task<IActionResult> FaceBookWebhook(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "{env}")] HttpRequest req, string env,
            ILogger log)
        {
            try
            {
                log.LogInformation($"C# FaceBookWebhook trigger function started at: {DateTime.Now}");
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"C# FaceBookWebhook trigger function in progress at: {DateTime.Now}" + requestBody);
                var startup = new Startup(env);
                IServiceProvider provider = startup.ConfigureServices();
                IFacebookDataRepositoryAsync _repo = provider.GetRequiredService<IFacebookDataRepositoryAsync>();
                var result = await _repo.ProcessFacebookWebhookAsync(requestBody, env);
                log.LogInformation($"C# FaceBookWebhook trigger function ended at: {DateTime.Now}" + result);
                return new OkObjectResult(result);
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling FaceBookWebhook." + ex.Message);
                return new OkObjectResult(ex.Message);
            }
        }
        #endregion

        #region Bayut

        [FunctionName("PrdBayutTrigger")]
        public async static Task PrdBayutTrigger([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("prd");
                IServiceProvider provider = startup.ConfigureServices();
                IBayutService _repo = provider.GetRequiredService<IBayutService>();
                await _repo.ProcessBayutAccountsAsync(log);
                log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling PrdBayutTrigger." + ex.Message);
            }
        }

        #endregion

        #region Property Finder
        [FunctionName("PrdPropertyFInder")]
        public async static Task PrdPropertyFinderTrigger([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                //log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("prd");
                IServiceProvider provider = startup.ConfigureServices();
                IPropertyFinderService _repo = provider.GetRequiredService<IPropertyFinderService>();
                await _repo.ProcessPropertyFinderAccountsAsync();
                //log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling PrdPropertyFInder." + ex.Message);
            }
        }

        [FunctionName("PrdPropertyFInderV2")]
        public async static Task PrdBayutTriggerV2([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                //        log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                //        Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("prd");
                IServiceProvider provider = startup.ConfigureServices();
                IPropertyFinderServiceV2 _repo = provider.GetRequiredService<IPropertyFinderServiceV2>();
                await _repo.V2ProcessPropertyFinderAccountsAsync();
                //        log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                //        Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling PrdPropertyFInderV2." + ex.Message);
            }
        }
        #endregion

        #region Set Property Listing Status
        //[FunctionName("PrdSetPropertyListingStatus")]
        //public async static Task PrdSetPropertyListingStatus([TimerTrigger("0 */30 * * * *")] TimerInfo myTimer, ILogger log)
        //{
        //    try
        //    {
        //        log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
        //        Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
        //        var startup = new Startup("prd");
        //        IServiceProvider provider = startup.ConfigureServices();
        //        IPropertyListingRepository _repo = provider.GetRequiredService<IPropertyListingRepository>();
        //        await _repo.SetPropertyListingStatus();
        //        log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
        //        Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
        //    }
        //    catch (Exception ex)
        //    {
        //        log.LogInformation("Exception details while calling PrdSetPropertyListingStatus." + ex.Message);
        //    }
        //}
        #endregion

        #region Dubizzle

        [FunctionName("PrdDubizzleTrigger")]
        public async static Task PrdDubizzleTrigger([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer, ILogger log)
        {
            try
            {
                //log.LogInformation($"C# Timer trigger function started at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function started at: {DateTime.Now}");
                var startup = new Startup("prd");
                IServiceProvider provider = startup.ConfigureServices();
                IDubizzleService _repo = provider.GetRequiredService<IDubizzleService>();
                await _repo.ProcessDubizzleAccountsAsync();
                //log.LogInformation($"C# Timer trigger function ended at: {DateTime.Now}");
                //Console.WriteLine($"C# Timer trigger function ended at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling PrdBayutTrigger." + ex.Message);
            }
        }

        #endregion

         #region Gmail
        [FunctionName("GmailWebhook")]
        public static async Task<IActionResult> RunGmailJob(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = "GmailWebhook/{env}")] HttpRequest req, string env,
            ILogger log)
        {
            try
            {
                //log.LogInformation("C# HTTP trigger function processed a request.");
                var startup = new Startup(env);
                IServiceProvider provider = startup.ConfigureServices();
                IGmailService _repo = provider.GetRequiredService<IGmailService>();
                var result = await _repo.ProcessGmailMessageAsync(req, env, log);
                //log.LogInformation($"C# GmailWebhook trigger function ended at: {DateTime.Now}" + result);
                return new OkObjectResult(result);
            }
            catch (Exception ex)
            {
                log.LogInformation("Exception details while calling FaceBookWebhook." + ex.Message);
                return new OkObjectResult(ex.Message);
            }
        }
        #endregion

        #endregion

#endif
    }
}
