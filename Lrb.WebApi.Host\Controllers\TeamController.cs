﻿using Lrb.Application.Team.Web;
using Lrb.Application.Team.Web.Dtos;
using Lrb.Application.Team.Web.Requests;
using Lrb.Application.UserDetails.Web.Dtos;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class TeamController : VersionedApiController
    {

        [HttpGet]
        [TenantIdHeader]
        [OpenApiOperation("Get all teams details.", "")]
        public Task<PagedResponse<TeamDto, string>> SearchAsync([FromQuery] GetAllTeamRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("id")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Teams)]
        [OpenApiOperation("Get team details.", "")]
        public Task<PagedResponse<GetTeamByIdDto, string>> GetAsync([FromQuery] GetTeamByIdRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpDelete]
        [TenantIdHeader]
        [OpenApiOperation("Delete a team.", "")]
        public Task<Response<bool>> DeleteAsync(List<Guid> id)
        {
            return Mediator.Send(new DeleteTeamRequest(id));
        }

        [HttpDelete("Users/Delete")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Teams)]
        [OpenApiOperation("Remove users from team.", "")]
        public Task<Response<bool>> RemoveTeamUserAsync(RemoveTeamUserRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("UnAssignedUsers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Teams)]
        [OpenApiOperation("Get all team unassigned user list.", "")]
        public Task<Response<List<UserListDto>>> UnAssignedUser()
        {
            return Mediator.Send(new GetAllUnassignedUsersRequest());
        }

        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Teams)]
        [OpenApiOperation("Create a new team.", "")]
        public Task<Response<Guid>> CreateAsync(CreateTeamRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPut]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Teams)]
        [OpenApiOperation("Update a team.", "")]
        public async Task<Response<Guid>> UpdateAsync(UpdateTeamRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("assignLeads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Teams)]
        [OpenApiOperation("Assign leads to team.", "")]
        public Task<Response<Guid>> AssignLeadsAsync(LeadsAssignRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("export/batch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Teams)]
        [OpenApiOperation("export team by excel.", "")]
        public Task<Response<Guid>> ExportTeamAsync(RunAWSBatchForTeamReportRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("retention")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Teams)]
        [OpenApiOperation("Create Retention Team", "")]
        public Task<Response<Guid>> RetentionTeamAsync(CreateRetentionTeamRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPut("retention")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Teams)]
        [OpenApiOperation("Update retention team.", "")]
        public Task<Response<bool>> UpdateRetentionTeamAsync(UpdateRetentionTeamRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("retention")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Teams)]
        [OpenApiOperation("Get all retention teams.", "")]
        public Task<Response<List<ViewRetentionTeamsDto>>> GetRetentionTeamsAsync()
        {
            return Mediator.Send(new GetAllRetentionTeamsRequest());
        }

        [HttpDelete("retention/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Teams)]
        [OpenApiOperation("Delete retention team.", "")]
        public Task<Response<bool>> DeleteRetentionTeamAsync(Guid id)
        {
            return Mediator.Send(new DeleteRetentionTeamRequest(id));
        }

        [HttpGet("export/tracker")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Teams)]
        [OpenApiOperation("Team export tracker.", "")]
        public Task<PagedResponse<ExportUserTrackerDto, string>> ExportTeamTrackerAsync([FromQuery] TeamExportTrackerRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("configuration")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Teams)]
        [OpenApiOperation("Get all teams with configuration.", "")]
        public Task<Response<List<ViewTeamLeadRotationInfoDto>>> GetTeamWithConfigurationAsync()
        {
            return Mediator.Send(new GetTeamWithConfigurationRequest());
        }
    }
}
