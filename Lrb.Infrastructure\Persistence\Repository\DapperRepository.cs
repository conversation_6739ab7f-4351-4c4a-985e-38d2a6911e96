using Dapper;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Dashboard.Web.Dtos;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.GlobalSettings.Common;
using Lrb.Application.GlobalSettings.Mobile.Dto;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.Multitenancy;
using Lrb.Application.OrgProfile.Web.Dtos;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.PushNotification;
using Lrb.Application.PushNotification.Mobile.Requests;
using Lrb.Application.Subscription.Web.Dtos;
using Lrb.Application.TempProject.Dtos;
using Lrb.Application.UserDetails.Web;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Domain.Common.Contracts;
using Lrb.Domain.Entities;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Multitenancy;
using Lrb.Infrastructure.Persistence;
using Lrb.Infrastructure.Persistence.Context;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Npgsql;
using Serilog;
using System.Data;


public partial class DapperRepository : IDapperRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly DatabaseSettings _settings;
    private readonly IServiceProvider _provider;
    private readonly LrbTenantInfo? _tenantInfo;
    private readonly IDisplayIndexPrefixService _displayIndexPrefixService;
    private readonly Serilog.ILogger _logger;

    public DapperRepository(ApplicationDbContext dbContext, IOptions<DatabaseSettings> options, IServiceProvider provider,
        LrbTenantInfo? tenantInfo, IDisplayIndexPrefixService displayIndexPrefixService, Serilog.ILogger logger)
    {
        _dbContext = dbContext;
        _settings = options.Value;
        _tenantInfo = tenantInfo;
        _provider = provider;
        _displayIndexPrefixService = displayIndexPrefixService;
        _logger = logger;
    }
    public string GetConnectionStringAsync()
    {
        return string.IsNullOrEmpty(_tenantInfo?.ConnectionString) ? _settings.ConnectionString : _tenantInfo.ConnectionString;
    }
    public string GetReadReplicaConnectionString()
    {
        return !string.IsNullOrEmpty(_tenantInfo?.ReadReplicaConnectionString) ? _tenantInfo.ReadReplicaConnectionString :
            !string.IsNullOrEmpty(_tenantInfo?.ConnectionString) ? _tenantInfo.ConnectionString :
            !string.IsNullOrEmpty(_settings.ReadReplicaConnectionString) ? _settings.ReadReplicaConnectionString :
            _settings.ConnectionString;
    }
    public async Task<IReadOnlyList<T>> QueryAsync<T>(string sql, object? param = null, IDbTransaction? transaction = null, CancellationToken cancellationToken = default)
    where T : class, IEntity =>
        (await _dbContext.Connection.QueryAsync<T>(sql, param, transaction))
            .AsList();
    public async Task<IReadOnlyList<T>> QueryAsync<T>(string sql, IDbTransaction? transaction = null, CancellationToken cancellationToken = default)
    where T : class =>
        (await _dbContext.Connection.QueryAsync<T>(sql, transaction))
            .AsList();

    public async Task<T?> QueryFirstOrDefaultAsync<T>(string sql, object? param = null, IDbTransaction? transaction = null, CancellationToken cancellationToken = default)
    where T : class, IEntity
    {
        if (_dbContext.Model.GetMultiTenantEntityTypes().Any(t => t.ClrType == typeof(T)))
        {
            sql = sql.Replace("@tenant", _dbContext.TenantInfo.Id);
        }

        return await _dbContext.Connection.QueryFirstOrDefaultAsync<T>(sql, param, transaction);
    }

    public Task<T> QuerySingleAsync<T>(string sql, object? param = null, IDbTransaction? transaction = null, CancellationToken cancellationToken = default)
    where T : class, IEntity
    {
        if (_dbContext.Model.GetMultiTenantEntityTypes().Any(t => t.ClrType == typeof(T)))
        {
            sql = sql.Replace("@tenant", _dbContext.TenantInfo.Id);
        }

        return _dbContext.Connection.QuerySingleAsync<T>(sql, param, transaction);
    }

    public async Task<IEnumerable<T>> QueryStoredProcedureAsync<T>(string schemaName, string spName, object param)
    {
        var conn = _dbContext.Connection;
        var formattedSPName = $"\"{schemaName}\"" + "." + $"\"{spName}\"";
        var res = await conn.QueryAsync<T>(formattedSPName, param, commandType: CommandType.StoredProcedure);
        return res;
    }

    public async Task<T> QueryStoredProcedureFirstOrDefaultAsync<T>(string schemaName, string spName, object param)
    {
        var conn = _dbContext.Connection;
        var formattedSPName = $"\"{schemaName}\"" + "." + $"\"{spName}\"";
        var res = await conn.QueryFirstOrDefaultAsync<T>(formattedSPName, param, commandType: CommandType.StoredProcedure);
        return res;
    }

    public async Task<IEnumerable<T>> QueryStoredProcedureV2Async<T>(string schemaName, string spName, object param)
    {
        // var conn = _dbContext.Connection;
        var conn = new NpgsqlConnection(GetConnectionStringAsync());
        try
        {
            await conn.OpenAsync();
            var formattedSPName = $"\"{schemaName}\"" + "." + $"\"{spName}\"";
            var res = await conn.QueryAsync<T>(formattedSPName, param, commandType: CommandType.StoredProcedure, commandTimeout: 300);
            await conn.CloseAsync();
            return res;
        }
        catch (Exception ex)
        {
            await conn.CloseAsync();
            throw;
        }
    }
    public async Task<IEnumerable<T>> QueryStoredProcedureV3Async<T>(string schemaName, string spName, object param)
    {
        // var conn = _dbContext.Connection;
        var conn = new NpgsqlConnection(GetConnectionStringAsync());
        try
        {
            await conn.OpenAsync();
            var formattedSPName = $"\"{schemaName}\"" + "." + $"\"{spName}\"";
            var res = await conn.QueryAsync<T>(formattedSPName, param, commandType: CommandType.StoredProcedure, commandTimeout: 500);
            await conn.CloseAsync();
            return res;
        }
        catch (Exception ex)
        {
            await conn.CloseAsync();
            throw;
        }
    }
    public async Task<List<string>> V2GetAdminIdsAsync(string tenantId)
    {
        try
        {
            string? getRoleIdQuery = $"SELECT \"Id\" FROM \"Identity\".\"Roles\" WHERE \"TenantId\" = '{tenantId}' AND \"Name\" = 'Admin'";
            var conn = new NpgsqlConnection(GetConnectionStringAsync());
            var adminId = (await conn.QueryAsync<string>(getRoleIdQuery)).FirstOrDefault();
            string? getUsersQuery = $"SELECT \"UserId\" FROM \"Identity\".\"UserRoles\" WHERE \"RoleId\" = '{adminId}'";
            var adminIds = await conn.QueryAsync<string>(getUsersQuery);
            return adminIds?.ToList() ?? new();
        }
        catch (Exception ex)
        {
            throw;
        }
    }
    public async Task<IEnumerable<T>> QueryStoredProcedureV3Async<T>(string schemaName, string spName, object param, List<string> paramNames)
    {
        var conn = new NpgsqlConnection(GetConnectionStringAsync());
        try
        {
            await conn.OpenAsync();
            var formattedSPName = $"SELECT * FROM \"{schemaName}\"" + "." + $"\"{spName}\"";
            if (paramNames?.Any() ?? false)
            {
                formattedSPName = formattedSPName + $"(";
                paramNames?.ForEach(i => formattedSPName = formattedSPName + $"@{i}");
                formattedSPName = formattedSPName + $")";
            }
            var result = await conn.QueryAsync<T>(formattedSPName, param, commandTimeout: 300);
            await conn.CloseAsync();
            return result;
        }
        catch (Exception ex)
        {
            await conn.CloseAsync();
            throw;
        }
    }

    public async Task<int> QueryStoredProcedureCountV2Async(string schemaName, string spName, object param)
    {
        //var conn = _dbContext.Connection;
        var conn = new NpgsqlConnection(GetConnectionStringAsync());
        try
        {
            var formattedSPName = $"\"{schemaName}\"" + "." + $"\"{spName}\"";
            var res = await conn.ExecuteScalarAsync<int>(formattedSPName, param, commandType: CommandType.StoredProcedure, commandTimeout: 300);
            return res;
        }
        catch (Exception ex)
        {
            await conn.CloseAsync();
            throw;
        }
    }
    public async Task<IEnumerable<T>> QueryStoredProcedureWithNewConnAsync<T>(string schemaName, string spName, object param)
    {
        //var conn = new NpgsqlConnection(_settings.ConnectionString);
        var scope = _provider.CreateAsyncScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        var conn = context.Connection;
        var formattedSPName = $"\"{schemaName}\"" + "." + $"\"{spName}\"";
        var res = await conn.QueryAsync<T>(formattedSPName, param, commandType: CommandType.StoredProcedure);
        return res;
    }
    public async Task<int> QueryStoredProcedureCountAsync(string schemaName, string spName, object param)
    {
        var conn = _dbContext.Connection;
        var formattedSPName = $"\"{schemaName}\"" + "." + $"\"{spName}\"";
        var res = await conn.ExecuteScalarAsync<int>(formattedSPName, param, commandType: CommandType.StoredProcedure);
        return res;
    }
    public async Task<IEnumerable<Guid>> GetSubordinateIdsAsync(Guid userId, string tenantId, bool? viewAllLeads, bool? isAdmin = null)
    {
        var conn = _dbContext.Connection;
        if (isAdmin == null)
        {
            string adminRoleQuery = $"select count(*) as count from \"Identity\".\"UserRoles\" " +
            $"where \"UserId\" = '{userId}' and \"TenantId\" = '{tenantId}' " +
            $"and \"RoleId\" = (Select \"Id\" from \"Identity\".\"Roles\" " +
            $"where \"Name\" = 'Admin' and \"TenantId\" = '{tenantId}')";
            isAdmin = (await conn.QueryAsync<int>(adminRoleQuery)).FirstOrDefault() > 0;
        }
        var subIdQuery = string.Empty;
        if (isAdmin == true || viewAllLeads == true)
        {
            subIdQuery = $"select \"UserId\" from \"LeadratBlack\".\"UserDetails\" where \"TenantId\" = '{tenantId}' and \"IsDeleted\" = 'false' ";
        }
        else
        {
            subIdQuery = $"WITH RECURSIVE tree as " +
                $"(select \"UserId\" from \"LeadratBlack\".\"UserDetails\" where \"UserId\" = '{userId}' and \"IsDeleted\" = 'false' " +
                $"and \"TenantId\" = '{tenantId}' union select u.\"UserId\" from \"LeadratBlack\".\"UserDetails\" " +
                $"as u join tree t on t.\"UserId\" = u.\"ReportsTo\" and u.\"IsDeleted\" = 'false') SELECT \"UserId\" FROM tree;";
        }
        var res = await conn.QueryAsync<Guid>(subIdQuery);
        return res;
    }

    public async Task<IEnumerable<Guid>> GetSubordinateIdsV2Async(Guid userId, string tenantId, bool? viewAllLeads, bool? isAdmin = null)
    {
        await using var conn = new NpgsqlConnection(GetConnectionStringAsync());
        if (isAdmin == null)
        {
            string adminRoleQuery = $"select count(*) as count from \"Identity\".\"UserRoles\" " +
                $"where \"UserId\" = '{userId}' and \"TenantId\" = '{tenantId}' " +
                $"and \"RoleId\" = (Select \"Id\" from \"Identity\".\"Roles\" " +
                $"where \"Name\" = 'Admin' and \"TenantId\" = '{tenantId}')";
            isAdmin = (await conn.QueryAsync<int>(adminRoleQuery)).FirstOrDefault() > 0;
        }
        var subIdQuery = string.Empty;
        if (isAdmin == true || viewAllLeads == true)
        {
            subIdQuery = $"select \"UserId\" from \"LeadratBlack\".\"UserDetails\" where \"TenantId\" = '{tenantId}' and \"IsDeleted\" = 'false' ";
        }
        else
        {
            subIdQuery = $"WITH RECURSIVE tree as " +
                $"(select \"UserId\" from \"LeadratBlack\".\"UserDetails\" where \"UserId\" = '{userId}' and \"IsDeleted\" = 'false' " +
                $"and \"TenantId\" = '{tenantId}' union select u.\"UserId\" from \"LeadratBlack\".\"UserDetails\" " +
                $"as u join tree t on t.\"UserId\" = u.\"ReportsTo\" and u.\"IsDeleted\" = 'false') SELECT \"UserId\" FROM tree;";
        }
        var res = await conn.QueryAsync<Guid>(subIdQuery);
        return res;
    }
    public async Task<bool> V2GetDualOwnershipDetails(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetConnectionStringAsync());
        try
        {
            var query = $"SELECT \"IsDualOwnershipEnabled\" FROM \"LeadratBlack\".\"GlobalSettings\" where \"IsDeleted\" = 'false' and \"TenantId\" = '{tenantId}';\r\n";
            var res = await conn.QueryFirstAsync<bool>(query);
            return res;
        }
        catch
        {
            return false;
        }
    }

    public async Task<KeyValuePair<bool, List<Guid>>> GetSubordinateIdsWithAdminAsync(Guid userId, string tenantId, bool? viewAllLeads = null)
    {
        var conn = _dbContext.Connection;
        string adminRoleQuery = $"select count(*) as count from \"Identity\".\"UserRoles\" " +
             $"where \"UserId\" = '{userId}' and \"TenantId\" = '{tenantId}' " +
             $"and \"RoleId\" = (Select \"Id\" from \"Identity\".\"Roles\" " +
             $"where \"Name\" = 'Admin' and \"TenantId\" = '{tenantId}')";
        var isAdmin = (await conn.QueryAsync<int>(adminRoleQuery)).FirstOrDefault() > 0;
        var subIdQuery = string.Empty;
        if (isAdmin == true || viewAllLeads == true)
        {
            subIdQuery = $"select \"UserId\" from \"LeadratBlack\".\"UserDetails\" where \"TenantId\" = '{tenantId}' and \"IsDeleted\" = 'false' ";
        }
        else
        {
            subIdQuery = $"WITH RECURSIVE tree as " +
                $"(select \"UserId\" from \"LeadratBlack\".\"UserDetails\" where \"UserId\" = '{userId}' and \"IsDeleted\" = 'false' " +
                $"and \"TenantId\" = '{tenantId}' union select u.\"UserId\" from \"LeadratBlack\".\"UserDetails\" " +
                $"as u join tree t on t.\"UserId\" = u.\"ReportsTo\" and u.\"IsDeleted\" = 'false') SELECT \"UserId\" FROM tree;";
        }
        var res = await conn.QueryAsync<Guid>(subIdQuery);
        return new KeyValuePair<bool, List<Guid>>(isAdmin, res.ToList());
    }
    public async Task<IEnumerable<Guid>> GetSubordinateIdsAsync(List<Guid> userIds, string tenantId)
    {
        var conn = _dbContext.Connection;
        string adminRoleQuery = $"select count(*) as count from \"Identity\".\"UserRoles\" " +
            $"where \"UserId\" = any(@user_ids) and \"TenantId\" = '{tenantId}' " +
            $"and \"RoleId\" = (Select \"Id\" from \"Identity\".\"Roles\" " +
            $"where \"Name\" = 'Admin' and \"TenantId\" = '{tenantId}')";
        var isAdmin = (await conn.QueryAsync<int>(adminRoleQuery, new { user_ids = userIds.ConvertAll(i => i.ToString()).ToList() })).FirstOrDefault() > 0;
        var subIdQuery = string.Empty;
        if (isAdmin)
        {
            subIdQuery = $"select \"UserId\" from \"LeadratBlack\".\"UserDetails\" where \"TenantId\" = '{tenantId}'";
        }
        else
        {
            subIdQuery = $"WITH RECURSIVE tree as " +
                $"(select \"UserId\" from \"LeadratBlack\".\"UserDetails\" where \"UserId\" = any(@user_ids) " +
                $"and \"TenantId\" = '{tenantId}' union select u.\"UserId\" from \"LeadratBlack\".\"UserDetails\" " +
                $"as u join tree t on t.\"UserId\" = u.\"ReportsTo\") SELECT \"UserId\" FROM tree;";
        }
        var res = await conn.QueryAsync<Guid>(subIdQuery, new { user_ids = userIds });
        return res;
    }
    public async Task<IEnumerable<Guid>> GetSubordinateIdsWithOnlyReporteesAsync(List<Guid> userIds, string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"WITH RECURSIVE tree as " +
                $"(select \"UserId\" from \"LeadratBlack\".\"UserDetails\" where \"UserId\" = any(@user_ids) " +
                $"and \"TenantId\" = '{tenantId}' union select u.\"UserId\" from \"LeadratBlack\".\"UserDetails\" " +
                $"as u join tree t on t.\"UserId\" = u.\"ReportsTo\") SELECT \"UserId\" FROM tree;";
            var res = await conn.QueryAsync<Guid>(query, new { user_ids = userIds });
            return res;
        }
        catch (Exception ex)
        {
            throw;
        }
    }

    public async Task<IEnumerable<Guid>> GetSubordinateIdsWithOnlyReporteesV2Async(List<Guid> userIds, string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        var query = $"WITH RECURSIVE tree as " +
                $"(select \"UserId\" from \"LeadratBlack\".\"UserDetails\" where \"UserId\" = any(@user_ids) " +
                $"and \"TenantId\" = '{tenantId}' union select u.\"UserId\" from \"LeadratBlack\".\"UserDetails\" " +
                $"as u join tree t on t.\"UserId\" = u.\"ReportsTo\") SELECT \"UserId\" FROM tree;";
        var res = await conn.QueryAsync<Guid>(query, new { user_ids = userIds });
        return res;
    }
    public async Task<IEnumerable<Guid>> GetSubordinateIdsWithColumnNameAsync(List<Guid> userIds, string tenantId, string? columnName)
    {
        var conn = _dbContext.Connection;
        var query = $"WITH RECURSIVE tree as " +
                $"(select \"UserId\" from \"LeadratBlack\".\"UserDetails\" where \"UserId\" = any(@user_ids) " +
                $"and \"TenantId\" = '{tenantId}' union select u.\"UserId\" from \"LeadratBlack\".\"UserDetails\" " +
                $"as u join tree t on t.\"UserId\" = u.\"{columnName}\") SELECT \"UserId\" FROM tree;";
        var res = await conn.QueryAsync<Guid>(query, new { user_ids = userIds });
        return res;
    }
    #region POC
    public async Task<IEnumerable<Guid>> GetSubordinateIdsPOCAsync(Guid userId, string tenantId)
    {
        try
        {
            const string adminRoleName = "Admin";

            var conn = _dbContext.Connection;

            string adminRoleQuery = "SELECT COUNT(*) as count " +
                "FROM \"Identity\".\"UserRoles\" " +
                "WHERE \"UserId\" = @UserId AND \"TenantId\" = @TenantId " +
                "AND \"RoleId\" = (SELECT \"Id\" FROM \"Identity\".\"Roles\" " +
                "WHERE \"Name\" = @RoleName AND \"TenantId\" = @TenantId)";

            var isAdmin = (await conn.QueryAsync<int>(
                adminRoleQuery,
                new { UserId = userId.ToString(), TenantId = tenantId, RoleName = adminRoleName }
            )).FirstOrDefault() > 0;

            string subIdQuery = string.Empty;

            if (isAdmin)
            {
                subIdQuery = "SELECT \"UserId\" FROM \"LeadratBlack\".\"UserDetails\"";
            }
            else
            {
                subIdQuery = "WITH RECURSIVE tree AS " +
                    "(SELECT \"UserId\" FROM \"LeadratBlack\".\"UserDetails\" WHERE \"UserId\" = @UserId AND \"TenantId\" = @TenantId " +
                    "UNION SELECT u.\"UserId\" FROM \"LeadratBlack\".\"UserDetails\" AS u " +
                    "JOIN tree t ON t.\"UserId\" = u.\"ReportsTo\") SELECT \"UserId\" FROM tree;";
            }

            var res = await conn.QueryAsync<Guid>(subIdQuery, new { UserId = userId, TenantId = tenantId });
            return res;
        }
        catch (Exception e)
        {
            await Console.Out.WriteLineAsync(e.Message);
            throw;
        }
    }


    public async Task<IEnumerable<Guid>> GetSubordinateIdsPOCAsync(List<Guid> userIds, string tenantId)
    {
        var conn = _dbContext.Connection;

        string combinedQuery = @"
    WITH admin_role_count AS (
        SELECT COUNT(*) as count 
        FROM ""Identity"".""UserRoles"" 
        WHERE ""UserId"" = ANY(@user_ids) 
          AND ""TenantId"" = @tenant_id
          AND ""RoleId"" = (
            SELECT ""Id"" 
            FROM ""Identity"".""Roles""
            WHERE ""Name"" = 'Admin' AND ""TenantId"" = @tenant_id
        )
    ), subordinates AS (
        SELECT CASE 
            WHEN (SELECT count FROM admin_role_count) > 0 THEN
                (SELECT ""UserId"" FROM ""LeadratBlack"".""UserDetails"")
            ELSE (
                WITH RECURSIVE tree AS (
                    SELECT ""UserId""
                    FROM ""LeadratBlack"".""UserDetails""
                    WHERE ""UserId"" = ANY(@user_ids) AND ""TenantId"" = @tenant_id
                    UNION
                    SELECT u.""UserId""
                    FROM ""LeadratBlack"".""UserDetails"" AS u
                    JOIN tree t ON t.""UserId"" = u.""ReportsTo""
                ) SELECT ""UserId"" FROM tree
            )
        END
    ) SELECT * FROM subordinates;";

        var res = await conn.QueryAsync<Guid>(combinedQuery, new { user_ids = userIds, tenant_id = tenantId });
        return res;
    }

    public async Task<IEnumerable<Guid>> GetSubordinateIdsWithOnlyReporteesPOCAsync(List<Guid> userIds, string tenantId)
    {
        var conn = _dbContext.Connection;
        var query = @"
        WITH RECURSIVE tree AS (
            SELECT ""UserId""
            FROM ""LeadratBlack"".""UserDetails""
            WHERE ""UserId"" = ANY(@user_ids) AND ""TenantId"" = @tenant_id
            UNION
            SELECT u.""UserId""
            FROM ""LeadratBlack"".""UserDetails"" AS u
            JOIN tree t ON t.""UserId"" = u.""ReportsTo""
        ) SELECT ""UserId"" FROM tree;
    ";
        var res = await conn.QueryAsync<Guid>(query, new { user_ids = userIds, tenant_id = tenantId });
        return res;
    }

    public async Task<IEnumerable<Guid>> GetLeadHistoryIdsByBaseLeadStatusPOC(string tenantId, string status)
    {
        if (string.IsNullOrEmpty(tenantId))
        {
            return Enumerable.Empty<Guid>();
        }
        //Creating new connection as its called in parallel
        var conn = new NpgsqlConnection(GetConnectionStringAsync());
        string query = @"
        SELECT ""Id""
        FROM ""LeadratBlack"".""LeadHistories""
        WHERE ""TenantId"" = @tenant_id
          AND ""IsDeleted"" = false
          AND (""BaseLeadStatus""::text ILIKE @status_pattern);
    ";
        var res = await conn.QueryAsync<Guid>(query, new { tenant_id = tenantId, status_pattern = $"%{status}%" });
        return res;
    }

    #endregion

    public async Task<bool> IsAdminAsync(Guid userId, string tenantId)
    {
        var conn = _dbContext.Connection;
        string adminRoleQuery = $"select count(*) as count from \"Identity\".\"UserRoles\" " +
            $"where \"UserId\" = '{userId}' and \"TenantId\" = '{tenantId}' " +
            $"and \"RoleId\" = (Select \"Id\" from \"Identity\".\"Roles\" " +
            $"where \"Name\" = 'Admin' and \"TenantId\" = '{tenantId}')";
        var isAdmin = (await conn.QueryAsync<int>(adminRoleQuery)).FirstOrDefault() > 0;
        return isAdmin;
    }
    public async Task<bool> IsAdminV2Async(Guid userId, string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetConnectionStringAsync());
        string adminRoleQuery = $"select count(*) as count from \"Identity\".\"UserRoles\" " +
            $"where \"UserId\" = '{userId}' and \"TenantId\" = '{tenantId}' " +
            $"and \"RoleId\" = (Select \"Id\" from \"Identity\".\"Roles\" " +
            $"where \"Name\" = 'Admin' and \"TenantId\" = '{tenantId}')";
        var isAdmin = (await conn.QueryAsync<int>(adminRoleQuery)).FirstOrDefault() > 0;
        return isAdmin;
    }
    public async Task<bool> IsAnyAdminAsync(List<Guid> userIds, string tenantId)
    {
        var conn = _dbContext.Connection;
        string adminRoleQuery = $"select count(*) as count from \"Identity\".\"UserRoles\" " +
            $"where \"UserId\" = any(@var1)) and \"TenantId\" = '{tenantId}' " +
            $"and \"RoleId\" = (Select \"Id\" from \"Identity\".\"Roles\" " +
            $"where \"Name\" = 'Admin' and \"TenantId\" = '{tenantId}')";
        var isAdmin = (await conn.QueryAsync<int>(adminRoleQuery, new { @var1 = userIds })).FirstOrDefault() > 0;
        return isAdmin;
    }

    public async Task<bool> CreateDifferentDatabseTenantInfo(CreateTenantRequest request, string connectionString = null)
    {
        try
        {
            var conn = new NpgsqlConnection(connectionString);
            var prefix = _displayIndexPrefixService.GenerateUniquePrefix(request.Id);

            var query = $"INSERT INTO \"MultiTenancy\".\"Tenants\"(\r\n\t\"Id\", \"Identifier\", \"Name\", \"ConnectionString\", \"AdminEmail\", \"IsActive\", \"ValidUpto\", \"Issuer\", \"DisplayPrefix\", \"ResolutionKeys\", \"ReadReplicaConnectionString\") " +
                        $"VALUES ('{request.Id}', '{request.Id}', '{request.Name}', '{request.ConnectionString}', '{request.AdminEmail}', '{true}', '{request.ValidUpto:yyyy-MM-dd HH:mm:ss}', '{request.Issuer}', '{prefix}', '{{}}', '{request.ReadReplicaConnectionString}');";

            var result = await conn.ExecuteAsync(query);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.Information("DubaiDatabase -> POST(Tenant), request: {0}, exception: {1}", JsonConvert.SerializeObject(request), ex.Message);
            return false;
        }

    }

    public async Task<IEnumerable<Guid>> GetLeadHistoryIdsByMeetingOrVisitStatusAsync(string tenantId, List<MeetingOrVisitCompletionStatus> statuses, DateTime? formDate, DateTime? toDate)
    {
        List<Guid> res = new();
        List<string> status = statuses.Select(i => i.ToString()).ToList();
        if (!string.IsNullOrEmpty(tenantId))
        {
            var conn = new NpgsqlConnection(GetConnectionStringAsync());
            await conn.OpenAsync();
            try
            {
                string query = $"SELECT get_meeting_and_sitevisit_details as data from public.get_meeting_and_sitevisit_details(@tenant_id, @statuses ";
                if (formDate != null && toDate != null)
                {
                    query = query + ", @form_date, @to_date )";
                }
                else
                {
                    query = query + " )";
                }
                var command = new NpgsqlCommand(query, conn);
                if (formDate != null && toDate != null)
                {
                    command.Parameters.AddWithValue("to_date", toDate);
                    command.Parameters.AddWithValue("form_date", formDate);
                }
                command.Parameters.AddWithValue("tenant_id", tenantId);
                command.Parameters.AddWithValue("statuses", status);
                var reader = await command.ExecuteReaderAsync();
                if (reader.Read())
                {
                    var result = (reader["data"]).ToString();
                    if (result != null && result != string.Empty)
                    {
                        res = ((Guid[])(reader["data"])).ToList();
                    }
                }
                reader.Close();
            }
            catch (Exception ex)
            {
            }
            finally
            {
                await conn.CloseAsync();
            }
        }
        return res;
    }
    public async Task<IEnumerable<string>> GetAllLeadsAddressesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select DISTINCT(TRIM(concat(\"Addresses\".\"SubLocality\"|| ' ', \"Addresses\".\"Locality\"|| ' ', \"Addresses\".\"District\"|| ' ',\"Addresses\".\"City\"|| ' ', " +
            $"\"Addresses\".\"State\"|| ' ',\"Addresses\".\"Country\"|| ' ', \"Addresses\".\"PostalCode\"))) from \"LeadratBlack\".\"Leads\" INNER join \"LeadratBlack\".\"LeadEnquiries\"" +
            $" ON \"LeadEnquiries\".\"LeadId\" = \"Leads\".\"Id\"" +
            $"INNER join \"LeadratBlack\".\"AddressLeadEnquiry\" ON \"LeadEnquiries\".\"Id\" = \"AddressLeadEnquiry\".\"EnquiriesId\" " +
            $"INNER join \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressLeadEnquiry\".\"AddressesId\" " +
            $" where \"Leads\".\"TenantId\" = '{tenantId}' and \"Leads\".\"IsDeleted\" = 'false';";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllLeadsAddressesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<string>();
        }
    }

    public async Task<IEnumerable<string>> GetAllLeadsZonesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select Distinct(\"Name\") from \"LeadratBlack\".\"Zones\" where \"TenantId\" = '{tenantId}' and \"IsDeleted\" = 'false'; ";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllLeadsZonesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<string>();
        }
    }

    public async Task<IEnumerable<string>> GetAllLeadsCitiesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select Distinct(\"Addresses\".\"City\") from \"LeadratBlack\".\"Leads\" " +
                $"INNER join \"LeadratBlack\".\"LeadEnquiries\" ON \"LeadEnquiries\".\"LeadId\" = \"Leads\".\"Id\" " +
                $"INNER join \"LeadratBlack\".\"AddressLeadEnquiry\" ON \"LeadEnquiries\".\"Id\" = \"AddressLeadEnquiry\".\"EnquiriesId\"  " +
                $"INNER join \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressLeadEnquiry\".\"AddressesId\"  " +
                $"where \"Leads\".\"TenantId\" = '{tenantId}' and \"Leads\".\"IsDeleted\" = 'false';";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllLeadsCitiesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<string>();
        }
    }

    public async Task<IEnumerable<string>> GetAllLeadsLocalitesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select DISTINCT(TRIM(concat(\"Addresses\".\"SubLocality\"|| ' ', \"Addresses\".\"Locality\"))) from \"LeadratBlack\".\"Leads\" " +
                $"INNER join \"LeadratBlack\".\"LeadEnquiries\" ON \"LeadEnquiries\".\"LeadId\" = \"Leads\".\"Id\" " +
                $"INNER join \"LeadratBlack\".\"AddressLeadEnquiry\" ON \"LeadEnquiries\".\"Id\" = \"AddressLeadEnquiry\".\"EnquiriesId\"  " +
                $"INNER join \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressLeadEnquiry\".\"AddressesId\"  " +
                $"where \"Leads\".\"TenantId\" = '{tenantId}' and \"Leads\".\"IsDeleted\" = 'false';";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllLeadsLocalitesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<string>();
        }
    }

    public async Task<IEnumerable<string>> GetAllLeadsStatesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select Distinct(\"Addresses\".\"State\") from \"LeadratBlack\".\"Leads\" " +
                $"INNER join \"LeadratBlack\".\"LeadEnquiries\" ON \"LeadEnquiries\".\"LeadId\" = \"Leads\".\"Id\" " +
                $"INNER join \"LeadratBlack\".\"AddressLeadEnquiry\" ON \"LeadEnquiries\".\"Id\" = \"AddressLeadEnquiry\".\"EnquiriesId\"  " +
                $"INNER join \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressLeadEnquiry\".\"AddressesId\"  " +
                $"where \"Leads\".\"TenantId\" = '{tenantId}' and \"Leads\".\"IsDeleted\" = 'false';";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllLeadsStatesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<string>();
        }
    }
    public async Task<IEnumerable<Guid>> GetLeadHistoryIdsByBaseLeadStatus(string tenantId, string status)
    {
        List<Guid> res = new();
        if (!string.IsNullOrEmpty(tenantId))
        {
            await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
            try
            {
                string query = $"select \"Id\" from \"LeadratBlack\".\"LeadHistories\" where \"TenantId\" = '{tenantId}' " +
                $"and \"IsDeleted\" = 'false' and (\"BaseLeadStatus\"::text ilike '%{status}%')";
                res = (await conn.QueryAsync<Guid>(query)).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception details while calling GetLeadHistoryIdsByBaseLeadStatus " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            }
        }
        return res;
    }



    public async Task<string> GetMeetingAndVisitReportAsync(string tenantId, int pagesize, int pagenumber, string? userName)
    {
        if (tenantId != null)
        {
            var conn = new NpgsqlConnection(GetConnectionStringAsync());
            string result = string.Empty;
            try
            {
                await conn.OpenAsync();
                string query = $"SELECT get_meeting_and_sitevisit_reports as data from public.get_meeting_and_sitevisit_reports(@tenant_id ,@page_size, @page_number ";
                if (userName != null)
                {
                    query = query + ", @user_name )";
                }
                else
                {
                    query = query + " )";
                }
                var command = new NpgsqlCommand(query, conn);
                command.Parameters.AddWithValue("tenant_id", tenantId);
                if (userName != null)
                {
                    command.Parameters.AddWithValue("user_name", userName);
                }
                command.Parameters.AddWithValue("page_size", pagesize);
                command.Parameters.AddWithValue("page_number", pagenumber);
                var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    result = (reader["data"]).ToString() ?? string.Empty;
                }
            }
            catch (Exception ex) { }
            finally { await conn.CloseAsync(); }
            return result;
        }
        return string.Empty;
    }

    public async Task<IEnumerable<T>> GetAllLeadsSubSourceAsync<T>(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"\r\nSELECT mls.\"Value\" AS \"LeadSource\", le.\"SubSource\"\r\n" +
                $"FROM \"LeadratBlack\".\"Leads\" l\r\n" +
                $"LEFT JOIN \"LeadratBlack\".\"LeadEnquiries\" le ON le.\"LeadId\" = l.\"Id\"\r\n" +
                $"JOIN \"LeadratBlack\".\"MasterLeadSources\" mls ON mls.\"Value\" = le.\"LeadSource\"\r\n" +
                $"WHERE l.\"TenantId\" = '{tenantId}' AND l.\"IsDeleted\" = 'false'\r\n" +
                $"GROUP BY mls.\"DisplayName\", mls.\"Value\", mls.\"OrderRank\", le.\"SubSource\"\r\n" +
                $";";
            var res = (await conn.QueryAsync<T>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllLeadsSubSourceAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<T>();
        }
    }

    public async Task<IEnumerable<T>> GetAllLeadsSubSourceV3Async<T>(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = @"SELECT le.""LeadSource"", le.""SubSource""
                     FROM ""LeadratBlack"".""Leads"" l
                     LEFT JOIN ""LeadratBlack"".""LeadEnquiries"" le ON le.""LeadId"" = l.""Id""
                     WHERE l.""TenantId"" = @TenantId AND l.""IsDeleted"" = 'false'
                     GROUP BY le.""LeadSource"", le.""SubSource"";";
            var parameters = new { TenantId = tenantId };
            var res = (await conn.QueryAsync<T>(query, parameters)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllLeadsSubSourceV3Async " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<T>();
        }
    }
    public async Task<IEnumerable<SourceDtoV3>> GetAllLeadsSubSourceV3Async(string tenantId)
    {
        var conn = _dbContext.Connection;
        string query = @"SELECT mls.""DisplayName"", mls.""Value"", mls.""OrderRank"", le.""SubSource""
                 FROM ""LeadratBlack"".""Leads"" l
                 LEFT JOIN ""LeadratBlack"".""LeadEnquiries"" le ON le.""LeadId"" = l.""Id""
                 JOIN ""LeadratBlack"".""MasterLeadSources"" mls ON mls.""Value"" = le.""LeadSource""
                 WHERE l.""TenantId"" = @TenantId AND l.""IsDeleted"" = 'false'
                 GROUP BY mls.""DisplayName"", mls.""Value"", mls.""OrderRank"", le.""SubSource""
                 UNION
                 SELECT mls.""DisplayName"", mls.""Value"", mls.""OrderRank"", ia.""AccountName"" as ""SubSource""
                 FROM ""LeadratBlack"".""IntegrationAccountInfo"" ia
                 JOIN ""LeadratBlack"".""MasterLeadSources"" mls ON mls.""Value"" = ia.""LeadSource""
                 WHERE ia.""AccountName"" IS NOT NULL AND ia.""TenantId"" = @TenantId AND ia.""IsDeleted"" = 'false'
                 UNION
                 SELECT mls.""DisplayName"", mls.""Value"", mls.""OrderRank"", fa.""AdName"" as ""SubSource""
                 FROM ""LeadratBlack"".""FacebookAdsInfo"" fa
                 JOIN ""LeadratBlack"".""MasterLeadSources"" mls ON mls.""Value"" = 2
                 WHERE fa.""AdName"" IS NOT NULL AND fa.""TenantId"" = @TenantId AND fa.""IsDeleted"" = 'false'
                 UNION
                 SELECT mls.""DisplayName"", mls.""Value"", mls.""OrderRank"", flf.""Name"" as ""SubSource""
                 FROM ""LeadratBlack"".""FacebookLeadGenForm"" flf
                 JOIN ""LeadratBlack"".""MasterLeadSources"" mls ON mls.""Value"" = 2
                 WHERE flf.""Name"" IS NOT NULL AND flf.""TenantId"" = @TenantId AND flf.""IsDeleted"" = 'false';";


        var parameters = new { TenantId = tenantId };
        var res = (await conn.QueryAsync<SourceDtoV3>(query, parameters)).ToList();
        return res;
    }
    public async Task<IEnumerable<SourceDtoV3>> GetAllLeadsSourceWithSubSourceV3Async(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = @"SELECT mls.""DisplayName"", mls.""Value"", mls.""OrderRank"", le.""SubSource""
                 FROM ""LeadratBlack"".""Leads"" l
                 LEFT JOIN ""LeadratBlack"".""LeadEnquiries"" le ON le.""LeadId"" = l.""Id"" AND l.""TenantId"" = @TenantId And l.""IsDeleted"" = 'false'
                 AND le.""SubSource"" != '' AND le.""SubSource"" is not null
                 RIGHT JOIN ""LeadratBlack"".""MasterLeadSources"" mls ON mls.""Value"" = le.""LeadSource""
                 GROUP BY mls.""DisplayName"", mls.""Value"", mls.""OrderRank"", le.""SubSource"";";
            var parameters = new { TenantId = tenantId };
            var res = (await conn.QueryAsync<SourceDtoV3>(query, parameters)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllLeadsSourceWithSubSourceV3Async " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<SourceDtoV3>();
        }
    }

    public async Task<IEnumerable<T>> GetAllIntegrationSubSourceAsync<T>(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT \"LeadSource\", \"AccountName\" as \"SubSource\" from " +
                $"\"LeadratBlack\".\"IntegrationAccountInfo\" where \"AccountName\" is not null " +
                $"and \"TenantId\" = '{tenantId}' and \"IsDeleted\" = 'false' " +
                $"union " +
                $"SELECT 2 as \"LeadSource\", \"AdName\" as \"SubSource\" FROM " +
                $"\"LeadratBlack\".\"FacebookAdsInfo\" where \"AdName\" is not null " +
                $"and \"TenantId\" = '{tenantId}' and \"IsDeleted\" = 'false' " +
                $"union " +
                $"SELECT 2 as \"LeadSource\", \"Name\" as \"SubSource\" FROM " +
                $"\"LeadratBlack\".\"FacebookLeadGenForm\" where \"Name\" is not null " +
                $"and \"TenantId\" = '{tenantId}' and \"IsDeleted\" = 'false' " +
                $"union " +
                $"select \"LeadSource\", \"SubSource\" FROM " +
                $"\"LeadratBlack\".\"Leads\" l INNER join \"LeadratBlack\".\"LeadEnquiries\" " +
                $"le ON le.\"LeadId\" = l.\"Id\" where \"SubSource\" is not null " +
                $"and l.\"TenantId\" = '{tenantId}' and l.\"IsDeleted\" = 'false';";
            var res = (await conn.QueryAsync<T>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllIntegrationSubSourceAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<T>();
        }
    }
    public async Task<List<string>> GetAgencyNamesAsync(string tenantId)
    {
        var conn = _dbContext.Connection;
        var query = $"SELECT f.\"AgencyName\" FROM \"LeadratBlack\".\"FacebookAdsInfo\" f " +
            $"where f.\"AgencyName\" is not null and " +
            $" f.\"TenantId\" = '{tenantId}' and f.\"IsDeleted\" = 'false' " +
            $"union " +
            $"SELECT g.\"AgencyName\" FROM \"LeadratBlack\".\"IntegrationAccountInfo\" g " +
            $"where g.\"AgencyName\" is not null and " +
            $"g.\"TenantId\" = '{tenantId}' and g.\"IsDeleted\" = 'false' " +
            $"union " +
            $"SELECT h.\"AgencyName\" FROM \"LeadratBlack\".\"FacebookLeadGenForm\" h " +
            $"where h.\"AgencyName\" is not null and " +
            $"h.\"TenantId\" = '{tenantId}' and h.\"IsDeleted\" = 'false' " +
            $"union " +
            $"select \"AgencyName\" FROM \"LeadratBlack\".\"Leads\" where \"TenantId\" = '{tenantId}' and " +
            $"\"AgencyName\" is not null " +
            $"union " +
            $"select \"AgencyName\" FROM \"LeadratBlack\".\"QRFormTemplates\" where \"TenantId\" = '{tenantId}' and " +
            $"\"AgencyName\" is not null";

        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }
    public async Task<List<string>> GetAssignedAgencyNamesInLeadAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        var query = $"SELECT Distinct(\"AgencyName\") FROM \"LeadratBlack\".\"Leads\" " +
            $" where \"AgencyName\" is not null and \"TenantId\" = '{tenantId}' and \"IsDeleted\" = 'false' ;";
        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }

    public async Task<List<string>> GetAssignedAgencyNamesInLeadV2Async(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        var query = $"SELECT Distinct(ag.\"Name\") FROM \"LeadratBlack\".\"Leads\" leads " +
            $"left join \"LeadratBlack\".\"AgencyLead\" ag_lead on leads.\"Id\" = ag_lead.\"LeadsId\" " +
            $"left join \"LeadratBlack\".\"Agencies\" ag on ag_lead.\"AgenciesId\" = ag.\"Id\"  " +
            $" where ag.\"Name\" is not null and leads.\"TenantId\" = '{tenantId}' and leads.\"IsDeleted\" = 'false' ;";
        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }

    public async Task<List<string>> GetOwnerNamesInAllPropertiesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT DISTINCT \"Name\"\r\nFROM \"LeadratBlack\".\"PropertyOwnerDetails\" po\r\n" +
                $"LEFT JOIN \"LeadratBlack\".\"PropertyPropertyOwnerDetails\" pp ON po.\"Id\" = pp.\"PropertyOwnerDetailsId\"" +
                $" \r\nLEFT JOIN \"LeadratBlack\".\"Properties\" pr  on pr.\"Id\" = pp.\"PropertiesId\"\r\nWHERE pr.\"TenantId\" = '{tenantId}' AND pr.\"IsDeleted\" = 'false' and po.\"Name\"<>''";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetOwnerNamesInAllPropertiesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new();
        }
    }

    public async Task<DateTime> GetLastModifiedDateByAgencyNameAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        var query = $"SELECT MAX(\"DateTime\") FROM \"Auditing\".\"AuditTrails\" " +
            $"WHERE \"TableName\" = 'Lead' and  \"AffectedColumns\" ilike '%agencyname%' and \"TenantId\" = '{tenantId}' ";
        var res = (await conn.QueryAsync<DateTime>(query)).ToList();
        return res.FirstOrDefault();
    }
    public async Task<int> GetLeadSourceUpdateStatusAsync(Guid leadId, string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select count(keys) from ( select lh.\"Id\" as leadId, jsonb_object_keys(lh.\"LeadSource\") as keys, " +
                $"(lh.\"LeadSource\")->>jsonb_object_keys(lh.\"LeadSource\") as lead_source " +
                $"from \"LeadratBlack\".\"LeadHistories\" lh where lh.\"Id\" = '{leadId}' and \"TenantId\" = '{tenantId}') sub";
            var res = (await conn.QueryAsync<int>(query)).ToList();
            return res.FirstOrDefault();
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetLeadSourceUpdateStatusAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return 0;
        }
    }
    public async Task<List<DeviceAndTokenDto>> GetDeviceIdsAndTokensAsync(List<Guid> userIds)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select \"Id\" as \"DeviceId\" ,\"NewNotificationToken\" as \"Token\" from \"LeadratBlack\".\"Devices\" " +
                $"where \"UserId\" = any(@userIds) and \"IsDeleted\" = 'false'";
            var res = (await conn.QueryAsync<DeviceAndTokenDto>(query, new
            {
                userIds = userIds
            })).ToList();
            return res;
        }
        catch (Exception ex) { }
        return new();
    }



    public async Task<int> UpdateChildLeadsCount(Guid leadId, int count)
    {
        try
        {
            var conn = _dbContext.Connection;
            var query = $"UPDATE \"LeadratBlack\".\"Leads\" SET \"ChildLeadsCount\" = @count WHERE \"Id\" = @leadId";

            var res = await conn.ExecuteAsync(query, new
            {
                count,
                leadId
            });
            return res;
        }
        catch (Exception ex) { throw; }
    }

    public async Task<int> UpdateChildLeadsCountInHistory(Guid leadId, int childsCount, int version)
    {
        try
        {
            var keyPairValues = new Dictionary<int, int>() { { version, childsCount } };
            var modifiedDate = new Dictionary<int, DateTime> { { version, DateTime.Now } };
            var conn = _dbContext.Connection;
            var query = $"UPDATE \"LeadratBlack\".\"Leads\" SET \"ChildLeadsCount\" = @keyPairValues, \"CurrentVersion\"= @version, \"ModifiedDate\"= @modifiedDate WHERE \"Id\" = @leadId";

            var res = await conn.ExecuteAsync(query, new
            {
                keyPairValues,
                version,
                modifiedDate,
                leadId
            });
            return res;
        }
        catch (Exception ex) { throw; }
    }

    public async Task<int> GetLeadById(Guid leadId)
    {
        var conn = _dbContext.Connection;
        var query = $"select \"Leads\".\"ChildLeadsCount\"  from \"LeadratBlack\".\"Leads\" " +
            $"where \"Id\" = @leadId";
        var res = conn.QueryFirstOrDefault<int>(query, new
        {
            leadId
        });
        return res;
    }

    public async Task<int> GetLeadHistoryById(Guid leadId)
    {
        var conn = _dbContext.Connection;
        try
        {
            var query = $"select \"LeadHistories\".\"ChildLeadsCount\"  from \"LeadratBlack\".\"LeadHistories\" " +
            $"where \"Id\" = @leadId";
            var res = conn.QueryFirstOrDefault<string>(query, new
            {
                leadId
            });
            var count = JsonConvert.DeserializeObject<Dictionary<int, int>>(res);
            return count.Values.LastOrDefault();

        }
        catch (Exception ex) { throw; }

    }
    public async Task<List<AttendanceLog>> GetAttendanceLogsByDateRangeWithTimeZoneAsync(DateTime startDate, DateTime endDate, Guid userId, string tenantId, string baseUtcOffset)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());

        try
        {
            var query = $"SELECT \"Id\",\"ClockInTime\" + INTERVAL '{baseUtcOffset}' AS \"ClockInTime\"," +
                $"\"ClockOutTime\" + INTERVAL '{baseUtcOffset}' AS \"ClockOutTime\",\"ClockInLatitude\", \"ClockInLongitude\", \"ClockInLocation\"," +
                $"\"ClockOutLatitude\", \"ClockOutLongitude\", \"ClockOutLocation\", \"IsClosed\", \"TenantId\", \"IsDeleted\"," +
                $"\"CreatedBy\", \"CreatedOn\", \"LastModifiedBy\", \"LastModifiedOn\", \"DeletedOn\", \"DeletedBy\", \"UserId\"," +
                $"\"IsSystemGenerated\", \"ClockInImageUrl\", \"ClockOutImageUrl\" FROM \"LeadratBlack\".\"AttendanceLogs\" " +
                $"WHERE \"IsDeleted\" = 'false' " +
                $"AND (\"CreatedOn\" BETWEEN @startDate AND @endDate)" +
                $"AND \"UserId\" = @userId AND \"TenantId\" = @tenantId ORDER BY \"CreatedOn\" desc";
            var res = conn.QueryAsync<AttendanceLog>(query, new
            {
                startDate,
                endDate,
                userId,
                tenantId
            });
            return res.Result.ToList();
        }
        catch (Exception ex)
        {
            throw;
        }
    }
    public async Task<List<AttendanceLog>> GetAttendanceLogsByDateRangeAsync(DateTime startDate, DateTime endDate, Guid userId, string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT \"Id\",\"ClockInTime\" + INTERVAL '05:30' AS \"ClockInTime\"," +
                $"\"ClockOutTime\" + INTERVAL '05:30' AS \"ClockOutTime\",\"ClockInLatitude\", \"ClockInLongitude\", \"ClockInLocation\"," +
                $"\"ClockOutLatitude\", \"ClockOutLongitude\", \"ClockOutLocation\", \"IsClosed\", \"TenantId\", \"IsDeleted\"," +
                $"\"CreatedBy\", \"CreatedOn\", \"LastModifiedBy\", \"LastModifiedOn\", \"DeletedOn\", \"DeletedBy\", \"UserId\"," +
                $"\"IsSystemGenerated\", \"ClockInImageUrl\", \"ClockOutImageUrl\" FROM \"LeadratBlack\".\"AttendanceLogs\" " +
                $"WHERE \"IsDeleted\" = 'false' " +
                $"AND (\"CreatedOn\" BETWEEN @startDate AND @endDate)" +
                $"AND \"UserId\" = @userId AND \"TenantId\" = @tenantId ORDER BY \"CreatedOn\" desc";
            var res = conn.QueryAsync<AttendanceLog>(query, new
            {
                startDate,
                endDate,
                userId,
                tenantId
            });
            return res.Result.ToList();
        }
        catch (Exception ex)
        {
            throw;
        }
    }
    public async Task<AttendanceLog> GetTodayAttendanceLogAsync(Guid userId, string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT * FROM \"LeadratBlack\".\"AttendanceLogs\"" +
                $" WHERE \"TenantId\" = @tenantId and \"UserId\" = @userId and " +
                $" \"IsDeleted\" = 'false' ORDER BY \"LastModifiedOn\" DESC  LIMIT 1";
            await conn.OpenAsync();
            var res = conn.QueryFirstOrDefault<AttendanceLog>(query, new
            {
                userId,
                tenantId
            });
            await conn.CloseAsync();
            return res ?? new AttendanceLog();
        }
        catch (Exception ex)
        {
            throw;
        }
    }

    public async Task<int> GetFacebookIntegrationAdsAndFormCount(string addName, string formName, string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"Select Count(le.\"Id\") \r\nfrom \"LeadratBlack\".\"Leads\" l\r\nleft Join \"LeadratBlack\".\"LeadEnquiries\" " +
                $"le on le.\"LeadId\" = l.\"Id\"\r\nwhere l.\"TenantId\" = '{tenantId}' and le.\"SubSource\" ilike '%{addName}%'";
            if (addName == null && formName != null)
            {
                query = $"Select Count(le.\"Id\") \r\nfrom \"LeadratBlack\".\"Leads\" l\r\nleft Join \"LeadratBlack\".\"LeadEnquiries\" " +
                $"le on le.\"LeadId\" = l.\"Id\"\r\nwhere l.\"TenantId\" = '{tenantId}' and le.\"SubSource\" ilike '%{formName}%'";
            }
            var res = (await conn.QueryAsync<int>(query));
            return res.FirstOrDefault();
        }
        catch (Exception ex)
        {
            throw;
        }
    }

    public async Task<IEnumerable<string>> GetAllChannelPartnerAsync(string tenantId, string searchByName)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"Select \"ChannelPartners\".\"FirmName\" from \"LeadratBlack\".\"ChannelPartners\" where \"TenantId\" = '{tenantId}'";
            if (!string.IsNullOrWhiteSpace(searchByName))
            {
                query = $"Select \"ChannelPartners\".\"FirmName\" from \"LeadratBlack\".\"ChannelPartners\" where \"TenantId\" = '{tenantId}' and \"FirmName\" ilike '%{searchByName}%' ";
            }
            var res = (await conn.QueryAsync<string>(query));
            return res.ToList();
        }
        catch (Exception ex)
        {
            throw;
        }
    }
    public async Task<Lrb.Application.Lead.Mobile.v2.ViewLeadAnonymousDto> GetLeadByIdAnonymousAsync(Guid leadId)
    {
        var conn = _dbContext.Connection;
        try
        {
            var query = $"Select l.\"Id\" as \"Id\", l.\"Name\", l.\"ContactNo\", le.\"LeadSource\", l.\"AssignTo\", l.\"Email\", l.\"Notes\", l.\"AlternateContactNo\", " +
                $"l.\"ScheduledDate\", l.\"CustomLeadStatusId\" as \"StatusId\", l.\"LastModifiedBy\", l.\"LastModifiedOn\", l.\"CreatedBy\" , l.\"CreatedOn\", " +
                $"le.\"NoOfBHKs\" as \"NoOfBHK\", le.\"LowerBudget\", le.\"UpperBudget\", le.\"PropertyTypeId\", le.\"SubSource\" " +
                $"from" +
                $"\"LeadratBlack\".\"Leads\" l " +
                $"left join \"LeadratBlack\".\"LeadEnquiries\" le on l.\"Id\" = le.\"LeadId\" " +
                $"where " +
                $"l.\"Id\" = '{leadId}' and l.\"IsDeleted\" = 'false' and l.\"IsArchived\" = 'false'";
            var res = (await conn.QueryAsync<Lrb.Application.Lead.Mobile.v2.ViewLeadAnonymousDto>(query));
            conn.Close();
            return res.FirstOrDefault() ?? new();
        }
        catch (Exception ex)
        {
            throw;
        }
    }
    public async Task<int> GetTotalNotificationsCountAsync(Guid userId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select count(*) from \"LeadratBlack\".\"Notifications\" " +
                        $"where \"UniqueId\" in " +
                            $"(select distinct(\"NotificationUniqueId\") from \"LeadratBlack\".\"PushNotificationRecords\" " +
                                $"where \"UserId\" = '{userId}' and \"IsDeleted\" = 'false')" +
                        $"and \"UserId\" = '{userId}' and \"IsDeleted\" = 'false'";
            var res = (await conn.QueryAsync<int>(query));
            return res.FirstOrDefault();
        }
        catch (Exception ex)
        {
            throw;
        }
    }
    public async Task<int> GetTotalUnOpenedNotificationsCountAsync(Guid userId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select count(*) from \"LeadratBlack\".\"Notifications\" " +
                        $"where \"UniqueId\" in " +
                            $"(select distinct(\"NotificationUniqueId\") from \"LeadratBlack\".\"PushNotificationRecords\" " +
                                $"where \"UserId\" = '{userId}' and \"IsDeleted\" = 'false' and \"IsOpened\" = 'false')" +
                        $"and \"UserId\" = '{userId}' and \"IsDeleted\" = 'false'";
            var res = (await conn.QueryAsync<int>(query));
            return res.FirstOrDefault();
        }
        catch (Exception ex)
        {
            throw;
        }
    }

    public async Task<FacebookConnectedPageAccount> GetFacebookPageAccountAsync(string tenantId, string pageId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        string query = $"select * from \"LeadratBlack\".\"FacebookConnectedPageAccount\" where \"FacebookId\" = '{pageId}' and \"TenantId\" = '{tenantId}'";
        var res = await conn.QueryFirstAsync<FacebookConnectedPageAccount>(query);
        return res;
    }
    public async Task<FacebookAuthResponse> GetFacebookAccountAsync(string tenantId, Guid id)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        string query = $"select * from \"LeadratBlack\".\"FacebookAuthResponses\" where \"Id\" = '{id}' and \"TenantId\" = '{tenantId}'";
        var res = await conn.QuerySingleAsync<FacebookAuthResponse>(query);
        return res;
    }

    public async Task<IEnumerable<Guid>> GetAllUserIdsAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select \"UserId\" from \"LeadratBlack\".\"UserDetails\" where \"TenantId\" = '{tenantId}' and \"IsDeleted\" = 'false'";
            var res = await conn.QueryAsync<Guid>(query);
            return res;
        }
        catch (Exception ex)
        {
            throw;
        }
    }

    public async Task<IEnumerable<Guid>> GetAllAdminIdsAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string adminRoleQuery = $"select \"UserId\" :: uuid as count from \"Identity\".\"UserRoles\" " +
                $"where \"TenantId\" = '{tenantId}' " +
                $"and \"RoleId\" = (Select \"Id\" from \"Identity\".\"Roles\" " +
                $"where \"Name\" = 'Admin' and \"TenantId\" = '{tenantId}')";
            var res = await conn.QueryAsync<Guid>(adminRoleQuery);
            return res;
        }
        catch (Exception ex)
        {
            throw;
        }
    }
    public async Task<IEnumerable<Guid>> GetUserIdsWithoutAdminAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var idWithoutAdminRoleQuery = $"select distinct u.\"Id\" :: uuid from \"Identity\".\"Users\" u " +
                $"inner join \"Identity\".\"UserRoles\" ur on u.\"Id\" = ur.\"UserId\" " +
                $"where ur.\"TenantId\"='{tenantId}' " +
                $"and u.\"IsActive\" = true " +
                $"and u.\"IsDeleted\" = false " +
                $"and ur.\"RoleId\" in (select \"Id\" from \"Identity\".\"Roles\" " +
                $"where \"Name\" <> 'Admin' and \"TenantId\" = '{tenantId}' and \"IsDeleted\" = false)";
            var res = await conn.QueryAsync<Guid>(idWithoutAdminRoleQuery);
            return res;
        }
        catch (Exception ex)
        {
            throw;
        }
    }
    public async Task<IEnumerable<T>> GetAllProspectSubSourceAsync<T>(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select ps.\"Id\" as SourceId, pe.\"SubSource\", ps.\"DisplayName\", ps.\"Value\" from \"LeadratBlack\".\"Prospects\" p\r\n" +
            $"inner join \"LeadratBlack\".\"ProspectEnquiries\" pe on p.\"Id\" = pe.\"ProspectId\" and p.\"TenantId\" = '{tenantId}' and p.\"IsDeleted\" = 'false'\r\n" +
            $"right join \"LeadratBlack\".\"MasterProspectSources\" ps on pe.\"SourceId\" = ps.\"Id\" and ps.\"IsDeleted\" = 'false' and pe.\"IsDeleted\" = 'false'\r\n" +
            $"group by ps.\"Id\", pe.\"SubSource\", ps.\"DisplayName\", ps.\"Value\"\r\n" +
            $"order by ps.\"DisplayName\" ";
            var res = (await conn.QueryAsync<T>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            throw;
        }
    }

    public async Task<IEnumerable<string>> GetAllProspectAddresses(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());

        try
        {
            var query = $"select DISTINCT(TRIM(concat(\"Addresses\".\"SubLocality\"|| ' ', \"Addresses\".\"Locality\"|| ' ', " +
            $"\"Addresses\".\"District\"|| ' ',\r\n\t\t\"Addresses\".\"City\"|| ' ', \"Addresses\".\"State\"|| ' ',\"Addresses\".\"Country\"" +
            $"|| ' ',  \"Addresses\".\"PostalCode\"))) \r\nfrom \"LeadratBlack\".\"Prospects\" \r\n" +
            $"INNER join " +
            $"\"LeadratBlack\".\"ProspectEnquiries\"\r\nON \"ProspectEnquiries\".\"ProspectId\" = \"Prospects\".\"Id\" \r\n" +
             $"INNER join \"LeadratBlack\".\"AddressProspectEnquiry\" ON \"AddressProspectEnquiry\".\"ProspectEnquiriesId\" = \"ProspectEnquiries\".\"Id\" " +
            $"INNER join \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressProspectEnquiry\".\"AddressesId\" " +
            $"where \"Prospects\".\"TenantId\" = '{tenantId}' and \"Prospects\".\"IsDeleted\" = 'false';";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch
        {
            throw;
        }

    }
    public async Task<IEnumerable<string>> GetAllProspectCities(string tenantId)
    {

        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select Distinct(\"City\") from \"LeadratBlack\".\"Prospects\" " +
                 $"INNER join \"LeadratBlack\".\"ProspectEnquiries\" ON \"ProspectEnquiries\".\"ProspectId\" = \"Prospects\".\"Id\" " +
                 $"INNER join \"LeadratBlack\".\"AddressProspectEnquiry\" ON \"AddressProspectEnquiry\".\"ProspectEnquiriesId\" = \"ProspectEnquiries\".\"Id\" " +
                $"INNER join \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressProspectEnquiry\".\"AddressesId\" " +
                $"where \"Prospects\".\"TenantId\" = '{tenantId}' and \"Prospects\".\"IsDeleted\" = 'false' and \"Addresses\".\"City\" IS NOT NULL;";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllProspectCities " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<string>();
        }
    }

    public async Task<IEnumerable<string>> GetAllProspectStatesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select Distinct(\"State\") from \"LeadratBlack\".\"Prospects\" " +
                 $"INNER join \"LeadratBlack\".\"ProspectEnquiries\" ON \"ProspectEnquiries\".\"ProspectId\" = \"Prospects\".\"Id\" " +
                 $"INNER join \"LeadratBlack\".\"AddressProspectEnquiry\" ON \"AddressProspectEnquiry\".\"ProspectEnquiriesId\" = \"ProspectEnquiries\".\"Id\" " +
                $"INNER join \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressProspectEnquiry\".\"AddressesId\" " +
                $"where \"Prospects\".\"TenantId\" = '{tenantId}' and \"Prospects\".\"IsDeleted\" = 'false' and \"Addresses\".\"State\" IS NOT NULL;";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllProspectStatesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<string>();
        }
    }

    public async Task<IEnumerable<string>> GetAllPropertiesAddressesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {

            string query = $"select distinct Concat(\"SubLocality\", ', ', \"Locality\")\r\nFrom \"LeadratBlack\".\"Addresses\"\r\nwhere \"Id\" IN (\r\n    SELECT DISTINCT \"AddressId\"\r\n    From \"LeadratBlack\".\"Properties\"\r\n    where \"TenantId\" = '{tenantId}'and \"IsDeleted\" = 'false')";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllProspectStatesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<string>();
        }
    }
    public async Task<IEnumerable<T>> GetPropertiesWithIdsAndNames<T>(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"SELECT DISTINCT \"Id\", \"Title\"\r\n FROM \"LeadratBlack\".\"Properties\"r\n  WHERE \"TenantId\" = '{tenantId}' AND \"IsDeleted\" = 'false' and \"IsArchived\" = 'false' and \"Status\" = 0 ";
            var res = (await conn.QueryAsync<T>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllProspectStatesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<T>();
        }
    }
    public async Task<Lrb.Application.LeadCallLog.Mobile.TenantDto> GetTenantIdByDeviceUDIDAsync(string deviceUDID)
    {
        var conn = _dbContext.Connection;
        try
        {
            var query = $"SELECT \"TenantId\", \"UserId\" FROM \"LeadratBlack\".\"Devices\" where \"IsDeleted\" = 'false' and \"DeviceUDID\" = '{deviceUDID}'\r\nORDER BY \"LastModifiedOn\" DESC LIMIT 1";
            var res = (await conn.QueryFirstAsync<Lrb.Application.LeadCallLog.Mobile.TenantDto>(query));
            return res;
        }
        catch (Exception ex)
        {
            Log.Information("Exception Details while calling GetTenantIdByDeviceUDIDAsync() => " + JsonConvert.SerializeObject(ex));
            return null;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<List<PropertyIdWithLatLongDto>> GetMatchingPropertyWithLatLong(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT properties.\"Id\",address.\"Latitude\",address.\"Longitude\" \r\n\tFROM \"LeadratBlack\".\"Properties\" properties " +
                $"\r\n\tINNER JOIN \"LeadratBlack\".\"Addresses\" address ON properties.\"AddressId\" = address.\"Id\" \r\n\t" +
                $"WHERE address.\"Latitude\" IS NOT null AND address.\"Longitude\" IS NOT null AND properties.\"TenantId\" = '{tenantId}';";
            var res = await conn.QueryAsync<PropertyIdWithLatLongDto>(query);
            return res.ToList();
        }
        catch
        {
            throw;
        }
    }

    public async Task<bool> GetDualOwnershipDetails(string tenantId)
    {
        var conn = _dbContext.Connection;
        try
        {
            var query = $"SELECT \"IsDualOwnershipEnabled\" FROM \"LeadratBlack\".\"GlobalSettings\" where \"IsDeleted\" = 'false' and \"TenantId\" = '{tenantId}';\r\n";
            var res = await conn.QueryFirstAsync<bool>(query);
            return res;
        }
        catch
        {
            return false;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<DateTime> GetLastModifiedDate(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT Max(\"LastModifiedOn\")\r\nFROM \"LeadratBlack\".\"CustomMasterLeadStatuses\"\r\nwhere \"TenantId\"='{tenantId}'";
            var res = (await conn.QueryAsync<DateTime>(query)).ToList();
            return res.FirstOrDefault();
        }
        catch (Exception ex)
        {
            throw;
        }
        finally
        {
            conn.Close();
        }

    }


    public async Task<Lrb.Application.OrgProfile.Web.Dtos.SubscriptionInfoDto> GetTenantSubsciptionInfoDetailsAsync(string tenant)
    {
        var conn = _dbContext.Connection;
        try
        {
            string query = $"SELECT t.\"Id\" AS \"TenantId\", t.\"ValidUpto\" AS \"PlanExpireDate\", Min(s.\"LicenseCreated\") as \"DateOfSubscription\", " +
                $"Sum(s.\"DueAmount\") as \"PaymentDue\", Sum(s.\"PaidAmount\") AS \"PaidAmount\", s.\"BillingType\" as \"BillingType\", " +
                $"Sum(s.\"SoldLicenses\") as \"LicenseBought\", p.\"NextDueDate\" AS \"PaymentDueDate\", p.\"NextDueDate\" AS \"NextBillingType\", \"ActiveUsers\"," +
                $" \"InActiveUsers\"\r\n" +
                $"FROM \"MultiTenancy\".\"Tenants\" t\r\n" +
                $"LEFT JOIN\r\n" +
                $"(\r\n\tSELECT COUNT(\"Id\") AS \"ActiveUsers\", \"TenantId\" FROM \"Identity\".\"Users\"\r\n\tWHERE \"IsDeleted\" = false AND \"IsActive\" = true " +
                $"AND \"Users\".\"UserName\"::text !~~ '%.admin%'::text\r\n\tGROUP BY \"Users\".\"TenantId\"\r\n) u ON u.\"TenantId\" = t.\"Id\"\r\n" +
                $"LEFT JOIN\r\n" +
                $"(\r\n\tSELECT COUNT(\"Id\") AS \"InActiveUsers\", \"TenantId\" FROM \"Identity\".\"Users\" WHERE \"IsDeleted\" = false \r\n\tAND \"IsActive\" = false " +
                $"AND \"Users\".\"UserName\"::text !~~ '%.admin%'::text\r\n\tGROUP BY \"Users\".\"TenantId\"\r\n) ui ON ui.\"TenantId\" = t.\"Id\"\r\n" +
                $"LEFT JOIN\r\n" +
                $"(\r\n\tSELECT \"TenantId\", Min(\"LicenseCreated\") as \"LicenseCreated\", Sum(\"DueAmount\") as \"DueAmount\", " +
                $"\r\n\t\"BillingType\", Sum(\"SoldLicenses\") as \"SoldLicenses\", Sum(\"PaidAmount\") as \"PaidAmount\"\r\n\tFROM tenantmaster.\"Subscriptions\"   " +
                $"\r\n\tWHERE \"IsDeleted\" = false\r\n\tGROUP BY \"TenantId\", \"BillingType\"\r\n) s on s.\"TenantId\" = t.\"Id\"\r\n" +
                $"Left Join\r\n" +
                $"(\r\n\tSELECT \"TenantId\", \"NextDueDate\" FROM tenantmaster.\"Payments\" \r\n\twhere \"NextDueDate\" IS NOT NULL ORDER bY \"NextDueDate\" " +
                $"DESC\r\n\tLIMIT 1\r\n) p on p.\"TenantId\" = t.\"Id\" WHERE t.\"Id\" = '{tenant}'\r\nGroup BY t.\"Id\", s.\"BillingType\", p.\"NextDueDate\", " +
                $"\"ActiveUsers\", \"InActiveUsers\"";
            var res = await conn.QueryAsync<Lrb.Application.OrgProfile.Web.Dtos.SubscriptionInfoDto>(query);
            return res.FirstOrDefault();
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }

    public async Task<IEnumerable<TransactionDetailsDto>> GetAllTransactionDetails(string tenant)
    {
        var conn = _dbContext.Connection;
        try
        {
            string query = $"SELECT t.\"Id\",s.\"NetAmount\" AS \"PlanPrice\", p.\"Amount\" AS \"PaidAmount\", s.\"TotalAmount\" AS \"Total\", s.\"GSTAmount\" AS \"GST\", s.\"DueAmount\" AS \"AmountDue\"," +
                $"\r\np.\"PartPaymentDate\" AS \"TransactionDate\", p.\"NextDueDate\" AS \"DueDate\", p.\"Mode\" AS \"Mode\"\r\n" +
                $"FROM \"MultiTenancy\".\"Tenants\" t\r\n" +
                $"LEFT JOIN" +
                $"\r\n(\r\n\tSELECT \"TenantId\", \"NetAmount\", \"TotalAmount\", \"GSTAmount\", \"DueAmount\"\r\n\t" +
                $"FROM tenantmaster.\"Subscriptions\"\r\n\tWHERE \"IsDeleted\" = false \r\n) s on s.\"TenantId\" = t.\"Id\"\r\n" +
                $"LEFT JOIN " +
                $"\r\n(\r\n\tSELECT \"TenantId\", \"NextDueDate\", \"Mode\", \"PartPaymentDate\",\"Amount\"   \r\n\tFROM tenantmaster.\"Payments\") p on p.\"TenantId\" = t.\"Id\" where t.\"Id\" = '{tenant}'";
            var res = await conn.QueryAsync<TransactionDetailsDto>(query);
            return res;
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }

    public async Task<bool> CheckUserMoreThanLicenseBought(string tenantId)
    {
        var tenantConn = _dbContext.Connection;
        var commonConn = new NpgsqlConnection(_settings.ConnectionString);
        try
        {
            var query1 = $"SELECT COUNT(\"Id\") FROM \"Identity\".\"Users\" WHERE \"Users\".\"IsDeleted\" = false and \"Users\".\"IsActive\" = true AND \"Users\".\"UserName\"::text !~~ '%{tenantId}.admin%'::text AND " +
                $"\"TenantId\" = '{tenantId}'";

            var query2 = $"SELECT \r\n COALESCE(s.TotalSoldLicenses, 0) + COALESCE(sa.TotalSoldLicenses, 0) AS \"TotalSoldLicenses\"\r\nFROM (\r\n SELECT SUM(\"SoldLicenses\") AS TotalSoldLicenses\r\n FROM \"LeadratBlack\".\"Subscriptions\"\r\n WHERE \"IsDeleted\" = false\r\n AND \"IsExpired\" = false\r\n      AND \"IsActive\" = true\r\n      AND \"TenantId\" = '{tenantId}'\r\n) s,\r\n(\r\n    SELECT SUM(\"SoldLicenses\") AS TotalSoldLicenses\r\n    FROM \"LeadratBlack\".\"SubscriptionAddOns\"\r\n    WHERE \"SubscriptionId\" IN (\r\n        SELECT \"Id\"\r\n        FROM \"LeadratBlack\".\"Subscriptions\"\r\n        WHERE \"IsDeleted\" = false\r\n          AND \"IsExpired\" = false\r\n          AND \"IsActive\" = true\r\n          AND \"TenantId\" = '{tenantId}'\r\n    ) and \"IsExpired\" = false \r\n\tand \"IsActive\" = true \r\n\tand \"IsDeleted\" = false\r\n) sa;";
            var activeUsers = (await tenantConn.QueryAsync<int>(query1)).FirstOrDefault();
            var licenseBought = (await commonConn.QueryAsync<int>(query2)).FirstOrDefault();
            if (activeUsers < licenseBought && licenseBought != 0)
            {
                return false;
            }
            else if (activeUsers >= licenseBought && licenseBought != 0)
            {
                return true;
            }
            return true;
        }
        catch
        {
            throw;
        }
        finally
        {
            tenantConn.Close();
            commonConn.Close();
        }
    }

    public async Task<List<AdminUserDto>> GetAdminsNameAndNumberAsync(string tenantId)
    {
        var conn = _dbContext.Connection;
        try
        {
            var query = $"Select Concat(u.\"FirstName\", ' ', u.\"LastName\") AS \"Name\", u.\"PhoneNumber\" from \"Identity\".\"Roles\" r\r\n" +
                $"Left Join" +
                $"\r\n(\r\n\tSelect \"UserId\", \"RoleId\" \r\n\tFrom \"Identity\".\"UserRoles\"\r\n) ur on ur.\"RoleId\" = r.\"Id\"\r\n" +
                $"LEFT JOIN " +
                $"\r\n(\r\n\tSelect \"FirstName\", \"LastName\", \"PhoneNumber\", \"Id\", \"UserName\" from \"Identity\".\"Users\" where \"IsDeleted\" = false AND \"IsActive\" = true\r\n) " +
                $"u on u.\"Id\" = ur.\"UserId\"\r\n" +
                $"where \"Name\" ilike '%Admin%' And \"IsDeleted\" = false AND u.\"UserName\"::text !~~ '%admin%'::text  AND \"TenantId\" = '{tenantId}'";
            var res = (await conn.QueryAsync<AdminUserDto>(query)).ToList();
            return res;
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }

    public async Task<string> GetEmailTemplateBodyAsync()
    {
        var conn = _dbContext.Connection;
        try
        {
            var query = $"SELECT \"Body\" FROM \"LeadratBlack\".\"MasterEmailTemplates\" where \"Event\" = 55";
            var res = (await conn.QueryAsync<string>(query)).FirstOrDefault();
            return res;
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }

    public async Task<IEnumerable<string>> GetStoredCurrencyAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select Distinct\"Currency\" from \t\"LeadratBlack\".\"Leads\" l\r\n    " +
               $"LEFT join \"LeadratBlack\".\"LeadEnquiries\" le  ON le.\"LeadId\" = l.\"Id\"\r\nwhere \"Currency\" is not null and" +
               $" \"TenantId\"='{tenantId}'";
            var res = await conn.QueryAsync<string>(query);
            return res;
        }

        catch
        {
            throw;
        }
    }
    public async Task<IEnumerable<string>> GetProspectStoredCurrencyAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select Distinct\"Currency\" from \t\"LeadratBlack\".\"Prospects\" l\r\n    " +
               $"LEFT join \"LeadratBlack\".\"ProspectEnquiries\" le  ON le.\"ProspectId\" = l.\"Id\"\r\nwhere \"Currency\" is not null and" +
               $" \"TenantId\"='{tenantId}'";
            var res = await conn.QueryAsync<string>(query);
            return res;
        }

        catch
        {
            throw;
        }
    }
    public async Task<IEnumerable<string>> GetPropertyCurrencyAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT Distinct(\"Currency\") FROM \"LeadratBlack\".\"PropertyMonetaryInfo\" pm\r\n" +
                $"left join \"LeadratBlack\".\"Properties\" p on pm.\"PropertyId\"=p.\"Id\"\r\nwhere " +
                $"\"TenantId\"='{tenantId}' and \"Currency\" is not null";
            var res = await conn.QueryAsync<string>(query);
            return res;
        }

        catch
        {
            throw;
        }
    }

    public async Task<List<SetWatchForGmailDto>> GetGmailDetailsAsync()
    {
        var conn = _dbContext.Connection;
        try
        {
            var query = $"SELECT \"Id\",\"TenantId\",\"RefreshTokenWeb\",\"Email\" FROM \"LeadratBlack\".\"GmailIntegrationData\"\r\nWHERE  \"IsDeleted\" = 'false'";
            var res = await conn.QueryAsync<SetWatchForGmailDto>(query);
            return res?.ToList() ?? new List<SetWatchForGmailDto>();
        }
        catch
        {
            return new();
        }
    }
    public async Task<bool> UpdateGmailHistoryIdAsync(string historyId, Guid id)
    {
        var conn = _dbContext.Connection;
        try
        {
            var query = $"UPDATE \"LeadratBlack\".\"GmailIntegrationData\" SET \"HistoryId\"={historyId} WHERE \"Id\" = '{id}';";
            var res = await conn.ExecuteAsync(query);
            return true;
        }
        catch
        {
            return false;
        }
    }
    public async Task<IEnumerable<Guid>> GetSubordinateIdsForDashboardAsync(List<Guid> userIds, string tenantId)
    {
        var conn = _dbContext.Connection;
        string adminRoleQuery = $"select count(*) as count from \"Identity\".\"UserRoles\" " +
            $"where \"UserId\" = any(@user_ids) and \"TenantId\" = '{tenantId}' " +
            $"and \"RoleId\" = (Select \"Id\" from \"Identity\".\"Roles\" " +
            $"where \"Name\" = 'Admin' and \"TenantId\" = '{tenantId}')";
        string viewOrgPermissionqQuery = $"select count(*) from \"Identity\".\"Users\" u " +
            $"left join \"Identity\".\"UserRoles\" ur on u.\"Id\" = ur.\"UserId\"" +
            $"left join \"Identity\".\"RoleClaims\" rc on ur.\"RoleId\" = rc.\"RoleId\"" +
            $"where u.\"Id\" = any(@user_ids) and u.\"TenantId\" = '{tenantId}'" +
            $"and rc.\"ClaimValue\" = 'Permissions.Dashboard.ViewOrg'";
        var isAdmin = (await conn.QueryAsync<int>(adminRoleQuery, new { user_ids = userIds.ConvertAll(i => i.ToString()).ToList() })).FirstOrDefault() > 0;
        var viewOrgPermission = isAdmin ? false : (await conn.QueryAsync<int>(viewOrgPermissionqQuery, new { user_ids = userIds.ConvertAll(i => i.ToString()).ToList() })).FirstOrDefault() > 0;
        var subIdQuery = string.Empty;
        if (isAdmin || viewOrgPermission)
        {
            subIdQuery = $"select \"UserId\" from \"LeadratBlack\".\"UserDetails\" where \"TenantId\" = '{tenantId}'";
        }
        else
        {
            subIdQuery = $"WITH RECURSIVE tree as " +
                $"(select \"UserId\" from \"LeadratBlack\".\"UserDetails\" where \"UserId\" = any(@user_ids) " +
                $"and \"TenantId\" = '{tenantId}' union select u.\"UserId\" from \"LeadratBlack\".\"UserDetails\" " +
                $"as u join tree t on t.\"UserId\" = u.\"ReportsTo\") SELECT \"UserId\" FROM tree;";
        }
        var res = await conn.QueryAsync<Guid>(subIdQuery, new { user_ids = userIds });
        return res;
    }

    public async Task<List<string>> GetAllProjectAddressesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT DISTINCT(TRIM(concat(\"Addresses\".\"SubLocality\"|| ' ', \"Addresses\".\"Locality\"|| ' ', \"Addresses\".\"District\"|| ' ',\"Addresses\".\"City\"|| ' ',\r\n\"Addresses\".\"State\"|| ' ',\"Addresses\".\"Country\"|| ' ', \"Addresses\".\"PostalCode\"))) " +
                $"FROM \"LeadratBlack\".\"Projects\" \r\n" +
                $"INNER JOIN \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"Projects\".\"AddressId\"\r\nWHERE \"Projects\".\"TenantId\" = '{tenantId}' AND \"Projects\".\"IsDeleted\" = 'false';";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            throw;
        }
    }
    public async Task<string> GetStatusNameByBaseIdAsync(Guid baseId, string tenantId)
    {
        if (baseId != default && !string.IsNullOrEmpty(tenantId))
        {
            await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
            try
            {
                var query = $"SELECT \"Status\" FROM \"LeadratBlack\".\"CustomMasterLeadStatuses\"\r\nWHERE \"Id\" = '{baseId}' and \"TenantId\" = '{tenantId}'\r\nlimit 1";
                var res = (await conn.QueryAsync<string>(query)).ToList();
                return res?.FirstOrDefault() ?? string.Empty;
            }
            catch (Exception ex)
            {
            }
            finally
            {
                conn.Close();
            }
        }
        return string.Empty;
    }
    public async Task<IEnumerable<string>> GetProjectCurrencyAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT Distinct(\"Currency\") FROM \"LeadratBlack\".\"ProjectMonetaryInfos\" pm\r\n" +
                $"left join \"LeadratBlack\".\"Projects\" p on pm.\"ProjectId\"=p.\"Id\"\r\nwhere " +
                $"\"TenantId\"='{tenantId}' and \"Currency\" is not null";
            var res = await conn.QueryAsync<string>(query);
            return res;
        }

        catch
        {
            throw;
        }
    }
    public async Task<IEnumerable<string>> GetProjectGalleryImageKeysAsync(string tenantId)
    {
        var conn = _dbContext.Connection;
        try
        {

            var query = $"SELECT Distinct(pg.\"ImageKey\") FROM \"LeadratBlack\".\"Projects\" p\r\n" +
                $"left join \"LeadratBlack\".\"ProjectGalleries\" pg on p.\"Id\"=pg.\"ProjectId\" \r\n" +
                $"where p.\"IsDeleted\"='false'  and pg.\"ImageKey\" is not null and p.\"TenantId\"='{tenantId}' ";
            var res = await conn.QueryAsync<string>(query);
            return res;
        }

        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<Guid> GetLeadDetails(string contactNo)
    {
        var conn = _dbContext.Connection;
        string query = @"
            SELECT ""Id""
            FROM ""LeadratBlack"".""Leads"" 
            WHERE ""ContactNo"" = @ContactNo AND ""DuplicateLeadVersion"" IS NOT NULL
            ORDER BY CAST(SUBSTRING(""DuplicateLeadVersion"" FROM 2) AS INTEGER) DESC
            LIMIT 1;
        ";

        var result = await conn.QuerySingleOrDefaultAsync<Lead>(query, new { ContactNo = contactNo });
        if (result != null)
        {
            return result.Id;
        }
        else
        {
            return Guid.Empty;
        }
    }
    public async Task<IEnumerable<Guid>> GetLatestDuplicateLeadsByContactNos(List<string> contactNos)
    {
        var conn = _dbContext.Connection;
        string query = @"
        SELECT DISTINCT ON (""ContactNo"") ""Id""
        FROM ""LeadratBlack"".""Leads""
        WHERE ""ContactNo"" = any(@ContactNos) AND ""DuplicateLeadVersion"" IS NOT NULL
        ORDER BY ""ContactNo"", CAST(SUBSTRING(""DuplicateLeadVersion"" FROM 2) AS INTEGER) DESC";

        var result = await conn.QueryAsync<Guid>(query, new { ContactNos = contactNos });
        return result.ToList();
    }
    public async Task<WAOTPTemplateInfo> GetOTPWhatsAppInfoAsync()
    {
        var conn = _dbContext.Connection;
        try
        {
            conn.Open();
            string query = $"SELECT * FROM \"LeadratBlack\".\"WATemplates\" where \"TenantId\" = 'root' and \"IsDeleted\" = 'false' limit 1";
            var res = await conn.QueryFirstOrDefaultAsync<WAOTPTemplateInfo>(query);
            string query1 = $"SELECT * FROM \"LeadratBlack\".\"WAApiInfos\" where \"TenantId\" = 'root' and \"IsDeleted\" = 'false' limit 1";
            var res1 = await conn.QueryFirstOrDefaultAsync<WAOTPApiInfo>(query1);
            res.WAOTPApiInfo = res1;
            conn.Close();
            return res;
        }
        catch (Exception ex)
        {
            conn.Close();
            throw;
        }

    }

    public async Task<List<LeadForWANotiification>> GetLeadsCountAsync(List<Guid> ids, string tenantId)
    {
        var conn = _dbContext.Connection;
        try
        {
            conn.Open();
            string query = $"SELECT \"Id\", \"Name\", \"AssignTo\" FROM \"LeadratBlack\".\"Leads\" WHERE \"TenantId\" = '{tenantId}' AND \"IsDeleted\" = 'false' AND \"Id\" = any(@lead_ids)";
            var leads = (await conn.QueryAsync(query, new { lead_ids = ids })).ToList();
            conn.Close();
            return leads.Adapt<List<LeadForWANotiification>>();
        }
        catch (Exception ex)
        {
            conn.Close();
            throw;
        }
    }
    public async Task<int> GetLeadCount(string tenantId, Guid id)
    {
        var conn = _dbContext.Connection;
        try
        {
            var query = $"SELECT COUNT(Distinct(l.\"Id\")) AS \"LeadCount\"\r\nFROM \"LeadratBlack\".\"Flags\" AS f\r\nLEFT JOIN \"LeadratBlack\".\"CustomFlags\" AS cf ON f.\"Id\" = cf.\"FlagId\" " +
                $"  \r\nLEFT JOIN \"LeadratBlack\".\"Leads\" AS l on  l.\"Id\" =cf.\"LeadId\"\r\nWHERE l.\"TenantId\" = '{tenantId}' and l.\"IsArchived\"='false' and l.\"IsDeleted\"='false'\r\nAND " +
                $"l.\"CustomLeadStatusId\" IS NOT NULL AND l.\"CustomLeadStatusId\" <> '00000000-0000-0000-0000-000000000000' and f.\"IsDeleted\"='false'\r\nand cf.\"IsDeleted\"=false and f.\"IsDeleted\"=false" +
                $" and  f.\"IsDeleted\"=false and f.\"Id\"='{id}'";
            var res = await conn.QuerySingleAsync<int>(query, new { TenantId = tenantId });
            return res;
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }


    public async Task<IEnumerable<string>> GetAllUplodTypeName(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select distinct \"UploadTypeName\" from \"LeadratBlack\".\"Leads\"\r\nwhere \"UploadTypeName\" is not null and \"TenantId\"='{tenantId}' and \"IsDeleted\"='false' and \"IsArchived\"='false'\r\nand \"CustomLeadStatusId\" is not null and \"CustomLeadStatusId\"<>'00000000-0000-0000-0000-000000000000'\r\n";
            var res = await conn.QueryAsync<string>(query);
            return res;
        }

        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }


    public async Task<IEnumerable<GeneralManagerUserDto>> GetAllGeneralManagersAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $" select DISTINCT ud.\"GeneralManager\" as \"Id\", u.\"FirstName\", u.\"LastName\", u.\"Email\", u.\"IsActive\" FROM \"Identity\".\"Users\" u " +
            $"LEFT JOIN \"LeadratBlack\".\"UserDetails\" ud ON u.\"Id\"::uuid = ud.\"GeneralManager\"::uuid WHERE u.\"IsDeleted\" = false AND ud.\"IsDeleted\" = false  AND ud.\"TenantId\"='{tenantId}' AND u.\"TenantId\"='{tenantId}' AND ud.\"GeneralManager\" != '00000000-0000-0000-0000-000000000000'\r\n";
            var result = await conn.QueryAsync<GeneralManagerUserDto>(query);
            return result;
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<List<string>> GetAllAgencyNamesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string? agencyNamesQuery = $"SELECT DISTINCT \"Name\" FROM \"LeadratBlack\".\"Agencies\" WHERE \"IsDeleted\" = false AND \"TenantId\" = @TenantId";
            var result = await conn.QueryAsync<string>(agencyNamesQuery, new { TenantId = tenantId });
            return result.ToList();
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<List<string>> GetAllChannelPartnerNamesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string? cpNamesQuery = $"SELECT DISTINCT \"FirmName\" FROM \"LeadratBlack\".\"ChannelPartners\" WHERE \"IsDeleted\" = false AND \"TenantId\" = @TenantId";
            var result = await conn.QueryAsync<string>(cpNamesQuery, new { TenantId = tenantId });
            return result.ToList();
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }


    public async Task<IEnumerable<string>> GetAllAgenciesAddressesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select DISTINCT(TRIM(concat(\"Addresses\".\"SubLocality\"|| ' ', \"Addresses\".\"Locality\"|| ' ', \"Addresses\".\"District\"|| ' ',\"Addresses\".\"City\"|| ' ', " +
            $"\"Addresses\".\"State\"|| ' ',\"Addresses\".\"Country\"|| ' ', \"Addresses\".\"PostalCode\"))) from \"LeadratBlack\".\"Agencies\" INNER join \"LeadratBlack\".\"Addresses\"" +
            $" ON \"Addresses\".\"Id\" = \"Agencies\".\"AddressId\"" +
            $" where \"Agencies\".\"TenantId\" = '{tenantId}' and \"Agencies\".\"IsDeleted\" = 'false';";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllAgenciesAddressesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<string>();
        }
    }


    public async Task<IEnumerable<string>> GetAllChannelPartnerAddressesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select DISTINCT(TRIM(concat(\"Addresses\".\"SubLocality\"|| ' ', \"Addresses\".\"Locality\"|| ' ', \"Addresses\".\"District\"|| ' ',\"Addresses\".\"City\"|| ' ', " +
        $"\"Addresses\".\"State\"|| ' ',\"Addresses\".\"Country\"|| ' ', \"Addresses\".\"PostalCode\"))) from \"LeadratBlack\".\"ChannelPartners\" INNER join \"LeadratBlack\".\"Addresses\"" +
        $" ON \"Addresses\".\"Id\" = \"ChannelPartners\".\"AddressId\"" +
        $" where \"ChannelPartners\".\"TenantId\" = '{tenantId}' and \"ChannelPartners\".\"IsDeleted\" = 'false';";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllChannelPartnerAddressesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<string>();
        }
    }
    public async Task<IEnumerable<string>> GetAllPropertyMicrositeSerialNumbers(string tenantId)
    {
        var conn = _dbContext.Connection;
        string query = $"SELECT \"SerialNo\" FROM \"LeadratBlack\".\"Properties\" where \"TenantId\" = '{tenantId}' and \"IsDeleted\" = 'false' and \"IsArchived\" = 'false';";
        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }




    public async Task<IEnumerable<Guid>> GeneralManagerAsync(List<Guid> userIds, string tenantId)
    {
        var conn = _dbContext.Connection;
        var GeneralManagerQuery = $"SELECT \"UserId\" \r\nFROM \"LeadratBlack\".\"UserDetails\"\r\nWHERE \"TenantId\" = '{tenantId}'\r\nAND " +
         $"\"GeneralManager\" = any(@user_ids);\r\n";
        var res = await conn.QueryAsync<Guid>(GeneralManagerQuery, new { user_ids = userIds });
        return res;
    }

    public async Task<IEnumerable<UserStatuses>> UserStatusesAsync(Guid userId, string tenantId, bool isAdmin, bool? IsTeamPresent = null)
    {
        var conn = _dbContext.Connection;
        var query = string.Empty;
        if (isAdmin)
        {
            query = $"select cms.\"DisplayName\", cms.\"OrderRank\",cms.\"Id\"\r\nfrom \"LeadratBlack\".\"CustomMasterLeadStatuses\" cms\r\nwhere cms.\"IsDeleted\" = false and cms.\"TenantId\" = '{tenantId}' and \r\n(cms.\"BaseId\" is null or cms.\"BaseId\"  =  '00000000-0000-0000-0000-000000000000')\r\norder by \"OrderRank\" asc";
        }
        else if (IsTeamPresent == false || IsTeamPresent == null)
        {
            query = $"select cms.\"DisplayName\", cms.\"OrderRank\",cms.\"Id\"\r\nfrom \"LeadratBlack\".\"CustomMasterLeadStatuses\" cms\r\nwhere cms.\"IsDeleted\" = false and cms.\"TenantId\" = '{tenantId}' and \r\n(cms.\"BaseId\" is null or cms.\"BaseId\"  =  '00000000-0000-0000-0000-000000000000')\r\norder by \"OrderRank\" asc";
        }
        else
        {
            query = $"SELECT DISTINCT cs.\"DisplayName\", cs.\"OrderRank\", cs.\"Id\"\r\nFROM \"LeadratBlack\".\"CustomMasterLeadStatuses\" AS cs" +
                $"\r\nLEFT JOIN \"LeadratBlack\".\"CustomMasterLeadStatusTeam\" AS cm ON cm.\"StatusesId\" = cs.\"Id\"\r\n" +
                $"LEFT JOIN \"LeadratBlack\".\"Teams\" AS t ON t.\"Id\" = cm.\"TeamsId\"\r\nWHERE t.\"TenantId\" = '{tenantId}' \r\n" +
                $"  AND t.\"IsDeleted\" = false\r\n  AND cs.\"TenantId\" = '{tenantId}' \r\n  AND cs.\"IsDeleted\" = false\r\n  " +
                $"AND ('{userId}' = ANY(t.\"UserIds\"::uuid[]) \r\n      " +
                $" OR '{userId}' = t.\"Manager\")\r\n  AND " +
                $"(cs.\"BaseId\" IS NULL OR cs.\"BaseId\" = '00000000-0000-0000-0000-000000000000')\r\n" +
                $"GROUP BY cs.\"DisplayName\", cs.\"OrderRank\", cs.\"Id\"\r\nORDER BY cs.\"OrderRank\" ASC;";
        }

        var res = await conn.QueryAsync<UserStatuses>(query);
        return res;
    }


    public async Task<List<string>> GetAdditionalPropertyValuesAsync(string tenantId, string key)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());

        try
        {
            string? leadsQuery = $"SELECT DISTINCT kv.value AS Value " +
                     $"FROM \"LeadratBlack\".\"Leads\", " +
                     $"LATERAL jsonb_each_text(\"AdditionalProperties\") AS kv(key, value) " +
                     $"WHERE \"IsDeleted\" = false AND \"TenantId\" =  @p1  " +
                     $"AND kv.key = @p2";
            var result = await conn.QueryAsync<string>(leadsQuery, new { p1 = tenantId, p2 = key });
            return result.ToList();
        }
        catch
        {
            throw;
        }
    }
    public async Task<IEnumerable<ProjectLeadCountDto>> GetLeadsCountByProjectIdsAsync(string tenantId, List<Guid> projectIds)
    {
        var conn = _dbContext.Connection;
        var query = $"SELECT p.\"Id\" AS \"ProjectId\", COUNT(DISTINCT l.\"Id\") AS \"LeadCount\", " +
            $"COUNT(DISTINCT CASE WHEN la.\"Type\" = 1 AND la.\"IsDone\" = 'true' THEN la.\"Id\" END) AS \"MeetingDoneCount\", " +
            $"COUNT(DISTINCT CASE WHEN la.\"Type\" = 1 AND la.\"IsDone\" = 'true' THEN la.\"LeadId\" END) AS \"MeetingDoneUniqueCount\", " +
            $"COUNT(DISTINCT CASE WHEN la.\"Type\" = 2 AND la.\"IsDone\" = 'true' THEN la.\"Id\" END) AS \"SiteVisitDoneCount\", " +
            $"COUNT(DISTINCT CASE WHEN la.\"Type\" = 2 AND la.\"IsDone\" = 'true' THEN la.\"LeadId\" END) AS \"SiteVisitDoneUniqueCount\" " +
            $"FROM \"LeadratBlack\".\"Projects\" p LEFT JOIN \"LeadratBlack\".\"LeadProject\" lp ON p.\"Id\" = lp.\"ProjectsId\" " +
            $"LEFT JOIN \"LeadratBlack\".\"Leads\" l ON lp.\"LeadsId\" = l.\"Id\" AND l.\"IsDeleted\" = 'false' " +
            $"AND l.\"IsArchived\" = 'false' AND l.\"TenantId\" = '{tenantId}' LEFT JOIN \"LeadratBlack\".\"LeadAppointments\" la ON la.\"LeadId\" = l.\"Id\" " +
            $"AND ((la.\"Type\" = 2 AND la.\"IsDone\" = 'true') OR (la.\"Type\" = 1 AND la.\"IsDone\" = 'true')) " +
            $"WHERE p.\"Id\" = ANY(@ProjectIds) AND p.\"TenantId\" = '{tenantId}' GROUP BY p.\"Id\";";
        var parameters = new { ProjectIds = projectIds.ToArray() };
        var result = await conn.QueryAsync<ProjectLeadCountDto>(query, parameters);
        return result;
    }

    public async Task<IEnumerable<ProjectLeadCountDto>> GetProspectsCountByProjectIdsAsync(string tenantId, List<Guid> projectIds)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT p.\"Id\" AS \"ProjectId\", " +
            $"COALESCE(COUNT(DISTINCT ps.\"Id\"), 0) AS \"ProspectCount\" " +
            $"FROM \"LeadratBlack\".\"Projects\" p " +
            $"LEFT JOIN \"LeadratBlack\".\"ProjectProspect\" pp ON p.\"Id\" = pp.\"ProjectsId\" " +
            $"LEFT JOIN \"LeadratBlack\".\"Prospects\" ps ON pp.\"ProspectsId\" = ps.\"Id\" " +
            $"where ps.\"IsDeleted\" = FALSE " +
            $"AND ps.\"IsArchived\" = FALSE " +
            $"AND ps.\"TenantId\" = '{tenantId}' " +
            $"and p.\"Id\" = ANY(@ProjectIds) " +
            $"GROUP BY p.\"Id\";";
            var parameters = new { ProjectIds = projectIds.ToArray() };
            var result = await conn.QueryAsync<ProjectLeadCountDto>(query, parameters);
            return result;
        }
        catch
        {
            conn.Close();
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<IEnumerable<PropertyLeadCountDto>> GetLeadsCountByPropertyIdsAsync(string tenantId, List<Guid> propertyIds)
    {
        var conn = _dbContext.Connection;
        var query = $"SELECT p.\"Id\" AS \"PropertyId\", COUNT(DISTINCT l.\"Id\") AS \"LeadCount\", " +
            $"COUNT(DISTINCT CASE WHEN la.\"Type\" = 1 AND la.\"IsDone\" = 'true' THEN la.\"Id\" END) AS \"MeetingDoneCount\", " +
            $"COUNT(DISTINCT CASE WHEN la.\"Type\" = 1 AND la.\"IsDone\" = 'true' THEN la.\"LeadId\" END) AS \"MeetingDoneUniqueCount\", " +
            $"COUNT(DISTINCT CASE WHEN la.\"Type\" = 2 AND la.\"IsDone\" = 'true' THEN la.\"Id\" END) AS \"SiteVisitDoneCount\", " +
            $"COUNT(DISTINCT CASE WHEN la.\"Type\" = 2 AND la.\"IsDone\" = 'true' THEN la.\"LeadId\" END) AS \"SiteVisitDoneUniqueCount\" " +
            $"FROM \"LeadratBlack\".\"Properties\" p LEFT JOIN \"LeadratBlack\".\"LeadProperty\" lp ON p.\"Id\" = lp.\"PropertiesId\" " +
            $"LEFT JOIN \"LeadratBlack\".\"Leads\" l ON lp.\"LeadsId\" = l.\"Id\" AND l.\"IsDeleted\" = 'false' " +
            $"AND l.\"IsArchived\" = 'false' AND l.\"TenantId\" = '{tenantId}' LEFT JOIN \"LeadratBlack\".\"LeadAppointments\" la ON la.\"LeadId\" = l.\"Id\" " +
            $"AND ((la.\"Type\" = 2 AND la.\"IsDone\" = 'true') OR (la.\"Type\" = 1 AND la.\"IsDone\" = 'true')) " +
            $"WHERE p.\"Id\" = ANY(@PropertyIds) AND p.\"TenantId\" = '{tenantId}' GROUP BY p.\"Id\";";
        var parameters = new { PropertyIds = propertyIds.ToArray() };
        var result = await conn.QueryAsync<PropertyLeadCountDto>(query, parameters);
        return result;
    }
    public async Task<IEnumerable<PropertyLeadCountDto>> GetProspectsCountByPropertyIdsAsync(string tenantId, List<Guid> propertyIds)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT p.\"Id\" AS \"PropertyId\", " +
            $"COALESCE(COUNT(DISTINCT ps.\"Id\"), 0) AS \"ProspectCount\" " +
            $"FROM \"LeadratBlack\".\"Properties\" p " +
            $"LEFT JOIN \"LeadratBlack\".\"PropertyProspect\" pp ON p.\"Id\" = pp.\"PropertiesId\" " +
            $"LEFT JOIN \"LeadratBlack\".\"Prospects\" ps ON pp.\"ProspectsId\" = ps.\"Id\" " +
            $"where ps.\"IsDeleted\" = FALSE " +
            $"AND ps.\"IsArchived\" = FALSE " +
            $"AND ps.\"TenantId\" = '{tenantId}' " +
            $"and p.\"Id\" = ANY(@PropertyIds) " +
            $"GROUP BY p.\"Id\";";
            var parameters = new { PropertyIds = propertyIds.ToArray() };
            var result = await conn.QueryAsync<PropertyLeadCountDto>(query, parameters);
            return result;
        }
        catch
        {
            conn.Close();
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<TransactionInfoViewDto> GetTransactionByIdAsync(string orderId)
    {
        var conn = _dbContext.Connection;
        try
        {
            string? query = $"select * from \"LeadratBlack\".\"TransactionInfos\" where \"OrderId\"='{orderId}'";
            var result = (await conn.QueryAsync<TransactionInfoViewDto>(query)).FirstOrDefault();
            return result;
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<int> UpdatePaymentTransactionInfoAsync(TransactionInfoViewDto? transactionInfo, CashfreePaymentDto? cashfreePaymentDto)
    {
        try
        {
            var conn = _dbContext.Connection;
            var query = $"update \"LeadratBlack\".\"TransactionInfos\" set \"Amount\"='{cashfreePaymentDto?.order_amount}' ," +
                $" \"State\"='{cashfreePaymentDto?.payment_status}',\"ResponseCode\"='{cashfreePaymentDto?.gateway_status_code}'," +
                $" \"PaymentInstrument\"='{JsonConvert.SerializeObject(cashfreePaymentDto?.payment_method)}'" +
                $",\"TransactionId\"='{cashfreePaymentDto?.cf_payment_id}' where \"Id\"='{transactionInfo?.Id}'";

            var res = await conn.ExecuteAsync(query);
            return res;
        }
        catch (Exception ex) { throw; }
    }
    public async Task<IEnumerable<string>> GetAllAmenitiesForPropertyAsync(string tenantId, List<Guid>? Ids)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string? amenityQuery = $"SELECT \"AmenityDisplayName\" " +
                                   $"FROM \"LeadratBlack\".\"MasterPropertyAmenities\" " +
                                   $"WHERE \"Id\" = ANY(@Ids) AND \"IsDeleted\" = false";
            var result = await conn.QueryAsync<string>(amenityQuery, new { tenantId, Ids });
            return result.ToList();
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<IEnumerable<string>> GetAllAmenitiesForProjetAsync(string tenantId, List<Guid>? Ids)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string? amenityQuery = $"SELECT \"AmenityDisplayName\" " +
                                   $"FROM \"LeadratBlack\".\"CustomMasterAmenities\" " +
                                   $"WHERE \"Id\" = ANY(@Ids) AND \"IsDeleted\" = false";
            var result = await conn.QueryAsync<string>(amenityQuery, new { tenantId, Ids });
            return result.ToList();
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }

    public async Task<bool> CheckAssignToByLeadIdAsync(string tenantId, Guid id, List<Guid>? subIds)
    {
        using var conn = _dbContext.Connection;
        try
        {
            string leadQuery = "SELECT \"Id\" FROM \"LeadratBlack\".\"Leads\" WHERE \"Id\" = @Id AND \"IsDeleted\" = false AND \"TenantId\" = @TenantId AND (\"AssignTo\" = ANY(@SubIds) OR \"SecondaryUserId\" = ANY(@SubIds) OR \"AssignTo\"='00000000-0000-0000-0000-000000000000')";
            var result = await conn.QuerySingleOrDefaultAsync<Guid?>(leadQuery, new { TenantId = tenantId, Id = id, SubIds = subIds });
            return (result ?? Guid.Empty) != Guid.Empty;
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<bool> CheckAssignToByProspectIdAsync(string tenantId, Guid id, List<Guid>? subIds)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string leadQuery = "SELECT \"Id\" FROM \"LeadratBlack\".\"Prospects\" WHERE \"Id\" = @Id AND \"IsDeleted\" = false AND \"TenantId\" = @TenantId AND (\"AssignTo\" = ANY(@SubIds) OR \"AssignTo\"='00000000-0000-0000-0000-000000000000')";
            var result = await conn.QuerySingleOrDefaultAsync<Guid?>(leadQuery, new { TenantId = tenantId, Id = id, SubIds = subIds });
            return (result ?? Guid.Empty) != Guid.Empty;

        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }

    public async Task<IEnumerable<Guid>> GetSubordinateIdsByUserIdAsync(Guid userId, string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var subIdQuery = string.Empty;
            subIdQuery = $"WITH RECURSIVE tree as " +
                $"(select \"UserId\" from \"LeadratBlack\".\"UserDetails\" where \"UserId\" = '{userId}' and \"IsDeleted\" = 'false' " +
                $"and \"TenantId\" = '{tenantId}' union select u.\"UserId\" from \"LeadratBlack\".\"UserDetails\" " +
                $"as u join tree t on t.\"UserId\" = u.\"ReportsTo\" and u.\"IsDeleted\" = 'false') SELECT \"UserId\" FROM tree;";
            var res = await conn.QueryAsync<Guid>(subIdQuery);
            return res;
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }

    public async Task<List<string>> GetAllSubCommunitiesForListingManagement(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"Select Distinct(\"SubCommunity\") from \"LeadratBlack\".\"ListingSourceAddresses\" where \"TenantId\" = '{tenantId}'";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch
        {
            throw;
        }
    }
    public async Task<List<string>> GetAllCommunitiesForListingManagement(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"Select Distinct(\"Community\") from \"LeadratBlack\".\"ListingSourceAddresses\" where \"TenantId\" = '{tenantId}'";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch
        {
            throw;
        }
    }
    public async Task<IEnumerable<string>> GetAllLeadsCountriesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select Distinct(\"Addresses\".\"Country\") from \"LeadratBlack\".\"Leads\" " +
            $"INNER join \"LeadratBlack\".\"LeadEnquiries\" ON \"LeadEnquiries\".\"LeadId\" = \"Leads\".\"Id\" " +
            $"INNER join \"LeadratBlack\".\"AddressLeadEnquiry\" ON \"LeadEnquiries\".\"Id\" = \"AddressLeadEnquiry\".\"EnquiriesId\"  " +
            $"INNER join \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressLeadEnquiry\".\"AddressesId\"  " +
            $"where \"Leads\".\"TenantId\" = '{tenantId}' and \"Leads\".\"IsDeleted\" = 'false';";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllLeadsCountriesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<string>();
        }
    }
    public async Task<IEnumerable<string>> GetAllLeadsCommunitiesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select Distinct(\"Addresses\".\"Community\") from \"LeadratBlack\".\"Leads\" " +
            $"INNER join \"LeadratBlack\".\"LeadEnquiries\" ON \"LeadEnquiries\".\"LeadId\" = \"Leads\".\"Id\" " +
            $"INNER join \"LeadratBlack\".\"AddressLeadEnquiry\" ON \"LeadEnquiries\".\"Id\" = \"AddressLeadEnquiry\".\"EnquiriesId\"  " +
            $"INNER join \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressLeadEnquiry\".\"AddressesId\"  " +
            $"where \"Leads\".\"TenantId\" = '{tenantId}' and \"Leads\".\"IsDeleted\" = 'false';";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllLeadsCommunitiesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<string>();
        }
    }
    public async Task<IEnumerable<string>> GetAllLeadsSubCommunitiesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select Distinct(\"Addresses\".\"SubCommunity\") from \"LeadratBlack\".\"Leads\" " +
            $"INNER join \"LeadratBlack\".\"LeadEnquiries\" ON \"LeadEnquiries\".\"LeadId\" = \"Leads\".\"Id\" " +
            $"INNER join \"LeadratBlack\".\"AddressLeadEnquiry\" ON \"LeadEnquiries\".\"Id\" = \"AddressLeadEnquiry\".\"EnquiriesId\"  " +
            $"INNER join \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressLeadEnquiry\".\"AddressesId\"  " +
            $"where \"Leads\".\"TenantId\" = '{tenantId}' and \"Leads\".\"IsDeleted\" = 'false';";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllLeadsSubCommunitiesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<string>();
        }
    }
    public async Task<IEnumerable<string>> GetAllLeadsTowerNamesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select Distinct(\"Addresses\".\"TowerName\") from \"LeadratBlack\".\"Leads\" " +
            $"INNER join \"LeadratBlack\".\"LeadEnquiries\" ON \"LeadEnquiries\".\"LeadId\" = \"Leads\".\"Id\" " +
            $"INNER join \"LeadratBlack\".\"AddressLeadEnquiry\" ON \"LeadEnquiries\".\"Id\" = \"AddressLeadEnquiry\".\"EnquiriesId\"  " +
            $"INNER join \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressLeadEnquiry\".\"AddressesId\"  " +
            $"where \"Leads\".\"TenantId\" = '{tenantId}' and \"Leads\".\"IsDeleted\" = 'false';";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllLeadsTowerNamesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<string>();
        }
    }
    public async Task<IEnumerable<string>> GetAllLeadsPostalCodeAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select Distinct(\"Addresses\".\"PostalCode\") from \"LeadratBlack\".\"Leads\" " +
            $"INNER join \"LeadratBlack\".\"LeadEnquiries\" ON \"LeadEnquiries\".\"LeadId\" = \"Leads\".\"Id\" " +
            $"INNER join \"LeadratBlack\".\"AddressLeadEnquiry\" ON \"LeadEnquiries\".\"Id\" = \"AddressLeadEnquiry\".\"EnquiriesId\"  " +
            $"INNER join \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressLeadEnquiry\".\"AddressesId\"  " +
            $"where \"Leads\".\"TenantId\" = '{tenantId}' and \"Leads\".\"IsDeleted\" = 'false';";
            var res = (await conn.QueryAsync<string>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllLeadsPostalCodeAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<string>();
        }
    }
    public async Task<IEnumerable<DuplicateDto>> CheckDuplicateLeadsAsync(List<string> contactNos, List<string>? altNumbers, string? tenantId)
    {
        contactNos = contactNos?.Distinct().ToList() ?? new();
        altNumbers = altNumbers?.Distinct().ToList() ?? new();
        var allNumbers = contactNos.Concat(altNumbers).Distinct().ToArray();
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());

        try
        {
            string query = $"select \"ContactNo\", \"AlternateContactNo\" from \"LeadratBlack\".\"Leads\" where \"IsArchived\" = FALSE AND \"IsDeleted\" = FALSE AND \"TenantId\" = '{tenantId}' AND (\"ContactNo\" = ANY(@AllNumbers) OR \"AlternateContactNo\" = ANY(@AllNumbers)) ORDER BY \"LastModifiedOn\" DESC;";

            var res = (await conn.QueryAsync<DuplicateDto>(query, new { AllNumbers = allNumbers }, commandTimeout: 600)).ToList();
            return res;
        }
        catch
        {
            throw;
        }
    }
    public async Task<IEnumerable<DuplicateDto>> CheckDuplicatePropsectsAsync(List<string> contactNos, List<string>? altNumbers, string? tenantId)
    {
        contactNos = contactNos?.Distinct().ToList() ?? new();
        altNumbers = altNumbers?.Distinct().ToList() ?? new();
        var allNumbers = contactNos.Concat(altNumbers).Distinct().ToArray();
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select \"ContactNo\", \"AlternateContactNo\" from \"LeadratBlack\".\"Prospects\" where \"IsArchived\" = FALSE AND \"IsDeleted\" = FALSE AND \"TenantId\" = '{tenantId}' AND (\"ContactNo\" = ANY(@AllNumbers) OR \"AlternateContactNo\" = ANY(@AllNumbers)) ORDER BY \"LastModifiedOn\" DESC;";

            var res = (await conn.QueryAsync<DuplicateDto>(query, new { AllNumbers = allNumbers }, commandTimeout: 600)).ToList();
            return res;
        }
        catch
        {
            throw;
        }
    }
    public async Task<IEnumerable<string>> GetNotificationTrackersJobId(string tenantId, Guid entityId, DateTime? scheduledDate = null)
    {

        if (scheduledDate == null || scheduledDate > DateTime.UtcNow)
        {
            await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
            try
            {
                var query = $"SELECT \"JobId\"\r\nFROM \"LeadratBlack\".\"NotificationTrackers\" AS n\r\nWHERE \r\n    n.\"DeletedOn\" IS NULL\r\n    AND n.\"TenantId\" = '{tenantId}'\r\n    AND n.\"IsDeleted\" = false\r\n    AND n.\"EntityId\" = '{entityId}'\r\n    AND n.\"ScheduleTime\"::timestamptz > now() AT TIME ZONE 'UTC'";
                conn.Open();
                var res = (await conn.QueryAsync<string>(query)).ToList();
                return res;
            }
            catch
            {
            }
        }
        return Enumerable.Empty<string>();
    }

    public async Task<Payment> GetPaymentByOrderId(string? orderId)
    {
        if (!string.IsNullOrWhiteSpace(orderId))
        {
            var conn = _dbContext.Connection;
            var query = $"select * from \"LeadratBlack\".\"Payments\" where \"IsDeleted\"='false' and \"OrderId\"='{orderId}' ";
            var res = (await conn.QueryAsync<Payment>(query)).FirstOrDefault();
            return res;
        }
        return null;
    }
    public async Task<IEnumerable<UserBasicInfoDto>> GetUserDetailsAsync(string tenantId, List<Guid> ids)
    {

        var conn = _dbContext.Connection;
        try
        {
            var query = $"select \"Id\",\"Email\",\"ReportsTo\",\"GeneralManager\" from \"LeadratBlack\".\"VWFullUserInfo\"" +
            $" where \"Id\" = any(@Ids)" +
            $" and \"TenantId\" = @TenantId" +
            $" and \"IsActive\" = true" +
            $" and \"IsDeleted\" = false";
            var res = (await conn.QueryAsync<UserFormattedDto>(query, new { Ids = ids, TenantId = tenantId })).ToList().Adapt<List<UserBasicInfoDto>>();
            return res;
        }
        catch (Exception ex)
        {
            throw ex;
        }
    }
    public async Task<IEnumerable<string>> GetAllProspectCountriesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());

        string query = $"\r\nSELECT DISTINCT \"Country\"\r\nFROM \"LeadratBlack\".\"Prospects\"\r\n" +
            $"INNER JOIN \"LeadratBlack\".\"ProspectEnquiries\" ON \"ProspectEnquiries\".\"ProspectId\" = \"Prospects\".\"Id\"\r\n" +
            $"INNER JOIN \"LeadratBlack\".\"AddressProspectEnquiry\" ON \"AddressProspectEnquiry\".\"ProspectEnquiriesId\" = \"ProspectEnquiries\".\"Id\"\r\n" +
            $"INNER JOIN \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressProspectEnquiry\".\"AddressesId\"\r\n" +
            $"WHERE \"Prospects\".\"TenantId\" = '{tenantId}' AND \"Prospects\".\"IsDeleted\" = FALSE AND \"Addresses\".\"Country\"<>'';\r\n";
        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }
    public async Task<IEnumerable<string>> GetAllProspectCommunitiesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        string query = $"SELECT DISTINCT \"Community\"\r\nFROM \"LeadratBlack\".\"Prospects\"\r\n" +
            $"INNER JOIN \"LeadratBlack\".\"ProspectEnquiries\" ON \"ProspectEnquiries\".\"ProspectId\" = \"Prospects\".\"Id\"\r\n" +
            $"INNER JOIN \"LeadratBlack\".\"AddressProspectEnquiry\" ON \"AddressProspectEnquiry\".\"ProspectEnquiriesId\" = \"ProspectEnquiries\".\"Id\"\r\n" +
            $"INNER JOIN \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressProspectEnquiry\".\"AddressesId\"\r\n" +
            $"WHERE \"Prospects\".\"TenantId\" = '{tenantId}' AND \"Prospects\".\"IsDeleted\" = FALSE AND \"Addresses\".\"Community\"<>'';";
        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }
    public async Task<IEnumerable<string>> GetAllProspectSubCommunitiesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        string query = $"\r\nSELECT DISTINCT \"SubCommunity\"\r\nFROM \"LeadratBlack\".\"Prospects\"\r\n" +
            $"INNER JOIN \"LeadratBlack\".\"ProspectEnquiries\" ON \"ProspectEnquiries\".\"ProspectId\" = \"Prospects\".\"Id\"\r\n" +
            $"INNER JOIN \"LeadratBlack\".\"AddressProspectEnquiry\" ON \"AddressProspectEnquiry\".\"ProspectEnquiriesId\" = \"ProspectEnquiries\".\"Id\"\r\n" +
            $"INNER JOIN \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressProspectEnquiry\".\"AddressesId\"\r\n" +
            $"WHERE \"Prospects\".\"TenantId\" = '{tenantId}' AND \"Prospects\".\"IsDeleted\" = FALSE AND \"Addresses\".\"SubCommunity\"<>'';\r\n";
        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }
    public async Task<IEnumerable<string>> GetAllProspectTowerNamesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        string query = $"\r\nSELECT DISTINCT \"TowerName\"\r\nFROM \"LeadratBlack\".\"Prospects\"\r\n" +
            $"INNER JOIN \"LeadratBlack\".\"ProspectEnquiries\" ON \"ProspectEnquiries\".\"ProspectId\" = \"Prospects\".\"Id\"\r\n" +
            $"INNER JOIN \"LeadratBlack\".\"AddressProspectEnquiry\" ON \"AddressProspectEnquiry\".\"ProspectEnquiriesId\" = \"ProspectEnquiries\".\"Id\"\r\n" +
            $"INNER JOIN \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressProspectEnquiry\".\"AddressesId\"\r\n" +
            $"WHERE \"Prospects\".\"TenantId\" = '{tenantId}' AND \"Prospects\".\"IsDeleted\" = FALSE AND \"Addresses\".\"TowerName\"<>'';\r\n\r\n";
        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }
    public async Task<IEnumerable<string>> GetAllProspectPostalCodeAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        string query = $"SELECT DISTINCT \"PostalCode\"\r\nFROM \"LeadratBlack\".\"Prospects\"\r\n" +
            $"INNER JOIN \"LeadratBlack\".\"ProspectEnquiries\" ON \"ProspectEnquiries\".\"ProspectId\" = \"Prospects\".\"Id\"\r\n" +
            $"INNER JOIN \"LeadratBlack\".\"AddressProspectEnquiry\" ON \"AddressProspectEnquiry\".\"ProspectEnquiriesId\" = \"ProspectEnquiries\".\"Id\"\r\n" +
            $"INNER JOIN \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressProspectEnquiry\".\"AddressesId\"\r\n" +
            $"WHERE \"Prospects\".\"TenantId\" = '{tenantId}' AND \"Prospects\".\"IsDeleted\" = FALSE AND \"Addresses\".\"PostalCode\"<>'';\r\n\r\n";
        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }
    public async Task<IEnumerable<string>> GetAllProspectsLocalitesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        string query = $"SELECT DISTINCT TRIM(CONCAT(\"Addresses\".\"SubLocality\", ' ', \"Addresses\".\"Locality\"))" +
          $"FROM \"LeadratBlack\".\"Prospects\"" +
          $"INNER JOIN \"LeadratBlack\".\"ProspectEnquiries\" ON \"ProspectEnquiries\".\"ProspectId\" = \"Prospects\".\"Id\"" +
          $"INNER JOIN \"LeadratBlack\".\"AddressProspectEnquiry\" ON \"ProspectEnquiries\".\"Id\" = \"AddressProspectEnquiry\".\"ProspectEnquiriesId\"" +
          $"INNER JOIN \"LeadratBlack\".\"Addresses\" ON \"Addresses\".\"Id\" = \"AddressProspectEnquiry\".\"AddressesId\"" +
          $"WHERE \"Prospects\".\"TenantId\" = '{tenantId}' AND \"Prospects\".\"IsDeleted\" = 'false' ";
        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }
    public async Task<string> GetDisplayIndexPrefixByTenantIdAsync(string tenantId)
    {
        var conn = _dbContext.Connection;
        try
        {
            string query = $"SELECT \"DisplayPrefix\" FROM \"MultiTenancy\".\"Tenants\" WHERE \"Identifier\" = '{tenantId}' LIMIT 1;";
            var res = await conn.QueryFirstOrDefaultAsync<string>(query);
            return res;
        }
        catch
        {
            throw;
        }
    }
    public async Task<long> GetLeadMaxSerialNumberAsync(string prefix)
    {
        var conn = _dbContext.Connection;
        try
        {
            string query = $"SELECT MAX(CAST(SUBSTRING(\"SerialNumber\", 3) AS BIGINT)) FROM \"LeadratBlack\".\"Leads\" WHERE \"SerialNumber\" LIKE @Prefix || '%'";
            var res = await conn.QueryFirstOrDefaultAsync<long?>(query, new { Prefix = prefix });
            return res ?? 0;
        }
        catch
        {
            throw;
        }
    }
    public async Task<long> GetProjectMaxSerialNumberAsync(string prefix)
    {
        var conn = _dbContext.Connection;
        try
        {
            string query = $"SELECT MAX(CAST(SUBSTRING(\"SerialNo\", 3) AS BIGINT)) FROM \"LeadratBlack\".\"Projects\" WHERE \"SerialNo\" LIKE @Prefix || '%'";
            var res = await conn.QueryFirstOrDefaultAsync<long?>(query, new { Prefix = prefix });
            return res ?? 0;
        }
        catch
        {
            throw;
        }
    }
    public async Task<long> GetPropertiesMaxSerialNumberAsync(string prefix)
    {
        var conn = _dbContext.Connection;
        try
        {
            string query = $"SELECT MAX(CAST(SUBSTRING(\"SerialNo\", 3) AS BIGINT)) FROM \"LeadratBlack\".\"Properties\" WHERE \"SerialNo\" LIKE @Prefix || '%'";
            var res = await conn.QueryFirstOrDefaultAsync<long?>(query, new { Prefix = prefix });
            return res ?? 0;
        }
        catch
        {
            throw;
        }
    }
    public async Task<IEnumerable<string>> GetAllProspectsNationalitiesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        string query = $"select Distinct(\"Nationality\")from \"LeadratBlack\".\"Prospects\"\r\n" +
            $"where \"TenantId\"='{tenantId}' and \"Nationality\" <>''";
        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }
    public async Task<IEnumerable<string>> GetAllLeadsNationalitiesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        string query = $"select Distinct(\"Nationality\")from \"LeadratBlack\".\"Leads\"\r\n" +
            $"where \"TenantId\"='{tenantId}' and \"Nationality\" <>''";
        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }
    public async Task<IEnumerable<string>> GetAllLeadUnitNameAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        string query = $"select Distinct(\"UnitName\")from\r\n\"LeadratBlack\".\"Leads\" as l\r\nleft join \"LeadratBlack\".\"LeadEnquiries\"" +
            $" as le on l.\"Id\"=le.\"LeadId\"\r\nwhere  \"UnitName\" <>'' and \"TenantId\"='{tenantId}'";
        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }
    public async Task<IEnumerable<string>> GetAllProspectUnitNameAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        string query = $"select Distinct(\"UnitName\")from\r\n\"LeadratBlack\".\"Prospects\" as l\r\n" +
            $"left join \"LeadratBlack\".\"ProspectEnquiries\" as le on l.\"Id\"=le.\"ProspectId\"\r\n" +
            $"where  \"UnitName\" <>'' and \"TenantId\"='{tenantId}'";
        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }
    public async Task<IEnumerable<string>> GetAllProspectClusterNameAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        string query = $"select Distinct(\"ClusterName\")from\r\n\"LeadratBlack\".\"Prospects\" as l\r\n" +
            $"left join \"LeadratBlack\".\"ProspectEnquiries\" as le on l.\"Id\"=le.\"ProspectId\"\r\n" +
            $"where  \"ClusterName\" <>'' and \"TenantId\"='{tenantId}'";
        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }
    public async Task<IEnumerable<string>> GetAllLeadClusterNameAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        string query = $"select Distinct(\"ClusterName\")from\r\n\"LeadratBlack\".\"Leads\" as l\r\n" +
            $"left join \"LeadratBlack\".\"LeadEnquiries\" as le on l.\"Id\"=le.\"LeadId\"\r\n" +
            $"where  \"ClusterName\" <>'' and \"TenantId\"='{tenantId}'";
        var res = (await conn.QueryAsync<string>(query)).ToList();
        return res;
    }
    public async Task<List<string>> GetAllCampaignsNamesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string? cpNamesQuery = $"SELECT DISTINCT \"Name\" FROM \"LeadratBlack\".\"Campaigns\" WHERE \"IsDeleted\" = false AND \"TenantId\" = @TenantId";
            var result = await conn.QueryAsync<string>(cpNamesQuery, new { TenantId = tenantId });
            return result.ToList();
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<List<string>> GetAllProjectNamesAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string? cpNamesQuery = $"select Distinct(\"Name\") from \"LeadratBlack\".\"Projects\"\r\n" +
                $"where \"IsDeleted\"='false' and \"IsArchived\"='false' and \"TenantId\"='{tenantId}' and \"Name\"<>''";
            var result = await conn.QueryAsync<string>(cpNamesQuery, new { TenantId = tenantId });
            return result.ToList();
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<List<string>> GetAllPropertyTitleAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string? cpNamesQuery = $"select Distinct(\"Title\") from \"LeadratBlack\".\"Properties\"\r\n" +
                $"where \"IsDeleted\"='false' and \"IsArchived\"='false' and \"TenantId\"='{tenantId}' and \"Title\"<>''";
            var result = await conn.QueryAsync<string>(cpNamesQuery, new { TenantId = tenantId });
            return result.ToList();
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<List<ParentLeadDto>> GetAllParentLeadInfo(string tenantId, List<Guid>? rootIds)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select \"Id\", \"Name\",\"IsArchived\" from \"LeadratBlack\".\"Leads\" where \"IsDeleted\"='false' and \"TenantId\" = @TenantId and \"Id\" = ANY(@RootIds)";
            var res = await conn.QueryAsync<ParentLeadDto>(query, new { TenantId = tenantId, RootIds = rootIds });
            return res?.ToList() ?? new List<ParentLeadDto>();
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<bool> UpdateLeadHistory(List<Guid> ids)
    {
        var conn = _dbContext.Connection;
        try
        {
            var idsString = string.Join(",", ids.Select(id => $"'{id}'"));
            var query = $"UPDATE \"LeadratBlack\".\"LeadHistories\"\r\n" +
                $"SET \"CurrentVersion\" = \"CurrentVersion\" + 1,\r\n   " +
                $" \"ModifiedDate\" = jsonb_set(\"ModifiedDate\", ('{{' || (\"CurrentVersion\" + 1) || '}}')::text[], to_jsonb(CURRENT_TIMESTAMP::timestamp with time zone),  true),\r\n  " +
                $"  \"AssignedTo\" = jsonb_set(\"AssignedTo\",  ('{{' || (\"CurrentVersion\" + 1) || '}}')::text[],  '\"00000000-0000-0000-0000-000000000000\"', true),\r\n  " +
                $"  \"LastModifiedBy\" = jsonb_set(\"LastModifiedBy\", ('{{' || (\"CurrentVersion\" + 1) || '}}')::text[],  '\"\"',  true),\r\n   " +
                $" \"AssignedToUser\" = jsonb_set(\"AssignedToUser\",  ('{{' || (\"CurrentVersion\" + 1) || '}}')::text[], '\"\"', true)," +
                $"\r\n\t\"AssignedFromUser\" = jsonb_set(\"AssignedFromUser\",  ('{{' || (\"CurrentVersion\" + 1) || '}}')::text[], '\"\"', true),\r\n " +
                $"   \"LastModifiedByUser\" = jsonb_set(\"LastModifiedByUser\", ('{{' || (\"CurrentVersion\" + 1) || '}}')::text[], '\"00000000-0000-0000-0000-000000000000\"',  true)," +
                $"\r\n\t\"SecondaryUserId\"=jsonb_set(\"SecondaryUserId\",('{{'|| (\"CurrentVersion\" + 1) || '}}')::text[], '\"00000000-0000-0000-0000-000000000000\"',  true)," +
                $"\r\n\t\"SecondaryUser\" = jsonb_set(\"SecondaryUser\",  ('{{' || (\"CurrentVersion\" + 1) || '}}')::text[], '\"\"', true)\r\n" +
                $"WHERE \"LeadId\" IN ({idsString});";
            var res = await conn.ExecuteAsync(query);
            return true;
        }
        catch (Exception ex)
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<bool> UpdateSecLeadHistory(List<Guid> ids, Guid CurrentUser, string username)
    {

        var conn = _dbContext.Connection;
        try
        {
            var idsString = string.Join(",", ids.Select(id => $"'{id}'"));
            var query = $"\r\nUPDATE \"LeadratBlack\".\"LeadHistories\"\r\nSET \r\n " +
             $"   \"CurrentVersion\" = \"CurrentVersion\" + 1,\r\n  " +
             $"  \"ModifiedDate\" = jsonb_set(\"ModifiedDate\", ('{{' || (\"CurrentVersion\" + 1) || '}}')::text[], to_jsonb(CURRENT_TIMESTAMP::timestamp with time zone), true),\r\n " +
             $"   \"LastModifiedBy\" = jsonb_set(\"LastModifiedBy\", ('{{' || (\"CurrentVersion\" + 1) || '}}')::text[], '\"{username}\"', true),\r\n " +
             $"   \"LastModifiedByUser\" = jsonb_set(\"LastModifiedByUser\", ('{{' || (\"CurrentVersion\" + 1) || '}}')::text[], '\"{CurrentUser}\"', true),\r\n  " +
             $"\r\n\t\"SecondaryUser\" = jsonb_set(\"SecondaryUser\",  ('{{' || (\"CurrentVersion\" + 1) || '}}')::text[], '\"\"', true),\r\n" +
             $"\t\"SecondaryUserId\" = jsonb_set(\"SecondaryUserId\", ('{{' || (\"CurrentVersion\" + 1) || '}}')::text[], '\"00000000-0000-0000-0000-000000000000\"', true)\r\n" +
             $"WHERE \"LeadId\" IN ({idsString});\r\n\r\n ";
            var res = await conn.ExecuteAsync(query);


            return true;
        }
        catch (Exception ex)
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<List<T>> GetAllLeadPropertyInfo<T>(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select \"Title\",\"IsArchived\" from \"LeadratBlack\".\"Properties\" where \"IsDeleted\"='false' and \"TenantId\" = '{tenantId}'";
            var res = await conn.QueryAsync<T>(query);
            return res?.ToList() ?? new List<T>();
        }
        catch (Exception ex)
        {
            conn.Close();
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<List<T>> GetAllLeadProjectInfo<T>(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select \"Name\",\"IsArchived\" from \"LeadratBlack\".\"Projects\" where \"IsDeleted\"='false' and \"TenantId\" = '{tenantId}'";
            var res = await conn.QueryAsync<T>(query);
            return res?.ToList() ?? new List<T>();
        }
        catch (Exception ex)
        {
            conn.Close();
            throw;
        }

        finally
        {
            conn.Close();
        }
    }

    public async Task<IEnumerable<string>> GetAllUplodTypeNameOfProspectAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select distinct \"UploadTypeName\" from \"LeadratBlack\".\"Prospects\"\r\nwhere \"UploadTypeName\" is not null and \"TenantId\"='{tenantId}' and \"IsDeleted\"='false' and \"IsArchived\"='false' ";
            var res = await conn.QueryAsync<string>(query);
            return res;
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }

    public async Task<List<string>> GetAllAmenityCategoryAsync()
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select Distinct(\"Category\")  from\"LeadratBlack\".\"CustomMasterAmenities\"\r\n";
            var res = await conn.QueryAsync<string>(query);
            return res?.ToList() ?? new List<string>();
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<IEnumerable<Lrb.Application.Lead.Mobile.SourceDtoV3>> GetAllLeadsSourceWithSubSourceV4Async(string tenantId, bool? isEnabled = null)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = @"SELECT s.""Id"", s.""DisplayName"", s.""Value"", s.""OrderRank"", le.""SubSource"", s.""IsEnabled""
                 FROM ""LeadratBlack"".""Leads"" l
                 LEFT JOIN ""LeadratBlack"".""LeadEnquiries"" le ON le.""LeadId"" = l.""Id"" AND l.""TenantId"" = @TenantId And l.""IsDeleted"" = 'false'
                 AND le.""SubSource"" != '' AND le.""SubSource"" is not null
                 RIGHT JOIN ""LeadratBlack"".""Sources"" s ON s.""Value"" = le.""LeadSource""
                  where s.""IsDeleted"" = 'false' and s.""TenantId"" = @TenantId 
                  AND (@IsEnabled IS NULL OR s.""IsEnabled"" = @IsEnabled)
                 GROUP BY s.""Id"", s.""DisplayName"", s.""Value"", s.""OrderRank"", le.""SubSource"", s.""IsEnabled"";";
            var parameters = new { TenantId = tenantId, IsEnabled = isEnabled };
            var res = (await conn.QueryAsync<Lrb.Application.Lead.Mobile.SourceDtoV3>(query, parameters)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllLeadsSourceWithSubSourceV3Async " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<Lrb.Application.Lead.Mobile.SourceDtoV3>();
        }
    }

    public async Task<IEnumerable<T>> GetAllProspectSubSourceV2Async<T>(string tenantId, bool? isEnabled = null)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select s.\"Id\" as SourceId, pe.\"SubSource\", s.\"DisplayName\", s.\"IsEnabled\" from \"LeadratBlack\".\"Prospects\" p\r\n" +
                $"inner join \"LeadratBlack\".\"ProspectEnquiries\" pe on p.\"Id\" = pe.\"ProspectId\" and p.\"TenantId\" = '{tenantId}' and p.\"IsDeleted\" = 'false'\r\n and pe.\"IsDeleted\" = 'false'\r\n" +
                $"right join \"LeadratBlack\".\"Sources\" s on pe.\"SourceId\" = s.\"Id\" " +
                $"where s.\"IsDeleted\" = 'false' and s.\"TenantId\" = '{tenantId}' " +
                $"and (cast(:isEnabled as boolean) IS NULL OR s.\"IsEnabled\" = :isEnabled)\r\n" +
                $"group by s.\"Id\", pe.\"SubSource\", s.\"DisplayName\", s.\"IsEnabled\"\r\n" +
                $"order by s.\"DisplayName\"";

            var parameters = new { isEnabled };

            var res = (await conn.QueryAsync<T>(query, parameters)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            throw;
        }
    }
    public async Task ExecuteQueryAsync(string query)
    {
        var conn = _dbContext.Connection;
        try
        {
            conn.Open();
            await conn.ExecuteAsync(query, commandTimeout: 1000);
            conn.Close();
        }
        catch (Exception ex)
        {
            conn.Close();
            throw;
        }
    }
    public async Task<bool> UpdatePropertyAssignentAsync(Guid userId)
    {

        var conn = _dbContext.Connection;
        try
        {
            var query = $"UPDATE \"LeadratBlack\".\"PropertyAssignments\"\r\nSET \"IsDeleted\" = 'true',\r\n    \"IsCurrentlyAssigned\" = 'false'\r\nWHERE \"AssignedTo\" = '{userId}';";
            var res = await conn.ExecuteAsync(query);


            return true;
        }
        catch (Exception ex)
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<bool> UpdatePropertyListingOnBehalfAsync(Guid userId)
    {

        var conn = _dbContext.Connection;
        try
        {
            var query = $"UPDATE \"LeadratBlack\".\"Properties\"\r\nSET \"ListingOnBehalf\" =null\r\nWHERE \"ListingOnBehalf\" @> ARRAY['{userId}'] ::uuid[]\r\n\r\n";
            var res = await conn.ExecuteAsync(query);


            return true;
        }
        catch (Exception ex)
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<IEnumerable<Guid>> GetLeadIdsBySourceValueAsync(string tenantId, int sourceValue, CancellationToken cancellationToken)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select l.\"Id\" as LeadId from \"LeadratBlack\".\"Leads\" l\r\n" +
            $"inner join \"LeadratBlack\".\"LeadEnquiries\" le on l.\"Id\" = le.\"LeadId\" and l.\"TenantId\" = '{tenantId}' and l.\"IsDeleted\" = 'false'\r\n" +
            $"where le.\"LeadSource\"::integer = :sourceValue\r\n" +
            $"group by l.\"Id\"";

            var parameters = new { sourceValue };

            var res = (await conn.QueryAsync<Guid>(query, parameters)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            throw;
        }
    }
    public async Task<List<Device>> GetDeviceInfo(string tenantId, List<Guid>? userIds = null)
    {

        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var idsString = string.Join(",", userIds.Select(id => $"'{id}'"));

            var query = $"SELECT * FROM \"LeadratBlack\".\"Devices\" WHERE \"IsDeleted\" = FALSE AND \"Platform\" = '2'AND \"UserId\" in({idsString})AND \"TenantId\" = '{tenantId}'\r\n";

            var res = (await conn.QueryAsync<Device>(query, new { Ids = userIds, TenantId = tenantId })).ToList().Adapt<List<Device>>();
            return res;
        }
        catch (Exception ex)
        {
            throw ex;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<IEnumerable<UserDetailsDto>> GetUserBasicDetailsAsync(List<Guid>? userIds, string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = @"
                 SELECT ""Id"" AS UserId, ""UserName"", ""FirstName"", ""LastName"", ""IsActive"", ""IsAutomationEnabled""
                 FROM ""LeadratBlack"".""VWUserInfo""
                 WHERE ""TenantId"" = @tenantId
                 AND ""IsActive"" = true
                 AND ""IsAutomationEnabled"" = true
                 AND ""IsDeleted"" = false
                 AND ""Id"" = ANY(@userIds)";
            var res = await conn.QueryAsync<UserDetailsDto>(query, new { tenantId, userIds });
            return res;
        }
        catch (Exception ex)
        {
            throw;
        }
    }

    public async Task<IEnumerable<string>> GetAllLeadLandLineDetailsAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select Distinct(\"LandLine\") from \"LeadratBlack\".\"Leads\"\r\nwhere \"LandLine\"<>'' and \"TenantId\"='{tenantId}' and \"IsDeleted\"='false'";
            var res = await conn.QueryAsync<string>(query);
            return res;
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<IEnumerable<string>> GetAllProspectLandLineDetailsAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"select Distinct(\"LandLine\") from \"LeadratBlack\".\"Prospects\"\r\nwhere \"LandLine\"<>'' and \"TenantId\"='{tenantId}' and \"IsDeleted\"='false'";
            var res = await conn.QueryAsync<string>(query);
            return res;
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }

    public async Task<bool> UpdateLead(Guid id)
    {
        var conn = _dbContext.Connection;
        try
        {
            var query = $"update \"LeadratBlack\".\"Leads\"\r\nSet \"AssignTo\"='00000000-0000-0000-0000-000000000000'," +
                $"\"SecondaryUserId\"='00000000-0000-0000-0000-000000000000'\r\nwhere \"AssignTo\"='{id}'";
            var res = await conn.ExecuteAsync(query);
            return true;
        }
        catch (Exception ex)
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<List<Guid>> GetManagerIdsAsync(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        List<Guid> managerIds = new();
        try
        {
            var query = $"select u.\"Id\" from \"Identity\".\"Users\" u \r\nleft join  \"Identity\".\"UserRoles\" ur on ur.\"UserId\" = u.\"Id\"\r\nleft join \"Identity\".\"Roles\" r on ur.\"RoleId\" = r.\"Id\"\r\nwhere r.\"Name\" = 'Manager' and r.\"TenantId\" = '{tenantId}' and u.\"TenantId\" = '{tenantId}' and r.\"IsDeleted\" = false";
            var result = await conn.QueryAsync<string>(query);
            if (result?.Any() ?? false)
            {
                return result.Select(i => Guid.Parse(i)).ToList();
            }
        }
        catch (Exception ex)
        {

        }
        return managerIds;
    }
    public async Task<Guid?> GetGeneralManagerIdAsync(string userId)
    {
        Guid? generalManagerId = null;

        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"SELECT \"GeneralManager\" FROM \"LeadratBlack\".\"UserDetails\" WHERE \"UserId\" = '{userId}'; ";
            var result = await conn.QueryFirstOrDefaultAsync<Guid?>(query);
            return result;
        }
        catch (Exception ex)
        {

        }
        return generalManagerId;
    }
    public async Task<List<T>> GetAllUserProjectInfo<T>(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $@"
                        SELECT p.""Id"" AS ""ProjectId"", 
                               p.""Name"" AS ""projectName"", 
                               a.""PlaceId"" AS ""PlaceId""
                        FROM ""LeadratBlack"".""Projects"" p
                        JOIN ""LeadratBlack"".""Addresses"" a ON a.""Id"" = p.""AddressId""
                        WHERE p.""TenantId"" = '{tenantId}'
                          AND p.""IsDeleted"" = 'false'
                          AND p.""Name"" IS NOT NULL and  p.""Name"" <>''
                          AND a.""PlaceId"" IS NOT NULL and a.""PlaceId"" <>''
                          AND a.""Longitude"" IS NOT NULL and a.""Longitude"" <>''
                          AND a.""Latitude"" IS NOT NULL and a.""Latitude"" <>''";
            var res = await conn.QueryAsync<T>(query);
            return res?.ToList() ?? new List<T>();
        }
        catch (Exception ex)
        {
            return null;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<List<T>> GetAllUserPropertyInfo<T>(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $@"
                        SELECT p.""Id"" AS ""PropertyId"", 
                               p.""Title"" AS ""PropertyName"", 
                               a.""PlaceId"" AS ""PlaceId""
                        FROM ""LeadratBlack"".""Properties"" p
                        JOIN ""LeadratBlack"".""Addresses"" a ON a.""Id"" = p.""AddressId""
                        WHERE  p.""TenantId"" = '{tenantId}'
                          AND p.""IsDeleted"" = 'false'
                          AND p.""Title"" IS NOT NULL and p.""Title"" <>''
                          AND a.""PlaceId"" IS NOT NULL and a.""PlaceId"" <>''
                          AND a.""Longitude"" IS NOT NULL and a.""Longitude"" <> ''
                          AND a.""Latitude"" IS NOT NULL and a.""Latitude"" <> ''";
            var res = await conn.QueryAsync<T>(query);
            return res?.ToList() ?? new List<T>();
        }
        catch (Exception ex)
        {
            return null;
        }
        finally
        {
            conn.Close();
        }
    }

    public async Task<IEnumerable<string>> GetCountryCode(string tenantId, string columnName, string tableName)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT Distinct(\"{columnName}\") FROM \"LeadratBlack\".\"{tableName}\" where \"TenantId\"='{tenantId}' and \"{columnName}\"<>'' and \"IsDeleted\"='false'\r\n";
            var res = await conn.QueryAsync<string>(query);
            return res;
        }
        catch
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }


    public async Task<string> GetThirdPartyIdByUserId(Guid userId, string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT \"ThirdPartyId\" FROM \"Identity\".\"Users\" WHERE \"IsDeleted\" = false AND \"IsActive\" = true AND \"TenantId\" = '{tenantId}' AND \"Id\" = '{userId}' ";
            var res = (await conn.QueryAsync<string>(query)).FirstOrDefault();
            return res ?? string.Empty;
        }
        catch
        {
            return string.Empty;
        }
        finally
        {
            conn.Close();
        }
    }

    public async Task<string> GetUserNameByThirdPartyId(int thirdPartyId, string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"Select Concat(\"FirstName\", ' ', \"LastName\") from \"Identity\".\"Users\" where \"IsDeleted\" = false and \"IsActive\" = true and \"ThirdPartyId\" = '{thirdPartyId}' and \"TenantId\" = '{tenantId}' ";
            var res = (await conn.QueryAsync<string>(query)).FirstOrDefault();
            return res ?? string.Empty;
        }
        catch
        {
            return string.Empty;
        }
        finally
        {
            conn.Close();
        }
    }
   
    public async Task<List<CustomPropertyAttributeDto?>> GetAttributeDetails(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"SELECT\"Id\",\"AttributeDisplayName\" FROM \"LeadratBlack\".\"CustomMasterAttributes\"\r\n" +
                $"where \"AttributeDisplayName\"in('Total Floors','No. of Bed Rooms','No. of Kitchens','No. of Bath Rooms','No. of Utilities',\r\n'No. of Drawing or Living Rooms','No. of Balconies','Floor Number','Maximum Occupants','Parking')\r\nand \"TenantId\"='{tenantId}'";

            var result = await conn.QueryAsync<CustomPropertyAttributeDto>(query);
            return result.ToList();
        }
        catch (Exception)
        {
            throw;
        }
        finally
        {
            await conn.CloseAsync();
        }
    }
    public async Task<List<LeadCommunication>?> GetLeadsCommunicationsByLeadIds(string tenantId, Guid userId, List<Guid> leadIds, bool isAdmin)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            if (isAdmin)
            {
                string query = $"select \"ContactType\",\"LeadId\",\"UniqueKey\" from \"LeadratBlack\".\"LeadCommunications\"" +
                    $"where \"IsDeleted\" = false" +
                    $" and \"LeadId\" = any(@leadIds)" +
                    $" and \"TenantId\" = '{tenantId}'" +
                    $" order by \"LastModifiedOn\" desc";
                var result = await conn.QueryAsync<LeadCommunication>(query, new { leadIds });
                return result.ToList();
            }
            else
            {
                string query = $"select \"ContactType\",\"LeadId\",\"UniqueKey\" from \"LeadratBlack\".\"LeadCommunications\"" +
                    $" where \"IsDeleted\" = false\r\n and \"LeadId\" = any(@leadIds)" +
                    $"and \"UserId\" = '{userId}'" +
                    $"and \"TenantId\" = '{tenantId}'" +
                    $"order by \"LastModifiedOn\" desc";
                var result = await conn.QueryAsync<LeadCommunication>(query, new { leadIds });
                return result.ToList();
            }

        }
        catch (Exception)
        {
            return new();
        }
        finally
        {
            await conn.CloseAsync();
        }
    }
    public async Task<List<ProspectCommunication>?> GetProspectsCommunicationsByProspectsIds(string tenantId, Guid userId, List<Guid> prospectIds, bool isAdmin)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            if (isAdmin)
            {
                string query = $"select \"ContactType\",\"ProspectId\" from \"LeadratBlack\".\"ProspectCommunications\"" +
                    $"where \"IsDeleted\" = false" +
                    $" and \"ProspectId\" = any(@prospectIds)" +
                    $" and \"TenantId\" = '{tenantId}'";
                var result = await conn.QueryAsync<ProspectCommunication>(query, new { prospectIds });
                return result.ToList();
            }
            else
            {
                string query = $"select \"ContactType\",\"ProspectId\" from \"LeadratBlack\".\"ProspectCommunications\"" +
                    $" where \"IsDeleted\" = false\r\n and \"ProspectId\" = any(@prospectIds)" +
                    $"and \"UserId\" = '{userId}'" +
                    $"and \"TenantId\" = '{tenantId}'";
                var result = await conn.QueryAsync<ProspectCommunication>(query, new { prospectIds });
                return result.ToList();
            }

        }
        catch (Exception)
        {
            return new();
        }
        finally
        {
            await conn.CloseAsync();
        }
    }

    public async Task<Guid> GetPropertyListingIdBySerialNoOrCustomRefNo(string refId, string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT \"Id\" FROM \"LeadratBlack\".\"Properties\" WHERE \"TenantId\" = '{tenantId}' AND (\"SourceReferenceIds\"::text ilike '%{refId}%' OR \"SerialNo\" = '{refId}')";
            var res = (await conn.QueryAsync<Guid>(query)).FirstOrDefault();
            return res;
        }
        catch
        {
            return Guid.Empty;
        }
        finally
        {
            await conn.CloseAsync();
        }
    }
    public async Task<List<LeadNotesHistoryDto>> GetLeadNotesHistoryByLeadId(Guid leadId, string tenantId, List<Guid>? userIds = null)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT \"Id\", \"LeadId\", \"CurrentVersion\", \"Notes\",\"ConfidentialNotes\", \"LastModifiedBy\", \"ModifiedDate\" FROM \"LeadratBlack\".\"LeadHistories\" WHERE \"LeadId\" = '{leadId}' AND \"TenantId\" = '{tenantId}'";

            if (userIds?.Any() ?? false)
            {
                var userIdList = string.Join(",", userIds.Select(id => $"'{id}'"));
                query += $" AND \"UserId\" IN ({userIdList})";
            }
            var result = (await conn.QueryAsync<LeadNotesHistoryDto>(query)).ToList();
            return result;
        }
        catch
        {
            return new List<LeadNotesHistoryDto>();
        }
        finally
        {
            await conn.CloseAsync();
        }
    }
    public async Task<DateTime> GetProspectStatusLastModifiedDate(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT Max(\"LastModifiedOn\")\r\nFROM \"LeadratBlack\".\"CustomProspectStatuses\"\r\nwhere \"TenantId\"='{tenantId}'";
            var res = (await conn.QueryAsync<DateTime>(query)).ToList();
            return res.FirstOrDefault();
        }
        catch (Exception ex)
        {
            throw;
        }
        finally
        {
            conn.Close();
        }

    }
    public async Task<List<GetAllOfflineProspectsDto>> GetAllOfflineProspectsAsync(string tenantId, Guid? userId, bool sendOnlyAssignedLeads, DateTime? dateRangeFrom = null, DateTime? dateRangeTo = null, int? pageNumber = null, int? pageSize = null)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"SELECT \"Id\", \"Name\", \"ContactNo\", \"AlternateContactNo\", \"AssignTo\", \"IsDeleted\", \"LastModifiedOn\", \"IsArchived\" FROM \"LeadratBlack\".\"Prospects\" where \"TenantId\" = '{tenantId}' AND \"IsDeleted\" = false";

            var parameters = new DynamicParameters();
            if (dateRangeFrom.HasValue && dateRangeTo.HasValue)
            {
                query += " AND \"LastModifiedOn\" >= @DateFrom AND \"LastModifiedOn\" <= @DateTo";
                parameters.Add("DateFrom", dateRangeFrom.Value);
                parameters.Add("DateTo", dateRangeTo.Value);
            }
            if (sendOnlyAssignedLeads && userId.HasValue)
            {
                query += " AND \"AssignTo\" = @UserId";
                parameters.Add("UserId", userId.Value);
            }

            query += " ORDER BY \"LastModifiedOn\" DESC";

            // Optional pagination if the base class supports it:
            if (pageNumber > 0 && pageSize > 0 && pageNumber.HasValue && pageSize.HasValue)
            {
                query += " OFFSET @Offset LIMIT @Limit";
                parameters.Add("Offset", (pageNumber - 1) * pageSize);
                parameters.Add("Limit", pageSize);
            }

            var result = await conn.QueryAsync<GetAllOfflineProspectsDto>(query, parameters, commandTimeout: 60);
            return result.ToList();
        }
        catch (Exception ex)
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    public async Task<List<GetAllOfflineLeadsDto>> GetAllOfflineLeadsAsync(string tenantId, Guid? userId, bool sendOnlyAssignedLeads, DateTime? dateRangeFrom = null, DateTime? dateRangeTo = null, int? pageNumber = null, int? pageSize = null, bool? offlineLeads = false)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"SELECT \"Id\", \"Name\", \"ContactNo\", \"AlternateContactNo\", \"AssignTo\", \"IsDeleted\", \"LastModifiedOn\", \"IsArchived\" FROM \"LeadratBlack\".\"Leads\" WHERE \"TenantId\" = '{tenantId}' AND \"IsDeleted\" = false";

            var parameters = new DynamicParameters();
            if (dateRangeFrom.HasValue && dateRangeTo.HasValue)
            {
                query += " AND \"LastModifiedOn\" >= @DateFrom AND \"LastModifiedOn\" <= @DateTo";
                parameters.Add("DateFrom", dateRangeFrom.Value);
                parameters.Add("DateTo", dateRangeTo.Value);
            }
            if (sendOnlyAssignedLeads && userId.HasValue && offlineLeads == true)
            {
                query += " AND \"AssignTo\" = @UserId";
                parameters.Add("UserId", userId.Value);
            }
            if (sendOnlyAssignedLeads && userId.HasValue && offlineLeads == false)
            {
                query += " AND (\"AssignTo\" = @UserId OR \"SecondaryUserId\" = @UserId)";
                parameters.Add("UserId", userId.Value);
            }

            query += " ORDER BY \"LastModifiedOn\" DESC";

            // Optional pagination if the base class supports it:
            if (pageNumber > 0 && pageSize > 0 && pageNumber.HasValue && pageSize.HasValue)
            {
                query += " OFFSET @Offset LIMIT @Limit";
                parameters.Add("Offset", (pageNumber - 1) * pageSize);
                parameters.Add("Limit", pageSize);
            }

            var result = await conn.QueryAsync<GetAllOfflineLeadsDto>(query, parameters, commandTimeout: 60);
            return result.ToList();
        }
        catch (Exception ex)
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
    }

    public async Task<AssignedUserDetailsDto> GetAssignToDetailsByContactNo(string? tenantId, string? contactNo)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"select \"Id\",\"UserName\",\"PhoneNumber\",(\"FirstName\" || ' '  || \"LastName\") as \r\n\"FullName\" from \"Identity\".\"Users\" " +
                $"where \"Id\" in\r\n(select \"AssignTo\"::text from \"LeadratBlack\".\"Leads\" \r\n" +
                $"where \"ContactNo\" ilike '%{contactNo}%' and \"TenantId\"='{tenantId}' order by \"LastModifiedOn\" desc limit 1)";
            var res = (await conn.QueryAsync<AssignedUserDetailsDto>(query)).FirstOrDefault();
            return res ?? new();
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling GetAllLeadsZonesAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new();
        }
    }
    public async Task<IEnumerable<T>> GetProjectsWithIdsAndNames<T>(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            string query = $"SELECT DISTINCT \"Id\", \"Name\"\r\n FROM \"LeadratBlack\".\"Projects\"r\n  WHERE \"TenantId\" = '{tenantId}' AND \"IsDeleted\" = 'false' and \"IsArchived\" = 'false' and \"CurrentStatus\" = 0 ";
            var res = (await conn.QueryAsync<T>(query)).ToList();
            return res;
        }
        catch (Exception ex)
        {
            _logger.Information("Exception details while calling GetProjectInfoAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            return new List<T>();
        }
    }
    public async Task<List<ProspectCommunication>?> GetProspectsCommunicationsForHistory(string tenantId, Guid prospectId, List<Guid>? userIds)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
                string query = $"select \"ContactType\",\"LastModifiedBy\",\"CreatedOn\", \"Message\" from \"LeadratBlack\".\"ProspectCommunications\"" +
                    $"where \"IsDeleted\" = false" +
                    $" and \"ProspectId\" = @prospectId" +
                    $" and \"TenantId\" = @tenantId ";

                var parameters = new DynamicParameters();
                parameters.Add("prospectId", prospectId);
                parameters.Add("tenantId", tenantId);
                if (userIds?.Any() ?? false)
                {
                    query += "AND \"UserId\" = ANY(@userIds) ";
                    parameters.Add("userIds", userIds);
                }

                var result = await conn.QueryAsync<ProspectCommunication>(query, parameters);
                return result.ToList();

        }
        catch (Exception)
        {
            return new();
        }
        finally
        {
            await conn.CloseAsync();
        }
    }
    public async Task<CallSettingsDto> GetCallSettings(string tenantId)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var query = $"SELECT \"CallSettings\", \"IsAssignedCallLogsEnabled\" FROM \"LeadratBlack\".\"GlobalSettings\" where \"IsDeleted\" = 'false' and \"TenantId\" = '{tenantId}';\r\n";
            var res = await conn.QueryFirstAsync<CallSettingsDto>(query);
            return res;
        }
        catch
        {
            return new();
        }
        finally
        {
            await conn.CloseAsync();
        }
    }
    public async Task<bool> UpdateLatModifiedDateAsync(string tenantId,int entityType)
    {
        var conn = _dbContext.Connection;
        try
        {
            var query = $"update \"LeadratBlack\".\"ModifiedDates\"\r\nset \"LastModifiedOn\"=NOW()\r\nwhere \"EntityType\"='{entityType}' and \"TenantId\"='{tenantId}'";
            var res = await conn.ExecuteAsync(query);
            return true;
        }
        catch (Exception ex)
        {
            throw;
        }
        finally
        {
            conn.Close();
        }
        
    }
}