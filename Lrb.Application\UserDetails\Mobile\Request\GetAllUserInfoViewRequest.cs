﻿using Lrb.Application.Lead.Mobile;
using Lrb.Application.UserDetails.Mobile.Specs;
using Lrb.Application.UserDetails.Mobile;

namespace Lrb.Application.UserDetails.Mobile.Request
{
    public class GetAllUserInfoViewRequest : PaginationFilter, IRequest<PagedResponse<UserDetailsDto, string>>
    {
        public string? UserSearch { get; set; }
        public bool? UserStatus { get; set; }
    }
    public class GetAllUserInfoVewRequestHandler : IRequestHandler<GetAllUserInfoViewRequest, PagedResponse<UserDetailsDto, string>>
    {
        private readonly IRepositoryWithEvents<UserView> _userViewRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;

        public GetAllUserInfoVewRequestHandler(IRepositoryWithEvents<UserView> userViewRepo, IRepositoryWithEvents<Domain.Entities.Lead> leadRepo)
        {
            _userViewRepo = userViewRepo;
            _leadRepo = leadRepo;
        }
        public async Task<PagedResponse<UserDetailsDto, string>> Handle(GetAllUserInfoViewRequest request, CancellationToken cancellationToken)
        {
            var viewUsers = await _userViewRepo.ListAsync(new UserViewSpec(request), cancellationToken);
            var userDtos = viewUsers.Adapt<List<UserDetailsDto>>();
            foreach (var user in userDtos)
            {
                user.LeadCount = await _leadRepo.CountAsync(new LeadsCountByUserIdSpec(user.UserId), cancellationToken);
            }
            var totalCount = await _userViewRepo.CountAsync(new UserViewCountSpec(request), cancellationToken);
            //var activeCountRequest = request.Adapt<GetAllUserInfoViewRequest>();
            //activeCountRequest.UserStatus = true;
            //var activeCount = await _userViewRepo.CountAsync(new UserViewCountSpec(activeCountRequest), cancellationToken);
            //var inActiveCountRequest = request.Adapt<GetAllUserInfoViewRequest>();
            //inActiveCountRequest.UserStatus = false;
            //var inActiveCount = await _userViewRepo.CountAsync(new UserViewCountSpec(inActiveCountRequest), cancellationToken);
            return new(userDtos, totalCount);
        }
    }
}
