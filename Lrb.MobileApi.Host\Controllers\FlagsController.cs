﻿using Lrb.Application.Flags.Mobile;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class FlagsController : VersionedApiController
    {
        [HttpGet]
        [TenantIdHeader]
        [OpenApiOperation("Get all flags.", "")]
        public async Task<PagedResponse<ViewFlagDto, string>> GetAsync([FromQuery] GetAllFlagsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut]
        [TenantIdHeader]
        [OpenApiOperation("Update a flag.", "")]
        public async Task<Response<UpdateFlagDto>> UpdateAsync(UpdateFlagRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpDelete("{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Delete a flag.", "")]
        public async Task<Response<bool>> DeleteAsync(Guid id)
        {
            return await Mediator.Send(new DeleteFlagRequest(id));
        }
        [HttpPost]
        [TenantIdHeader]
        [OpenApiOperation("Create a new flag.", "")]
        public async Task<Response<CreateFlagDto>> CreateAsync(CreateFlagRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("module")]
        [TenantIdHeader]
        [OpenApiOperation("Get flags by module.", "")]
        public async Task<Response<List<ViewFlagDto>>> GetAsync([FromQuery] GetFlagsByModuleRequest request)
        {
            return await Mediator.Send(request);
        }
    }
}
