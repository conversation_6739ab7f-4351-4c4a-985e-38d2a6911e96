﻿using Lrb.Application.Identity.Users;
using Lrb.Application.Team.Web;
using Lrb.Application.Team.Web.Requests;
using Lrb.Application.TimeZone.Dto;
using Lrb.Application.UserDetails.Web.Dtos;
using Newtonsoft.Json;

namespace Lrb.Application.UserDetails.Web
{
    public static class UserMapping
    {
        private static IUserService userService = null;
        private static IReadRepository<Domain.Entities.Lead> leadRepo = null;
        public static List<Identity.Users.UserDetailsDto> _allUsers = new();
        public static async void Configure(IServiceProvider serviceProvider)
        {
            TypeAdapterConfig<Identity.Users.UserDetailsDto, UserDto>
               .NewConfig()
               .Map(dest => dest.Name, src => src.FirstName + " " + src.LastName)
               .Map(dest => dest.ContactNo, src => src.PhoneNumber);

            TypeAdapterConfig<Identity.Users.UserDetailsDto, TeamUserDto>
               .NewConfig()
               .Map(dest => dest.Name, src => src.FirstName + " " + src.LastName)
               .Map(dest => dest.ContactNo, src => src.PhoneNumber);

            //TypeAdapterConfig<Domain.Entities.UserDetails, UserDetailsDto>
            //    .NewConfig()
            //    .Map(dest => dest.Address, src => src.CurrentAddress)
            //    .AfterMapping(async (src, dest) =>
            //    {
            //        userService = MapContext.Current.GetService<IUserService>();
            //        if (_allUsers.Count <= 0 || _allUsers.Count < userService.GetCountAsync(CancellationToken.None).Result)
            //        {
            //            _allUsers = userService.GetListAsync(CancellationToken.None).Result;
            //        }
            //        leadRepo = MapContext.Current.GetService<IReadRepository<Domain.Entities.Lead>>();
            //        if (userService != null && leadRepo != null)
            //        {
            //            var user = _allUsers?.FirstOrDefault(i => i.Id == src.UserId);
            //            dest.ReportsTo = _allUsers?.FirstOrDefault(i => i.Id == src.ReportsTo)?.Adapt<Team.Web.UserDto>();
            //            dest.UserName = user?.UserName;
            //            dest.FirstName = user?.FirstName;
            //            dest.LastName = user?.LastName;
            //            dest.IsActive = user?.IsActive ?? false;
            //            dest.Email = user?.Email;
            //            dest.PhoneNumber = user?.PhoneNumber;
            //            dest.ImageUrl = user?.ImageUrl;
            //            //var roles = userService.GetRolesWithPermissions(src.UserId.ToString()).Where(i => i.Enabled);
            //            //dest.RolePermission = roles.ToList();
            //            //dest.LeadCount = leadRepo.CountAsync(new LeadsCountByUserIdSpec(src.UserId), CancellationToken.None).Result;
            //        }
            //    });

            //TypeAdapterConfig<Identity.Users.UserDetailsDto, UserDetailsDto>
            //    .NewConfig()
            //    .AfterMapping(async (src, dest) =>
            //    {
            //        userService = serviceProvider.GetRequiredService<IUserService>();
            //        var roles = userService.GetRolesWithPermissionsAsync(src.Id.ToString(), CancellationToken.None).Result.Where(i => i.Enabled);
            //        dest.RolePermission = roles.ToList();
            //        leadRepo = serviceProvider.GetRequiredService<IReadRepository<Domain.Entities.Lead>>();
            //        dest.LeadCount = leadRepo.CountAsync(new LeadsCountByUserIdSpec(src.Id), CancellationToken.None).Result;
            //    });

            TypeAdapterConfig<FullUserView, UserDetailsDto>
                .NewConfig()
                .Map(dest => dest.UserId, src => src.Id)
                .Map(dest => dest.Documents, src => src.Documents == null ? null : src.Documents.GroupBy(i => i.DocumentType).ToDictionary(i => i.Key, i => i.ToList().Adapt<List<ViewUserDocumentDto>>()))
                .Map(dest => dest.TimeZoneInfo, src => string.IsNullOrWhiteSpace(src.TimeZoneInfo) == false ? JsonConvert.DeserializeObject<CreateTimeZoneDto>(src.TimeZoneInfo) : new());

            TypeAdapterConfig<UserView, UserDetailsDto>
                .NewConfig()
                .Map(dest => dest.UserId, src => src.Id)
                .Map(dest => dest.TimeZoneInfo, src => string.IsNullOrWhiteSpace(src.TimeZoneInfo) == false ? JsonConvert.DeserializeObject<CreateTimeZoneDto>(src.TimeZoneInfo) : new());

            TypeAdapterConfig<UserReportsTo, UserDto>
                .NewConfig()
                .Map(dest => dest.Name, src => $"{src.FirstName} {src.LastName}")
                .Map(dest => dest.ContactNo, src => src.PhoneNumber);

            TypeAdapterConfig<UserDetailsDto, ExportUserFormattedDto>
                .NewConfig()
                .Map(dest => dest.Name, src => $"{src.FirstName} {src.LastName}")
                .Map(dest => dest.ReportsTo, src => src.ReportsTo.Name)
                .Map(dest => dest.Departments, src => src.Department.Name)
                .Map(dest => dest.Designations, src => src.Designation.Name)
                .Map(dest => dest.UserRoles, src => src.UserRoles != null ? string.Join(", ", src.UserRoles.Select(role => role.Name)) : "");

            TypeAdapterConfig<UserFilterDto, UserFormattedFilterDto>
                .NewConfig()
                .MapWith(src => new UserFormattedFilterDto
                {
                    Departments = string.Join(", ", src.Departments ?? new List<string>()),
                    Designations = string.Join(", ", src.Designations ?? new List<string>()),
                    GeneralManagerIds = ConvertListGuidtostring(src.GeneralManagerIds)
                });

            TypeAdapterConfig<UserView, TeamFormattedDto>
               .NewConfig()
               .Map(dest => dest.Name, src => src.FirstName + " " + src.LastName)
               .Map(dest => dest.WorkPhone, src => src.PhoneNumber)
               .Map(dest => dest.PersonalPhone, src => src.AltPhoneNumber)
               .Map(dest => dest.Department, src => src.Department != null ? src.Department.Name : string.Empty)
               .Map(dest => dest.Designation, src => src.Designation != null ? src.Designation.Name : string.Empty)
               .Map(dest => dest.WorkLocation, src => src.OfficeAddress);

            TypeAdapterConfig<UserView, UserDto>
               .NewConfig()
               .Map(dest => dest.Name, src => src.FirstName + " " + src.LastName)
               .Map(dest => dest.ContactNo, src => src.PhoneNumber);

            TypeAdapterConfig<UserView, TeamUserDto>
               .NewConfig()
               .Map(dest => dest.Name, src => src.FirstName + " " + src.LastName)
               .Map(dest => dest.ContactNo, src => src.PhoneNumber);

            TypeAdapterConfig<UserView, UserListDto>
               .NewConfig()
               .Map(dest => dest.UserName, src => src.FirstName + " " + src.LastName);
            TypeAdapterConfig<Lrb.Application.Identity.Users.UserDetailsDto, UserBasicDetailsDto>
                .NewConfig()
                .Map(dest => dest.TimeZoneInfo, src => string.IsNullOrWhiteSpace(src.TimeZoneInfo) == false ? JsonConvert.DeserializeObject<CreateTimeZoneDto>(src.TimeZoneInfo) : new());
           
            TypeAdapterConfig<Lrb.Application.Identity.Users.UserDetailsDto, UserDetailsDto>
              .NewConfig()
              .Map(dest => dest.TimeZoneInfo, src => string.IsNullOrWhiteSpace(src.TimeZoneInfo) == false ? JsonConvert.DeserializeObject<CreateTimeZoneDto>(src.TimeZoneInfo) : new());
            TypeAdapterConfig<UserFormattedDto, UserBasicInfoDto>
             .NewConfig()
             .Map(dest => dest.ReportsTo, src => string.IsNullOrWhiteSpace(src.ReportsTo) == false ? JsonConvert.DeserializeObject<UserReportsTo>(src.ReportsTo) : new())
             .Map(dest => dest.GeneralManager, src => string.IsNullOrWhiteSpace(src.GeneralManager) == false ? JsonConvert.DeserializeObject<UserReportsTo>(src.GeneralManager) : new());
            TypeAdapterConfig<Lrb.Domain.Entities.Property, UserPropertyDto>
               .NewConfig()
               .Map(dest => dest.PropertyId, src => src.Id)
               .Map(dest => dest.PropertyName, src => src.Title)
               .Map(dest => dest.Longitude, src => src.Address.Longitude)
               .Map(dest => dest.Latitude, src => src.Address.Latitude);
            TypeAdapterConfig<Lrb.Domain.Entities.Project, UserProjectDto>
                .NewConfig()
                .Map(dest => dest.ProjectId, src => src.Id)
                .Map(dest => dest.ProjectName, src => src.Name)
                .Map(dest => dest.Longitude, src => src.Address.Longitude)
                .Map(dest => dest.Latitude, src => src.Address.Latitude);

        }
        private static string ConvertListGuidtostring(List<Guid>? propertyTypes)
        {
            if (propertyTypes == null || propertyTypes.Count == 0)
            {
                return null;
            }



            return string.Join(",", propertyTypes.Select(g => g.ToString()));
        }
    }
}
