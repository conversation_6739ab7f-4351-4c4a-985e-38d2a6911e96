﻿using Lrb.Application.Common.Exceptions;
using Lrb.Application.Identity.Tokens;
using Lrb.Application.Identity.Users.Password;
using Lrb.Application.OrgProfile.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Lrb.Shared.Extensions;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using System.Threading;

namespace Lrb.Infrastructure.Identity;

internal partial class UserService
{

    private readonly string baseUri = "https://api.textlocal.in/send/";
    public async Task<string> ForgotPasswordAsync(ForgotPasswordRequest request, string origin)
    {
        EnsureValidTenant();

        var user = await _userManager.FindByIdAsync(request.UserId);
        if (user is null)
        {
            // Don't reveal that the user does not exist or is not confirmed
            throw new InternalServerException("An Error has occurred!", null, ErrorActionCode.NoOp);
        }

        // For more information on how to enable account confirmation and password reset please
        // visit https://go.microsoft.com/fwlink/?LinkID=532713
        Random generator = new Random();
        string otp = generator.Next(0, 1000000).ToString("D6");
        string message = $"<#> {otp}  is the OTP to proceed on leadrat. Please do not share it with others. If this is not initiated by you, <NAME_EMAIL> With love, leadrat Q2s8tU9l0ro";
        string code = await _userManager.GeneratePasswordResetTokenAsync(user);
        const string route = "api/users/reset-password";
        var endpointUri = new Uri(string.Concat($"{origin}/", route));
        string passwordResetUrl = QueryHelpers.AddQueryString(endpointUri.ToString(), "Token", code);
        #region mail
        //if (await _userManager.IsEmailConfirmedAsync(user))
        //{
            //await _graphEmailService.SendEmailWithoutCCAsync(LeadratEmails.NoReplyEmail,
          //"Reset Password", $"Your Password Reset Otp is\n {otp}.",
          //new List<string>() { user.Email }, Microsoft.Graph.BodyType.Text);
        //}
        await _graphEmailService.SendEmailWithoutCCAsync(LeadratEmails.SupportEmail,
          "Reset Password", $"Your Password Reset Otp is\n {otp}.",
          new List<string>() { user.Email }, Microsoft.Graph.BodyType.Text);
        #endregion
        #region sms
        var credential = _smsSettings.Value;
        List<string> PhoneNumbers = new() { request.PhoneNumber };
        await _localTextService.SendSMSByTextLocalAsync(PhoneNumbers, credential.Apikey, message, credential.Sender, credential.DLTId, baseUri);
        #endregion
        #region Whatsapp
        try
        {
            var otpSettings = new OTPSettings()
            {
                Channels = new() { ContactType.WhatsApp },
                IsEnabled = true,
                Receiver = OTPReceiver.Self,
                RetryInSecs = 10
            };
            List<string>? phoneNumbers = new() { request.PhoneNumber };
            var orgProfile = await _orgProfileRepo.FirstOrDefaultAsync(new GetProfileSpec(), CancellationToken.None);
            await SendWhatsAppOTPAsync(otp, orgProfile, otpSettings, phoneNumbers, Guid.Parse(request.UserId),CancellationToken.None);
        }
        catch (Exception ex)
        {
        }

        #endregion
        #region store credential in db
        Application.ResetPassword.ResetPasswordRequest ResetPasswordRequest = new() { Otp = otp, Token = code, UserId = Guid.Parse(request.UserId) };
        await _mediator.Send(ResetPasswordRequest);
        #endregion

        //_jobService.Enqueue(() => 
        //    _graphEmailService.SendEmailWithoutCCAsync(LeadratEmails.NoReplyEmail, 
        //    "Reset Password", $"Your Password Reset Token is '{code}'. You can reset your password using the {endpointUri} Endpoint.",
        //    new List<string>() { user.Email }, Microsoft.Graph.BodyType.Text));

        //var mailRequest = new MailRequest(
        //    new List<string> { request.Email },
        //    "Reset Password",
        //    $"Your Password Reset Token is '{code}'. You can reset your password using the {endpointUri} Endpoint.");
        //_jobService.Enqueue(() => _mailService.SendAsync(mailRequest, CancellationToken.None));

        return "Password Reset otp has been sent to your authorized Email and Phone Number.";
    }
    public async Task<string> SendConfirmationEmailAsync(string email, string origin)
    {
        EnsureValidTenant();

        var user = await _userManager.FindByEmailAsync(email.Normalize());
        if (user is null || !await _userManager.IsEmailConfirmedAsync(user))
        {
            // Don't reveal that the user does not exist or is not confirmed
            throw new InternalServerException("An Error has occurred!", null, ErrorActionCode.NoOp);
        }
        if (_securitySettings.RequireConfirmedAccount && !string.IsNullOrEmpty(user.Email))
        {
            // send verification email
            string emailVerificationUri = await GetEmailVerificationUriAsync(user, origin);
            RegisterUserEmailModel eMailModel = new RegisterUserEmailModel()
            {
                Email = user.Email,
                UserName = user.UserName,
                Url = emailVerificationUri
            };

            await _graphEmailService.SendEmailWithoutCCAsync(LeadratEmails.NoReplyEmail,
                "Confirm Email", $"Email: {user.Email},\n UserName: {user.UserName},\n Url: {emailVerificationUri} ",
                new List<string> { user.Email }, Microsoft.Graph.BodyType.Text);
            //var jobId = _jobService.Enqueue(() =>
            //    _graphEmailService.SendEmailWithoutCCAsync(LeadratEmails.NoReplyEmail,
            //    "Confirm Email", $"Email: {user.Email},\n UserName: {user.UserName},\n Url: {emailVerificationUri} ",
            //    new List<string> { user.Email }, Microsoft.Graph.BodyType.Text));
            //var mailRequest = new MailRequest(
            //    new List<string> { user.Email },
            //    "Confirm Registration",
            //   _templateService.GenerateEmailTemplate("email-confirmation", eMailModel));
            //_jobService.Enqueue(() => _mailService.SendAsync(mailRequest, CancellationToken.None));
            return $"Please check {user.Email} to verify your account!";
        }
        return string.Empty;
    }

    public async Task<string> ResetPasswordAsync(ResetPasswordRequest request)
    {
        var user = await _userManager.FindByIdAsync(request.UserId);

        // Don't reveal that the user does not exist
        _ = user ?? throw new InternalServerException("An Error has occurred!", null, ErrorActionCode.NoOp);
        #region otp verification
        Application.ResetPassword.Requests.GetResetPasswordCredentialsRequest GetRequest = new() { UserId = Guid.Parse(request.UserId) };
        var resetCredentils = await _mediator.Send(GetRequest);
        var resetCredentil = resetCredentils.OrderByDescending(i => i.CreatedOn).FirstOrDefault();
        if (!resetCredentil?.Otp.IsNullOrEmpty() ?? false)
        {
            if (!string.Equals(resetCredentil.Otp, request.Otp))
            {
                // Don't reveal that the user does not exist
                throw new InternalServerException("Invalid OTP!", null, ErrorActionCode.NoOp);
            }

        }
        else
        {
            // Don't reveal that the user does not exist
            throw new InternalServerException("An Error has occurred!", null, ErrorActionCode.NoOp);

        }
        #endregion
        var result = await _userManager.ResetPasswordAsync(user, resetCredentil.Token, request.Password);
        if (result.Succeeded && !string.IsNullOrWhiteSpace(request.Password))
        {
            var passTimeStamp = DateTime.UtcNow;
            var isReset = await _cognitoService.ResetPasswordAsync(user.UserName, request.Password, passTimeStamp);
            if (isReset)
            {
                user.PasswordTimeStamp = passTimeStamp;
                var isStampUpdatedInDB = await _userManager.UpdateAsync(user);

                if (isStampUpdatedInDB.Succeeded)
                {
                    await _userRepository.UpdateUserLockAccount(false, user.UserName, _currentTenant?.Id ?? string.Empty);
                    await _cognitoService.AddUserLockedSettingClaimAync(user.UserName, false);
                    await RemoveFailedAttempts(user.UserName);
                }

                return isStampUpdatedInDB.Succeeded
                ? "Password Reset Successful!"
                : throw new InternalServerException("An Error has occurred!", null, ErrorActionCode.NoOp);
            }
            return isReset
                ? "Password Reset Successful!"
                : throw new InternalServerException("An Error has occurred!", null, ErrorActionCode.NoOp);
        }
        return result.Succeeded
            ? "Password Reset Successful!"
            : throw new InternalServerException("An Error has occurred!", null, ErrorActionCode.NoOp);
    }
    public async Task<string> SetDefaultPasswordAsync(SetDefaultPasswordRequest request)
    {
        if (request.UserId == null || !request.UserId.Any())
        {
            throw new ArgumentException("User IDs cannot be null or empty.");
        }

        bool isAllPasswordResetsSuccessful = true; 

        foreach (var userId in request.UserId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                isAllPasswordResetsSuccessful = false; 
                continue;
            }

            var defaultPassword = "123Pa$$word!";
            var resetToken = await _userManager.GeneratePasswordResetTokenAsync(user);
            var result = await _userManager.ResetPasswordAsync(user, resetToken, defaultPassword);

            if (result.Succeeded)
            {
                var passTimeStamp = DateTime.UtcNow;
                var isReset = await _cognitoService.ResetPasswordAsync(user.UserName, defaultPassword, passTimeStamp);
                if (isReset)
                {
                    user.PasswordTimeStamp = passTimeStamp;
                    var isStampUpdatedInDB = await _userManager.UpdateAsync(user);
                    await _userRepository.UpdateUserLockAccount(false, user.UserName, _currentTenant?.Id ?? string.Empty);
                    await _cognitoService.AddUserLockedSettingClaimAync(user.UserName, false);
                    await RemoveFailedAttempts(user.UserName);
                    if (!isStampUpdatedInDB.Succeeded)
                    {
                        isAllPasswordResetsSuccessful = false; 
                    }
                }
                else
                {
                    isAllPasswordResetsSuccessful = false; 
                }
            }
            else
            {
                isAllPasswordResetsSuccessful = false; 
            }
        }

        if (isAllPasswordResetsSuccessful)
        {
            return "Default password reset successful for all users.";
        }
        else
        {
            return "Default password not set for all users.";
        }
    }



    public async Task<string> ChangePasswordAsync(ChangePasswordRequest model, string userId)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(userId);

            _ = user ?? throw new NotFoundException("User Not Found.");

            var result = await _userManager.ChangePasswordAsync(user, model.Password, model.NewPassword);
            if (result.Succeeded)
            {
                if (result.Succeeded)
                {
                    var passTimeStamp = DateTime.UtcNow;
                    var isReset = await _cognitoService.ResetPasswordAsync(user.UserName, model.NewPassword, passTimeStamp);
                    if (isReset)
                    {
                        user.PasswordTimeStamp = passTimeStamp;
                        var isStampUpdatedInDB = await _userManager.UpdateAsync(user);

                        return isStampUpdatedInDB.Succeeded
                        ? "Password Reset Successful!"
                        : throw new InternalServerException("An Error has occurred!", null, ErrorActionCode.NoOp);
                    }
                    return isReset
                        ? "Password changed Successful!"
                        : throw new InternalServerException("An Error has occurred!", null, ErrorActionCode.NoOp);
                }
                else
                {
                    throw new InternalServerException("Change password failed", result.GetErrors());
                }
            }
            else
            {
                throw new InternalServerException("Change password failed", result.GetErrors());
            }
        }
        catch (Exception ex)
        {
            var error = new LrbError()
            {
                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                ErrorSource = ex?.Source,
                StackTrace = ex?.StackTrace,
                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                ErrorModule = "UserService -> ChangePasswordAsync()"
            };
            await _leadRepositoryAsync.AddErrorAsync(error);
            throw;
        }
    }

    private async Task SendWhatsAppOTPAsync(string otp, Profile? orgProfile, OTPSettings otpSettings, List<string>? phoneNumbers,Guid userId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.Information($"SendWhatsAppOTPAsync() => {phoneNumbers?.Serialize()}");
            var temaplte = await _dapperRepository.GetOTPWhatsAppInfoAsync();
            if (temaplte != null && temaplte.WAOTPApiInfo != null && (phoneNumbers?.Any() ?? false))
            {
                (string, bool) data = new("", false);
                foreach (var item in phoneNumbers)
                {
                    data = await _interaktService.SendOTPAsync(temaplte.WAOTPApiInfo.URL ?? string.Empty, temaplte.WAOTPApiInfo.MethodType ?? string.Empty, temaplte.Name, temaplte.WAOTPApiInfo.JsonPayload ?? string.Empty, JsonConvert.DeserializeObject<Dictionary<string, string>>(temaplte.WAOTPApiInfo.Headers ?? string.Empty) ?? new(), otp, item);
                    _logger.Information("SendWhatsAppOTPAsync() Res: " + data.Item1);
                }
                if (data.Item2)
                {
                    await _otpMessageRepo.AddAsync(new()
                    {
                        Channel = ContactType.WhatsApp,
                        OTP = otp,
                        Receiver = otpSettings.Receiver,
                        ReceiverInfo = phoneNumbers?.Select(i => i.ToString()).ToList() ?? new(),
                        UserId = userId,
                    }, cancellationToken);
                    if (orgProfile != null)
                    {
                        orgProfile.WhatsAppOTPCount += phoneNumbers?.Count() ?? 0;
                        await _orgProfileRepo.UpdateAsync(orgProfile, cancellationToken);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.Information("SendWhatsAppOTPAsync() Res: " + ex.Message ?? ex.InnerException?.Message ?? string.Empty);
        }
    }

    #region Reset failed attempts
    private async Task RemoveFailedAttempts(string username)
    {
        var retyInfo = GetLockoutInfo(username);
        if(retyInfo != null)
        {
            ResetFailedAttempts(username);
        }
    }

    private LockoutInfo GetLockoutInfo(string username)
    {
        var lockoutKey = GetLockoutCacheKey(username);
        return _memoryCache.Get<LockoutInfo>(lockoutKey);
    }

    private void ResetFailedAttempts(string username)
    {
        var attemptsKey = GetAttemptsCacheKey(username);
        var lockoutKey = GetLockoutCacheKey(username);

        _memoryCache.Remove(attemptsKey);
        _memoryCache.Remove(lockoutKey);

        _logger.Information("Reset failed attempts for user: {Username}", username);
    }

    private string GetAttemptsCacheKey(string username) => $"failed_attempts_{username.ToLower()}";
    private string GetLockoutCacheKey(string username) => $"lockout_{username.ToLower()}";
    #endregion
}
