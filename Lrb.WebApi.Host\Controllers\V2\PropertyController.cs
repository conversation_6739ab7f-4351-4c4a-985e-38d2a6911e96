﻿using Lrb.Application.Property.Web.V2.Dtos;
using Lrb.Application.Property.Web.V2.Requests;
using Mapster;

namespace Lrb.WebApi.Host.Controllers.V2
{
    [Authorize]
    [Route("api/v2/[controller]")]
    [ApiVersionNeutral]
    public class PropertyController : VersionedApiController
    {
        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all Listing details.", "")]
        public async Task<PagedResponse<ViewPropertyDtoV2, string>> SearchAsync([FromQuery] GetAllListingV2Request request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("Create a new property lsiting.", "")]
        public Task<Response<UpdatePropertyDtoV2>> CreateAsync([FromBody] CreatePropertyDtoV2 dto)
        {
            CreatePropertyListingV2Request request = dto.Adapt<CreatePropertyListingV2Request>();
            return Mediator.Send(request);
        }

        [HttpPut("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Properties)]
        [OpenApiOperation("Update a property listing.", "")]
        public async Task<ActionResult<Guid>> UpdateAsync(UpdatePropertyDtoV2 dto, Guid id)
        {
            return id != dto.Id
                ? BadRequest()
                : Ok(await Mediator.Send(dto.Adapt<UpdatePropertyListingV2Request>()));
        }

        [HttpDelete("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Properties)]
        [OpenApiOperation("Delete a property listing.", "")]
        public Task<Response<Guid>> DeleteAsync(Guid id)
        {
            return Mediator.Send(new DeletePropertyListingByIdV2Request(id));
        }

        [HttpPost("publish")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.PublishProperty, LrbResource.Properties)]
        [OpenApiOperation("send properties for listing.", "")]
        public async Task<Response<bool>> ListPropertyAsync(PublishPropertyListingRequestV2 request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("location")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("Search location.", "")]
        public async Task<Response<List<ListingSourceAddressDtoV2>>> SearchAsync([FromQuery] GetPFLocationRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get property details by Id.", "")]
        public async Task<Response<ViewPropertyDtoV2>> GetAsync(Guid id)
        {
            return await Mediator.Send(new GetPropertyListingByIdRequestV2(id));
        }
    }
}
