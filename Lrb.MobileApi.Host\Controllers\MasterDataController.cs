﻿using Lrb.Application.MasterData;
using Lrb.Application.ModifiedDate.Mobile;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class MasterDataController : VersionedApiController
    {
        [HttpGet("propertyTypes")]
        //[MustHavePermission(LrbAction.Search, LrbResource.MasterData)]
        public async Task<PagedResponse<MasterPropertyTypeDto, string>> GetPropertyTypes()
        {
            return await Mediator.Send(new GetPropertyTypeRequest());
        }
        [HttpGet("leadSources")]
        //[MustHavePermission(LrbAction.Search, LrbResource.MasterData)]
        public async Task<PagedResponse<MasterLeadSource, string>> GetleadSources()
        {
            return await Mediator.Send(new GetLeadSourceRequest());
        }
        [HttpGet("masterAreaUnits")]
        //[MustHavePermission(LrbAction.Search, LrbResource.MasterData)]
        public async Task<PagedResponse<MasterAreaUnit, string>> GetMasterAreaUnits()
        {
            return await Mediator.Send(new GetAreaUnitRequest());
        }
        [TenantIdHeader]
        [HttpGet("masterLeadStatuses")]
        //[MustHavePermission(LrbAction.Search, LrbResource.MasterData)]
        public async Task<PagedResponse<MasterLeadStatusDto,string>> GetMasterLeadStatuses()
        {
            return await Mediator.Send(new Lrb.Application.MasterData.Mobile.GetLeadStatusRequest());
        }
        [HttpGet("masterPropertyAmenities")]
        //[MustHavePermission(LrbAction.Search, LrbResource.MasterData)]
        public async Task<Response<Dictionary<string, List<CustomMasterAmenity>>>> GetMasterPropertyAmenities()
        {
            return await Mediator.Send(new GetMasterAmenitityRequest());
        }
        [HttpGet("masterPropertyAttributes")]
        //[MustHavePermission(LrbAction.Search, LrbResource.MasterData)]
        public async Task<PagedResponse<CustomMasterAttribute, string>> GetMasterPropertyAttributes()
        {
            return await Mediator.Send(new GetPropertyAttributesRequest());
        }
        [HttpGet("masterUserServices")]
        //[MustHavePermission(LrbAction.Search, LrbResource.MasterData)]
        public async Task<PagedResponse<MasterUserService, string>> GetMasterUserServices()
        {
            return await Mediator.Send(new GetUserServiceRequest());
        }
        [HttpGet("lastModifiedDates")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.MasterData)]
        public async Task<Response<Dictionary<FeatureName, DateTime?>>> GetLastModifiedDates()
        {
            return await Mediator.Send(new GetLastModifiedDatesRequest());
        }

        [HttpGet("modifiedDates")]
        [TenantIdHeader]
        public async Task<Response<Dictionary<EntityType, DateTime>>> GetModifiedDates()
        {
            return await Mediator.Send(new Lrb.Application.ModifiedDate.Mobile.GetModifiedDatesRequest());
        }

        //[TenantIdHeader]
        //[HttpGet("custom/masterLeadStatuses")]
        ////[MustHavePermission(LrbAction.Search, LrbResource.MasterData)]
        //public async Task<Response<CustomMasterLeadStatusDto>> GetCustomMasterLeadStatuses()
        //{
        //    return await Mediator.Send(new GetLeadStatusRequest());
        //}

        [HttpGet("masterProjectTypes")]
        //[MustHavePermission(LrbAction.Search, LrbResource.MasterData)]
        public async Task<PagedResponse<MasterProjectTypeDto, string>> GetMasterprojecttypes()
        {
            return await Mediator.Send(new GetProjectTypeRequest());
        }

        [HttpGet("projectAttributes")]
        //[MustHavePermission(LrbAction.Search, LrbResource.MasterData)]
        public async Task<PagedResponse<MasterProjectUnitArrtibute, string>> GetMasterProjectAttributes()
        {
            return await Mediator.Send(new GetProjectUnitAttributesRequest());
        }

        [HttpGet("projectAmenities")]
        //[MustHavePermission(LrbAction.Search, LrbResource.MasterData)]
        public async Task<Response<Dictionary<string, List<CustomMasterAmenity>>>> GetMasterProjectAminities()
        {
            return await Mediator.Send(new GetProjectAmenitiesRequest());
        }

        [HttpGet("associatedBank")]
        //[MustHavePermission(LrbAction.Search, LrbResource.MasterData)]
        public async Task<Response<List<MasterAssociatedBank>>> GetMasterProjectAssociatedBank()
        {
            return await Mediator.Send(new GetProjectAssociatedBankRequest());
        }
        [HttpGet("propertyTypes/listing")]
        //[MustHavePermission(LrbAction.Search, LrbResource.MasterData)]
        public async Task<PagedResponse<MasterPropertyTypeDto, string>> GetPropertyTypesBasedOnSetting()
        {
            return await Mediator.Send(new GetMasterPropertyTypeBasedOnSettingRequest());
        }
        [HttpGet("masterPropertyAmenities/listing")]
        //[MustHavePermission(LrbAction.Search, LrbResource.MasterData)]
        public async Task<Response<Dictionary<string, List<CustomMasterAmenity>>>> GetMasterPropertyAmenitiesBasedOnSetting()
        {
            return await Mediator.Send(new GetPropertyAmenitiesBasedOnSettingRequest());
        }

    }
}
