﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application
{
    public class TeamConfig : IEntityTypeConfiguration<Domain.Entities.Team>
    {
        public void Configure(EntityTypeBuilder<Domain.Entities.Team> builder)
        {
            builder.IsMultiTenant();
            builder.Property(i => i.Name).IsRequired();
            builder.Ignore(t => t.Configuration);
            //builder.HasOne(p => p.Configuration) // one-to-one
            //    .WithOne()
            //    .HasForeignKey<Domain.Entities.Team>(p => p.ConfigurationId);
            builder.HasMany(p => p.Configurations) // one-to-many
                .WithOne(ua => ua.Team)
                .HasForeignKey(ua => ua.TeamId);
        }
    }
}
