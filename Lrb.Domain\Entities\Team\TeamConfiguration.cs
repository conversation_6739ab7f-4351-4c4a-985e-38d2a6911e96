﻿using System.Text.Json.Serialization;

namespace Lrb.Domain.Entities
{
    public class TeamConfiguration : AuditableEntity, IAggregateRoot
    {
        public TimeSpan? RotationTime { get; set; }
        public DateTime? ShiftStartTime { get; set; }
        public DateTime? ShiftEndTime { get; set; }
        public int NoOfRotation { get; set; }
        public Guid? TeamId { get; set; }
        [JsonIgnore]
        public Team? Team { get; set; }
        public Guid? AssignedUserId { get; set; }
        public Guid? PreviousAssignedUser { get; set; }
        public Guid? LastAssignedUser { get; set; }
        public Guid? NextUserToBeAssigned { get; set; }
        public List<DayOfWeek>? DayOfWeeks { get; set; }
        public TimeSpan? StartTime { get; set; }
        public TimeSpan? EndTime { get; set; }
        public bool? IsForRetention { get; set; }
        public bool? IsSourceLevel { get; set; }
        public List<LeadSource>? LeadSources { get; set; }
        public TimeSpan? BufferTime { get; set; }
        public List<Guid>? IntegrationAccountIds { get; set; }

    }
}
