﻿

using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Specs;

namespace Lrb.Application.DataManagement.Mobile.Requests
{
    public class GetProspectCommunicationsRequest : IRequest<Response<Dictionary<Guid, Dictionary<ContactType, int>>>?>
    {
        public List<Guid>? ProspectIds { get; set; }
    }
    public class GetProspectCommunicationsRequestHandler : IRequestHandler<GetProspectCommunicationsRequest, Response<Dictionary<Guid, Dictionary<ContactType, int>>>?>
    {
        private readonly IRepositoryWithEvents<ProspectCommunication> _communicationRepo;
        private readonly ICurrentUser _currentUser;
        protected readonly IDapperRepository _dapperRepository;

        public GetProspectCommunicationsRequestHandler(IRepositoryWithEvents<ProspectCommunication> communicationRepo, ICurrentUser currentUser, IDapperRepository dapperRepository)
        {
            _communicationRepo = communicationRepo;
            _currentUser = currentUser;
            _dapperRepository = dapperRepository;
        }

        public async Task<Response<Dictionary<Guid, Dictionary<ContactType, int>>>?> Handle(GetProspectCommunicationsRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (request?.ProspectIds?.Count() <= 0)
                {
                    return new(null);
                }
                var userId = _currentUser.GetUserId();
                var tenantId = _currentUser.GetTenant();
                var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                var communications = await _dapperRepository.GetProspectsCommunicationsByProspectsIds(tenantId ?? string.Empty, userId, request.ProspectIds, isAdmin);
                var result = new List<DataCommunicationDto>();
                if (communications?.Any() ?? false)
                {
                    return new(communications
                                  .Select(communication => new
                                  {
                                      ProspectId = communication.ProspectId,
                                      ContactType = communication.ContactType
                                  })
                                  .GroupBy(group => new { group.ProspectId })
                                  .Select(group => new
                                  {
                                      ProspectId = group.Key.ProspectId,
                                      ContactRecords = group
                                   .Select(communication => communication.ContactType)
                                   .GroupBy(contactType => contactType)
                                   .ToDictionary(contactTypeGroup => contactTypeGroup.Key, contactTypeGroup => contactTypeGroup.Count())
                                  })
                                    .ToDictionary(dto => dto.ProspectId, dto => dto.ContactRecords));
                }
                else
                {
                    return new(null);
                }
            }
            catch (Exception ex)
            {
                return new(null, ex.Message);
            }

        }
    }
}
