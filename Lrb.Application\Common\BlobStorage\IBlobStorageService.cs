﻿using Microsoft.AspNetCore.Http;

namespace Lrb.Application.Common.BlobStorage
{
    public interface IBlobStorageService
    {
        public string? BucketName { get; set; }
        public string? AWSS3BucketUrl { get; set; }
        Task<string> UploadObjectAsync(string bucketName, string folderName, string bytebase64encodedFile);
        Task<string> UploadMobileObjectAsync(string bucketName, string folderName, string bytebase64encodedFile, string fileName);
        Task<string> UploadObjectAsync(string bucketName, string folderName, IFormFile formFile);
        Task<string> UploadObjectAsync(string bucketName, string folderName, string fileName, byte[] bytes);
        Task<string> UploadObjectAsync(string bucketName, string folderName, string fileName, byte[] bytes, params int[] optional);
        Task<string> UploadObjectAsync(string bucketName, string folderName, string fileName, Stream stream);
        Task<Stream> GetObjectAsync(string bucketName, string key);
        Task<bool> DeleteObjectAsync(string bucketName, string key);
        Task<string> GetPreSignedURL(string bucketName, string key, int validityInSeconds = 3600);
        Task<string> UploadFileAsync(string fileName, byte[] imageData);
        Task<string> UploadWebObjectAsync(string bucketName, string folderName, string bytebase64encodedFile, string fileName);
        Task<string> UploadWebObjectV2Async(string bucketName, string folderName, string bytebase64encodedFile, string fileName);
    }
}
