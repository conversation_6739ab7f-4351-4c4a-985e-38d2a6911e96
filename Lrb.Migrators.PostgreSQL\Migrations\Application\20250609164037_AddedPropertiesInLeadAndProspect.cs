﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class AddedPropertiesInLeadAndProspect : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "DateOfBirth",
                schema: "LeadratBlack",
                table: "Prospects",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Gender",
                schema: "LeadratBlack",
                table: "Prospects",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MaritalStatus",
                schema: "LeadratBlack",
                table: "Prospects",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateOfBirth",
                schema: "LeadratBlack",
                table: "Leads",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Gender",
                schema: "LeadratBlack",
                table: "Leads",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MaritalStatus",
                schema: "LeadratBlack",
                table: "Leads",
                type: "integer",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DateOfBirth",
                schema: "LeadratBlack",
                table: "Prospects");

            migrationBuilder.DropColumn(
                name: "Gender",
                schema: "LeadratBlack",
                table: "Prospects");

            migrationBuilder.DropColumn(
                name: "MaritalStatus",
                schema: "LeadratBlack",
                table: "Prospects");

            migrationBuilder.DropColumn(
                name: "DateOfBirth",
                schema: "LeadratBlack",
                table: "Leads");

            migrationBuilder.DropColumn(
                name: "Gender",
                schema: "LeadratBlack",
                table: "Leads");

            migrationBuilder.DropColumn(
                name: "MaritalStatus",
                schema: "LeadratBlack",
                table: "Leads");
        }
    }
}
