﻿using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Lead.Mobile.Dtos.v2;
using Lrb.Application.Lead.Mobile.Specs.v1;
using Lrb.Application.Lead.Web;
using Lrb.Application.LeadCallLog.Mobile;
using Lrb.Application.Project.Mobile;
using Lrb.Application.Property.Mobile;
using Lrb.Application.WhatsAppCloudApi.Mobile;
using static Lrb.Application.Lead.Mobile.LeadHistoryDto;

namespace Lrb.Application.Lead.Mobile
{
    public class GetLeadHistoryByIdRequest : IRequest<Response<Dictionary<DateTime, Dictionary<DateTime, List<LeadHistoryDto>>>>>
    {
        public Guid Id { get; set; }
        public List<LeadHistoryFilterKey>? FilterKeys { get; set; }
        public bool? CanAccessAllLeads { get; set; }
    }
    public class GetLeadHistoryByIdRequestHandler : IRequestHandler<GetLeadHistoryByIdRequest, Response<Dictionary<DateTime, Dictionary<DateTime, List<LeadHistoryDto>>>>>
    {
        private readonly IReadRepository<LeadBookedDetail> _bookedInfoRepo;
        private readonly IReadRepository<LeadHistory> _leadHistoryRepo;
        private readonly IReadRepository<Domain.Entities.Lead> _leadRepository;
        private readonly IUserService _userService;
        private readonly IReadRepository<LeadAppointment> _leadAppointmentRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IReadRepository<LeadCommunication> _leadCommunicationRepo;
        private readonly IRepositoryWithEvents<ServetelCallLog> _servetelCallLogs;
        private readonly IReadRepository<Domain.Entities.WhatsAppCommunication> _whatsAppCommunicationRepo;
        private readonly IReadRepository<Address> _addressRepo;
        private readonly IReadRepository<Domain.Entities.IVRCommonCallLog> _ivrCommonCallLogRepo;
        public GetLeadHistoryByIdRequestHandler(
            IReadRepository<LeadHistory> leadHistoryRepo,
            IReadRepository<Address> addressRepo,
            IReadRepository<Domain.Entities.Lead> leadRepository,
            IUserService userService,
            IReadRepository<LeadCommunication> leadCommunicationRepo,
            IReadRepository<LeadAppointment> leadAppointmentRepo,
            ICurrentUser currentUser, IDapperRepository dapperRepository,
            IRepositoryWithEvents<ServetelCallLog> servetelCallLogs,

            IReadRepository<IVRCommonCallLog> ivrCommonCallLogRepo,
            IReadRepository<Domain.Entities.WhatsAppCommunication> whatsAppCommunicationRepo,
            IReadRepository<LeadBookedDetail> bookedInfoRepo
            )
        {
            _leadHistoryRepo = leadHistoryRepo;
            _leadRepository = leadRepository;
            _userService = userService;
            _leadAppointmentRepo = leadAppointmentRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _leadCommunicationRepo = leadCommunicationRepo;
            _servetelCallLogs = servetelCallLogs;
            _addressRepo = addressRepo;
            _whatsAppCommunicationRepo = whatsAppCommunicationRepo;
            _bookedInfoRepo = bookedInfoRepo;
            _ivrCommonCallLogRepo = ivrCommonCallLogRepo;
        }
        public async Task<Response<Dictionary<DateTime, Dictionary<DateTime, List<LeadHistoryDto>>>>> Handle(GetLeadHistoryByIdRequest request, CancellationToken cancellationToken)
        {
            var lead = await _leadRepository.FirstOrDefaultAsync(new LeadByIdSpec(request.Id));
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            List<Guid> subIds = new();
            if (lead != null)
            {
                List<LeadHistory> leadHistories = null;
                var isAdmin = await _dapperRepository.IsAdminAsync((_currentUser?.GetUserId() ?? Guid.Empty), _currentUser?.GetTenant() ?? string.Empty);
                if (isAdmin)
                {
                    leadHistories = await _leadHistoryRepo.ListAsync(new LeadHistorySpec(request.Id));
                    var latestHistory = leadHistories;
                }
                else
                {
                    subIds = (await _dapperRepository.GetSubordinateIdsAsync(currentUserId, tenantId ?? string.Empty, request?.CanAccessAllLeads, isAdmin))?.ToList() ?? new();
                    subIds.AddRange(new List<Guid>() { currentUserId, lead.AssignTo });
                    if (lead?.SecondaryUserId != null && lead.SecondaryUserId != Guid.Empty)
                    {
                        subIds.Add(lead?.SecondaryUserId ?? Guid.Empty);
                    }
                    subIds = subIds?.Where(i => i != Guid.Empty)?.Distinct()?.ToList() ?? new List<Guid>();
                    leadHistories = await _leadHistoryRepo.ListAsync(new LeadHistorySpec(request.Id, subIds));

                    var latestHistory = leadHistories
                    .Select(h => new
                    {
                      LeadHistory = h,
                      LatestModifiedDate = h.ModifiedDate?.Max(kvp => kvp.Value)
                    })
                    .OrderByDescending(x => x.LatestModifiedDate)
                    .FirstOrDefault()?.LeadHistory;
                    var assignment = latestHistory?.LeadAssignmentType?.OrderByDescending(i => i.Value).FirstOrDefault();
                    if ((latestHistory != null) && (assignment != null) && assignment.Value.Value != LeadAssignmentType.WithHistory)
                    {
                        leadHistories = new();
                        latestHistory?.LeadAssignmentType?.Remove(assignment.Value.Key);
                        leadHistories.Add(latestHistory);
                    }
                }
                //var leadHistory = await _leadHistoryRepo.GetByIdAsync(request.Id);
                var leadcomunications = await _leadCommunicationRepo.ListAsync(new Specs.v2.GetCommunicationDetailsByLeadId(request.Id, subIds));
                List<WhatsAppCommunication>? whatsAppCommunications = await _whatsAppCommunicationRepo.ListAsync(new GetWhatsAppCommunicationSpec(lead.Id), cancellationToken);
                List<LeadAppointment> leadAppointments = await _leadAppointmentRepo.ListAsync(new LeadAppointmentByLeadIdSpec(lead?.Id ?? Guid.Empty), cancellationToken);
                var leadWithCallLogs = (await _leadRepository.ListAsync(new LeadCallLogByLeadIdSpec(lead.Id), cancellationToken)).FirstOrDefault();
                List<LeadCallLogDto>? callLogDtos = leadWithCallLogs?.LeadCallLogs?.ToList().Adapt<List<LeadCallLogDto>>();
                List<ServetelCallLog> servetelCallLogs = new();
                List<IVRCommonCallLog> ivrCommonCallLogs = new();
                if (isAdmin)
                {
                    servetelCallLogs = await _servetelCallLogs.ListAsync(new ServetelCallLogByLeadAndUserSpec(lead.Id));
                    ivrCommonCallLogs = await _ivrCommonCallLogRepo.ListAsync(new IVRCommonCallLogByLeadIdSpec(lead.Id));
                }
                else
                {
                    servetelCallLogs = await _servetelCallLogs.ListAsync(new ServetelCallLogByLeadAndUserSpec(lead.Id, lead.AssignTo));
                    ivrCommonCallLogs = await _ivrCommonCallLogRepo.ListAsync(new IVRCommonCallLogByLeadIdSpec(lead.Id, lead.AssignTo));
                    callLogDtos = callLogDtos?.Where(i => i.UserId == lead.AssignTo).ToList();
                }
                servetelCallLogs = await FormatTimeStampsAsync(servetelCallLogs);
                List<LeadCallLogDto> leadCallLogs = servetelCallLogs.Adapt<List<LeadCallLogDto>>();
                leadCallLogs.AddRange(ivrCommonCallLogs.Adapt<List<LeadCallLogDto>>());
                if (callLogDtos?.Any() ?? false)
                {
                    callLogDtos.AddRange(leadCallLogs);
                }
                else
                {
                    callLogDtos = leadCallLogs;
                }
                List<Guid> userIds = leadAppointments.Select(i => i.LastModifiedBy).ToList();
                userIds.AddRange(leadcomunications.Select(i => i.LastModifiedBy).ToList());

                userIds.AddRange(whatsAppCommunications.Select(i => i.LastModifiedBy).ToList());

                userIds.AddRange(lead.BookedDetails.Select(i => i.LastModifiedBy).ToList());

                userIds.AddRange(lead.BookedDetails.Where(i => i.BookedBy.HasValue).Select(i => i.BookedBy.Value).ToList());

                userIds.AddRange(lead.BookedDetails.Where(i => i.UserId.HasValue).Select(i => i.UserId.Value).ToList());

                userIds.AddRange(lead.BookedDetails.Where(i => i.SecondaryOwner.HasValue).Select(i => i.SecondaryOwner.Value).ToList());

                userIds.AddRange(callLogDtos.Where(i => i.LastModifiedBy.HasValue).Select(i => i.LastModifiedBy.Value).ToList());

                userIds.AddRange(lead.BookedDetails.Where(i => i.TeamHead.HasValue).Select(i => i.TeamHead.Value).ToList());
                var users = await _userService.GetListAsync(cancellationToken);
              //  var usersWithAppointments = _userService.GetListAsync(cancellationToken).Result.Where(i => userIds.Contains(i.Id)).ToList();
                List<LeadAppointmentDto> appointmentDtos = leadAppointments.Adapt<List<LeadAppointmentDto>>();
                appointmentDtos.ForEach(i =>
                {
                    if (!string.IsNullOrWhiteSpace(i.Image))
                    {
                        i.ImagesWithName ??= new();
                        i.ImagesWithName.Add(new LeadDocument() { FilePath = i.Image });
                    }
                });
                List<AppointmentDataDto> leadAppointmentDtos = appointmentDtos.Adapt<List<AppointmentDataDto>>();
                leadAppointmentDtos.ForEach(i =>
                {
                    var user = users.FirstOrDefault(j => j.Id == i.LastModifiedBy);
                    i.LastModifiedByUser = user?.FirstName + " " + user?.LastName;
                });
                List<WhatsAppCommunicationDto> whatsAppCommunicationDtos = whatsAppCommunications.Adapt<List<WhatsAppCommunicationDto>>();
                if (!isAdmin)
                {
                    whatsAppCommunicationDtos.RemoveAll(i => i.UserId != currentUserId || i.UserId == Guid.Empty);
                }
                whatsAppCommunicationDtos.ForEach(wc =>
                {
                    var user = users.FirstOrDefault(user => user.Id == wc.UserId);
                    wc.LastModifiedByUser = user?.FirstName + " " + user?.LastName;
                    wc.LastModifiedBy = user?.Id ?? default;
                });
              //  var whatsAppCommunications = await GetWhatsAppCommunicationsAsync(lead, cancellationToken, isAdmin, currentUserId);
                //var users = await _userService.GetListOfUsersByIdsAsync((leadWithCallLogs?.LeadCallLogs?.Select(i => i.UserId.ToString()).ToList() ?? new List<string>())?
                //    .Concat(leadcomunications?.Select(i => i.LastModifiedBy.ToString())?.ToList() ?? new List<string>())?
                //    .Concat(whatsAppCommunications?.Select(i => i.LastModifiedBy.ToString())?.ToList() ?? new List<string>())?
                //                                                            .Distinct()?.ToList() ?? new List<string>(), cancellationToken);
                var comunicationDtos = leadcomunications?.Adapt<List<Dtos.v2.LeadCommunicationDto>>();
                comunicationDtos?.ForEach(i =>
                {
                    var user = users.FirstOrDefault(j => j.Id == i.LastModifiedBy);
                    i.LastModifiedByUser = user?.FirstName + " " + user?.LastName;
                    i.LastModifiedByUser = user?.FirstName + " " + user?.LastName;
                });
                //var userDtos = await _userService.GetListOfUsersByIdsAsync((servetelCallLogs?.Select(i => i.UserId?.ToString() ?? string.Empty).ToList() ?? new List<string>())?
                //                                                            .Concat(leadcomunications?.Select(i => i.LastModifiedBy.ToString())?.ToList() ?? new List<string>())?
                //                                                            .Concat(whatsAppCommunications?.Select(i => i.LastModifiedBy.ToString())?.ToList() ?? new List<string>())?
                //                                                            .ToList() ?? new List<string>(), cancellationToken);

                var bookDetails = await GetBookedDetailsAsync(lead, cancellationToken, isAdmin, currentUserId,users);
                if (leadHistories?.Any() ?? false)
                {
                    var leadHistoryModel = LeadHistoryHelper.FormLeadHistoryViewModelMobile(leadHistories, request.FilterKeys, callLogDtos, users, leadAppointmentDtos, comunicationDtos, whatsAppCommunicationDtos, _addressRepo, bookDetails);
                    return new Response<Dictionary<DateTime, Dictionary<DateTime, List<LeadHistoryDto>>>>(leadHistoryModel);
                }
                else
                {
                    return new();
                }
            }
            else
            {
                throw new NotFoundException("lead not found by this id");
            }
        }
        private async Task<List<ServetelCallLog>> FormatTimeStampsAsync(List<ServetelCallLog>? servetelCallLogs)
        {
            if (servetelCallLogs?.Any() ?? false)
            {
                servetelCallLogs.ForEach(log =>
                {
                    log.StartStamp = log.StartStamp?.Replace('+', ' ');
                    log.EndStamp = log.EndStamp?.Replace('+', ' ');
                    if (!string.IsNullOrEmpty(log.StartStamp))
                    {
                        try
                        {
                            var result = Uri.UnescapeDataString(log.StartStamp);
                            DateTime dateTimeObj = DateTime.ParseExact(result, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                            log.StartStamp = dateTimeObj.ToString();
                        }
                        catch (Exception ex)
                        {
                            if (DateTime.TryParseExact(log.StartStamp, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out DateTime parsedDateTime))
                            {
                                log.StartStamp = parsedDateTime.ToString();
                            }
                            else
                            {
                                log.StartStamp = null;
                            }
                        }
                    }
                    if (!string.IsNullOrEmpty(log.EndStamp))
                    {
                        try
                        {
                            var result = Uri.UnescapeDataString(log.EndStamp);
                            DateTime dateTimeObj = DateTime.ParseExact(result, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                            log.EndStamp = dateTimeObj.ToString();
                        }
                        catch (Exception ex)
                        {
                            if (DateTime.TryParseExact(log.EndStamp, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out DateTime parsedDateTime))
                            {
                                log.EndStamp = parsedDateTime.ToString();
                            }
                            else
                            {
                                log.EndStamp = null;
                            }
                        }
                    }
                });
            }
            return servetelCallLogs ?? new();
        }
        private async Task<List<WhatsAppCommunicationDto>> GetWhatsAppCommunicationsAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken, bool? isAdmin, Guid? currentUserId)
        {
            List<WhatsAppCommunication>? whatsAppCommunications = await _whatsAppCommunicationRepo.ListAsync(new GetWhatsAppCommunicationSpec(lead.Id), cancellationToken);
            List<WhatsAppCommunicationDto> whatsAppCommunicationDtos = whatsAppCommunications.Adapt<List<WhatsAppCommunicationDto>>();
            if (!isAdmin ?? true)
            {
                whatsAppCommunicationDtos.RemoveAll(i => i.UserId != currentUserId || i.UserId == Guid.Empty);
            }
            var users = await _userService.GetListOfUsersByIdsAsync(whatsAppCommunicationDtos.Select(i => i.UserId.ToString()).ToList(), cancellationToken);
            whatsAppCommunicationDtos.ForEach(wc =>
            {
                var user = users.FirstOrDefault(user => user.Id == wc.UserId);
                wc.LastModifiedByUser = user?.FirstName + " " + user?.LastName;
                wc.LastModifiedBy = user?.Id ?? default;
            });
            return whatsAppCommunicationDtos;
        }
        private async Task<(List<BookedDetailsDto>? bookedDetailsInfo, List<List<DocumentsDto>>? documentsInfo, List<LeadBrokerageInfoDto>? leadBrokerageInfo)> GetBookedDetailsAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken, bool? isAdmin, Guid? currentUserId, List<UserDetailsDto>? users)
        {
            List<LeadBookedDetail> bookDetails = await _bookedInfoRepo.ListAsync(new GetBookedDetailsByIdSpec(lead.Id), cancellationToken);
            List<BookedDetailsDto> bookDetailsDto = bookDetails.Adapt<List<BookedDetailsDto>>();
            List<List<DocumentsDto>> listOfDocuments = new List<List<DocumentsDto>>();
            List<LeadBrokerageInfoDto> leadBrokerageList = new();
            //List<BasicPropertyInfoDto> propertiesInfo = new();
            //List<BasicProjectDto> projectsInfo = new();
            //List<UnitTypeDto> unitTypesInfo = new();
            if (!isAdmin ?? false)
            {
                bookDetailsDto.RemoveAll(i => i.UserId != currentUserId || i.UserId == Guid.Empty);
            }
            foreach (var bookedDetail in bookDetailsDto)
            {
                List<DocumentsDto> documents = new List<DocumentsDto>();
                LeadBrokerageInfoDto leadBrokerage = new();
                //BasicPropertyInfoDto propertyInfo = new();
                //BasicProjectDto projectInfo = new();
                //UnitTypeDto unitTypeInfo = new();
                try
                {
                    var teamHeadDetails = users.Find(i => i.Id == bookedDetail.TeamHead);
                    if (teamHeadDetails != null)
                    {
                        bookedDetail.TeamHeadName = teamHeadDetails.FirstName + " " + teamHeadDetails.LastName;
                    }
                }
                catch (Exception ex) { }
                try
                {
                    var bookedByDetails = users.Find(i => i.Id == bookedDetail.BookedBy);
                    if (bookedByDetails != null)
                    {
                        bookedDetail.BookedByName = bookedByDetails.FirstName + " " + bookedByDetails.LastName;
                    }
                }
                catch (Exception ex) { }
                try
                {
                    var secondaryOwnerDetails = users.Find(i => i.Id == bookedDetail.SecondaryOwner);
                    if (secondaryOwnerDetails != null)
                    {
                        bookedDetail.SecondaryOwnerName = secondaryOwnerDetails.FirstName + " " + secondaryOwnerDetails.LastName;
                    }
                }
                catch { }
                try
                {
                    var userDetails = users.Find(i => i.Id == bookedDetail.UserId);
                    if (userDetails != null)
                    {
                        bookedDetail.UserName = userDetails.FirstName + " " + userDetails.LastName;
                    }
                }
                catch (Exception ex) { }
                try
                {
                    var LastModifiedByUser = users.Find(i => i.Id == bookedDetail.LastModifiedBy);
                    if (LastModifiedByUser != null)
                    {
                        bookedDetail.LastModifiedByUser = LastModifiedByUser.FirstName + " " + LastModifiedByUser.LastName;
                    }
                }
                catch (Exception ex) { }
                if (bookedDetail.Documents?.Any() ?? false)
                {
                    documents = bookedDetail.Documents?.ToList() ?? default;
                    listOfDocuments.Add(documents);
                }
                if (bookedDetail.BrokerageInfo != null)
                {
                    leadBrokerage = bookedDetail.BrokerageInfo ?? default;
                    leadBrokerageList.Add(leadBrokerage);
                }
                //if (bookedDetail.Property != null)
                //{
                //    propertyInfo = bookedDetail.Property ?? default;
                //    propertiesInfo.Add(propertyInfo);
                //}
                //if (bookedDetail.Projects != null)
                //{
                //    projectInfo = bookedDetail.Projects ?? default;
                //    projectsInfo.Add(projectInfo);
                //}
                //if (bookedDetail.UnitType != null)
                //{
                //    unitTypeInfo = bookedDetail.UnitType ?? default;
                //    unitTypesInfo.Add(unitTypeInfo);
                //}
            }
            return (bookDetailsDto, listOfDocuments, leadBrokerageList);
        }
    }
}
