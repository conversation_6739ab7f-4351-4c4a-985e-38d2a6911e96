﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Domain.Entities
{
    public class WhatsAppNotificationRecipients
    {
        public bool IsLeadEnabled { get; set; } = false;
        public bool IsAssignToEnabled { get; set; } = false;
        public bool IsManagerEnabled { get; set; } = false;
        public bool IsAdminEnabled { get; set; } = false;
        public bool IsGeneralManagerEnabled { get; set; } = false;
    }
}
