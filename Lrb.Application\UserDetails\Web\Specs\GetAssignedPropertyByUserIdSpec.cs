﻿namespace Lrb.Application.UserDetails.Web.Specs
{
    public class GetAssignedPropertyByUserIdSpec : Specification<Domain.Entities.Property>
    {
        public GetAssignedPropertyByUserIdSpec(Guid userId)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.PropertyAssignments != null && i.PropertyAssignments.Any() && i.PropertyAssignments.Any(pa => pa.AssignedTo == userId && pa.IsCurrentlyAssigned))
            .Include(i => i.Address)
            .Include(i => i.PropertyAssignments);
        }
        public GetAssignedPropertyByUserIdSpec(List<Guid> userIds)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.PropertyAssignments != null &&
            i.PropertyAssignments.Any() && i.PropertyAssignments.Any(pa => userIds.Contains(pa.AssignedTo ?? Guid.Empty) && pa.IsCurrentlyAssigned) && i.Address.PlaceId != null && i.Address.PlaceId != string.Empty)
            .Include(i => i.Address)
            .Include(i => i.PropertyAssignments);
        }
    }
}
