﻿using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.UserDetails.Web.Specs;
using NodaTime;
using NodaTime.Extensions;
using NodaTime.TimeZones;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Linq;
using Lrb.Application.Reports.Web;
using Lrb.Application.Property.Web;
using Lrb.Application.Common.Persistence.New_Implementation;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GetAllUserInfoViewRequest : PaginationFilter, IRequest<PagedResponse<UserDetailsDto, UserDetailsCountDto>>
    {
        public string? UserSearch { get; set; }
        public bool? UserStatus { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<Guid>? ReportsToIds { get; set; }
        public List<Guid>? GeneralManagerIds { get; set; }
        public List<string>? Departments { get; set; }
        public List<string>? Designations { get; set; }
        public bool? ShouldShowReportees { get; set; }
        public List<string>? TimeZoneIds { get; set; }
        public PropertyDateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? LicenseNo { get; set; }
    }
    public class GetAllUserInfoVewRequestHandler : IRequestHandler<GetAllUserInfoViewRequest, PagedResponse<UserDetailsDto, UserDetailsCountDto>>
    {
        private readonly IRepositoryWithEvents<UserView> _userViewRepo;
        private readonly ICurrentUser _currentTenant;
        public GetAllUserInfoVewRequestHandler(IRepositoryWithEvents<UserView> userViewRepo, ICurrentUser currentTenant)
        {
            _userViewRepo = userViewRepo;
            _currentTenant = currentTenant;
        }
        public async Task<PagedResponse<UserDetailsDto, UserDetailsCountDto>> Handle(GetAllUserInfoViewRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentTenant.GetTenant();
            List<UserView>? viewUsers = null;
            List<UserDetailsDto>? userDtos = null;
            var totalCount = 0;
            viewUsers = await _userViewRepo.ListAsync(new UserViewByTimeZoneSpec(request), cancellationToken);
            userDtos = viewUsers.Adapt<List<UserDetailsDto>>();
            var activeCount = userDtos.Where(i => i.IsActive).Count();
            var inActiveCount = userDtos.Where(i => !i.IsActive).Count();
            switch (request.UserStatus)
            {
                case true:
                    totalCount = activeCount;
                    break;
                case false:
                    totalCount = inActiveCount;
                    break;
                default:
                    totalCount = userDtos.Count();
                    break;
            }
            var allUserCount = inActiveCount + activeCount;
            userDtos = (request.UserStatus != null) ? userDtos.Where(i => i.IsActive == request.UserStatus).ToList() : userDtos.ToList();
            userDtos = userDtos.Skip((request.PageNumber - 1) * request.PageSize).Take(request.PageSize).ToList();
            return new(userDtos, totalCount, new() { AllUserCount = allUserCount, ActiveUserCount = activeCount, InactiveUserCount = inActiveCount });
        }
    }
}
