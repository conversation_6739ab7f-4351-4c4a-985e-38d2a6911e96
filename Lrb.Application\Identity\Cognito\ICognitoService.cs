﻿using Amazon.Extensions.CognitoAuthentication;
using Lrb.Application.Common.Identity;
using Lrb.Application.Identity.Tokens;
using System.Security.Claims;

namespace Lrb.Application.Identity.Cognito;

public interface ICognitoService<T> where T : Client
{
    Task<CreateCognitoUserResponse> CreateUserAsync(CreateCognitoUserRequest request, CancellationToken cancellationToken);
    Task<CognitoTokenResponse> TryLoginAsync(string cognitoUserId, string password, CancellationToken cancellationToken);
    Task<(Guid UserId, string SessionId, string SecretLoginCode)> TryLoginAsync(string cognitoUserId, CancellationToken cancellationToken);
    Task<AuthFlowResponse> VerifyAuthChallenge(string userId, string sessionId, string otp, CancellationToken cancellationToken);
    Task<(bool HTTPStatusCode, bool MFAStatus)> ToggleMFAAsync(string userName, CancellationToken cancellationToken);
    public Task<CognitoTokenResponse> GetTokensFromRefreshToken(string refreshToken, string cognitoUserId);
    Task<bool> UpdateUserAttributesAsync(UpdateCognitoAttributesRequest request, CancellationToken cancellationToken);
    Task<bool> DisableUserAsync(string username);
    Task<bool> DeleteUserAsync(string username);
    Task<bool> EnableUserAsync(string username);
    Task<bool> ResetPasswordAsync(string username, string password, DateTime passwordTimeStamp);
    Task<bool> IsUserEnabled(string username);
    Task<DateTime> GetPasswordTimeStampAsync(string username);
    Task<bool> AddAttendanceSettingClaimAync(string username, Claim claim);
    Task<bool?> GetUserShiftTimeAsync(AdminUserRecord? response, string? userName);
    public string GetUserPoolId();
    Task<AdminUserRecord> GetUserAsync(string username);
    Task<bool> AddUserLockedSettingClaimAync(string username, bool isLoked);
    Task<bool?> GetUserLockedInfoAsync(Lrb.Domain.Entities.AdminUserRecord? response, string? userName);
}
