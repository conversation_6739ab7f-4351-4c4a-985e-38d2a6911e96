﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Web.Specs
{
    public class GetProspectHistoryByProspectIdSpecs : Specification<ProspectHistory>
    {
        public GetProspectHistoryByProspectIdSpecs(Guid prospectid)
        {
            Query.Where(i => !i.IsDeleted && i.ProspectId == prospectid && i.Version < 3);
        }
        public GetProspectHistoryByProspectIdSpecs(Guid prospectid, List<Guid>? userIds)
        {
            Query.Where(i => !i.IsDeleted && i.ProspectId == prospectid && (userIds.Contains(i.UserId ?? Guid.Empty) || userIds.Contains(i.LastModifiedById ?? Guid.Empty)));
        }
        //public GetProspectHistoryByProspectIdSpecs(Guid id, List<Guid> userIds)
        //{
        //    Query.Where(i => i.ProspectId == id && userIds.Contains())
        //}
    }
    public class GetProspectV1HistoryByProspectIdSpecs : Specification<ProspectHistory>
    {
        public GetProspectV1HistoryByProspectIdSpecs(Guid prospectid)
        {
            Query.Where(i => !i.IsDeleted && i.ProspectId == prospectid && i.Version ==1);
        }
        public GetProspectV1HistoryByProspectIdSpecs(Guid prospectid, Guid userId)
        {
            Query.Where(i => i.ProspectId == prospectid);
        }
    }
}
