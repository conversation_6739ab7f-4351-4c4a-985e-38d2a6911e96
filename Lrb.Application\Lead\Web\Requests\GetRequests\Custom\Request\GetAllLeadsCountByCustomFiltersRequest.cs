﻿using Lrb.Application.Common.Persistence.New_Implementation;

namespace Lrb.Application.Lead.Web
{
    public class GetAllLeadsCountByCustomFiltersRequest : GetAllLeadsParametersNewFilters, IRequest<Response<List<CustomFiltersDto>>>
    {
    }
    public class GetAllLeadsCountByCustomFiltersRequestHandler : IRequestHandler<GetAllLeadsCountByCustomFiltersRequest, Response<List<CustomFiltersDto>>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepository _efLeadRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<CustomFilter> _repo;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetails;

        public GetAllLeadsCountByCustomFiltersRequestHandler(
            ICurrentUser currentUser,
            ILeadRepository efLeadRepository,
            IDapperRepository dapperRepository,
            IRepositoryWithEvents<CustomFilter> repo,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetails)
        {
            _currentUser = currentUser;
            _efLeadRepository = efLeadRepository;
            _dapperRepository = dapperRepository;
            _repo = repo;
            _userDetails= userDetails;  
        }

        public async Task<Response<List<CustomFiltersDto>>> Handle(GetAllLeadsCountByCustomFiltersRequest request, CancellationToken cancellationToken)
        {
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = new List<Guid>();
            var isAdmin = await _dapperRepository.IsAdminV2Async(userId, tenantId ?? string.Empty);
            List<CustomFilter>? filters = null;
            if (request.IsGenManagerWithTeam ?? false && (request.GeneralManagerIds?.Any() ?? false))
            {
                var generalManagerIds = await _dapperRepository.GeneralManagerAsync(request.GeneralManagerIds ?? new(), tenantId ?? string.Empty);
                if (generalManagerIds?.Any() ?? false)
                {
                    var subordinateIds = await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(generalManagerIds.ToList(), tenantId ?? string.Empty);
                    if (request.AssignTo == null)
                    {
                        request.AssignTo = new List<Guid>();
                    }
                    request.AssignTo.AddRange(subordinateIds);
                    request.AssignTo.AddRange(generalManagerIds);
                    request.AssignTo.AddRange(request.GeneralManagerIds);
                }
                if (request.AssignTo == null)
                {
                    request.AssignTo = new List<Guid>();
                }
                request.AssignTo.AddRange(request.GeneralManagerIds);
            }
            else if ((request.GeneralManagerIds?.Any() ?? false))
            {
                if (request.AssignTo == null)
                {
                    request.AssignTo = new List<Guid>();
                }
                request.AssignTo.AddRange(request.GeneralManagerIds);

            }
            if (request?.DesignationsId?.Any() ?? false)
            {
                var users = await _userDetails.ListAsync(new Lrb.Application.Dashboard.Web.Specs.GetUsersByDesignationIdSpec(request.DesignationsId));
                var userIds = users.Select(i => i.UserId).ToList();
                if (request.AssignTo == null)
                {
                    request.AssignTo = new List<Guid>();
                }
                request.AssignTo.AddRange(userIds);

            }
            var tasks = new Task[]
            {
               Task.Run(async () => request.IsDualOwnershipEnabled = request.IsDualOwnershipEnabled == null ? await _dapperRepository.V2GetDualOwnershipDetails(tenantId ?? string.Empty) : request.IsDualOwnershipEnabled),
               Task.Run(async () => subIds = await GetSubordinateIdsAsync(request.Adapt<GetAllLeadsOnlyByNewFiltersRequest>(), userId, tenantId ?? string.Empty,isAdmin)),
               Task.Run(async () => filters = await _repo.ListAsync(new GetCustomFiltersSpec(userId, isAdmin), cancellationToken)),
            };
            await Task.WhenAll(tasks);
            var customFilters = await _efLeadRepository.GetLeadsCountByCustomFiltersForWebAsync(filters, request, subIds, userId, isAdmin, new(), true);
            var data = CustomFilterHelper.GetCustomFilters(customFilters);
            return new(data ?? new List<CustomFiltersDto>());
        }
        private async Task<List<Guid>> GetSubordinateIdsAsync(GetAllLeadsOnlyByNewFiltersRequest request, Guid userId, string tenantId, bool isAdmin)
        {            
            var assignToIds = request?.AssignTo ?? new List<Guid>();
            return assignToIds.Any()
            ? (request?.IsWithTeam ?? false)
                    ? (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesV2Async(assignToIds, tenantId ?? string.Empty)).ToList()
            : assignToIds
                : (await _dapperRepository.GetSubordinateIdsV2Async(userId, tenantId ?? string.Empty, request.CanAccessAllLeads, isAdmin))?.ToList() ?? new List<Guid>();
        }
    }
}
