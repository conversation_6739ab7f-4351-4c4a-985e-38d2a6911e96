﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class ChangedSingleToMultiIntegrationAccInTeams : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Teams_IntegrationAccountInfo_IntegrationAccountInfoId",
                schema: "LeadratBlack",
                table: "Teams");

            migrationBuilder.DropIndex(
                name: "IX_Teams_IntegrationAccountInfoId",
                schema: "LeadratBlack",
                table: "Teams");

            migrationBuilder.DropColumn(
                name: "IntegrationAccountInfoId",
                schema: "LeadratBlack",
                table: "Teams");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "IntegrationAccountInfoId",
                schema: "LeadratBlack",
                table: "Teams",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Teams_IntegrationAccountInfoId",
                schema: "LeadratBlack",
                table: "Teams",
                column: "IntegrationAccountInfoId");

            migrationBuilder.AddForeignKey(
                name: "FK_Teams_IntegrationAccountInfo_IntegrationAccountInfoId",
                schema: "LeadratBlack",
                table: "Teams",
                column: "IntegrationAccountInfoId",
                principalSchema: "LeadratBlack",
                principalTable: "IntegrationAccountInfo",
                principalColumn: "Id");
        }
    }
}
