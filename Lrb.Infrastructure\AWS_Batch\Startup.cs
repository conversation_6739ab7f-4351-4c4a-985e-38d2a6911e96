﻿using Lrb.Application.Common.AWS_Batch;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Infrastructure.AWS_Batch
{
    public static class Startup
    {
        public static IServiceCollection AddAWSBatch(this IServiceCollection services, IConfiguration config)
        {
            services.Configure<ExcelUploadBatchJobSettings>(config.GetSection(nameof(ExcelUploadBatchJobSettings)));
            services.AddSingleton<IAWSBatchService, AWSBatchService>();
            services.AddSingleton<IAwsSecretService, AwsSecretService>();
            return services;
        }
    }
}
