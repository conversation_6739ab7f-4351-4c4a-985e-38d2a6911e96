﻿using System;
using System.Collections.Generic;
using Lrb.Domain.Enums;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class AddedPropertiesToLeadHisotry : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<IDictionary<int, DateTime?>>(
                name: "DateOfBirth",
                schema: "LeadratBlack",
                table: "LeadHistories",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<IDictionary<int, Gender>>(
                name: "Gender",
                schema: "LeadratBlack",
                table: "LeadHistories",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<IDictionary<int, MaritalStatusType>>(
                name: "MaritalStatus",
                schema: "LeadratBlack",
                table: "LeadHistories",
                type: "jsonb",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DateOfBirth",
                schema: "LeadratBlack",
                table: "LeadHistories");

            migrationBuilder.DropColumn(
                name: "Gender",
                schema: "LeadratBlack",
                table: "LeadHistories");

            migrationBuilder.DropColumn(
                name: "MaritalStatus",
                schema: "LeadratBlack",
                table: "LeadHistories");
        }
    }
}
