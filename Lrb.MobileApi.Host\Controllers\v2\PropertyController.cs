using Lrb.Application.Common.Persistence;
using Lrb.Application.Property.Mobile;
using Lrb.Application.Property.Mobile.Dtos;
using Lrb.Application.Property.Mobile.Dtos.V2;
using Lrb.Application.Property.Mobile.Requests;
using Lrb.Application.Property.Mobile.Requests.V2;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Mapster;

namespace Lrb.MobileApi.Host.Controllers.v2
{
    [Authorize]
    [Route("api/v2/[controller]")]
    [ApiVersionNeutral]
    public class PropertyController : VersionedApiController
    {
        private readonly IRepository<Property> _repository;
        private readonly IRepository<Address> _addressRepo;
        private readonly IRepository<MasterPropertyType> _masterPrRepo;

        public PropertyController(IRepository<Property> repository, IRepository<Address> addressRepo, IRepository<MasterPropertyType> masterPrRepo)
        {
            _repository = repository;
            _addressRepo = addressRepo;
            _masterPrRepo = masterPrRepo;
        }
        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property details.", "")]
        public Task<PagedResponse<V2GetAllPropertyDTO, string>> SearchAsync([FromQuery] V2GetAllPropertyRequest request)
        {
            return Mediator.Send(request);
        }


        [HttpPut("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Properties)]
        [OpenApiOperation("Update a property.", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateAsync(V2UpdatePropertyDto dto, Guid id)
        {
            return id != dto.Id
                ? BadRequest()
                : Ok(await Mediator.Send(dto.Adapt<V2UpdatePropertyRequest>()));
        }

        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get property details.", "")]
        public Task<Response<ViewPropertyDto>> GetAsync(Guid id)
        {
            return Mediator.Send(new GetProrpertyRequest(id));
        }

        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("Create a new property lsiting.", "")]
        public Task<Response<UpdatePropertyDtoV2>> CreateAsync([FromBody] CreatePropertyDtoV2 dto)
        {
            CreatePropertyListingV2Request request = dto.Adapt<CreatePropertyListingV2Request>();
            return Mediator.Send(request);
        }

        [HttpPut("listing/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Properties)]
        [OpenApiOperation("Update a property listing.", "")]
        public async Task<ActionResult<Guid>> UpdateAsync(UpdatePropertyDtoV2 dto, Guid id)
        {
            return id != dto.Id
                ? BadRequest()
                : Ok(await Mediator.Send(dto.Adapt<UpdatePropertyListingV2Request>()));
        }

        [HttpGet("location")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("Search location.", "")]
        public async Task<Response<List<ListingSourceAddressDtoV2>>> SearchAsync([FromQuery] GetPFLocationRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("listing/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get property details by Id.", "")]
        public async Task<Response<ViewPropertyDtoV2>> GetByIdAsync(Guid id)
        {
            return await Mediator.Send(new GetPropertyListingByIdRequestV2(id));
        }
    }
}


