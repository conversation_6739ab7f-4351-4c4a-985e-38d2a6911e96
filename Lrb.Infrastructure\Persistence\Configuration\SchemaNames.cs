﻿namespace Lrb.Infrastructure.Persistence.Configuration;

internal static class SchemaNames
{
    public static string Auditing = nameof(Auditing); // "AUDITING";
    public static string Identity = nameof(Identity); // "IDENTITY";
    public static string MultiTenancy = nameof(MultiTenancy); // "MULTITENANCY";
    public static string LeadratBlack = nameof(LeadratBlack); //APPLICATION;
    public static string ApiLogs = nameof(ApiLogs); 
}
internal static class ViewNames
{
    public static string VWUserInfo = nameof(VWUserInfo);
    public static string VWFullUserInfo = nameof(VWFullUserInfo);
}