﻿using LrbIntegrationBackgroundJobs.Dtos;
using LrbIntegrationBackgroundJobs.Dtos.PFV2;
using System.Threading.Tasks;

namespace LrbIntegrationBackgroundJobs.Services
{
    public interface IPropertyFinderServiceV2
    {

        /// <summary>
        /// Fetch all the available leads for a Property Finder account V2 (Providing Credentials) 
        /// </summary>
        /// <param name="creds"></param>
        /// <returns></returns>
        Task<PropertyFinderResponseV2> V2FetchLeadsFromPropertyFinderAsync(PFIntegrationCredDtoV2 cred);

        /// <summary>
        /// Get Access Token From Property Finder Auth V2 api (Providing Credentials) 
        /// </summary>
        /// <param name="creds"></param>
        /// <returns></returns>
        Task<string> V2GetPFAcessesToken(PFIntegrationCredDtoV2 cred);

        Task V2ProcessPropertyFinderAccountsAsync();
    }
}
