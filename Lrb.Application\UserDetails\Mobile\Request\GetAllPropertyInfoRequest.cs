﻿using Lrb.Application.UserDetails.Mobile.Dtos;

namespace Lrb.Application.UserDetails.Mobile.Request
{
    public class GetAllPropertyInfoRequest : IRequest<Response<List<UserPropertyInfoDto>>>
    {
    }

    public class GetAllPropertyInfoRequestHandler : IRequestHandler<GetAllPropertyInfoRequest, Response<List<UserPropertyInfoDto>>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public GetAllPropertyInfoRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<Response<List<UserPropertyInfoDto>>> Handle(GetAllPropertyInfoRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var tenantId = _currentUser.GetTenant();
                var properties = (await _dapperRepository.GetAllUserPropertyInfo<UserPropertyInfoDto>(tenantId)).ToList();
                return new Response<List<UserPropertyInfoDto>>(properties);
            }
            catch (Exception ex)
            {
                return new Response<List<UserPropertyInfoDto>>(null, $"Error: {ex.Message}");
            }
        }
    }
}

