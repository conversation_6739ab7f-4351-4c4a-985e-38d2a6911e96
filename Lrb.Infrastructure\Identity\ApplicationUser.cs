using Microsoft.AspNetCore.Identity;

namespace Lrb.Infrastructure.Identity;

public class ApplicationUser : IdentityUser
{
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? ImageUrl { get; set; }
    public bool IsActive { get; set; }
    public DateTime PasswordTimeStamp { get; set; } = DateTime.UtcNow;
    public bool IsMFAEnabled { get; set; }
    public string? OTP { get; set; }
    public DateTime? OTPUpdatedOn { get; set; }
    public string? RefreshToken { get; set; }
    public DateTime RefreshTokenExpiryTime { get; set; }
    public DateTime? LastModifiedOn { get; set; }
    public DateTime? CreatedOn { get; set; }
    public Guid CreatedBy { get; set; }
    public Guid LastModifiedBy { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime? DeletedOn { get; set; }
    public Guid? DeletedBy { get; set; }

    public string? ObjectId { get; set; }
    public string? LicenseNo { get; set; }
    public bool? IsLocked { get; set; }
}