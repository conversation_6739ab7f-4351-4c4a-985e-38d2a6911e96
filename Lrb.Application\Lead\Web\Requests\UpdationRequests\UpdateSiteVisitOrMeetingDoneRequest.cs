﻿using Lrb.Application.Common.Interfaces;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.ListingManagement.Web.Dtos;
using Lrb.Application.Property.Web;
using Lrb.Domain.Enums;

namespace Lrb.Application.Lead.Web.Requests
{
    public class UpdateSiteVisitOrMeetingDoneRequest : IRequest<Response<bool>>
    {
        public Guid LeadId { get; set; }
        public AppointmentType MeetingOrSiteVisit { get; set; }
        public bool IsDone { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string? ProjectName { get; set; }
        public string? ExecutiveName { get; set; }
        public string? ExecutiveContactNo { get; set; }
        public string? Image { get; set; }
        public List<LeadDocument>? ImagesWithName { get; set; }
        public bool? IsManual { get; set; }
        public string? Notes { get; set; }
        public AddressDto? Address { get; set; }
        public string? PropertyName { get; set; }
        public Guid? UserId { get; set; }
        public DateTime? AppointmentDoneOn { get; set; }
    }
    public class UpdateSiteVisitOrMeetingDoneRequestHandler : LeadCommonRequestHandler, IRequestHandler<UpdateSiteVisitOrMeetingDoneRequest, Response<bool>>
    {
        public UpdateSiteVisitOrMeetingDoneRequestHandler(IServiceProvider serviceProvider) : base(serviceProvider, typeof(UpdateSiteVisitOrMeetingDoneRequestHandler).Name, "Handle")
        {
        }

        public async Task<Response<bool>> Handle(UpdateSiteVisitOrMeetingDoneRequest request, CancellationToken cancellationToken)
        {
            try
            {
                Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var lead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.LeadId), cancellationToken) ?? throw new NotFoundException("No Lead exists with the given Id");

                try
                {
                    lead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(lead, request.Adapt<PickedLeadDto>());
                }
                catch (Exception ex)
                {
                    throw;
                }

                var appointment = request.Adapt<LeadAppointment>();
                appointment.Location = request.Address?.Adapt<Address>();
                appointment.Latitude = request.Latitude == default ? double.TryParse(request.Address?.Latitude, out double lat) ? lat : request.Latitude : default;
                appointment.Longitude = request.Longitude == default ? double.TryParse(request.Address?.Longitude, out double lng) ? lng : request.Longitude : default;
                appointment.UniqueKey = Guid.NewGuid();

                #region appointment done and not done by user
                var currentUser = _currentUser.GetUserId();

                if (request.UserId.HasValue && request.UserId.Value != Guid.Empty)
                {
                    appointment.UserId = request.UserId.Value;
                    currentUser = request.UserId.Value;
                }
                else
                {
                    appointment.UserId = lead.AssignTo;
                }

                #endregion

                await SetLeadAppointmentAsync(lead, appointment, request.MeetingOrSiteVisit, request.ImagesWithName, cancellationToken);
                //Add Appointment Documents to Lead Documents
                if (request.ImagesWithName != null && request.ImagesWithName.Any())
                {
                    var docType = request.MeetingOrSiteVisit == AppointmentType.Meeting ? Domain.Enums.LeadDocumentType.Meeting : request.MeetingOrSiteVisit == AppointmentType.SiteVisit ? Domain.Enums.LeadDocumentType.SiteVisit : Domain.Enums.LeadDocumentType.None;
                    await SetLeadDocsAsync(lead, request.ImagesWithName, docType, cancellationToken);
                }
                lead.Notes = appointment.Notes;
                lead.AppointmentDoneOn = appointment.AppointmentDoneOn;
                await _leadRepo.UpdateAsync(lead);

                var leadDto = lead.Adapt<ViewLeadDto>();
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId: currentUser);

                await UpdateLeadHistoryAsync(lead, leadDto, appointmentType: request.MeetingOrSiteVisit, cancellationToken: cancellationToken, currentUserId: currentUser);

                await SendLeadAppointmentNotificationAsync(lead, request.MeetingOrSiteVisit, request.IsDone, globalSettings, cancellationToken);

                return new(true);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{typeof(AddDocumentRequestHandler).Name} - Handle()");
                throw;
            }
        }
    }

}
