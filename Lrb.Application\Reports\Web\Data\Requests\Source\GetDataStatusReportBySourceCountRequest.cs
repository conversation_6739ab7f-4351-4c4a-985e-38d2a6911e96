﻿using Lrb.Application.Utils;
using Lrb.Shared.Extensions;

namespace Lrb.Application.Reports.Web
{
    public class GetDataStatusReportBySourceCountRequest : IRequest<int>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DataManagement.Web.Request.ProspectDateType? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public string? SearchText { get; set; }
        public List<Guid>? SourceIds { get; set; }
        public List<string>? SubSources { get; set; }
        public List<string>? Projects { get; set; }
        public UserStatus? UserStatus { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
    }
    public class GetDataStatusReportBySourceCountRequestHandler : IRequestHandler<GetDataStatusReportBySourceCountRequest, int>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public GetDataStatusReportBySourceCountRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<int> Handle(GetDataStatusReportBySourceCountRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            var count = (await _dapperRepository.QueryStoredProcedureCountFromReadReplicaAsync("LeadratBlack", "Data_GetDataCountReportBySource", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                datetype = request.DateType,
                tenantid = tenantId,
                userids = request.UserIds,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                //userstatus = (request?.UserStatus ?? 0)
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                sources = request?.SourceIds,
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize())
            }));
            return count;
        }
    }
}
