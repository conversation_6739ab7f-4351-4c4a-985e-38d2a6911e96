﻿using Lrb.Application.Common.Listing;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.ListingManagement.Web.Specs;
using Lrb.Application.Property.Web.Specs;
using Lrb.Application.Property.Web.V2.Dtos;
using Lrb.Application.Property.Web.V2.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Mapster;

namespace ListingBackgroundJobs
{
    internal class FunctionEntryPoint : IFunctionEntryPoint
    {
        private readonly IPropertyFinderListingService _pFListingService;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Property> _propertyRepo;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<MasterPropertyType> _masterPropertyType;
        private readonly IRepositoryWithEvents<CustomMasterAmenity> _customAmenities;
        private readonly IRepositoryWithEvents<CustomMasterAttribute> _customAttributes;
        private readonly IRepositoryWithEvents<ListingIntegrationInfo> _listingAccountRepo;
        private readonly IRepositoryWithEvents<CustomListingSource> _listingSourceRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<LeadEnquiry> _leadEnquiryRepo;
        protected readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        protected readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        public FunctionEntryPoint(IPropertyFinderListingService pFListingService,
            IRepositoryWithEvents<Lrb.Domain.Entities.Property> propertyRepo,
            IUserService userService,
            IRepositoryWithEvents<MasterPropertyType> masterPropertyType,
            IRepositoryWithEvents<CustomMasterAmenity> customAmenities,
            IRepositoryWithEvents<CustomMasterAttribute> customAttributes,
            IRepositoryWithEvents<ListingIntegrationInfo> listingAccountRepo,
            IRepositoryWithEvents<CustomListingSource> listingSourceRepo,
            IDapperRepository dapperRepository,
            IRepositoryWithEvents<Lrb.Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<LeadEnquiry> leadEnquiryRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IRepositoryWithEvents<Address> addressRepo
            )
        {
            _pFListingService = pFListingService;
            _propertyRepo = propertyRepo;
            _userService = userService;
            _masterPropertyType = masterPropertyType;
            _customAmenities = customAmenities;
            _customAttributes = customAttributes;
            _listingAccountRepo = listingAccountRepo;
            _listingSourceRepo = listingSourceRepo;
            _dapperRepository = dapperRepository;
            _leadRepo = leadRepo;
            _leadEnquiryRepo = leadEnquiryRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _addressRepo = addressRepo;
        }

        #region Publish Listing
        public async Task<bool> PublishListingAsync(ListAndDelistListingDtoV2 dto, string tenantId, Guid currentUserId)
        {
            foreach (var id in dto?.ListingIds ?? new())
            {
                var property = await _propertyRepo.FirstOrDefaultAsync(new GetPropertyByIdSpecsV2(id));
                if (property != null)
                {
                    var listingAccount = await _listingAccountRepo.FirstOrDefaultAsync(new ListingSiteAccountSpecs(dto?.SourceId ?? Guid.Empty));
                    var attributes = await _customAttributes.ListAsync(new GetCustomAttributesForListingSpecs());
                    var amenities = await _customAmenities.ListAsync(new GetCustomMasterAmetiesForListingSpecs());
                    var listingSource = await _listingSourceRepo.FirstOrDefaultAsync(new GetAllListingSourceByIds(dto?.SourceId ?? Guid.Empty));
                    if (listingAccount != null && listingSource != null)
                    {
                        var getResponse = await _pFListingService.SearchListingOnPF(new PFListingFilter
                        {
                            Page = 1,
                            Perpage = 10,
                            IsDraft = true,
                            Reference = property?.SerialNo ?? string.Empty
                        }, listingAccount?.ApiKey ?? string.Empty, listingAccount?.SecretKey ?? string.Empty);

                        if(getResponse.results?.Any() ?? false)
                        {
                            #region Re Publish
                            var existingListing = getResponse.results.FirstOrDefault(i => i.reference == property?.SerialNo);
                            if(existingListing != null)
                            {
                                var isPublished = await _pFListingService.PublishListingOnPF(existingListing?.id ?? string.Empty, listingAccount);
                                if (isPublished)
                                {
                                    #region Update Property
                                    property.PFListingId = existingListing?.id ?? string.Empty;
                                    property.ListingStatus = ListingStatus.Approved;
                                    property.PublishedOn = DateTime.UtcNow;
                                    property.PublishedBy = currentUserId;
                                    property.ShouldVisisbleOnListing = true;
                                    if (property.ListingSources?.Any() ?? false)
                                    {
                                        property.ListingSources.Add(listingSource);
                                    }
                                    else
                                    {
                                        property.ListingSources = new List<CustomListingSource>() { listingSource };
                                    }
                                    #endregion

                                    await _propertyRepo.UpdateAsync(property);
                                }
                                else
                                {
                                    #region Update Property
                                    property.ListingStatus = ListingStatus.Draft;
                                    property.ShouldVisisbleOnListing = false;
                                    if (property.ListingSources?.Any() ?? false)
                                    {
                                        property.ListingSources.Remove(listingSource);
                                    }
                                    #endregion

                                    await _propertyRepo.UpdateAsync(property);
                                }
                            }
                            #endregion
                        }
                        else
                        {
                            #region New Publish
                            var pfListing = await MapProperty(property, attributes, dto?.SourceId ?? Guid.Empty, listingAccount, amenities, tenantId);
                            var res = await _pFListingService.CreateListingOnPF(pfListing, listingAccount);
                            if (res != null)
                            {
                                var isPublished = await _pFListingService.PublishListingOnPF(res?.Id ?? string.Empty, listingAccount);
                                if (isPublished)
                                {
                                    #region Update Property
                                    property.PFListingId = res?.Id ?? string.Empty;
                                    property.ListingStatus = ListingStatus.Approved;
                                    property.PublishedOn = DateTime.UtcNow;
                                    property.PublishedBy = currentUserId;
                                    property.ShouldVisisbleOnListing = true;
                                    if (property.ListingSources?.Any() ?? false)
                                    {
                                        property.ListingSources.Add(listingSource);
                                    }
                                    else
                                    {
                                        property.ListingSources = new List<CustomListingSource>() { listingSource };
                                    }
                                    #endregion

                                    await _propertyRepo.UpdateAsync(property);
                                }
                                else
                                {
                                    #region Update Property
                                    property.ListingStatus = ListingStatus.Draft;
                                    property.ShouldVisisbleOnListing = false;
                                    if (property.ListingSources?.Any() ?? false)
                                    {
                                        property.ListingSources.Remove(listingSource);
                                    }
                                    #endregion

                                    await _propertyRepo.UpdateAsync(property);
                                }
                            }
                            else
                            {
                                #region Update Property
                                property.ListingStatus = ListingStatus.Draft;
                                property.ShouldVisisbleOnListing = false;
                                if (property.ListingSources?.Any() ?? false)
                                {
                                    property.ListingSources.Remove(listingSource);
                                }
                                #endregion

                                await _propertyRepo.UpdateAsync(property);

                                return false;
                            }
                            #endregion
                        }

                        return true;
                    }
                }
            }
            return false;
        }
        #endregion

        #region Delete Listing
        public async Task<bool> DeleteListingAsync(ListAndDelistListingDtoV2 dto, string tenantId, Guid currentUserId)
        {
            foreach(var id in dto.ListingIds ?? new())
            {
                var property = await _propertyRepo.FirstOrDefaultAsync(new GetPropertyToDelistByIdSpecsV2(id));
                if(property != null)
                {
                    var account = await _listingAccountRepo.FirstOrDefaultAsync(new ListingSiteAccountSpecs(dto?.SourceId ?? Guid.Empty));
                    var source = await _listingSourceRepo.FirstOrDefaultAsync(new GetAllListingSourceByIds(dto?.SourceId ?? Guid.Empty));
                    if (account != null && source != null)
                    {
                        var isDeleted = await _pFListingService.DeleteListingOnPF(property?.PFListingId ?? string.Empty, account);
                        if (isDeleted)
                        {
                            property.ListingStatus = ListingStatus.Draft;
                            property.ShouldVisisbleOnListing = false;
                            property.PFListingId = string.Empty;
                            property.PublishedOn = null;
                            property.ListingSources?.Remove(source);
                            await _propertyRepo.UpdateAsync(property);
                            return true;
                        }
                    }
                }
                return false;
            }
            return false;
        }
        #endregion

        #region Update Listing
        public async Task<bool> UpdateListingAsync(ListAndDelistListingDtoV2 dto, string tenantId, Guid currentUserId)
        {
            try
            {
                foreach (var id in dto?.ListingIds ?? new())
                {
                    var property = await _propertyRepo.FirstOrDefaultAsync(new GetPropertyByIdSpecsV2(id));
                    if (property != null)
                    {
                        var listingAccount = await _listingAccountRepo.FirstOrDefaultAsync(new ListingSiteAccountSpecs(dto?.SourceId ?? Guid.Empty));
                        var attributes = await _customAttributes.ListAsync(new GetCustomAttributesForListingSpecs());
                        var amenities = await _customAmenities.ListAsync(new GetCustomMasterAmetiesForListingSpecs());
                        if (listingAccount != null)
                        {
                            var pfListing = await MapProperty(property, attributes, dto?.SourceId ?? Guid.Empty, listingAccount, amenities, tenantId);
                            var res = await _pFListingService.UpdateListingOnPF(pfListing, property?.PFListingId ?? string.Empty, listingAccount);
                            if(res != null)
                            {
                                return true;
                            }
                        }
                    }
                }
                return false;
            }
            catch 
            {
                return false;
            }
        }
        #endregion

        #region Permanent Delete Listing
        public async Task<bool> PermanentDeleteListingAsync(ListAndDelistListingDtoV2 dto, string tenantId, Guid currentUserId)
        {
            foreach (var id in dto.ListingIds ?? new())
            {
                var property = await _propertyRepo.FirstOrDefaultAsync(new GetPropertyToDelistByIdSpecsV2(id));
                if (property != null)
                {
                    var account = await _listingAccountRepo.FirstOrDefaultAsync(new ListingSiteAccountSpecs(dto?.SourceId ?? Guid.Empty));
                    var source = await _listingSourceRepo.FirstOrDefaultAsync(new GetAllListingSourceByIds(dto?.SourceId ?? Guid.Empty));
                    if (account != null && source != null)
                    {
                        var isDeleted = await _pFListingService.DeleteListingOnPF(property?.PFListingId ?? string.Empty, account);
                        if (isDeleted)
                        {
                            property.ListingStatus = ListingStatus.Archived;
                            property.ShouldVisisbleOnListing = false;
                            property.PFListingId = string.Empty;
                            property.PublishedOn = null;
                            property.ListingSources?.Remove(source);
                            await _propertyRepo.UpdateAsync(property);
                            return true;
                        }
                    }
                }
                return false;
            }
            return false;
        }
        #endregion

        #region Map Property
        private async Task<CreateListingOnPFRequestDto> MapProperty(Lrb.Domain.Entities.Property property, List<CustomMasterAttribute> attributes, Guid sourceId, ListingIntegrationInfo account, List<CustomMasterAmenity> amenities, string tenantId)
        {
            var assignedUser = property.PropertyAssignments?.FirstOrDefault(i => i.IsCurrentlyAssigned == true);
            var userId = await _dapperRepository.GetThirdPartyIdByUserId(assignedUser?.AssignedTo ?? Guid.Empty, tenantId);
            var isConverted = int.TryParse(userId, out int thirdPartyId);
            var basePropertyType = await _masterPropertyType.GetByIdAsync(property.PropertyType?.BaseId ?? Guid.Empty);
            var refId = property.SourceReferenceIds?.FirstOrDefault(i => i.Key == sourceId).Value.ReferenceId;
            var pfListing = new CreateListingOnPFRequestDto
            {
                age = property?.Age != null ? (int?)property.Age : null,
                assignedTo = new AssignedTo()
                {
                    id = isConverted ? thirdPartyId : null,
                },
                availableFrom = property?.PossessionDate?.ToString("yyyy-MM-dd"),
                category = basePropertyType?.DisplayName?.ToLower(),
                type = property?.PropertyType?.DisplayName?.ToLower() ?? null,
                createdBy = new CreatedBy()
                {
                    id = isConverted ? thirdPartyId : null,
                },
                description = new Description()
                {
                    ar = property?.AboutPropertyWithLanguage ?? null,
                    en = property?.AboutProperty ?? null
                },
                developer = property?.Project?.BuilderDetail?.Name ?? null,
                size = (int)property.Dimension?.Area,
                title = new Title()
                {
                    ar = property?.TitleWithLanguage ?? null,
                    en = property?.Title ?? null
                },
                reference = !string.IsNullOrEmpty(refId) ? refId : property?.SerialNo,
                projectStatus = property?.CompletionStatus?.ToString()?.ToLower() ?? null,
            };

            switch (property?.CompletionStatus)
            {
                case CompletionStatus.Completed:
                    pfListing.projectStatus = "completed";
                    break;

                case CompletionStatus.OffPlan:
                    pfListing.projectStatus = "off_plan";
                    break;
                case CompletionStatus.CompletedPrimary:
                    pfListing.projectStatus = "completed_primary";
                    break;
                case CompletionStatus.OffPlanPrimary:
                    pfListing.projectStatus = "off_plan_primary";
                    break;
            }

            switch (property?.FurnishStatus)
            {
                case FurnishStatus.Furnished:
                    pfListing.furnishingType = "furnished";
                    break;

                case FurnishStatus.Semifurnished:
                    pfListing.furnishingType = "semi-furnished";
                    break;
                case FurnishStatus.Unfurnished:
                    pfListing.furnishingType = "unfurnished";
                    break;
            }

            switch (property?.FinishingType)
            {
                case FinishingType.FullyFinished:
                    pfListing.finishingType = "fully-finished";
                    break;

                case FinishingType.SemiFinished:
                    pfListing.finishingType = "semi-finished";
                    break;
                case FinishingType.Unfinished:
                    pfListing.finishingType = "unfinished";
                    break;
            }

            if (property?.UaeEmirate == UaeEmirate.dubai || property?.UaeEmirate == UaeEmirate.abu_dhabi)
            {
                pfListing.compliance = new Lrb.Application.Property.Web.V2.Dtos.Compliance()
                {
                    advertisementLicenseIssuanceDate = property.Compliance?.LicenseIssuanceDate ?? null,
                    listingAdvertisementNumber = property.Compliance?.ListingAdvertisementNumber ?? string.Empty,
                    type = property.Compliance?.Type.ToString()?.ToLower(),
                    userConfirmedDataIsCorrect = property.Compliance?.UserConfirmedDataIsCorrect ?? null
                };
            }
            else
            {
                pfListing.uaeEmirate = property?.UaeEmirate.ToString()?.ToLower() ?? null;
            }

            #region Property Type
            switch (property?.PropertyType?.Type?.ToLower())
            {
                case "compound":
                    pfListing.type = "compound";
                    break;
                case "whole_building":
                    pfListing.type = "whole-building";
                    break;
                case "factory":
                    pfListing.type = "factory";
                    break;
                case "apartment":
                    pfListing.type = "apartment";
                    break;
                case "shop":
                    pfListing.type = "shop";
                    break;
                case "warehouse":
                    pfListing.type = "warehouse";
                    break;
                case "office_space":
                    pfListing.type = "office-space";
                    break;
                case "full_floor":
                    pfListing.type = "full-floor";
                    break;
                case "villa":
                    pfListing.type = "villa";
                    break;
                case "farm":
                    pfListing.type = "farm";
                    break;
                case "showroom":
                    pfListing.type = "show-room";
                    break;
                case "bulk_sale_unit":
                    pfListing.type = "bulk-sale-unit";
                    break;
                case "restaurants":
                    pfListing.type = "restaurant";
                    break;
                case "half_floor":
                    pfListing.type = "half-floor";
                    break;
                case "bulk_rent _unit":
                    pfListing.type = "bulk-rent-unit";
                    break;
                case "co_working_office_space":
                    pfListing.type = "co-working-space";
                    break;
                case "hotel_apartment":
                    pfListing.type = "hotel-apartment";
                    break;
                case "retail":
                    pfListing.type = "retail";
                    break;
                case "duplex":
                    pfListing.type = "duplex";
                    break;
                case "townhouse":
                    pfListing.type = "townhouse";
                    break;
                case "staff_accommodation":
                    pfListing.type = "staff-accommodation";
                    break;
                case "penthouse":
                    pfListing.type = "penthouse";
                    break;
                case "bungalow":
                    pfListing.type = "bungalow";
                    break;
                case "labor_camp":
                    pfListing.type = "labor-camp";
                    break;
                case "business_centre":
                    pfListing.type = "business-center";
                    break;
            }
            #endregion

            #region Location
            var pfLocation = property?.ListingSourceAddresses?.Where(i => i.ListingSourceId == sourceId).FirstOrDefault();
            if (pfLocation?.LocationId != null)
            {
                var isLocationConverted = int.TryParse(pfLocation.LocationId, out int locId);
                if (isLocationConverted)
                {
                    pfListing.location = new Lrb.Application.Property.Web.V2.Dtos.Location()
                    {
                        id = locId,
                    };
                }
            }
            else
            {
                var locationString = !string.IsNullOrEmpty(pfLocation?.TowerName) ? pfLocation.TowerName + " " + pfLocation.SubCommunity + " " + pfLocation.Community + " " + pfLocation.City : pfLocation?.SubCommunity + " " + pfLocation?.Community + " " + pfLocation?.City;
                var filter = new PFLocationFilter()
                {
                    Page = 1,
                    PerPage = 10,
                    Search = locationString
                };
                var location = await _pFListingService.GetPFLocation(filter, account?.ApiKey ?? string.Empty, account?.SecretKey ?? string.Empty);
                pfListing.location = new Lrb.Application.Property.Web.V2.Dtos.Location()
                {
                    id = location.data?.FirstOrDefault()?.id ?? null,
                };
            }
            #endregion

            #region Amenities
            var amenityIds = property?.Amenities?.Select(i => i.MasterPropertyAmenityId);
            var amenityName = amenities.Where(i => (amenityIds?.Any() ?? false) && amenityIds.Contains(i.Id)).Select(i => i.AmenityDisplayName).ToList();
            List<string> pfAmenitites = new();
            var amenityMap = new List<(string Key, string Value)>
            {
                ("Air Conditioner", "central-ac"),
                ("Built-in Wardrobes", "built-in-wardrobes"),
                ("Built-in Kitchen Appliances", "kitchen-appliances"),
                ("Security", "security"),
                ("Concierge Service", "concierge"),
                ("Maid Service", "maid-service"),
                ("Balcony", "balcony"),
                ("Private Gym", "private-gym"),
                ("Shared Gym", "shared-gym"),
                ("private-jacuzzi", "private-jacuzzi"),
                ("Shared Spa", "shared-spa"),
                ("Covered Parking", "covered-parking"),
                ("Maid Room", "maids-room"),
                ("Study", "study"),
                ("Children Play Area", "childrens-play-area"),
                ("Pets Allowed", "pets-allowed"),
                ("BBQ Area", "barbecue-area"),
                ("Shared Pool", "shared-pool"),
                ("Children's Pool", "childrens-pool"),
                ("Private Garden", "private-garden"),
                ("Private Pool", "private-pool"),
                ("View of Water", "view-of-water"),
                ("View of Landmark", "view-of-landmark"),
                ("Walk-in Closet", "walk-in-closet"),
                ("Vastu compliant", "vastu-compliant")
            };
            foreach (var (key, value) in amenityMap)
            {
                if (amenityName.Contains(key))
                {
                    pfAmenitites.Add(value);
                }
            }

            if (amenityName?.Any() ?? false)
            {
                pfListing.amenities = pfAmenitites;
            }
            #endregion

            #region Attributes
            var bathroomId = attributes?.Where(i => i.AttributeName == "numberOfBathrooms").FirstOrDefault()?.Id;
            var bathroomValue = property?.Attributes?.Where(i => i.MasterPropertyAttributeId == bathroomId).FirstOrDefault()?.Value;
            pfListing.bathrooms = bathroomValue;

            var bedroomId = attributes?.Where(i => i.AttributeName == "numberOfBedrooms").FirstOrDefault()?.Id;
            var bedroomValue = property?.Attributes?.Where(i => i.MasterPropertyAttributeId == bedroomId).FirstOrDefault()?.Value;
            if(!string.IsNullOrEmpty(bedroomValue))
            {
                pfListing.bedrooms = bedroomValue;
            }
            else if(property?.NoOfBHKs != default && property?.NoOfBHKs != null && property.NoOfBHKs == 0.5)
            {
                pfListing.bedrooms = "studio";
            }

            #endregion

            #region Images
            if (property?.Galleries?.Any() ?? false)
            {
                var original = new List<Lrb.Application.Property.Web.V2.Dtos.Image>();
                var images = property.Galleries.OrderBy(i => i.OrderRank);
                foreach (var image in images)
                {
                    var imageSize = new ImageSize()
                    {
                        url = !string.IsNullOrEmpty(image.ImageFilePath) ? $"https://leadrat-black.s3.ap-south-1.amazonaws.com/{image.ImageFilePath}" : null,
                        width = image.Width ?? default,
                        height = image.Height ?? default
                    };
                    var thumbnail = new ImageSize()
                    {
                        url = !string.IsNullOrEmpty(image.ImageFilePath) ? $"https://leadrat-black.s3.ap-south-1.amazonaws.com/{image.ImageFilePath}" : null,
                        width = image.Width ?? default,
                        height = image.Width ?? default
                    };
                    var imageDto = new Lrb.Application.Property.Web.V2.Dtos.Image()
                    {
                        original = imageSize,
                        thumbnail = thumbnail,
                    };
                    original.Add(imageDto);
                }
                pfListing.media = new Lrb.Application.Property.Web.V2.Dtos.Media()
                {
                    images = original,
                    videos = new Videos()
                    {
                        view360 = property?.View360Url?.FirstOrDefault() ?? null,
                    }
                };
            }
            #endregion

            #region Price
            if (property?.MonetaryInfo != null)
            {
                switch (property.MonetaryInfo.PaymentFrequency)
                {
                    case PaymentFrequency.Daily:
                        pfListing.price = new Price()
                        {
                            type = property?.MonetaryInfo?.PaymentFrequency.ToString()?.ToLower() ?? null,
                            amounts = new Amounts()
                            {
                                daily = (int?)property?.MonetaryInfo?.ExpectedPrice ?? null,
                            },
                        };
                        break;
                    case PaymentFrequency.Monthly:
                        pfListing.price = new Price()
                        {
                            type = property?.MonetaryInfo?.PaymentFrequency.ToString()?.ToLower() ?? null,
                            amounts = new Amounts()
                            {
                                monthly = (int?)property?.MonetaryInfo?.ExpectedPrice ?? null,
                            },
                        };
                        break;
                    case PaymentFrequency.Weekly:
                        pfListing.price = new Price()
                        {
                            type = property?.MonetaryInfo?.PaymentFrequency.ToString()?.ToLower() ?? null,
                            amounts = new Amounts()
                            {
                                weekly = (int?)property?.MonetaryInfo?.ExpectedPrice ?? null,
                            },
                        };
                        break;
                    case PaymentFrequency.Yearly:
                        pfListing.price = new Price()
                        {
                            type = property?.MonetaryInfo?.PaymentFrequency.ToString()?.ToLower() ?? null,
                            amounts = new Amounts()
                            {
                                yearly = (int?)property?.MonetaryInfo?.ExpectedPrice ?? null,
                            },
                        };
                        break;
                }

                if (property?.UaeEmirate != UaeEmirate.dubai && property?.EnquiredFor == EnquiryType.Sale)
                {
                    pfListing.price = new Price()
                    {
                        type = "sale" ?? null,
                        amounts = new Amounts()
                        {
                            sale = (int?)property?.MonetaryInfo?.ExpectedPrice ?? null,
                        },
                        downpayment = (int?)property?.MonetaryInfo?.Downpayment ?? null,
                    };
                }
            }
            
            #endregion

            return pfListing;
        }
        #endregion

        #region Map Listing Details to Leads
        public async Task<bool> MapListingDetailsToLeadsAsync(MapListingDetailsToLeads dto, string tenantId)
        {
            try
            {
                var listing = await _pFListingService.SearchListingOnPF(new PFListingFilter
                {
                    Page = 1,
                    Perpage = 10,
                    Id = dto.ListingId
                }, dto?.ApiKey ?? string.Empty, dto?.SecretKey ?? string.Empty);

                var lead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(dto?.LeadId ?? Guid.Empty));

                if (lead != null)
                {
                    var pfListing = listing.results?.FirstOrDefault();

                    #region Update Property
                    if (!string.IsNullOrEmpty(pfListing?.title?.en))
                    {
                        var propertyTitle = pfListing?.title?.en;
                        try
                        {
                            var existingProperty = await _propertyRepo.FirstOrDefaultAsync(new GetPropertyByTitleSpec(propertyTitle));
                            if (existingProperty != null)
                            {
                                if (lead.Properties != null)
                                {
                                    lead.Properties.Add(existingProperty);
                                }
                                else
                                {
                                    lead.Properties = new List<Lrb.Domain.Entities.Property>() { existingProperty };
                                }
                            }
                            else
                            {
                                Lrb.Domain.Entities.Property property = new() { Title = propertyTitle };
                                property = await _propertyRepo.AddAsync(property);
                                if (lead.Properties != null)
                                {
                                    lead.Properties.Add(property);
                                }
                                else
                                {
                                    lead.Properties = new List<Lrb.Domain.Entities.Property>() { property };
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                        }
                    }
                    #endregion

                    await _leadRepo.UpdateAsync(lead);

                    #region Update Lead Enquiry
                    var enquiry = lead.Enquiries.Where(i => i.IsPrimary).FirstOrDefault();
                    if (enquiry != null)
                    {
                        List<int> beds = new();
                        List<int> bath = new();

                        var pflocation = await _pFListingService.GetPFLocation(new PFLocationFilter
                        {
                            PerPage = 10,
                            Page = 1,
                            locationId = pfListing?.location?.id ?? null
                        }, dto?.ApiKey ?? string.Empty, dto?.SecretKey ?? string.Empty);

                        var location = pflocation?.data?.FirstOrDefault();
                        if(location != null)
                        {
                            var address = new Address()
                            {
                                City = location?.tree?.Where(i => i.type == "CITY").FirstOrDefault()?.name ?? null,
                                Community = location?.tree?.Where(i => i.type == "COMMUNITY").FirstOrDefault()?.name ?? null,
                                SubCommunity = location?.tree?.Where(i => i.type == "SUBCOMMUNITY").FirstOrDefault()?.name ?? null,
                                TowerName = location?.tree?.Where(i => i.type == "TOWER").FirstOrDefault()?.name ?? null,
                                Latitude = location?.Coordinates?.lat.ToString() ?? null,
                                Longitude = location?.Coordinates?.lng.ToString() ?? null,
                            };

                            await _addressRepo.AddAsync(address);

                            enquiry.Addresses = new List<Address>() { address };
                        }

                        if (!string.IsNullOrEmpty(pfListing?.type))
                        {
                            var propertyType = await _masterPropertyType.FirstOrDefaultAsync(new GetMasterPropertyTypeByNameSpecs(pfListing.type));
                            if (propertyType != null)
                            {
                                enquiry.PropertyType = propertyType;
                                enquiry.PropertyTypes = new List<MasterPropertyType>() { propertyType };
                            }
                        }
                        var enquiryType = pfListing?.price?.amounts?.sale != null ? EnquiryType.Sale : EnquiryType.Rent;
                        enquiry.EnquiryTypes = new List<EnquiryType>() { enquiryType };

                        var budget = pfListing?.price?.amounts?.yearly != null ? pfListing?.price?.amounts?.yearly : pfListing?.price?.amounts?.sale;
                        enquiry.UpperBudget = budget;
                        enquiry.LowerBudget = budget;

                        if (!string.IsNullOrWhiteSpace(pfListing?.bedrooms))
                        {
                            var isParsed = int.TryParse(pfListing?.bedrooms, out var bedroomValue);
                            if (isParsed)
                            {
                                beds.Add(bedroomValue);
                                enquiry.Beds = beds;
                            }
                        }
                        if (!string.IsNullOrWhiteSpace(pfListing?.bathrooms))
                        {
                            var isParsed = int.TryParse(pfListing?.bathrooms, out var bathroomValue);
                            if (isParsed)
                            {
                                bath.Add(bathroomValue);
                                enquiry.Baths = bath;
                            }
                        }

                        if (pfListing?.furnishingType == "furnished")
                        {
                            enquiry.Furnished = FurnishStatus.Furnished;
                        }
                        else if (pfListing?.furnishingType == "unfurnished")
                        {
                            enquiry.Furnished = FurnishStatus.Unfurnished;
                        }
                        else if (pfListing?.furnishingType == "semi-furnished")
                        {
                            enquiry.Furnished = FurnishStatus.Semifurnished;
                        }

                        if (pfListing?.size != null)
                        {
                            var masterAreaUnit = await _masterAreaUnitRepo.FirstOrDefaultAsync(new GetMasterAreaUnitByUnitSpecs());
                            enquiry.CarpetArea = pfListing?.size;
                            enquiry.CarpetAreaInSqMtr = masterAreaUnit?.ConversionFactor * pfListing?.size;
                            enquiry.CarpetAreaUnitId = masterAreaUnit?.Id ?? Guid.Empty;
                        }

                        await _leadEnquiryRepo.UpdateAsync(enquiry);

                    }
                    #endregion

                    var fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(dto?.LeadId ?? Guid.Empty));

                    #region Update LeadHistory
                    var leadDto = fullLead?.Adapt<ViewLeadDto>();

                    await leadDto.SetUsersInViewLeadDtoAsync(_userService, default, source: enquiry?.LeadSource);

                    var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);

                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id));

                    if (existingLeadHistory != null)
                    {
                        await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), default);
                    }
                    #endregion
                }

                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
            
        }
        #endregion
    }
}
