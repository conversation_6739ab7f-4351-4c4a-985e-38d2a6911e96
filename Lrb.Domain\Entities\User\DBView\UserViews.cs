﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Domain.Entities
{
    public class FullUserView : AuditableEntity, IAggregateRoot
    {
        private List<UserRolePermission>? rolePermission;
        private List<UserRole>? userRoles;
        private List<UserDoc>? documents;

        public string UserName { get; set; } = default!;
        public string FirstName { get; set; } = default!;
        public string LastName { get; set; } = default!;
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public bool IsActive { get; set; }
        public bool EmailConfirmed { get; set; }
        public bool PhoneNumberConfirmed { get; set; }
        public bool TwoFactorEnabled { get; set; }
        public string? ImageUrl { get; set; }
        public string? AltPhoneNumber { get; set; }
        public string? Address { get; set; }
        public string? AltEmail { get; set; }
        public BloodGroupType? BloodGroup { get; set; }
        public Gender? Gender { get; set; }
        public string? PermanentAddress { get; set; }
        public string? EmpNo { get; set; }
        public string? OfficeName { get; set; }
        public string? OfficeAddress { get; set; }
        public string? Description { get; set; }
        public bool IsAutomationEnabled { get; set; }

        [Column(TypeName = "jsonb")]
        public UserReportsTo? ReportsTo { get; set; }
        [Column(TypeName = "jsonb")]
        public UserReportsTo? GeneralManager { get; set; }

        [Column(TypeName = "jsonb")]
        public UserDepartmant? Department { get; set; }

        [Column(TypeName = "jsonb")]
        public UserDesignation? Designation { get; set; }

        [Column(TypeName = "jsonb")]
        public List<UserDoc>? Documents { get => documents?.DistinctBy(i=> i.Id)?.ToList(); set => documents = value?.DistinctBy(i => i.Id)?.ToList(); }

        [Column(TypeName = "jsonb")]
        public List<UserRole>? UserRoles { get => userRoles?.DistinctBy(i => i.RoleId)?.ToList(); set => userRoles = value?.DistinctBy(i => i.RoleId)?.ToList(); }

        [Column(TypeName = "jsonb")]
        public List<UserRolePermission>? RolePermission { get => rolePermission?.DistinctBy(i => i.Id)?.ToList(); set => rolePermission = value?.DistinctBy(i => i.Id)?.ToList(); }
        public string? TimeZoneInfo { get; set; }
        public bool? ShouldShowTimeZone { get; set; }
        public string? LicenseNo { get; set; }
        public CallThrough? CallThrough { get; set; }
        public WhatsappThrough? WhatsappThrough { get; set; }
        public bool? IsGeoFenceActive { get; set; }
    }

    public class UserView : AuditableEntity, IAggregateRoot
    {
        private List<UserRole>? userRoles;

        public string UserName { get; set; } = default!;
        public string FirstName { get; set; } = default!;
        public string LastName { get; set; } = default!;
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public bool IsActive { get; set; }
        public bool EmailConfirmed { get; set; }
        public bool PhoneNumberConfirmed { get; set; }
        public bool TwoFactorEnabled { get; set; }
        public string? ImageUrl { get; set; }
        public string? AltPhoneNumber { get; set; }
        public string? Address { get; set; }
        public string? AltEmail { get; set; }
        public BloodGroupType? BloodGroup { get; set; }
        public Gender? Gender { get; set; }
        public string? PermanentAddress { get; set; }
        public string? EmpNo { get; set; }
        public string? OfficeName { get; set; }
        public string? OfficeAddress { get; set; }
        public string? Description { get; set; }
        public bool IsAutomationEnabled { get; set; }

        [Column(TypeName = "jsonb")]
        public UserReportsTo? ReportsTo { get; set; }
        [Column(TypeName = "jsonb")]
        public UserReportsTo? GeneralManager { get; set; }

        [Column(TypeName = "jsonb")]
        public UserDepartmant? Department { get; set; }

        [Column(TypeName = "jsonb")]
        public UserDesignation? Designation { get; set; }

        [Column(TypeName = "jsonb")]
        public List<UserRole>? UserRoles { get => userRoles?.DistinctBy(i => i.RoleId)?.ToList(); set => userRoles = value?.DistinctBy(i => i.RoleId)?.ToList(); }
        public string? TimeZoneInfo { get; set; }
        public bool? ShouldShowTimeZone { get; set; }
        public string? LicenseNo { get; set; }
        public bool? IsGeoFenceActive { get; set; }
    }

    public class UserReportsTo
    {
        public Guid Id { get; set; }
        public string FirstName { get; set; } = default!;
        public string LastName { get; set; } = default!;
        public string? ImageUrl { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
    }

    public class UserDepartmant
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = default!;
    }

    public class UserDesignation
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = default!;
    }

    public class UserDoc
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public string DocName { get; set; } = default!;
        public string FilePath { get; set; } = default!;
        public int SizeInBytes { get; set; }
        public DateTime? FileUpdatedOn { get; set; }
        public DocumentType DocumentType { get; set; }
    }

    public class UserRole
    {
        public Guid UserId { get; set; }
        public string RoleId { get; set; } = default!;
        public string Name { get; set; } = default!;
        public string TenantId { get; set; } = default!;
        public bool Enabled { get; set; }
    }

    public class UserRolePermission
    {
        public string Id { get; set; } = default!;
        public string Name { get; set; } = default!;
        public string? Description { get; set; }
        public bool Enabled { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public bool IsDeleted { get; set; }
        public List<string>? Permissions { get; set; }
    }
}
