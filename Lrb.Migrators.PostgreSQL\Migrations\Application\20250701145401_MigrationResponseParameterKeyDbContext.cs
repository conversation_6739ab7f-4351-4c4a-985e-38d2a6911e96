﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class MigrationResponseParameterKeyDbContext : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "ResponseParameterKey",
                schema: "LeadratBlack",
                table: "IVROutboundConfigurations",
                type: "text",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer",
                oldNullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "ResponseParameterKey",
                schema: "LeadratBlack",
                table: "IVROutboundConfigurations",
                type: "integer",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);
        }
    }
}
