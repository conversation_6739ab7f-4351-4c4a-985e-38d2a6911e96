﻿using LrbIntegrationBackgroundJobs.Dtos.PFV2;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace LrbIntegrationBackgroundJobs.Services
{
    internal class PropertyFinderServiceV2 : IPropertyFinderServiceV2
    {
        private readonly WorkingEndPoint _endpoints;
        private IDbRepositoryAsync _dbRepo = default!;
        private ILrbAPIService _lrbService = default!;
        public PropertyFinderServiceV2(IOptions<WorkingEndPoint> endpoints, IDbRepositoryAsync dbRepo, ILrbAPIService lrbService)
        {
            _endpoints = endpoints.Value;
            _dbRepo = dbRepo;
            _lrbService = lrbService;
        }

        #region Get All Leads
        public async Task<List<LrbIntegrationPostDtoV2>> GetAllLeadsFromPFAsync(IntegrationAccountDto pfAccount)
        {
            try
            {
                var cred = pfAccount.GetCreds<PFIntegrationCredDtoV2>();
                List<LrbIntegrationPostDtoV2> lrbLeads = new();
                if (cred != null)
                {
                    var pfLeads = await V2FetchLeadsFromPropertyFinderAsync(cred);
                    var lrbUsers = await _dbRepo.GetAllLrbUserAsync(pfAccount.TenantId);
                    if (pfLeads?.Data?.Any() ?? false)
                    {
                        foreach (var lead in pfLeads.Data)
                        {
                            lrbLeads.Add(lead.MapToLrbIngrDtoV2(lrbUsers));
                        }
                    }
                }
                return lrbLeads;
            }
            catch
            {
                return null;
            }
            
        }
        #endregion

        #region Fetch Leads
        public async Task<PropertyFinderResponseV2> V2FetchLeadsFromPropertyFinderAsync(PFIntegrationCredDtoV2 cred)
        {
            try
            {
                var options = new RestClientOptions(_endpoints.V2PropertyFinderBaseUri)
                {
                    MaxTimeout = -1,
                };
                string fromDate = DateTime.UtcNow.AddMinutes(-5).ToString("yyyy-MM-ddTHH:mm:ssZ");
                string toDate = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");
                var token = await V2GetPFAcessesToken(cred);
                var client = new RestClient(options);
                var request = new RestRequest($"/v1/leads?page=1&perPage=100&channel={cred?.requestType?.ToLower()}&createdAtTo={toDate}&createdAtFrom={fromDate}", Method.Get);
                request.AddHeader("Authorization", $"Bearer {token}");
                RestResponse response = await client.ExecuteAsync(request);
                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<PropertyFinderResponseV2>(response?.Content ?? string.Empty);
                    return data ?? new();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }
        #endregion

        #region Auth Api
        public async Task<string> V2GetPFAcessesToken(PFIntegrationCredDtoV2 cred)
        {
            try
            {
                var options = new RestClientOptions(_endpoints.V2PropertyFinderBaseUri ?? string.Empty)
                {
                    MaxTimeout = -1,
                };
                var client = new RestClient(options);
                var request = new RestRequest("/v1/auth/token", Method.Post);
                request.AddHeader("Content-Type", "application/json");
                var body = new PFNewAuthDto()
                {
                    apiKey = cred.apiKey,
                    apiSecret = cred.secretKey,
                };
                var jsonBody = JsonConvert.SerializeObject(body);
                request.AddStringBody(jsonBody, DataFormat.Json);
                RestResponse response = await client.PostAsync(request);
                var data = JsonConvert.DeserializeObject<V2PFAuthResponseDto>(response?.Content ?? string.Empty);

                return data?.accessToken ?? string.Empty;
            }
            catch (Exception ex)
            {
                return string.Empty;
            }
        }
        #endregion

        #region Post Leads
        private async Task<int> V2PostPropertyFinderAsLrbLeadsAsync(List<LrbIntegrationPostDtoV2> lrbLeads, IntegrationAccountDto bayutLeadAccount)
        {
            int counter = 0;
            var distinctLeads = lrbLeads?.DistinctBy(x => x.Mobile)?.ToList();
            foreach (var lrbLead in distinctLeads)
            {
                try
                {
                    var res = await _lrbService.V2PostProperyFinderAsync(lrbLead, bayutLeadAccount.GetApiKey());
                    bayutLeadAccount.SyncedCount++;
                    await _dbRepo.UpdateSyncedCountAsync(bayutLeadAccount);
                    counter++;
                }
                catch { }
            }
            return counter;
        }
        #endregion

        public async Task V2ProcessPropertyFinderAccountsAsync()
        {
            Console.WriteLine("V2ProcessPropertyFinderAccountsAsync() Statted.");
            var pfAccounts = await _dbRepo.GetAllPropertFinderIntegrationsAsync();
            var distinctAccount = pfAccounts?.DistinctBy(x => x.Id).ToList();
            foreach (var account in distinctAccount)
            {
                try
                {
                    var cred = account.GetCreds<PFIntegrationCredDtoV2>();
                    var lrbLeads = await GetAllLeadsFromPFAsync(account);
                    var result = await V2PostPropertyFinderAsLrbLeadsAsync(lrbLeads, account);
                }
                catch { }
            }
        }
    }
}
