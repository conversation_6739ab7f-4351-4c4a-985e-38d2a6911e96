﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.UserDetails.Web.Specs
{
    public class GetProjectsAddressByIdsSpec : Specification<Domain.Entities.Project>
    {
        public GetProjectsAddressByIdsSpec(List<Guid> projectIds)
        {
            Query.Where(p => !p.IsDeleted && projectIds.Contains(p.Id) && p.Address.Latitude != null && p.Address.Latitude != string.Empty && p.Address.Longitude != null && p.Address.Longitude != string.Empty)
                 .Include(p => p.Address);
        }
    }
    public class GetPropertiesWithAddressByIdsSpec : Specification<Domain.Entities.Property>
    {
        public GetPropertiesWithAddressByIdsSpec(List<Guid> propertyIds)
        {
            Query.Where(p => !p.IsDeleted && propertyIds.Contains(p.Id) && p.Address.Latitude != null && p.Address.Latitude != string.Empty && p.Address.Longitude != null && p.Address.Longitude != string.Empty)
                 .Include(p => p.Address);
        }
    }
}
