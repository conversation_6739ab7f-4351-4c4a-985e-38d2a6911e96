﻿namespace Lrb.Shared.Authorization
{
    public static class IdTokenClaimsKey
    {
        public const string CognitoUserId = "cognito:username";
        public const string FirstName = "given_name";
        public const string LastName = "family_name";
        public const string TenantId = "custom:tenant_id";
        public const string UserId = "custom:user_id";
        public const string PhoneNumber = "phone_number";
        public const string TokenUse = "token_use";
        public const string UserIdGuid = "sub";
        public const string PassTimeStamp = "custom:pass_stamp";
        public const string Issuer = "iss";
        public const string ShiftTime = "custom:shift_time";
        public const string IsLocked = "custom:is_locked";
    }
    public static class ContextKeys
    {
        public const string CognitoUserId = "CognitoUserId";
        public const string FirstName = "FirstName";
        public const string LastName = "LastName";
        public const string TenantId = "TenantId";
        public const string UserId = "UserId";
        public const string PhoneNumber = "PhoneNumber";
        public const string UserIdGuid = "sub";
    }
}
