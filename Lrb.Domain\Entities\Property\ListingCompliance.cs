﻿using System.Text.Json.Serialization;

namespace Lrb.Domain.Entities
{
    public class ListingCompliance : BaseEntity, IAggregateRoot
    {
        public DateTime? LicenseIssuanceDate { get; set; }
        public string? ListingAdvertisementNumber { get; set; }
        public ComplianceType Type { get; set; }
        public bool? UserConfirmedDataIsCorrect { get; set; }
        public Guid? PropertyId { get; set; }
        [JsonIgnore]
        public Property? Property { get; set; }
    }
}
