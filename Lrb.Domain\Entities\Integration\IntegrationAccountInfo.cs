﻿using Lrb.Domain.Entities.Integration;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace Lrb.Domain.Entities
{
    public class IntegrationAccountInfo : AuditableEntity, IAggregateRoot
    {
        public string? AccountName { get; set; }
        public Guid LicenseId { get; set; }
        public LeadSource LeadSource { get; set; }
        public string? JsonTemplate { get; set; }
        public long LeadCount { get; set; }
        public Guid GmailAccountId { get; set; }
        public string? FileUrl { get; set; }
        [Column(TypeName = "jsonb")]
        public Dictionary<string, string>? Credentials { get; set; }
        public bool IsPrimary { get; set; }                 
        public string? ApiKey { get; set; }
        public Guid? AutomationId { get; set; }
        public bool IsAutomated { get; set; }
        public Guid GoogleadLeadFormId { get; set; }
        public Guid FacebookAccountId { get; set; }
        public string? AgencyName { get; set; }
        public int? SyncedCount { get; set; }
        public UserAssignment? UserAssignment { get; set; }
        public IntegrationAssignment? Assignment { get; set; }
        public Guid GoogleSheetAccountId { get; set; }
        public IList<IVRAssignment>? IVRAssignments { get; set; }
        public Guid? IVRApiConfigurationId { get; set; }
        [JsonIgnore]
        public IVRApiConfiguration? IVRApiConfiguration { get; set; }
        public IVRType? IVRCallType { get; set; }
        public IList<IntegrationFilterInfo>? IntegrationFilterInfos { get; set; }
        public long? TotalLeadCount { get; set; }
        public IVROutboundConfiguration? IVROutboundConfiguration { get; set; }
        public IVRPayloadMapping? IVRPayloadMapping { get; set; }
        public string? IVRServiceProvider { get; set; }
        public Agency? Agency { get; set; }
        public List<WAApiInfo>? WAApiInfo { get; set; }
        public WAPayloadMapping? WAPayloadMapping { get; set; }
        public WebhookPayloadMapping? WebhookPayloadMapping { get; set; }
        public string? CountryCode { get; set; }
        public string? EndPointUrl { get; set; }
        public int? EmailCount { get; set; }
        [Column(TypeName = "jsonb")]
        public List<string>? ToRecipients { get; set; }
        [Column(TypeName = "jsonb")]
        public List<string>? CcRecipients { get; set; }
        [Column(TypeName = "jsonb")]
        public List<string>? BccRecipients { get; set; }
        [Column(TypeName = "jsonb")]
        public Dictionary<string, string>? ThirdPartyPushCredentials { get; set; }
        public Campaign? Campaign { get; set; }
        public string? LoginEmail { get; set; }
    }
}
