﻿
using Lrb.Domain.Entities;

namespace Lrb.Application.Team.Web
{
    public class TeamByNameCustomFilterSpec : EntitiesByPaginationFilterSpec<Domain.Entities.Team>
    {
        public TeamByNameCustomFilterSpec(GetAllTeamRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.Configurations)
                 .OrderBy(i => i.LastModifiedOn);

            if (!string.IsNullOrWhiteSpace(filter.SearchTeamName))
            {
                Query.Where(i => i.Name.ToLower().Contains(filter.SearchTeamName.ToLower()));
            }
        }
    }
    public class TeamCountByNameCustomFilterSpec : Specification<Domain.Entities.Team>
    {
        public TeamCountByNameCustomFilterSpec(GetAllTeamRequest filter)
        {
            Query.Where(i => !i.IsDeleted);

            if (!string.IsNullOrWhiteSpace(filter.SearchTeamName))
            {
                Query.Where(i => i.Name.ToLower().Contains(filter.SearchTeamName.ToLower()));
            }
        }
    }

    public class GetTeamWithStatusSpec : Specification<Lrb.Domain.Entities.Team>
    {
        public GetTeamWithStatusSpec(Guid userId) 
        {
            Query.Where(i => !i.IsDeleted)
                 .Include(i => i.Statuses)
                 .OrderBy(i => i.LastModifiedOn);

            if (userId != Guid.Empty)
            {
                Query.Where(i => i.UserIds.Contains(userId) && i.Statuses.Any());
            }
        }
    }
}
