﻿using Lrb.Application.Identity.Users;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Specs;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Property.Web.Requests.Microsite
{
    public class GetPropertyMicrositeForListingBySerialNoRequest : IRequest<Response<PropertyMicrositeDtoListingManagement>>
    {
        public string? SerialNo { get; set; }
        public GetPropertyMicrositeForListingBySerialNoRequest(string serialNo) => SerialNo = serialNo;
    }

    public class GetPropertyMicrositeForListingBySerialNoRequestHandler : IRequestHandler<GetPropertyMicrositeForListingBySerialNoRequest, Response<PropertyMicrositeDtoListingManagement>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Property> _propertyRepo;
        private readonly IReadRepository<PropertyDimension> _dimensionRepo;
        private readonly IReadRepository<PropertyAttribute> _attributeRepo;
        private readonly IReadRepository<PropertyAmenity> _amenityRepo;
        private readonly IReadRepository<CustomMasterAttribute> _masterAttributeRepo;
        private readonly IReadRepository<CustomMasterAmenity> _masterAmenityRepo;
        private readonly IReadRepository<MasterPropertyType> _masterPropertyTypeRepo;
        private readonly IReadRepository<Address> _addressRepo;
        private readonly IReadRepository<PropertyOwnerDetails> _ownerDetailsRepo;
        private readonly IReadRepository<PropertyTagInfo> _tagsRepo;
        private readonly IReadRepository<PropertyGallery> _galleryRepo;
        private readonly IReadRepository<PropertyMonetaryInfo> _monetaryRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Profile> _orgProfileRepo;
        private readonly IUserService _userService;

        public GetPropertyMicrositeForListingBySerialNoRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            IReadRepository<PropertyDimension> dimensionRepo,
            IReadRepository<PropertyAttribute> attributeRepo,
            IReadRepository<PropertyAmenity> amenityRepo,
            IReadRepository<CustomMasterAttribute> masterAttributeRepo,
            IReadRepository<CustomMasterAmenity> masterAmenityRepo,
            IReadRepository<MasterPropertyType> masterPropertyTypeRepo,
            IReadRepository<Address> addressRepo,
            IReadRepository<PropertyOwnerDetails> ownerDetailsRepo,
            IReadRepository<PropertyTagInfo> tagsRepo,
            IReadRepository<PropertyGallery> galleryRepo,
            IReadRepository<PropertyMonetaryInfo> monetaryRepo,
            IRepositoryWithEvents<Profile> orgProfileRepo,
            IUserService userService)
        {
            _propertyRepo = propertyRepo;
            _dimensionRepo = dimensionRepo;
            _attributeRepo = attributeRepo;
            _amenityRepo = amenityRepo;
            _masterAttributeRepo = masterAttributeRepo;
            _masterAmenityRepo = masterAmenityRepo;
            _masterPropertyTypeRepo = masterPropertyTypeRepo;
            _addressRepo = addressRepo;
            _ownerDetailsRepo = ownerDetailsRepo;
            _tagsRepo = tagsRepo;
            _galleryRepo = galleryRepo;
            _monetaryRepo = monetaryRepo;
            _orgProfileRepo = orgProfileRepo;
            _userService = userService;
        }
        public async Task<Response<PropertyMicrositeDtoListingManagement>> Handle(GetPropertyMicrositeForListingBySerialNoRequest request, CancellationToken cancellationToken)
        {
            var property = (await _propertyRepo.ListAsync(new PropertyBySerialNoSpec(request?.SerialNo ?? string.Empty), cancellationToken))?.FirstOrDefault();
            if (property == null) { throw new NotFoundException("No Property found by this Id."); }
            var orgDetails = (await _orgProfileRepo.ListAsync(cancellationToken)).FirstOrDefault();
            var propertyMicrosite = property.Adapt<PropertyMicrositeDtoListingManagement>();
            UserDetailsDto? user = null;
            if(property.PropertyAssignments?.Any() ?? false)
            {
                try
                {
                    var userId = property.PropertyAssignments?.FirstOrDefault()?.AssignedTo.ToString();
                    user = await _userService.GetUserAsync(userId ?? string.Empty, cancellationToken);
                }
                catch
                {

                }
            }
            propertyMicrosite.RegulatoryInfomation = new()
            {
                Reference = property.SerialNo,
                ListingExpireDate = property.ListingExpireDate,
                BrokerName = user?.FirstName + " " + user?.LastName,
                BrokerLicenseNo = user?.LicenseNo,
                DLDPermitNumber = property.PermitNumber,
                DTCMPermitNumber = property.DTCMPermit,
                ADRECPermitNumber = property?.Compliance?.Type == ComplianceType.ADREC ? property.Compliance.ListingAdvertisementNumber : null
            };
            if (property != null && (property.Amenities?.Any() ?? false))
            {
                var amenitiesIds = property.Amenities.Select(i => i.MasterPropertyAmenityId).ToList();
                var amenities = await _masterAmenityRepo.ListAsync(new AmenitiesByPropertyidsSpec(amenitiesIds), cancellationToken);
                var propertyAmenities = amenities.Adapt<List<MasterPropertyAmenitiesDto>>();
                propertyMicrosite.Amenities = propertyAmenities.ToList();
            }
            if (orgDetails != null)
            {
                var org = orgDetails.Adapt<OrganizationDetails>();
                propertyMicrosite.OrgDetails = org;
            }
            if (property?.Attributes?.Any() ?? false)
            {
                List<Guid> attributeIds = property.Attributes.Select(i => i.MasterPropertyAttributeId).ToList();
                var masterAttributes = await _masterAttributeRepo.ListAsync(new MasterAttributesByIdsSpec(attributeIds), cancellationToken);
                propertyMicrosite = await UpdateMasterAttributes(propertyMicrosite, masterAttributes);
            }
            return new(propertyMicrosite);
        }

        private async static Task<PropertyMicrositeDtoListingManagement> UpdateMasterAttributes(PropertyMicrositeDtoListingManagement propertyDto, List<CustomMasterAttribute> masterAttributes)
        {
            try
            {
                var attributes = propertyDto.Attributes?.ToList();
                attributes?.ForEach(i =>
                {
                    var matchingAttribute = masterAttributes.FirstOrDefault(j => j.Id == i.MasterPropertyAttributeId);

                    i.AttributeDisplayName = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.AttributeDisplayName;
                    i.AttributeName = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.AttributeName;
                    i.AttributeType = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.AttributeType;
                    i.ActiveImageURL = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.ActiveImageURL;
                    i.IsActive = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.IsActive;


                });
                propertyDto.Attributes = attributes;
                return propertyDto;
            }
            catch
            {
                return propertyDto;
            }
        }
    }
}
