﻿
using Lrb.Application.UserDetails.Mobile;

namespace Lrb.Application.Todo.Mobile
{
    public class ViewTodoDto : BaseViewTodoDto
    {
        public Dictionary<DateTime, List<TodoHistoryDto>>? TodoHistories { get; set; } = new();

    }
    public class BaseViewTodoDto : IDto
    {
        public Guid Id { get; set; }
        public string Title { get; set; }
        public string Notes { get; set; }
        public Priority Priority { get; set; }
        public bool IsMarkedDone { get; set; }
        public bool IsRepeated { get; set; }
        public DateTime? ScheduledDateTime { get; set; }
        public ViewUserDto? LastModifiedUser { get; set; }
        public ViewUserDto? AssignedFrom { get; set; }
        public List<ViewUserDto>? AssignedUsers { get; set; } = new();
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public TodoFilterType Status { get; set; }
        public Dictionary<DateTime, List<TodoViewModel>>? Tasks { get; set; }
    }
    public class CreateTodoDto : BaseTodoDto
    {

    }

    public class UpdateTodoDto : BaseTodoDto
    {

    }
    public class BaseTodoDto : IDto
    {
        public Guid Id { get; set; }
        public string Title { get; set; }
        public string Notes { get; set; }
        public Priority Priority { get; set; }
        public bool IsRepeated { get; set; }
        public bool IsMarkedDone { get; set; }
        public DateTime? ScheduledDateTime { get; set; }
        public Guid? AssignedFrom { get; set; }
        public List<Guid>? AssignedUserIds { get; set; } = new();

    }
    public class CreateMultipleTodosDto : IDto
    {
        public string Title { get; set; }
        public string Notes { get; set; }
        public Priority Priority { get; set; }
        public bool IsRepeated { get; set; }
        public bool IsMarkedDone { get; set; }
        public DateTime? ScheduledDateTime { get; set; }
        public Guid? AssignedFrom { get; set; }
        public List<Guid>? AssignedUserIds { get; set; } = new();
    }
    public class TodoViewModel : IDto
    {
        public Guid Id { get; set; }
        public string Title { get; set; }
        public string Notes { get; set; }
        public Priority Priority { get; set; }
        public bool IsMarkedDone { get; set; }
        public bool IsRepeated { get; set; }
        public DateTime? ScheduledDateTime { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
    }
}
