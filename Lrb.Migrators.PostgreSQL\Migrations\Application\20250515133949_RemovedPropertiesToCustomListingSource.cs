﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class RemovedPropertiesToCustomListingSource : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LRListingId",
                schema: "LeadratBlack",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "IsEnabled",
                schema: "LeadratBlack",
                table: "CustomListingSources");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "LRListingId",
                schema: "LeadratBlack",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsEnabled",
                schema: "LeadratBlack",
                table: "CustomListingSources",
                type: "boolean",
                nullable: true);
        }
    }
}
