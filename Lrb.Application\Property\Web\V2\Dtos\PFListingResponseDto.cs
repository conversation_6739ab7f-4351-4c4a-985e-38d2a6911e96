﻿namespace Lrb.Application.Property.Web.V2.Dtos
{
    public class PFListingResponseDto
    {

    }

    public class PFAuthResponseDto
    {
        public string? accessToken { get; set; }
    }

    #region PF create property response
    public class PropertyResponse
    {
        public List<string>? Amenities { get; set; }
        public UserReference? AssignedTo { get; set; }
        public string? Bathrooms { get; set; }
        public string? Bedrooms { get; set; }
        public string? Category { get; set; }
        public DateTime? CreatedAt { get; set; }
        public UserReference? CreatedBy { get; set; }
        public DescriptionResponse? Description { get; set; }
        public string? FinishingType { get; set; }
        public string? Id { get; set; }
        public LocationResponse? Location { get; set; }
        public MediaResponse? Media { get; set; }
        public int? PfCategoryId { get; set; }
        public int? PfTypeId { get; set; }
        public PortalsResponse? Portals { get; set; }
        public PriceResponse? Price { get; set; }
        public string? ProjectStatus { get; set; }
        public QualityScoreResponse? QualityScore { get; set; }
        public string? Reference { get; set; }
        public int? Size { get; set; }
        public StateResponse? State { get; set; }
        public TitleResponse? Title { get; set; }
        public string? Type { get; set; }
        public string? UaeEmirate { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class UserReference
    {
        public int? Id { get; set; }
    }

    public class DescriptionResponse
    {
        public string? En { get; set; }
    }

    public class LocationResponse
    {
        public int? Id { get; set; }
    }

    public class MediaResponse
    {
        public List<ImageResponse>? Images { get; set; }
    }

    public class ImageResponse
    {
        public ImageOriginalResponse? Original { get; set; }
    }

    public class ImageOriginalResponse
    {
        public int? Height { get; set; }
        public string? Url { get; set; }
        public int? Width { get; set; }
    }

    public class PortalsResponse
    {
        public PropertyFinderResponse? PropertyFinder { get; set; }
    }

    public class PropertyFinderResponse
    {
        public bool? IsLive { get; set; }
        public string? Name { get; set; }
    }

    public class PriceResponse
    {
        public PriceAmounts? Amounts { get; set; }
        public string? Type { get; set; }
    }

    public class PriceAmounts
    {
        public int? Yearly { get; set; }
    }

    public class QualityScoreResponse
    {
        public string? Color { get; set; }
        public QualityScoreDetails? Details { get; set; }
        public int? Value { get; set; }
    }

    public class QualityScoreDetails
    {
        public ScoreDetail? Description { get; set; }
        public ScoreDetail? Image { get; set; }
        public ScoreDetail? ImageDiversity { get; set; }
        public ScoreDetail? ImageDuplicates { get; set; }
        public ScoreDetail? ImagesDimensions { get; set; }
        public ScoreDetail? Location { get; set; }
        public ScoreDetail? Title { get; set; }
        public ScoreDetail? Verified { get; set; }
    }

    public class ScoreDetail
    {
        public string? Color { get; set; }
        public string? Group { get; set; }
        public string? Tag { get; set; }
        public string? TagAr { get; set; }
        public int? Value { get; set; }
        public int? Weight { get; set; }
        public string? Help { get; set; }
        public string? HelpAr { get; set; }
    }

    public class StateResponse
    {
        public string? Stage { get; set; }
        public string? Type { get; set; }
    }

    public class TitleResponse
    {
        public string? En { get; set; }
    }

    #endregion

    #region PF Webhook Response
    public class PFWebhookResponseDto
    {
        public string? createdAt { get; set; }
        public string? eventId { get; set; }
        public string? url { get; set; }
    }
    #endregion

    #region Get All Listing Response
    public class RootGetAll
    {
        public List<ResultGetAll>? results { get; set; }
        public PaginationGetAll? pagination { get; set; }
        public ActiveCtsGetAll? activeCts { get; set; }
    }

    public class ResultGetAll
    {
        public int? age { get; set; }
        public List<string>? amenities { get; set; }
        public AssignedToGetAll? assignedTo { get; set; }
        public string? availableFrom { get; set; }
        public string? bathrooms { get; set; }
        public string? bedrooms { get; set; }
        public string? category { get; set; }
        public ComplianceGetAll? compliance { get; set; }
        public string? createdAt { get; set; }
        public CreatedByGetAll? createdBy { get; set; }
        public DescriptionGetAll? description { get; set; }
        public string? developer { get; set; }
        public string? finishingType { get; set; }
        public string? floorNumber { get; set; }
        public string? furnishingType { get; set; }
        public bool? hasGarden { get; set; }
        public bool? hasKitchen { get; set; }
        public bool? hasParkingOnSite { get; set; }
        public string? id { get; set; }
        public Location2GetAll? location { get; set; }
        public MediaGetAll? media { get; set; }
        public int? pfCategoryId { get; set; }
        public int? pfTypeId { get; set; }
        public string? plotNumber { get; set; }
        public int? plotSize { get; set; }
        public PortalsGetAll? portals { get; set; }
        public PriceGetAll? price { get; set; }
        public ProductsGetAll? products { get; set; }
        public string? projectStatus { get; set; }
        public QualityScoreGetAll? qualityScore { get; set; }
        public string? reference { get; set; }
        public int? size { get; set; }
        public StateGetAll? state { get; set; }
        public StreetGetAll? street { get; set; }
        public TitleGetAll? title { get; set; }
        public string? type { get; set; }
        public string? uaeEmirate { get; set; }
        public string? unitNumber { get; set; }
        public string? updatedAt { get; set; }
        public UpdatedByGetAll? updatedBy { get; set; }
        public string? verificationStatus { get; set; }
    }

    public class AssignedToGetAll
    {
        public int? id { get; set; }
        public string? name { get; set; }
        public PhotosGetAll? photos { get; set; }
    }

    public class PhotosGetAll
    {
        public string? thumbnail { get; set; }
    }

    public class ComplianceGetAll
    {
        public string? advertisementLicenseIssuanceDate { get; set; }
        public string? listingAdvertisementNumber { get; set; }
        public RegaResponseGetAll? regaResponse { get; set; }
        public string? type { get; set; }
        public bool? userConfirmedDataIsCorrect { get; set; }
    }

    public class RegaResponseGetAll
    {
        public int? age { get; set; }
        public List<string>? amenities { get; set; }
        public string? bathrooms { get; set; }
        public string? bedrooms { get; set; }
        public string? category { get; set; }
        public Compliance2GetAll? compliance { get; set; }
        public string? landNumber { get; set; }
        public LocationGetAll? location { get; set; }
        public string? mojDeedLocationDescription { get; set; }
        public string? plotNumber { get; set; }
        public Price2GetAll? price { get; set; }
        public int? size { get; set; }
        public StreetGetAll? street { get; set; }
        public string? type { get; set; }
    }

    public class Compliance2GetAll
    {
        public string? advertisementLicenseIssuanceDate { get; set; }
    }

    public class LocationGetAll
    {
        public FullNameGetAll? fullName { get; set; }
        public int? id { get; set; }
        public string? lat { get; set; }
        public string? lon { get; set; }
        public string? path { get; set; }
    }

    public class FullNameGetAll
    {
        public string? ar { get; set; }
        public string? en { get; set; }
    }

    public class Price2GetAll
    {
        public AmountsGetAll? amounts { get; set; }
        public ObligationGetAll? obligation { get; set; }
        public string? type { get; set; }
        public ValueAffectedGetAll? valueAffected { get; set; }
    }

    public class AmountsGetAll
    {
        public int? daily { get; set; }
        public int? monthly { get; set; }
        public int? sale { get; set; }
        public int? weekly { get; set; }
        public int? yearly { get; set; }
    }

    public class ObligationGetAll
    {
        public string? comment { get; set; }
        public bool? enabled { get; set; }
    }

    public class ValueAffectedGetAll
    {
        public string? comment { get; set; }
        public bool? enabled { get; set; }
    }

    public class StreetGetAll
    {
        public string? direction { get; set; }
        public int? width { get; set; }
    }

    public class CreatedByGetAll
    {
        public int? id { get; set; }
        public string? name { get; set; }
        public PhotosGetAll? photos { get; set; }
    }

    public class Location2GetAll
    {
        public int? id { get; set; }
    }

    public class MediaGetAll
    {
        public List<ImageGetAll>? images { get; set; }
        public VideosGetAll? videos { get; set; }
    }

    public class ImageGetAll
    {
        public ImageSizeGetAll? large { get; set; }
        public ImageSizeGetAll? medium { get; set; }
        public ImageSizeGetAll? original { get; set; }
        public ImageSizeGetAll? thumbnail { get; set; }
        public ImageSizeGetAll? watermarked { get; set; }
    }

    public class ImageSizeGetAll
    {
        public int? height { get; set; }
        public string? url { get; set; }
        public int? width { get; set; }
    }

    public class VideosGetAll
    {
        public string? @default { get; set; }
        public string? view360 { get; set; }
    }

    public class PortalsGetAll
    {
        public PropertyfinderGetAll? propertyfinder { get; set; }
    }

    public class PropertyfinderGetAll
    {
        public bool? isLive { get; set; }
        public string? name { get; set; }
        public string? publishedAt { get; set; }
    }

    public class PriceGetAll
    {
        public AmountsGetAll? amounts { get; set; }
        public int? downpayment { get; set; }
        public int? minimalRentalPeriod { get; set; }
        public MortgageGetAll? mortgage { get; set; }
        public int? numberOfCheques { get; set; }
        public int? numberOfMortgageYears { get; set; }
        public ObligationGetAll? obligation { get; set; }
        public bool? onRequest { get; set; }
        public List<string>? paymentMethods { get; set; }
        public string? type { get; set; }
        public bool? utilitiesInclusive { get; set; }
        public ValueAffectedGetAll? valueAffected { get; set; }
    }

    public class MortgageGetAll
    {
        public string? comment { get; set; }
        public bool? enabled { get; set; }
    }

    public class ProductsGetAll
    {
        public ProductGetAll? featured { get; set; }
        public ProductGetAll? premium { get; set; }
        public ProductGetAll? standard { get; set; }
    }

    public class ProductGetAll
    {
        public string? createdAt { get; set; }
        public string? expiresAt { get; set; }
        public string? id { get; set; }
        public string? purchasedProductId { get; set; }
        public bool? renewalEnabled { get; set; }
    }

    public class QualityScoreGetAll
    {
        public string? color { get; set; }
        public QualityScoreDetailsGetAll? details { get; set; }
        public double? value { get; set; }
    }

    public class QualityScoreDetailsGetAll
    {
        public QualityScoreItemGetAll? description { get; set; }
        public QualityScoreItemGetAll? hasAgentUser { get; set; }
        public QualityScoreItemGetAll? image { get; set; }
        public QualityScoreItemGetAll? imageDiversity { get; set; }
        public QualityScoreItemGetAll? imageDuplicates { get; set; }
        public QualityScoreItemGetAll? imagesDimensions { get; set; }
        public QualityScoreItemGetAll? listingCompletion { get; set; }
        public QualityScoreItemGetAll? location { get; set; }
        public QualityScoreItemGetAll? priceRealism { get; set; }
        public QualityScoreItemGetAll? title { get; set; }
        public QualityScoreItemGetAll? titleDeed { get; set; }
        public QualityScoreItemGetAll? verified { get; set; }
    }

    public class QualityScoreItemGetAll
    {
        public string? color { get; set; }
        public string? group { get; set; }
        public string? help { get; set; }
        public string? helpAr { get; set; }
        public string? tag { get; set; }
        public string? tagAr { get; set; }
        public int? value { get; set; }
        public int? weight { get; set; }
    }

    public class StateGetAll
    {
        public List<ReasonGetAll>? reasons { get; set; }
        public string? stage { get; set; }
        public string? type { get; set; }
    }

    public class ReasonGetAll
    {
        public string? ar { get; set; }
        public string? en { get; set; }
    }

    public class TitleGetAll
    {
        public string? ar { get; set; }
        public string? en { get; set; }
    }

    public class UpdatedByGetAll
    {
        public int? id { get; set; }
        public string? name { get; set; }
        public PhotosGetAll? photos { get; set; }
    }

    public class PaginationGetAll
    {
        public int? total { get; set; }
        public int? page { get; set; }
        public int? perPage { get; set; }
        public int? totalPages { get; set; }
        public int? nextPage { get; set; }
        public int? prevPage { get; set; }
    }

    public class ActiveCtsGetAll
    {
        public List<Location3GetAll>? locations { get; set; }
        public List<string>? categories { get; set; }
        public List<string>? offeringTypes { get; set; }
    }

    public class Location3GetAll
    {
        public int? id { get; set; }
        public string? name { get; set; }
    }

    public class DescriptionGetAll
    {
        public string? ar { get; set; }
        public string? en { get; set; }
    }

    #endregion
}
