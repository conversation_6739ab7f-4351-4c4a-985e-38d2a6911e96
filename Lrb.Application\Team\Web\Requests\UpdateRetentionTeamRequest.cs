﻿using Lrb.Application.Team.Web.Dtos;
using Lrb.Application.CustomStatus.Web;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Team.Web.Requests
{
    public class UpdateRetentionTeamRequest : UpdateRetentionDto, IRequest<Response<bool>>
    {
    }

    public class UpdateRetentionTeamRequestHandler : IRequestHandler<UpdateRetentionTeamRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Team> _teamRepo;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _custLeadStatusRepo;
        private readonly IRepositoryWithEvents<TeamConfiguration> _teamConfigRepo;
        public UpdateRetentionTeamRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Team> teamRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> custLeadStatusRepo,
            IRepositoryWithEvents<TeamConfiguration> teamConfigRepo
            )
        {
            _teamRepo = teamRepo;
            _custLeadStatusRepo = custLeadStatusRepo;
            _teamConfigRepo = teamConfigRepo;
        }

        public async Task<Response<bool>> Handle(UpdateRetentionTeamRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var team = await _teamRepo.FirstOrDefaultAsync(new TeamByIdSpec(request.Id ?? Guid.Empty));
                if (team == null)
                {
                    throw new InvalidOperationException("Team not found by this Id");
                }
                List<CustomMasterLeadStatus> customMasterLeadStatuses = new();
                if (request.StatusesIds?.Any() ?? false)
                {
                    var statuses = await _custLeadStatusRepo.ListAsync(new GetAllStatusByIdSpec(request.StatusesIds ?? new List<Guid>()), cancellationToken);
                    if (statuses?.Any() ?? false)
                    {
                        customMasterLeadStatuses.AddRange(statuses);
                    }
                }
                if (request.SubStatusesIds?.Any() ?? false)
                {
                    var subStatuses = await _custLeadStatusRepo.ListAsync(new GetAllStatusByIdSpec(request.SubStatusesIds ?? new List<Guid>()), cancellationToken);
                    if (subStatuses?.Any() ?? false)
                    {
                        customMasterLeadStatuses.AddRange(subStatuses);
                    }
                }
                team.IsRotationEnabled = request.IsRotationEnabled ?? team.IsRotationEnabled;
                team.Statuses = customMasterLeadStatuses;
                await _teamRepo.UpdateAsync(team);
                if (team.Configurations!= null && request.Configuration != null )
                {
                    var teamConfig = team.Configurations.FirstOrDefault(i => i.IsForRetention == true);
                    teamConfig = request.Configuration.Adapt(teamConfig);
                    teamConfig.IsForRetention = true;
                    await _teamConfigRepo.UpdateAsync(teamConfig);
                }
                else if(request.Configuration != null) 
                {
                    var newTeamConfig = request.Configuration.Adapt<TeamConfiguration>();
                        newTeamConfig.TeamId = team.Id;
                        newTeamConfig.IsForRetention = true;
                    await _teamConfigRepo.AddAsync(newTeamConfig);
                }
                return new(true);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
