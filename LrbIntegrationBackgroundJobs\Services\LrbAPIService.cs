﻿using LrbIntegrationBackgroundJobs.Dtos;
using LrbIntegrationBackgroundJobs.Dtos.Bayut;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace LrbIntegrationBackgroundJobs
{
    public class LrbAPIService : ILrbAPIService
    {
        private readonly WorkingEndPoint _endPoints;

        public LrbAPIService(IOptions<WorkingEndPoint> endPointOptions)
        {
            _endPoints = endPointOptions.Value;
        }
        public async Task<Response<bool>> PostJustLeadAsync(LrbIntegrationPostDto dto, string apiKey)
        {
            try
            {
                HttpClient client = new()
                {
                    BaseAddress = new Uri(_endPoints.LrbBaseUri)
                };
                var request = new HttpRequestMessage(HttpMethod.Post, "/api/v1/integration/justlead");
                var json = JsonSerializer.Serialize(dto);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                request.Headers.Add("API-Key", apiKey);
                var res = await client.SendAsync(request);
                var bytes = await res.Content.ReadAsByteArrayAsync(CancellationToken.None);
                var jsonString = Encoding.UTF8.GetString(bytes);
                var body = JsonSerializer.Deserialize<Response<bool>>(jsonString);  
                return body ?? new(false);
            }
            catch
            {
                throw;
            }
        }

        public async Task<Response<bool>> PostCommonFloorAsync(LrbIntegrationPostDto dto, string apiKey)
        {
            try
            {
                HttpClient client = new()
                {
                    BaseAddress = new Uri(_endPoints.LrbBaseUri)
                };
                var request = new HttpRequestMessage(HttpMethod.Post, "/api/v1/integration/commonfloor");
                var json = JsonSerializer.Serialize(dto);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                request.Headers.Add("API-Key", apiKey);
                var res = await client.SendAsync(request);
                var bytes = await res.Content.ReadAsByteArrayAsync(CancellationToken.None);
                var jsonString = Encoding.UTF8.GetString(bytes);
                var body = JsonSerializer.Deserialize<Response<bool>>(jsonString);
                return body ?? new(false);
            }
            catch
            {
                throw;
            }
        }

        public async Task<Response<bool>> PostBayutAsync(BayutWhatsappDto dto, string apiKey)
        {
            try
            {
                HttpClient client = new()
                {
                    BaseAddress = new Uri(_endPoints.LrbBaseUri)
                };
                var request = new HttpRequestMessage(HttpMethod.Post, "/api/v1/integration/bayut/wa");
                var json = JsonSerializer.Serialize(dto);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                request.Headers.Add("API-Key", apiKey);
                Task.Run(() => client.SendAsync(request));
                return new(true);
            }
            catch 
            {
                return new(false);
            }
        }

        public async Task<Response<bool>> PostProperyFinderAsync(LrbIntegrationPostDto dto, string apiKey)
        {
            try
            {
                HttpClient client = new()
                {
                    BaseAddress = new Uri(_endPoints.LrbBaseUri)
                };
                var request = new HttpRequestMessage(HttpMethod.Post, "/api/v1/integration/propertyfinder");
                var json = JsonSerializer.Serialize(dto);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                request.Headers.Add("API-Key", apiKey);
                var res = await client.SendAsync(request);
                var bytes = await res.Content.ReadAsByteArrayAsync(CancellationToken.None);
                var jsonString = Encoding.UTF8.GetString(bytes);
                var body = JsonSerializer.Deserialize<Response<bool>>(jsonString);
                return body ?? new(false);
            }
            catch
            {
                throw;
            }
        }

        public async Task<Response<bool>> PostBayutCallAsync(LrbBayutCallLogIntegrationPostDto dto, string apiKey)
        {
            try
            {
                HttpClient client = new()
                {
                    BaseAddress = new Uri(_endPoints.LrbBaseUri)
                };
                var request = new HttpRequestMessage(HttpMethod.Post, "/api/v1/integration/bayut/ivr");
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(dto);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                request.Headers.Add("API-Key", apiKey);
                Task.Run(() => client.SendAsync(request));
                return new(true);
            }
            catch
            {
                return new(false);
            }
        }

        public async Task<Response<bool>> PostBayutemailAsync(LrbIntegrationPostDto dto, string apiKey, ILogger logger)
        {
            int retryCount = 0;
            try
            {
                HttpClient client = new()
                {
                    BaseAddress = new Uri(_endPoints.LrbBaseUri)
                };
                var request = new HttpRequestMessage(HttpMethod.Post, "/api/v1/integration/bayut/email-lead");
                var json = JsonSerializer.Serialize(dto);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                request.Headers.Add("API-Key", apiKey);
                logger.LogInformation($"Lrb Leads: {JsonSerializer.Serialize(dto)}");
                Task.Run(() => client.SendAsync(request));
                retryCount++;
                logger.LogInformation($"Retry Count: {retryCount}");
                return new(true);
            }
            catch
            {
                return new(false);
            }
        }

        public async Task<Response<bool>> PostPropertyFinderCallAsync(LrbIntegrationIVRDto dto, string apiKey)
        {
            try
            {
                HttpClient client = new()
                {
                    BaseAddress = new Uri(_endPoints.LrbBaseUri)
                };
                var request = new HttpRequestMessage(HttpMethod.Post, "/api/v1/integration/bayut/ivr");
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(dto);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                request.Headers.Add("API-Key", apiKey);
                var res = await client.SendAsync(request);
                var bytes = await res.Content.ReadAsByteArrayAsync(CancellationToken.None);
                var jsonString = Encoding.UTF8.GetString(bytes);
                var body = Newtonsoft.Json.JsonConvert.DeserializeObject<Response<bool>>(jsonString);
                return body ?? new(false);
            }
            catch
            {
                throw;
            }
        }

        public async Task<Response<bool>> PostDubbizleAsync(BayutWhatsappDto dto, string apiKey)
        {
            try
            {
                HttpClient client = new()
                {
                    BaseAddress = new Uri(_endPoints.LrbBaseUri)
                };
                var request = new HttpRequestMessage(HttpMethod.Post, "/api/v1/integration/dubizzle/WA");
                var json = JsonSerializer.Serialize(dto);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                request.Headers.Add("API-Key", apiKey);
                Task.Run(() => client.SendAsync(request));
                return new(true);
            }
            catch
            {
                return new(false);
            }
        }

        public async Task<Response<bool>> PostDubizzleCallAsync(LrbBayutCallLogIntegrationPostDto dto, string apiKey)
        {
            try
            {
                HttpClient client = new()
                {
                    BaseAddress = new Uri(_endPoints.LrbBaseUri)
                };
                var request = new HttpRequestMessage(HttpMethod.Post, "/api/v1/integration/dubizzle/ivr");
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(dto);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                request.Headers.Add("API-Key", apiKey);
                Task.Run(() => client.SendAsync(request));
                return new(true);
            }
            catch
            {
                return new(false);
            }
        }

        public async Task<Response<bool>> PostDubizzleEmailAsync(LrbIntegrationPostDto dto, string apiKey)
        {
            try
            {
                HttpClient client = new()
                {
                    BaseAddress = new Uri(_endPoints.LrbBaseUri)
                };
                var request = new HttpRequestMessage(HttpMethod.Post, "/api/v1/integration/dubizzle/email");
                var json = JsonSerializer.Serialize(dto);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                request.Headers.Add("API-Key", apiKey);
                Task.Run(() => client.SendAsync(request));
                return new(true);
            }
            catch
            {
                return new(false);
            }
        }

        public async Task<Response<bool>> V2PostProperyFinderAsync(LrbIntegrationPostDtoV2 dto, string apiKey)
        {
            try
            {
                HttpClient client = new()
                {
                    BaseAddress = new Uri(_endPoints.LrbBaseUri)
                };
                var request = new HttpRequestMessage(HttpMethod.Post, "/api/v1/integration/propertyfinder");
                var json = JsonSerializer.Serialize(dto);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                request.Headers.Add("API-Key", apiKey);
                Task.Run(() => client.SendAsync(request));
                return new(true);
            }
            catch
            {
                return new(false);
            }
        }
    }
}
