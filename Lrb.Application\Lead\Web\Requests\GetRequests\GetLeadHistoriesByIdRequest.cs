﻿using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.LeadCallLog.Web;
using Lrb.Application.Project.Web;
using Lrb.Application.Property.Web;
using Lrb.Application.WhatsAppCloudApi.Web;
using Lrb.Domain.Entities;


namespace Lrb.Application.Lead.Web.Requests.GetRequests
{

    public class GetLeadHistoriesByIdRequest : IRequest<Response<List<LeadHistoryDto>>>
    {
        public Guid LeadId { get; set; }
        public bool? CanAccessAllLeads { get; set; }
        public GetLeadHistoriesByIdRequest(Guid id) => LeadId = id;
    }
    public class GetLeadHistoriesByIdRequestHandler : IRequestHandler<GetLeadHistoriesByIdRequest, Response<List<LeadHistoryDto>>>
    {
        private readonly IReadRepository<Domain.Entities.Lead> _leadRepo;
        private readonly IReadRepository<LeadBookedDetail> _bookedInfoRepo;
        private readonly IReadRepository<LeadHistory> _leadHistoryRepo;
        private readonly IReadRepository<Address> _addressRepo;
        private readonly IReadRepository<LeadAppointment> _leadAppointmentRepo;
        private readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IReadRepository<LeadCommunication> _leadCommunicationRepo;
        private readonly IRepositoryWithEvents<ServetelCallLog> _servetelCallLogs;
        private readonly IReadRepository<Domain.Entities.Lead> _leadRepository;
        private readonly IReadRepository<Domain.Entities.WhatsAppCommunication> _whatsAppCommunicationRepo;
        private readonly IReadRepository<Domain.Entities.IVRCommonCallLog> _ivrCommonCallLogRepo;


        public GetLeadHistoriesByIdRequestHandler(IReadRepository<LeadHistory> leadHistoryRepo,
            IReadRepository<LeadBookedDetail> bookedInfoRepo,
            IReadRepository<Address> addressRepo,
            IReadRepository<LeadAppointment> leadAppointmentRepo,
            IUserService userService, IDapperRepository dapperRepository,
            ICurrentUser currentUser, IReadRepository<Domain.Entities.Lead> leadRepo,
            IReadRepository<LeadCommunication> leadCommunicationRepo,
            IRepositoryWithEvents<ServetelCallLog> servetelCallLogs, IReadRepository<Domain.Entities.Lead> leadRepository,
            IReadRepository<WhatsAppCommunication> whatsAppCommunicationRepo,
            IReadRepository<IVRCommonCallLog> ivrCommonCallLogRepo)
        {
            _leadHistoryRepo = leadHistoryRepo;
            _bookedInfoRepo = bookedInfoRepo;
            _addressRepo = addressRepo;
            _leadAppointmentRepo = leadAppointmentRepo;
            _userService = userService;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _leadRepo = leadRepo;
            _leadCommunicationRepo = leadCommunicationRepo;
            _servetelCallLogs = servetelCallLogs;
            _leadRepository = leadRepository;
            _whatsAppCommunicationRepo = whatsAppCommunicationRepo;
            _ivrCommonCallLogRepo = ivrCommonCallLogRepo;
        }
        public async Task<Response<List<LeadHistoryDto>>> Handle(GetLeadHistoriesByIdRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var lead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.LeadId, true));
                var currentUserId = _currentUser.GetUserId();
                var tenantId = _currentUser.GetTenant();
                List<Guid> subIds = new();
                if (lead != null)
            {
                    List<LeadHistory> leadHistories = null;
                    var isAdmin = await _dapperRepository.IsAdminAsync((_currentUser?.GetUserId() ?? Guid.Empty), _currentUser?.GetTenant() ?? string.Empty);
                    if (isAdmin)
                    {
                        leadHistories = await _leadHistoryRepo.ListAsync(new LeadHistorySpec(request.LeadId));
                        var latestHistory = leadHistories;
                    }
                    else
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsAsync(currentUserId, tenantId ?? string.Empty, request?.CanAccessAllLeads, isAdmin))?.ToList() ?? new();
                        subIds.AddRange(new List<Guid>() { currentUserId, lead.AssignTo });
                        if (lead?.SecondaryUserId != null && lead.SecondaryUserId != Guid.Empty)
                        {
                            subIds.Add(lead?.SecondaryUserId ?? Guid.Empty);
                        }
                        subIds = subIds?.Where(i => i != Guid.Empty)?.Distinct()?.ToList() ?? new List<Guid>(); ;
                        leadHistories = await _leadHistoryRepo.ListAsync(new LeadHistorySpec(request.LeadId, subIds));
                        var latestHistory = leadHistories
                         .Select(h => new
                         {
                             LeadHistory = h,
                             LatestModifiedDate = h.ModifiedDate?.Max(kvp => kvp.Value)
                         })
                         .OrderByDescending(x => x.LatestModifiedDate)
                         .FirstOrDefault()?.LeadHistory;
                        var assignment = latestHistory?.LeadAssignmentType?.OrderByDescending(i => i.Value).FirstOrDefault().Value;
                        if ((latestHistory != null) && (assignment != null) && assignment != LeadAssignmentType.WithHistory)
                        {

                            leadHistories = new();
                            leadHistories.Add(latestHistory);
                        }
                    }
                    List<LeadAppointment> leadAppointments = await _leadAppointmentRepo.ListAsync(new LeadAppointmentByLeadIdSpec(lead?.Id ?? Guid.Empty, subIds), cancellationToken);
                    var leadcomunications = await _leadCommunicationRepo.ListAsync(new GetCommunicationDetailsByLeadId(request.LeadId, subIds), cancellationToken);
                    List<WhatsAppCommunication>? whatsAppCommunications = await _whatsAppCommunicationRepo.ListAsync(new GetWhatsAppCommunicationSpec(lead.Id), cancellationToken);
                    var leadWithCallLogs = (await _leadRepository.ListAsync(new LeadCallLogByLeadIdSpec(lead.Id), cancellationToken)).FirstOrDefault();
                    List<LeadCallLogDto>? callLogDtos = leadWithCallLogs?.LeadCallLogs?.ToList().Adapt<List<LeadCallLogDto>>();
                    List<ServetelCallLog> servetelCallLogs = new();
                    List<IVRCommonCallLog> ivrCommonCallLogs = new();
                    if (isAdmin)
                    {
                        servetelCallLogs = await _servetelCallLogs.ListAsync(new ServetelCallLogByLeadAndUserSpec(lead.Id));
                        ivrCommonCallLogs = await _ivrCommonCallLogRepo.ListAsync(new IVRCommonCallLogByLeadIdSpec(lead.Id));
                    }
                    else
                    {
                        servetelCallLogs = await _servetelCallLogs.ListAsync(new ServetelCallLogByLeadAndUserSpec(lead.Id, lead.AssignTo));
                        ivrCommonCallLogs = await _ivrCommonCallLogRepo.ListAsync(new IVRCommonCallLogByLeadIdSpec(lead.Id, lead.AssignTo));
                        callLogDtos = callLogDtos?.Where(i => i.UserId == lead.AssignTo).ToList();
                    }
                    servetelCallLogs = await FormatTimeStampsAsync(servetelCallLogs);

                    List<LeadCallLogDto> leadCallLogs = servetelCallLogs.Adapt<List<LeadCallLogDto>>();
                    leadCallLogs.AddRange(ivrCommonCallLogs.Adapt<List<LeadCallLogDto>>());
                    if (callLogDtos?.Any() ?? false)
                    {
                        callLogDtos.AddRange(leadCallLogs);
                    }
                    else
                    {
                        callLogDtos = leadCallLogs;
                    }
                    //    var users = await _userService.GetListAsync(cancellationToken);
                    List<Guid> userIds = leadAppointments.Select(i => i.LastModifiedBy).ToList();
                    userIds.AddRange(leadcomunications.Select(i => i.LastModifiedBy).ToList());
                    userIds.AddRange(whatsAppCommunications.Select(i => i.LastModifiedBy).ToList());
                    userIds.AddRange(lead.BookedDetails.Select(i => i.LastModifiedBy).ToList());
                    userIds.AddRange(lead.BookedDetails.Where(i => i.BookedBy.HasValue).Select(i => i.BookedBy.Value).ToList());
                    userIds.AddRange(lead.BookedDetails.Where(i => i.UserId.HasValue).Select(i => i.UserId.Value).ToList());
                    userIds.AddRange(lead.BookedDetails.Where(i => i.SecondaryOwner.HasValue).Select(i => i.SecondaryOwner.Value).ToList());
                    userIds.AddRange(lead.BookedDetails.Where(i => i.TeamHead.HasValue).Select(i => i.TeamHead.Value).ToList());
                    userIds.AddRange(callLogDtos.Where(i => i.LastModifiedBy.HasValue).Select(i => i.LastModifiedBy.Value).ToList());
                    var users = _userService.GetListAsync(cancellationToken).Result.Where(i => userIds.Contains(i.Id)).ToList();
                    List<LeadAppointmentDto> appointmentDto = leadAppointments.Adapt<List<LeadAppointmentDto>>();
                    appointmentDto.ForEach(i =>
                    {
                        if (!string.IsNullOrWhiteSpace(i.Image))
                        {
                            i.ImagesWithName ??= new();
                            i.ImagesWithName.Add(new LeadDocument() { FilePath = i.Image });
                        }
                    });
                    appointmentDto = appointmentDto.Where(i => i.Type != AppointmentType.None).ToList();
                    List<AppointmentDataDto> leadAppointmentDtos = appointmentDto.Adapt<List<AppointmentDataDto>>();
                    leadAppointmentDtos.ForEach(i =>
                    {
                        var user = users.FirstOrDefault(j => j.Id == i.LastModifiedBy);
                        i.LastModifiedByUser = user?.FirstName + " " + user?.LastName;
                    });

                    var communicationDtos = leadcomunications.Adapt<List<LeadCommunicationDto>>();
                    communicationDtos.ForEach(i =>
                    {
                        var user = users.FirstOrDefault(j => j.Id == i.LastModifiedBy);
                        i.LastModifiedByUser = user?.FirstName + " " + user?.LastName;
                    });
                    
                   
                    var bookDetails = await GetBookedDetailsAsync(lead, cancellationToken, isAdmin, currentUserId, users);
                    List<WhatsAppCommunicationDto> whatsAppCommunicationDtos = whatsAppCommunications.Adapt<List<WhatsAppCommunicationDto>>();
                    if (!isAdmin)
                    {
                        whatsAppCommunicationDtos.RemoveAll(i => i.UserId != currentUserId || i.UserId == Guid.Empty);
                    }
                    whatsAppCommunicationDtos.ForEach(wc =>
                    {
                        var user = users.FirstOrDefault(user => user.Id == wc.UserId);
                        wc.LastModifiedByUser = user?.FirstName + " " + user?.LastName;
                        wc.LastModifiedBy = user?.Id ?? default;
                    });
                    //var userDtos = await _userService.GetListOfUsersByIdsAsync((callLogDtos?.Select(i => i.UserId.ToString() ?? string.Empty).ToList() ?? new List<string>())?
                    //                                                            .Concat(leadcomunications?.Select(i => i.LastModifiedBy.ToString())?.ToList() ?? new List<string>())?
                    //                                                            .Concat(whatsAppCommunications?.Select(i => i.LastModifiedBy.ToString())?.ToList() ?? new List<string>())?
                    //                                                            .ToList() ?? new List<string>(), cancellationToken);
                    if (leadHistories?.Any() ?? false)
                    {
                        return new Response<List<LeadHistoryDto>>(LeadHistoryHelper.FormLeadHistoryViewModelUserBasedOnWeb(leadHistories, _addressRepo, leadAppointmentDtos, communicationDtos, leadCallLogs: callLogDtos, users, whatsAppCommunicationDtos, bookDetails));
                    }
                    else
                    {
                        return new();
                    }
                }
                else
                {
                    throw new Exception("lead not found by this id");
                }
            }
            catch(Exception ex)
            {
                return new()
                {
                    Message = ex.Message,
                };
            }
        }
        private async Task<List<ServetelCallLog>> FormatTimeStampsAsync(List<ServetelCallLog>? servetelCallLogs)
        {
            if (servetelCallLogs?.Any() ?? false)
            {
                servetelCallLogs.ForEach(log =>
                {
                    log.StartStamp = log.StartStamp?.Replace('+', ' ');
                    log.EndStamp = log.EndStamp?.Replace('+', ' ');
                    if (!string.IsNullOrEmpty(log.StartStamp))
                    {
                        try
                        {
                            var result = Uri.UnescapeDataString(log.StartStamp);
                            DateTime dateTimeObj = DateTime.ParseExact(result, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                            log.StartStamp = dateTimeObj.ToString();
                        }
                        catch (Exception ex)
                        {
                            if (DateTime.TryParseExact(log.StartStamp, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out DateTime parsedDateTime))
                            {
                                log.StartStamp = parsedDateTime.ToString();
                            }
                            else
                            {
                                log.StartStamp = null;
                            }
                        }
                    }
                    if (!string.IsNullOrEmpty(log.EndStamp))
                    {
                        try
                        {
                            var result = Uri.UnescapeDataString(log.EndStamp);
                            DateTime dateTimeObj = DateTime.ParseExact(result, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                            log.EndStamp = dateTimeObj.ToString();
                        }
                        catch (Exception ex)
                        {
                            if (DateTime.TryParseExact(log.EndStamp, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out DateTime parsedDateTime))
                            {
                                log.EndStamp = parsedDateTime.ToString();
                            }
                            else
                            {
                                log.EndStamp = null;
                            }
                        }
                    }
                });
            }
            return servetelCallLogs ?? new();
        }
        private async Task<List<WhatsAppCommunicationDto>> GetWhatsAppCommunicationsAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken, bool? isAdmin, Guid? currentUserId)
        {
            List<WhatsAppCommunication>? whatsAppCommunications = await _whatsAppCommunicationRepo.ListAsync(new GetWhatsAppCommunicationSpec(lead.Id), cancellationToken);
            List<WhatsAppCommunicationDto> whatsAppCommunicationDtos = whatsAppCommunications.Adapt<List<WhatsAppCommunicationDto>>();
            if (!isAdmin ?? true)
            {
                whatsAppCommunicationDtos.RemoveAll(i => i.UserId != currentUserId || i.UserId == Guid.Empty);
            }
            var users = await _userService.GetListOfUsersByIdsAsync(whatsAppCommunicationDtos.Select(i => i.UserId.ToString()).ToList(), cancellationToken);
            whatsAppCommunicationDtos.ForEach(wc =>
            {
                var user = users.FirstOrDefault(user => user.Id == wc.UserId);
                wc.LastModifiedByUser = user?.FirstName + " " + user?.LastName;
                wc.LastModifiedBy = user?.Id ?? default;
            });
            return whatsAppCommunicationDtos;
        }
        private async Task<(List<BookedDetailsDto>? bookedDetailsInfo, List<List<DocumentsDto>>? documentsInfo, List<LeadBrokerageInfoDto>? leadBrokerageInfo)> GetBookedDetailsAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken, bool? isAdmin, Guid? currentUserId, List<UserDetailsDto>? users)
        {
            List<LeadBookedDetail> bookDetails = await _bookedInfoRepo.ListAsync(new GetBookedDetailsByIdSpec(lead.Id), cancellationToken);
            List<BookedDetailsDto> bookDetailsDto = bookDetails.Adapt<List<BookedDetailsDto>>();
            List<List<DocumentsDto>> listOfDocuments = new List<List<DocumentsDto>>();
            List<LeadBrokerageInfoDto> leadBrokerageList = new();
            //List<BasicPropertyInfoDto> propertiesInfo = new();
            //List<BasicProjectDto> projectsInfo = new();
            // List<UnitTypeDto> unitTypesInfo = new();
            if (!isAdmin ?? false)
            {
                bookDetailsDto.RemoveAll(i => i.UserId != currentUserId || i.UserId == Guid.Empty);
            }
            foreach (var bookedDetail in bookDetailsDto)
            {
                List<DocumentsDto> documents = new List<DocumentsDto>();
                LeadBrokerageInfoDto leadBrokerage = new();
                //BasicPropertyInfoDto propertyInfo = new();
                //BasicProjectDto projectInfo = new();
                // UnitTypeDto unitTypeInfo = new();
                try
                {
                    var teamHeadDetails = users?.Find(i => i.Id == bookedDetail.TeamHead);
                    if (teamHeadDetails != null)
                    {
                        bookedDetail.TeamHeadName = teamHeadDetails.FirstName + " " + teamHeadDetails.LastName;
                    }
                }
                catch (Exception ex) { }
                try
                {
                    var bookedByDetails = users?.Find(i => i.Id == bookedDetail.BookedBy);
                    if (bookedByDetails != null)
                    {
                        bookedDetail.BookedByName = bookedByDetails.FirstName + " " + bookedByDetails.LastName;
                    }
                }
                catch (Exception ex) { }
                try
                {
                    var secondaryOwnerDetails = users?.Find(i => i.Id == bookedDetail.SecondaryOwner);
                    if (secondaryOwnerDetails != null)
                    {
                        bookedDetail.SecondaryOwnerName = secondaryOwnerDetails.FirstName + " " + secondaryOwnerDetails.LastName;
                    }
                }
                catch { }
                try
                {
                    var userDetails = users?.Find(i => i.Id == bookedDetail.UserId);
                    if (userDetails != null)
                    {
                        bookedDetail.UserName = userDetails.FirstName + " " + userDetails.LastName;
                    }
                }
                catch (Exception ex) { }
                try
                {
                    var LastModifiedByUser = users?.Find(i => i.Id == bookedDetail.LastModifiedBy);
                    if (LastModifiedByUser != null)
                    {
                        bookedDetail.LastModifiedByUser = LastModifiedByUser.FirstName + " " + LastModifiedByUser.LastName;
                    }
                }
                catch (Exception ex) { }
                //try
                //{
                //    var userId = bookedDetail?.Documents?.FirstOrDefault()?.Id ?? Guid.Empty;
                //    var LastModifiedByUser = users.Find(i => i.Id == userId);
                //    if (LastModifiedByUser != null)
                //    {
                //        bookedDetail.LastModifiedByUser = LastModifiedByUser.FirstName + " " + LastModifiedByUser.LastName;
                //    }
                //}
                //catch (Exception ex) { }
                if (bookedDetail.Documents?.Any() ?? false)
                {
                    documents = bookedDetail?.Documents?.ToList() ?? default;
                    listOfDocuments.Add(documents);
                }
                if (bookedDetail.BrokerageInfo != null)
                {
                    leadBrokerage = bookedDetail?.BrokerageInfo ?? default;
                    leadBrokerageList.Add(leadBrokerage);
                }
                //if (bookedDetail.Property != null)
                //{
                //    propertyInfo = bookedDetail.Property ?? default;
                //    propertiesInfo.Add(propertyInfo);
                //}
                //if (bookedDetail.Projects != null)
                //{
                //    projectInfo = bookedDetail.Projects ?? default;
                //    projectsInfo.Add(projectInfo);
                //}
                //if (bookedDetail.UnitType != null)
                //{
                //    unitTypeInfo = bookedDetail.UnitType ?? default;
                //    unitTypesInfo.Add(unitTypeInfo);
                //}
            }
            return (bookDetailsDto, listOfDocuments, leadBrokerageList);
        }
    }
}
