﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Specs;
using Lrb.Application.ZonewiseLocation.Web.Helpers;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Generic;

namespace Lrb.Application.DataManagement.Web.Request.CommonHandler
{
    public class BaseFilterL1andL2CommonRequestHandler
    {
        private readonly IServiceProvider _serviceProvider;
        protected readonly IRepositoryWithEvents<Location> _locationRepo;
        protected readonly IMediator _mediator;
        protected readonly IRepositoryWithEvents<Address> _addressRepo;
        protected readonly IGooglePlacesService _googlePlacesService;
        private readonly IProspectRepository _efProspectRepository;
        protected readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;

        public BaseFilterL1andL2CommonRequestHandler(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _locationRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Location>>(); ;
            _mediator = _serviceProvider.GetRequiredService<IMediator>(); ;
            _addressRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Address>>();
            _googlePlacesService = _serviceProvider.GetRequiredService<IGooglePlacesService>();
            _efProspectRepository = _serviceProvider.GetRequiredService<IProspectRepository>();
            _propertyTypeRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<MasterPropertyType>>();
        }


        protected async Task<Address?> CreateAddressAsync(AddressDto? addressDto, CancellationToken cancellationToken = default)
        {
            try
            {
                Address? address = null;
                if (addressDto?.LocationId != null && addressDto?.LocationId != Guid.Empty)
                {
                    address = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocationIdSpec(new() { addressDto?.LocationId ?? Guid.Empty }), cancellationToken);
                    if (address == null)
                    {
                        var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(addressDto?.LocationId ?? Guid.Empty), cancellationToken);
                        if (location != null)
                        {
                            address = location.MapToAddress();
                            if (address != null)
                            {
                                address.Id = Guid.Empty;
                                address = await _addressRepo.AddAsync(address);
                            }
                        }
                    }
                }
                else if (!string.IsNullOrWhiteSpace(addressDto?.PlaceId) && address == null)
                {
                    address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(addressDto.PlaceId), cancellationToken)).FirstOrDefault();
                    if (address == null)
                    {
                        try
                        {
                            address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(addressDto.PlaceId))?.Adapt<Address>() ?? null;
                        }
                        catch (Exception ex)
                        {

                        }
                        if (address != null)
                        {
                            address = await _addressRepo.AddAsync(address);
                            await MapAddressToLocationAndSaveAsync(address);
                        }
                    }
                }
                else if (double.TryParse(addressDto?.Longitude ?? "0", out double lng) && lng > 0 && double.TryParse(addressDto?.Latitude ?? "0", out double lat) && lat > 0)
                {
                    var places = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(lat, lng);
                    var place = places.FirstOrDefault();
                    if (place != null && place.PlaceId != null)
                    {
                        address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                        if (address == null)
                        {
                            try
                            {
                                address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(place.PlaceId))?.Adapt<Address>() ?? null;
                            }
                            catch (Exception ex)
                            {

                            }
                            if (address != null)
                            {
                                address = await _addressRepo.AddAsync(address);
                                await MapAddressToLocationAndSaveAsync(address);
                            }
                        }
                    }
                    else if (place != null)
                    {
                        address = place.Adapt<Address>();
                        address = await _addressRepo.AddAsync(address);
                        await MapAddressToLocationAndSaveAsync(address);
                    }
                }
                else if (address == null && (addressDto?.Adapt<Address>()?.Validate(out Address? newAddress) ?? false))
                {
                    if (newAddress != null)
                    {
                        address = await _addressRepo.AddAsync(newAddress ?? new());
                        await MapAddressToLocationAndSaveAsync(address);
                    }
                }
                return address;
            }
            catch (Exception ex)
            {
                throw;
            }

        }

        private async Task MapAddressToLocationAndSaveAsync(Address address)
        {
            try
            {
                var location = address.MapToLocationRequest();
                if (location != null)
                {
                    var locationRes = await _mediator.Send(location.Adapt<AddLocationRequest>());
                    var createdLocation = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(locationRes.Data), default);
                    address.Location = createdLocation;
                    await _addressRepo.UpdateAsync(address);
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<V2ProspectCountByFilterDto> AddBaseProspectsCount(V2ProspectCountByFilterDto prospectCount, string propertyName, GetAllProspectRequest request, Guid userId, IEnumerable<Guid> subIds, List<CustomProspectStatus> statuses,IQueryable<Prospect> query, bool isAdmin)
        {
            switch (propertyName)
            {
                case nameof(ProspectCountByFilterDto.MyDataCount):
                    request.ProspectVisiblity = ProspectVisiblity.Self;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.MyDataCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds,query, statuses, isAdmin));
                    break;
                case nameof(ProspectCountByFilterDto.TeamDataCount):
                    request.ProspectVisiblity = ProspectVisiblity.Reportee;
                    request.FirstLevelFilter = FirstLevelFilter.All;

                    prospectCount.TeamDataCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses, isAdmin));
                    break;
                case nameof(ProspectCountByFilterDto.AllDataCount):
                    request.ProspectVisiblity = ProspectVisiblity.SelfWithReportee;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.AllDataCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses, isAdmin));
                    break;
                case nameof(ProspectCountByFilterDto.UnAssignedCount):
                    request.ProspectVisiblity = ProspectVisiblity.UnassignData;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.UnAssignedCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses, isAdmin));
                    break;
                case nameof(ProspectCountByFilterDto.DeletedDataCount):
                    request.ProspectVisiblity = ProspectVisiblity.DeletedData;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.DeletedDataCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses, isAdmin));
                    break;
                case nameof(ProspectCountByFilterDto.ConvertedCount):
                    request.ProspectVisiblity = ProspectVisiblity.ConvertedData;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.ConvertedCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses, isAdmin));
                    break;
            }

            return prospectCount;
        }
        public async Task<ProspectCountByFilterDto> AddBaseProspectCount(ProspectCountByFilterDto prospectCount, string propertyName, GetAllProspectRequest request, Guid userId, IEnumerable<Guid> subIds, List<CustomProspectStatus> statuses,IQueryable<Prospect> query)
        {
            switch (propertyName)
            {
                case nameof(ProspectCountByFilterDto.ManageDataCount):
                    request.ProspectVisiblity = (ProspectVisiblity)(-1);
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.ManageDataCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.MyDataCount):
                    request.ProspectVisiblity = ProspectVisiblity.Self;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.MyDataCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.TeamDataCount):
                    request.ProspectVisiblity = ProspectVisiblity.Reportee;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.TeamDataCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.AllDataCount):
                    request.ProspectVisiblity = ProspectVisiblity.SelfWithReportee;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.AllDataCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.UnAssignedCount):
                    request.ProspectVisiblity = ProspectVisiblity.UnassignData;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.UnAssignedCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.DeletedDataCount):
                    request.ProspectVisiblity = ProspectVisiblity.DeletedData;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.DeletedDataCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.NewDataCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.New;
                    prospectCount.NewDataCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.ActiveDataCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    prospectCount.ActiveDataCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.FollowUpsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Followups;
                    prospectCount.FollowUpsCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.BacklogCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Backlog;
                    prospectCount.BacklogCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.NotInterestedCount):
                    request.FirstLevelFilter = FirstLevelFilter.Inactive;
                    request.SecondLevelFilter = SecondLevelFilter.NotInterested;
                    prospectCount.NotInterestedCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.NotReachableCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.NotReachable;
                    prospectCount.NotReachableCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.InvalidCount):
                    request.FirstLevelFilter = FirstLevelFilter.Inactive;
                    request.SecondLevelFilter = SecondLevelFilter.InValid;
                    prospectCount.InvalidCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.InactiveCount):
                    request.FirstLevelFilter = FirstLevelFilter.Inactive;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    prospectCount.InactiveCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.NotAnsweredCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.NotAnswered;
                    prospectCount.NotAnsweredCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.QualifiedCount):
                    request.FirstLevelFilter = FirstLevelFilter.Qualified;
                    prospectCount.QualifiedCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.ConvertedCount):
                    request.ProspectVisiblity = ProspectVisiblity.ConvertedData;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.ConvertedCount = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
            }
            return prospectCount;
        }

        //FirstLevel filter & SecondLevel filter
        public async Task<(long, int, int)> AddrospectCount(ProspectCountByNewTopLevelFilterDto prospectCount, System.Reflection.PropertyInfo property, GetAllProspectRequest request, Guid userId, IEnumerable<Guid> subIds, List<CustomProspectStatus> statuses,IQueryable<Prospect> query)
        {
            var propertyName = property.Name;
            long count = 0;
            switch (propertyName)
            {
                case nameof(ProspectCountByNewTopLevelFilterDto.ManageDataCount):
                    request.ProspectVisiblity = (ProspectVisiblity)(-1);
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.MyData):
                    request.ProspectVisiblity = request.ProspectVisiblity != ProspectVisiblity.UnassignData && request.ProspectVisiblity != ProspectVisiblity.DeletedData ? ProspectVisiblity.Self : request.ProspectVisiblity;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.Team):
                    request.ProspectVisiblity = request.ProspectVisiblity != ProspectVisiblity.UnassignData && request.ProspectVisiblity != ProspectVisiblity.DeletedData ? ProspectVisiblity.Reportee : request.ProspectVisiblity; ;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.AllData):
                    //  request.ProspectVisiblity = request.ProspectVisiblity != ProspectVisiblity.UnassignData && request.ProspectVisiblity != ProspectVisiblity.DeletedData ? ProspectVisiblity.SelfWithReportee : request.ProspectVisiblity;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.Unassigned):
                    request.ProspectVisiblity = request.ProspectVisiblity != ProspectVisiblity.UnassignData && request.ProspectVisiblity != ProspectVisiblity.DeletedData ? ProspectVisiblity.UnassignData : request.ProspectVisiblity;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.DeletedData):
                    request.ProspectVisiblity = request.ProspectVisiblity != ProspectVisiblity.UnassignData && request.ProspectVisiblity != ProspectVisiblity.DeletedData ? ProspectVisiblity.DeletedData : request.ProspectVisiblity;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.New):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.New;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.ActiveData):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.Followups):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Followups;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.Backlog):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Backlog;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.NotInterested):
                    request.FirstLevelFilter = FirstLevelFilter.Inactive;
                    request.SecondLevelFilter = SecondLevelFilter.NotInterested;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.NotReachable):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.NotReachable;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.Invalid):
                    request.FirstLevelFilter = FirstLevelFilter.Inactive;
                    request.SecondLevelFilter = SecondLevelFilter.InValid;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.Inactive):
                    request.FirstLevelFilter = FirstLevelFilter.Inactive;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.NotAnswered):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.NotAnswered;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.Qualified):
                    request.FirstLevelFilter = FirstLevelFilter.Qualified;
                    count = (await _efProspectRepository.GetProspectsCountForWebAsync(request, userId, subIds, query, statuses));
                    break;
            }
            return (count, (int)request.FirstLevelFilter, (int)request.SecondLevelFilter);
        }

        public async Task<ProspectCountByStatusFilterDto> ReturProspectCountInStructure(GetProspectCountByCustomStatusFilterRequest request, Guid userId, List<Guid> subIds, List<CustomProspectStatus> statuses)
        {
            ProspectCountByFilterDto prospectCount = new();
            var propertyName = typeof(ProspectCountByFilterDto).GetProperties().Select(i => i.Name);
            //foreach (var property in propertyName)
            //{
            //    prospectCount = await AddBaseProspectCount(prospectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
            //}
            var query = await _efProspectRepository.BuildQueryForProspectsCount(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
           // var tasks = propertyName.Select(property => AddBaseProspectCount(prospectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses,query));
            foreach (var property in propertyName)
            {
                await AddBaseProspectCount(prospectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses, query);
            }
            prospectCount =  prospectCount ?? new();
            var statusFilterDto1 = new ProspectCountByStatusFilterDto();
            request.ProspectVisiblity = ProspectVisiblity.SelfWithReportee;
            statusFilterDto1.Count = prospectCount.AllDataCount;
            statusFilterDto1.EnumValue = 0;
            statusFilterDto1.Name = "AllData";
            statusFilterDto1.DisplayName = "All Data";
            statusFilterDto1.FilterPayloadKey = "ProspectVisiblity";
            statusFilterDto1.logoUrl = "assets/images/muso-team.svg";
            statusFilterDto1.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);

            return statusFilterDto1;
        }
        public async Task<List<ProspectCountByStatusFilterDto>> ReturnProspectAllDataCountInStructure(GetProspectCountBaseFilterLevel_1Request request, Guid userId, List<Guid> subIds, List<CustomProspectStatus> statuses)
        {
            ProspectCountByFilterDto prospectCount = new();
            var propertyName = typeof(ProspectCountByFilterDto).GetProperties().Select(i => i.Name);
            //foreach (var property in propertyName)
            //{
            //    prospectCount = await AddBaseProspectCount(prospectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
            //}
            var query = await _efProspectRepository.BuildQueryForProspectsCount(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
           // var tasks = propertyName.Select(property => AddBaseProspectCount(prospectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses,query));
            foreach (var property in propertyName)
            {
                await AddBaseProspectCount(prospectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses, query);
            }
            prospectCount = prospectCount ?? new();
            List<ProspectCountByStatusFilterDto> data = new List<ProspectCountByStatusFilterDto>();

            var statusFilterDto1 = new ProspectCountByStatusFilterDto();
            request.ProspectVisiblity = ProspectVisiblity.SelfWithReportee;
            statusFilterDto1.Count = prospectCount.AllDataCount;
            statusFilterDto1.EnumValue = 0;
            statusFilterDto1.Name = "AllData";
            statusFilterDto1.DisplayName = "All Data";
            statusFilterDto1.FilterPayloadKey = "ProspectVisiblity";
            statusFilterDto1.logoUrl = "assets/images/muso-team.svg";
            statusFilterDto1.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
            data.Add(statusFilterDto1);

            var statusFilterDto2 = new ProspectCountByStatusFilterDto();
            request.ProspectVisiblity = ProspectVisiblity.Self;
            statusFilterDto2.Count = prospectCount.MyDataCount;
            statusFilterDto2.EnumValue = 1;
            statusFilterDto2.Name = "MyData";
            statusFilterDto2.DisplayName = "My Data";
            statusFilterDto2.FilterPayloadKey = "ProspectVisiblity";
            statusFilterDto2.logoUrl = "assets/images/muso-team.svg";
            statusFilterDto2.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
            data.Add(statusFilterDto2);

            return data;
        }

        public async Task<List<ProspectCountByStatusFilterDto>> ReturnProspectTeamDataCountInStructure(GetProspectCountBaseFilterLevel_2Request request, Guid userId, List<Guid> subIds, List<CustomProspectStatus> statuses)
        {
            ProspectCountByFilterDto prospectCount = new();
            var propertyName = typeof(ProspectCountByFilterDto).GetProperties().Select(i => i.Name);
            List<ProspectCountByStatusFilterDto> data = new List<ProspectCountByStatusFilterDto>();
            var query = await _efProspectRepository.BuildQueryForProspectsCount(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
         //   var tasks = propertyName.Select(property => AddBaseProspectCount(prospectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses,query));
            foreach (var property in propertyName)
            {
                await AddBaseProspectCount(prospectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses, query);
            }
            prospectCount = prospectCount ?? new();

            var statusFilterDto1 = new ProspectCountByStatusFilterDto();
            request.ProspectVisiblity = ProspectVisiblity.Reportee;
            statusFilterDto1.Count = prospectCount.TeamDataCount;
            statusFilterDto1.EnumValue = 2;
            statusFilterDto1.Name = "TeamData";
            statusFilterDto1.DisplayName = "Team";
            statusFilterDto1.FilterPayloadKey = "ProspectVisiblity";
            statusFilterDto1.logoUrl = "assets/images/muso-team.svg";
            statusFilterDto1.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
            data.Add(statusFilterDto1);

            var statusFilterDto2 = new ProspectCountByStatusFilterDto();
            request.ProspectVisiblity = ProspectVisiblity.ConvertedData;
            statusFilterDto2.Count = prospectCount.ConvertedCount;
            statusFilterDto2.EnumValue = 3;
            statusFilterDto2.Name = "Converted";
            statusFilterDto2.DisplayName = "Converted";
            statusFilterDto2.FilterPayloadKey = "ProspectVisiblity";
            statusFilterDto2.logoUrl = "assets/images/muso-team.svg";
            statusFilterDto2.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
            data.Add(statusFilterDto2);

            var statusFilterDto4 = new ProspectCountByStatusFilterDto();
            request.ProspectVisiblity = ProspectVisiblity.UnassignData;
            statusFilterDto4.Count = prospectCount.UnAssignedCount;
            statusFilterDto4.EnumValue = 5;
            statusFilterDto4.Name = "UnassignData";
            statusFilterDto4.DisplayName = "Unassigned";
            statusFilterDto4.FilterPayloadKey = "ProspectVisiblity";
            statusFilterDto4.logoUrl = "assets/images/unknown-person.svg";
            statusFilterDto4.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
            data.Add(statusFilterDto4);

            var statusFilterDto3 = new ProspectCountByStatusFilterDto();
            request.ProspectVisiblity = ProspectVisiblity.DeletedData;
            statusFilterDto3.Count = prospectCount.DeletedDataCount;
            statusFilterDto3.EnumValue = 4;
            statusFilterDto3.Name = "DeletedData";
            statusFilterDto3.DisplayName = "Deleted";
            statusFilterDto3.FilterPayloadKey = "ProspectVisiblity";
            statusFilterDto3.logoUrl = "assets/images/delete.svg";
            statusFilterDto3.IsDeleted = true;
            statusFilterDto3.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
            data.Add(statusFilterDto3);




            return data;
        }
        public async Task<List<ProspectCountNewDto>> GetFirstAndSecondLevelFilterCountAsync(GetAllProspectRequest request, Guid userId, List<Guid> subIds, List<CustomProspectStatus> statuses)
        {
            List<ProspectCountNewDto> newProspectStatusFilterDto = new List<ProspectCountNewDto>();
            var properties = typeof(ProspectCountByNewFilterDto).GetProperties().ToList();
            ProspectCountByNewTopLevelFilterDto newProspectCount = new();
            var query = await _efProspectRepository.BuildQueryForProspectsCount(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
            foreach (var property in properties)
            {
                var filterDto = new ProspectCountNewDto();
                var statusFilterDto = new ProspectCountByStatusDto();
                List<ProspectCountNewDto> subStastusCounts = new();

                switch (property.Name)
                {
                    case "AllData":
                        var allCount = await AddrospectCount(newProspectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses,query);
                        filterDto.Count = allCount.Item1;
                        filterDto.EnumValue = 0;
                        filterDto.Name = property.Name;
                        filterDto.DisplayName = "All Data";
                        filterDto.IsDefault = true;
                        filterDto.FilterPayloadKey = "FirstLevelFilter";
                        filterDto.Children = subStastusCounts;
                        break;

                    case "ActiveData":
                        var activeBaseCount = await AddrospectCount(newProspectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses,query);

                        filterDto.Count = activeBaseCount.Item1;
                        filterDto.EnumValue = 1;
                        filterDto.Name = property.Name;
                        filterDto.DisplayName = "Active Data";
                        filterDto.FilterPayloadKey = "FirstLevelFilter";

                        var activeFlags = typeof(ProspectActiveStatusCount).GetProperties().ToList();
                        var activeSubStastusCount = activeFlags.Select(async activeFlag =>
                        {
                            var activeCount = await AddrospectCount(newProspectCount, activeFlag, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses,query);
                            var displayName = ProspectHelper.InsertSpaceBeforeCapital(activeFlag.Name);

                            return new ProspectCountNewDto()
                            {
                                Count = activeCount.Item1,
                                EnumValue = activeCount.Item3,
                                Name = activeFlag.Name,
                                DisplayName = displayName,
                                FilterPayloadKey = "SecondLevelFilter"
                            };
                        }).Select(task => task.Result).ToList();

                        filterDto.Children = activeSubStastusCount;

                        break;


                    //case "ActiveData":
                    //    var activeBaseCount = await AddrospectCount(newProspectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);

                    //    filterDto.Count = activeBaseCount.Item1;
                    //    filterDto.EnumValue = 1;
                    //    filterDto.Name = property.Name;
                    //    filterDto.DisplayName = "Active Data";
                    //    filterDto.FilterPayloadKey = "FirstLevelFilter";

                    //    var activeFlags = typeof(ProspectActiveStatusCount).GetProperties().ToList();
                    //    foreach (var activeFlag in activeFlags)
                    //    {
                    //        var activeCount = await AddrospectCount(newProspectCount, activeFlag, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                    //        var displayName = ProspectHelper.InsertSpaceBeforeCapital(activeFlag.Name);
                    //        subStastusCounts.Add(new ProspectCountNewDto() { Count = activeCount.Item1, EnumValue = activeCount.Item3, Name = activeFlag.Name, DisplayName = displayName, FilterPayloadKey = "SecondLevelFilter" });
                    //        filterDto.Children = subStastusCounts;
                    //    }
                    //    break;

                    case "Inactive":
                        var inactiveBaseCount = await AddrospectCount(newProspectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses,query);

                        filterDto.Count = inactiveBaseCount.Item1;
                        filterDto.EnumValue = 3;
                        filterDto.Name = property.Name;
                        filterDto.DisplayName = "Inactive Data";
                        filterDto.FilterPayloadKey = "FirstLevelFilter";

                        var inactiveFlags = typeof(ProspectInActiveStatusCount).GetProperties().ToList();

                        var inactiveSubStastusCount = inactiveFlags.Select(async inActiveFlag =>
                        {
                            var inctiveCount = await AddrospectCount(newProspectCount, inActiveFlag, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses,query);
                            var displayName = ProspectHelper.InsertSpaceBeforeCapital(inActiveFlag.Name);

                            return new ProspectCountNewDto()
                            {
                                Count = inctiveCount.Item1,
                                EnumValue = inctiveCount.Item3,
                                Name = inActiveFlag.Name,
                                DisplayName = displayName,
                                FilterPayloadKey = "SecondLevelFilter"
                            };
                        }).Select(task => task.Result).ToList();

                        filterDto.Children = inactiveSubStastusCount;

                        break;


                    //case "Inactive":
                    //    var inactiveBaseCount = await AddrospectCount(newProspectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);

                    //    filterDto.Count = inactiveBaseCount.Item1;
                    //    filterDto.EnumValue = 3;
                    //    filterDto.Name = property.Name;
                    //    filterDto.DisplayName = "Inactive Data";
                    //    filterDto.FilterPayloadKey = "FirstLevelFilter";

                    //    var inactiveFlags = typeof(ProspectInActiveStatusCount).GetProperties().ToList();
                    //    foreach (var inActiveFlag in inactiveFlags)
                    //    {
                    //        var inctiveCount = await AddrospectCount(newProspectCount, inActiveFlag, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                    //        var displayName = ProspectHelper.InsertSpaceBeforeCapital(inActiveFlag.Name);
                    //        subStastusCounts.Add(new ProspectCountNewDto() { Count = inctiveCount.Item1, EnumValue = inctiveCount.Item3, Name = inActiveFlag.Name, DisplayName = displayName, FilterPayloadKey = "SecondLevelFilter" });
                    //        filterDto.Children = subStastusCounts;
                    //    }
                    //    break;

                    case "Qualified":
                        var qualifiedCount = await AddrospectCount(newProspectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses,query);

                        filterDto.Count = qualifiedCount.Item1;
                        filterDto.EnumValue = 2;
                        filterDto.Name = property.Name;
                        filterDto.DisplayName = "Qualified";
                        filterDto.FilterPayloadKey = "FirstLevelFilter";
                        statusFilterDto.ChildCount = subStastusCounts;
                        break;
                }
                newProspectStatusFilterDto.Add(filterDto);
            }
            return newProspectStatusFilterDto;
        }


        public Lrb.Domain.Entities.Lead MapProspectToLead(Prospect prospect, CancellationToken cancellationToken)
        {
            Lrb.Domain.Entities.Lead lead = new()
            {
                Name = prospect?.Name?.Trim(),
                ContactNo = prospect?.ContactNo?.Trim(),
                AlternateContactNo = prospect?.ContactNo?.Trim(),
                Notes = prospect?.Notes,
                Email = prospect?.Email,
                Address = new Address()
                {
                    SubLocality = prospect?.Address?.SubLocality,
                    Locality = prospect?.Address?.Locality,
                    PlaceId = prospect?.Address?.PlaceId,
                    City = prospect?.Address?.City,
                    District = prospect?.Address?.District,
                    State = prospect?.Address?.State,
                    Country = prospect?.Address?.Country
                },
                AgencyName = prospect?.AgencyName?.Trim(),
                ChannelPartners = prospect?.ChannelPartners,
                ClosingManager = prospect?.ClosingManager,
                SourcingManager = prospect?.SourcingManager,
                ContactRecords = prospect?.ContactRecords,
                CompanyName = prospect?.CompanyName,
                Properties = prospect?.Properties,
                Projects = prospect?.Projects,
                Profession = prospect.Profession,
                ReferralEmail = prospect?.ReferralEmail,
                ReferralContactNo = prospect?.ReferralContactNo,
                ReferralName = prospect?.ReferralName,
                Nationality = prospect?.Nationality,
                PossesionType = prospect?.Enquiries?.FirstOrDefault()?.PossesionType,


            };
            lead.LeadNumber = lead?.Name?[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
            return lead;
        }

        public async Task<Lrb.Domain.Entities.LeadEnquiry> MapProspectEnquiryToLeadEnquiry(ProspectEnquiry? enquiry, CancellationToken cancellationToken)
        {

            
            List<MasterPropertyType>? propertyTypes = null;
            if (enquiry?.PropertyTypes != null)
            {
                propertyTypes = await _propertyTypeRepo.ListAsync(new GetMasterPropertyTypeSpec(enquiry.PropertyTypes.Select(i => i.Id).ToList()));
            }

            LeadSource? source = EnumFromDescription.GetValueFromDescription<LeadSource>(enquiry?.Source.DisplayName ?? string.Empty);

            var leadEnquiry = new Lrb.Domain.Entities.LeadEnquiry()
            {
                EnquiredFor = enquiry.EnquiryType,
                SubSource = enquiry.SubSource,
                LowerBudget = enquiry.LowerBudget,
                UpperBudget = enquiry.UpperBudget,
                NoOfBHKs = enquiry.NoOfBhks,
                BHKType = enquiry.BHKType,
                CarpetArea = enquiry.CarpetArea,
                CarpetAreaUnitId = enquiry.CarpetAreaUnitId,
                BuiltUpArea = enquiry.BuiltUpArea,
                BuiltUpAreaUnitId = enquiry.BuiltUpAreaUnitId ?? Guid.Empty,
                SaleableArea = enquiry.SaleableArea,
                SaleableAreaUnitId = enquiry.SaleableAreaUnitId ?? Guid.Empty,
                PropertyArea = enquiry.PropertyArea,
                PropertyAreaUnitId = enquiry.PropertyAreaUnitId ?? Guid.Empty,
                NetArea = enquiry.NetArea,
                NetAreaUnitId = enquiry.NetAreaUnitId ?? Guid.Empty,
                UnitName = enquiry.UnitName,
                ClusterName = enquiry.ClusterName,




                Address = new Address()
                {
                    SubLocality = enquiry?.Address?.SubLocality,
                    Locality = enquiry?.Address?.Locality,
                    PlaceId = enquiry?.Address?.PlaceId,
                    City = enquiry?.Address?.City,
                    District = enquiry?.Address?.District,
                    State = enquiry?.Address?.State,
                    Country = enquiry?.Address?.Country,
                }
            };

            leadEnquiry.LeadSource = source ?? LeadSource.Data;

            if (propertyTypes != null)
            {
                leadEnquiry.PropertyTypes = propertyTypes;
                leadEnquiry.PropertyType = propertyTypes.FirstOrDefault();
            }
            return leadEnquiry;
        }
        public async Task<List<ProspectCountByStatusFilterDto>> ReturnProspectAllDataCount(GetProspectCountForAllBaseFiltersRequest request, Guid userId, List<Guid> subIds, List<CustomProspectStatus> statuses, bool isAdmin)
        {
            V2ProspectCountByFilterDto prospectCount = new();
            var propertyName = typeof(V2ProspectCountByFilterDto).GetProperties().Select(i => i.Name);
            var query = await _efProspectRepository.BuildQueryForProspectsCount(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
            foreach (var property in propertyName)
            {
                await AddBaseProspectsCount(prospectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses, query, isAdmin);
            }
           // await Task.WhenAll(tasks);
            prospectCount =  prospectCount ?? new();
            List<ProspectCountByStatusFilterDto> data = new List<ProspectCountByStatusFilterDto>();

            var statusFilterDto1 = new ProspectCountByStatusFilterDto();
            request.ProspectVisiblity = ProspectVisiblity.SelfWithReportee;
            statusFilterDto1.Count = prospectCount.AllDataCount;
            statusFilterDto1.EnumValue = 0;
            statusFilterDto1.Name = "AllData";
            statusFilterDto1.DisplayName = "All Data";
            statusFilterDto1.FilterPayloadKey = "ProspectVisiblity";
            statusFilterDto1.logoUrl = "logos/muso-team.svg";
            data.Add(statusFilterDto1);

            var statusFilterDto2 = new ProspectCountByStatusFilterDto();
            request.ProspectVisiblity = ProspectVisiblity.Self;
            statusFilterDto2.Count = prospectCount.MyDataCount;
            statusFilterDto2.EnumValue = 1;
            statusFilterDto2.Name = "MyData";
            statusFilterDto2.DisplayName = "My Data";
            statusFilterDto2.FilterPayloadKey = "ProspectVisiblity";
            statusFilterDto2.logoUrl = "logos/single-muso.svg";
            data.Add(statusFilterDto2);

            var statusFilterDto3 = new ProspectCountByStatusFilterDto();
            request.ProspectVisiblity = ProspectVisiblity.Reportee;
            statusFilterDto3.Count = prospectCount.TeamDataCount;
            statusFilterDto3.EnumValue = 2;
            statusFilterDto3.Name = "TeamData";
            statusFilterDto3.DisplayName = "Team";
            statusFilterDto3.FilterPayloadKey = "ProspectVisiblity";
            statusFilterDto3.logoUrl = "logos/muso-team.svg";
            data.Add(statusFilterDto3);

            var statusFilterDto4 = new ProspectCountByStatusFilterDto();
            request.ProspectVisiblity = ProspectVisiblity.ConvertedData;
            statusFilterDto4.Count = prospectCount.ConvertedCount;
            statusFilterDto4.EnumValue = 3;
            statusFilterDto4.Name = "Converted";
            statusFilterDto4.DisplayName = "Converted";
            statusFilterDto4.FilterPayloadKey = "ProspectVisiblity";
            statusFilterDto4.logoUrl = "logos/muso-team.svg";
            data.Add(statusFilterDto4);

            var statusFilterDto5 = new ProspectCountByStatusFilterDto();
            request.ProspectVisiblity = ProspectVisiblity.DeletedData;
            statusFilterDto5.Count = prospectCount.DeletedDataCount;
            statusFilterDto5.EnumValue = 4;
            statusFilterDto5.Name = "DeletedData";
            statusFilterDto5.DisplayName = "Deleted";
            statusFilterDto5.FilterPayloadKey = "ProspectVisiblity";
            statusFilterDto5.logoUrl = "logos/delete.svg";
            statusFilterDto5.IsDeleted = true;
            data.Add(statusFilterDto5);

            var statusFilterDto6 = new ProspectCountByStatusFilterDto();
            request.ProspectVisiblity = ProspectVisiblity.UnassignData;
            statusFilterDto6.Count = prospectCount.UnAssignedCount;
            statusFilterDto6.EnumValue = 5;
            statusFilterDto6.Name = "UnassignData";
            statusFilterDto6.DisplayName = "Unassigned";
            statusFilterDto6.FilterPayloadKey = "ProspectVisiblity";
            statusFilterDto6.logoUrl = "logos/unknown-person.svg";
            data.Add(statusFilterDto6);
            return data;
        }
    }
}
