#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

ARG ASP_ENVIRONMENT
ENV ASPNETCORE_ENVIRONMENT="dev"

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src
COPY ["Lrb.Identity.Host/Lrb.Identity.Host.csproj", "Lrb.Identity.Host/"]
RUN dotnet restore "Lrb.Identity.Host/Lrb.Identity.Host.csproj"
COPY . .
WORKDIR "/src/Lrb.Identity.Host"
RUN dotnet build "Lrb.Identity.Host.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Lrb.Identity.Host.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Lrb.Identity.Host.dll"]