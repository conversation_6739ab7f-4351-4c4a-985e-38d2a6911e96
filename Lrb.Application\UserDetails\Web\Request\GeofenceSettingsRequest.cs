﻿using Lrb.Domain.Entities.ErrorModule;
using Newtonsoft.Json;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GeofenceSettingsRequest : IRequest<Response<bool>>
    {
        public Guid UserId { get; set; }
        public List<Guid>? PropertyIds { get; set; }
        public List<Guid>? ProjectIds { get; set; }
        public long? GeoFenceRadius { get; set; }
        public Radius? RadiusUnit { get; set; }
    }
    public class GeofenceSettingsRequestHandler : IRequestHandler<GeofenceSettingsRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.UserSettings> _userSettingsRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;

        public GeofenceSettingsRequestHandler(
            IRepositoryWithEvents<Domain.Entities.UserSettings> userSettingsRepo,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,
            ILeadRepositoryAsync leadRepositoryAsync)
        {
            _userSettingsRepo = userSettingsRepo;
            _userDetailsRepo = userDetailsRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
        }

        public async Task<Response<bool>> Handle(GeofenceSettingsRequest request, CancellationToken cancellationToken)
        {
            try
            {

                // Get or create user settings
                var userSettings = await _userSettingsRepo.FirstOrDefaultAsync(new GetUserSettingsSpec(request.UserId), cancellationToken);

                if (userSettings == null)
                {
                    // Create new user settings
                    userSettings = new Domain.Entities.UserSettings
                    {
                        UserId = request.UserId,
                        GeoFencePropertyIds = request.PropertyIds ?? new List<Guid>(),
                        GeoFenceProjectIds = request.ProjectIds ?? new List<Guid>(),
                        GeoFenceRadius = request.GeoFenceRadius,
                        RadiusUnit = request.RadiusUnit
                    };

                    await _userSettingsRepo.AddAsync(userSettings, cancellationToken);
                }
                else
                {
                    // Update existing user settings
                    userSettings.GeoFencePropertyIds = request.PropertyIds ?? new List<Guid>();
                    userSettings.GeoFenceProjectIds = request.ProjectIds ?? new List<Guid>();
                    userSettings.GeoFenceRadius = request.GeoFenceRadius;
                    userSettings.RadiusUnit = request.RadiusUnit;

                    await _userSettingsRepo.UpdateAsync(userSettings, cancellationToken);
                }

                // Update user details to enable geo-fencing
                var userDetails = await _userDetailsRepo.FirstOrDefaultAsync(new GetUserDetailsByIdSpec(request.UserId), cancellationToken);

                if (userDetails != null)
                {
                    // Enable geo-fencing if projects or properties are provided
                    bool shouldEnableGeoFencing = (request.ProjectIds != null && request.ProjectIds.Any()) ||
                                                 (request.PropertyIds != null && request.PropertyIds.Any());

                    userDetails.IsGeoFenceActive = shouldEnableGeoFencing;
                    await _userDetailsRepo.UpdateAsync(userDetails, cancellationToken);
                }

                return new Response<bool>(true, "Geo-fencing configuration has been updated successfully");
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GeofenceSettingsRequestHandler -> Handle() "
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return new Response<bool>(false, "An error occurred while configuring geo-fencing");
            }
        }
    }
}


