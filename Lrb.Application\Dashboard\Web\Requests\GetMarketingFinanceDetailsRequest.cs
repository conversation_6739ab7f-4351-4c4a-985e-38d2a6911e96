﻿using Lrb.Application.Dashboard.Web.Requests;
using Lrb.Application.Integration.Web.Requests.Facebook;
using System.Text.Json;

namespace Lrb.Application.Dashboard.Web
{
    public class GetMarketingFinanceDetailsRequest : GetAllDashboardParameterFilter, IRequest<PagedResponse<UserPerformanceDto, string>>
    {
        public Guid AccountId { get; set; }
        public string? Searchtext { get; set; }
        public bool? IsWithTeam { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? Projects { get; set; }
    }
    public class GetMarketingFinanceDetailsRequestHandler : IRequestHandler<GetMarketingFinanceDetailsRequest, PagedResponse<UserPerformanceDto, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<FacebookLeadGenForm> _facebookLeadGenFormRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepository;
        private readonly IRepositoryWithEvents<FacebookAuthResponse> _facebookAuthResponseRepo;
        private readonly IMediator _mediator;
        public GetMarketingFinanceDetailsRequestHandler(IDapperRepository dapperRepository,
            ICurrentUser currentUser, IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepository,
            IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo, IMediator mediator)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _facebookLeadGenFormRepo = facebookLeadGenFormRepo;
            _leadRepository = leadRepository;
            _facebookAuthResponseRepo = facebookAuthResponseRepo;
            _mediator = mediator;
        }
        public async Task<PagedResponse<UserPerformanceDto, string>> Handle(GetMarketingFinanceDetailsRequest request, CancellationToken cancellationToken)
        {
            Guid currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            List<Guid>? filterIds = new();
            List<Guid>? teamUserIds = new();
            if (request.UserIds?.Any() ?? false)
            {
                filterIds.AddRange(request.UserIds);
                if (request.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                    filterIds.AddRange(teamUserIds);
                }
            }
            else
            {
                switch (request.LeadVisibility)
                {
                    case LeadVisibility.Me:
                        filterIds = new() { currentUserId };
                        break;
                    case LeadVisibility.MyTeam:
                        filterIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { currentUserId }, tenantId))?.ToList() ?? new();
                        break;
                    case LeadVisibility.Organization:
                        filterIds = (await _dapperRepository.GetSubordinateIdsForDashboardAsync(new List<Guid>() { currentUserId }, tenantId))?.ToList() ?? new();
                        break;
                }
            }

            List<UserPerformanceDto> userDtos = new List<UserPerformanceDto>();
            request.PageNumber = request.PageNumber == 0 ? 1 : request.PageNumber;
            var users = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<UserPerformanceDto>(
                "LeadratBlack", "Marketing_Finances_Report", new
                {
                    p_tenantid = tenantId,
                    p_pagesize = request.PageSize,
                    p_pagenumber = request.PageNumber,
                    p_searchtext = request.Searchtext,
                    p_userids = filterIds,
                    p_projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                })).ToList();

            // Fetch Facebook leads for users
            var userIds = users.Where(x => x.User.HasValue).Select(x => x.User.Value).ToArray();
            var facebookLeads = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<FacebookLeadDto>(
                "LeadratBlack", "GetFacebookLeadsByUser", new
                {
                    p_tenantid = tenantId,
                    p_userids = filterIds,
                    p_projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                })).ToList();

            if (facebookLeads == null)
                throw new ArgumentNullException(nameof(facebookLeads));
            var campaignAdsJson = JsonSerializer.Serialize(
                facebookLeads.Select(x => new { x.CampaignId, x.AdId }).Distinct());
            var result = await _mediator.Send(new GetCampaignAdMetricsRequest
            {
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                AccountId = request.AccountId,
                CampaignAds = campaignAdsJson,
                TimeZoneId = request.TimeZoneId,
                BaseUTcOffset = request.BaseUTcOffset,
            }, cancellationToken);
            if (result?.Data == null)
            { return null; }
            var metrics = result.Data.ToDictionary(x => (x.CampaignId, x.AdId));
            foreach (var user in users)
            {
                var userLeads = facebookLeads.Where(l => l.UserId == user.User).ToList();
                // Group leads by campaign and ad to count leads per ad
                var leadsByAd = userLeads.GroupBy(l => new { l.CampaignId, l.AdId })
                                        .Select(g => new
                                        {
                                            CampaignId = g.Key.CampaignId,
                                            AdId = g.Key.AdId,
                                            LeadCount = g.Count()
                                        }).ToList();
                // Calculate CPL per user: sum of (CPL * number of leads) for each ad
                user.CPLPerUser = leadsByAd.Sum(ad =>
                {
                    var metric = metrics.GetValueOrDefault((ad.CampaignId, ad.AdId));
                    var cplForAd = metric?.CostPerLead ?? 0;
                    return cplForAd * ad.LeadCount;
                });
                decimal totalSoldPrice = userLeads.Sum(l => (decimal)l.SoldPrice);
                // Calculate ROI: ((Total Sold Price - Total CPL) / Total CPL) * 100
                var roi = totalSoldPrice > 0 && user.CPLPerUser > 0 ? ((totalSoldPrice - user.CPLPerUser) / user.CPLPerUser) * 100 : 0;
                user.ROI = Math.Round(roi ?? 0, 2);
                // ROI and TotalRevenue calculations remain the same
                user.TotalRevenue = totalSoldPrice;
            }
            return new(users, users.Count());
        }
    }
}
