﻿using Lrb.Application.Common.WA;
using Lrb.Application.Integration.Web;
using Lrb.Application.Utils;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.WebUtilities;
using Newtonsoft.Json.Linq;
using Serilog;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Lrb.Application.WA.Web.Requests
{
    public class GetWATemplatesByWebhookRequest : IRequest<Response<bool>>
    {
        public string ApiKey { get; set; }
        public HttpRequest HttpRequest { get; set; }
        public string TenantId { get; set; }

        public GetWATemplatesByWebhookRequest(HttpRequest request, string tenant, string base64)
        {
            HttpRequest = request;
            TenantId = tenant;
            ApiKey = base64;
        }
    }

    public class GetWATemplatesByWebhookHandler : IRequestHandler<GetWATemplatesByWebhookRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly IRepositoryWithEvents<WATemplate> _waTemplateRepo;
        private readonly IReadRepository<WAPayloadMapping> _waPayloadRepo;
        private readonly IWAService _waService;
        private readonly IMediator _mediator;
        private readonly ILogger _logger;
        public GetWATemplatesByWebhookHandler(
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccRepo,
            IMediator mediator,
            ILogger logger,
            IRepositoryWithEvents<Lrb.Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.GlobalSettings> globalSettingsRepo,
            IRepositoryWithEvents<WATemplate> waTemplateRepo,
            IReadRepository<WAPayloadMapping> waPayloadRepo,
            IWAService waService)
        {
            _integrationAccRepo = integrationAccRepo;
            _mediator = mediator;
            _logger = logger;
            _leadRepo = leadRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _waTemplateRepo = waTemplateRepo;
            _waPayloadRepo = waPayloadRepo;
            _waService = waService;
        }

        public async Task<Response<bool>> Handle(GetWATemplatesByWebhookRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var accountId = request.ApiKey.GetAccountId();
                var integrationAccount = await _integrationAccRepo.FirstOrDefaultAsync(new GetAllIntegrationAccountInfoWithWASpec(accountId), cancellationToken);
                if (integrationAccount == null)
                {
                    throw new InvalidOperationException("Integration account not found.");
                }
                //if (integrationAccount != null && integrationAccount.WAApiInfo != null && !integrationAccount.WAApiInfo.Any(x => x.WAApiAction == WAApiAction.GetTemplates))
                //{
                //    throw new InvalidOperationException("API info for template sync not found.");
                //}
                var payloadmapping = integrationAccount?.WAPayloadMapping;
                if (payloadmapping != null && payloadmapping.TemplateMapping != null && !payloadmapping.TemplateMapping.ContainsKey(WAApiAction.GetTemplates))
                {
                    throw new InvalidOperationException("Template Mapping for sync not found.");
                }

                var templateApiInfo = integrationAccount?.WAApiInfo?.FirstOrDefault(x => x.WAApiAction == WAApiAction.SendTextWithMediaTemplate || x.WAApiAction == WAApiAction.SendTextTemplate);
                var nameToIdMap = (await _waTemplateRepo.ListAsync(new GetAllWATemplatesSpec(), cancellationToken))
                .ToDictionary(item => item.Name, item => item.Id) ?? new();
                var bodyInString = "";
                var getTemplateApiInfo = integrationAccount?.WAApiInfo?.FirstOrDefault(x => x.WAApiAction == WAApiAction.GetTemplates);
            
                Stream stream = request.HttpRequest.Body;
                HttpRequestStreamReader reader = new(stream, System.Text.Encoding.UTF8);

                bodyInString = await reader.ReadToEndAsync();
                if (string.IsNullOrWhiteSpace(bodyInString))
                {
                    throw new ArgumentNullException("Payload Cannot be empty");
                }

                var parsedObj = JObject.Parse(bodyInString);
                var templates = MapWATemplates(parsedObj, payloadmapping ?? new());
                SynchronizeWATemplates(templates);
                if (getTemplateApiInfo != null)
                {
                    try
                    { 
                        var fullTemplate = await _waService.RetriveWATemaplteByName(templates?.FirstOrDefault()?.Name, getTemplateApiInfo, payloadmapping); 
                        if(fullTemplate.Any())
                        {
                            templates = fullTemplate;
                        }
                    }
                    catch(Exception ex)
                    {

                    }
                }
                templates.ForEach(x => x.WAApiInfo = templateApiInfo);
                var pendingTemplates = templates.Where(t => !nameToIdMap.Keys.Contains(t.Name)).ToList();
                var updateTemplates = templates.Join(nameToIdMap, t => t.Name, e => e.Key,
                        (t, e) => new
                        {
                            Template = t,
                            UpdatedId = e.Value
                        })
                        .Where(t => nameToIdMap.Keys.Contains(t.Template.Name))
                        .Select(t =>
                        {
                            t.Template.Id = t.UpdatedId;
                            return t.Template;
                        });
                if (pendingTemplates.Any())
                {
                    await _waTemplateRepo.AddRangeAsync(pendingTemplates, cancellationToken);
                }
                if (updateTemplates.Any())
                {
                    await _waTemplateRepo.UpdateRangeAsync(updateTemplates, cancellationToken);
                }
                return new Response<bool>
                {
                    Data = true,
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                return new Response<bool>
                {
                    Data = false,
                    Succeeded = false,
                    Message = ex.Message
                };
            }
        }
        private List<WATemplate> MapWATemplates(JToken result, WAPayloadMapping payloadMapping)
        {
            var PropertyMapping = payloadMapping.TemplateMapping.First(x => x.Key == WAApiAction.GetTemplates).Value;

            var templateResponseMapping = PropertyMapping.Where(x => x.TargetObject == "Result")
               .ToDictionary(d => d.SourceProperty, d => d.TargetProperty);

            var templateMapping = PropertyMapping.Where(x => x.TargetObject == "TemplateProperty")
                .ToDictionary(d => d.SourceProperty, d => d.TargetProperty);

            var buttonPropertyMapping = PropertyMapping.Where(x => x.TargetObject == "ButtonProperty")
                .ToDictionary(d => d.SourceProperty, d => d.TargetProperty);

            var mediaTypeMapping = PropertyMapping.Where(x => x.TargetObject == "MediaTypeMapping")
                .ToDictionary(d => d.SourceProperty, d => d.TargetProperty);

            var templates = new List<WATemplate>();

            JToken dataArray = new JArray();
            bool isSuccess = false;

            var templateProperties = GetPropertyKeyValuePairs<WATemplate>(new WATemplate());
            var templateButtonProperties = GetPropertyKeyValuePairs<WAButton>(new WAButton());

            if (result.Type == JTokenType.Object)
            {
                string succeededKey = string.Empty;
                string dataKey = string.Empty;

                templateResponseMapping?.TryGetValue("Data", out dataKey);
                templateResponseMapping?.TryGetValue("Succeeded", out succeededKey);


                var success = result[succeededKey ?? string.Empty];

                isSuccess = success switch
                {
                    JValue jValue when jValue.Type == JTokenType.Boolean => (bool)jValue,
                    JValue jValue when jValue.Type == JTokenType.String =>
                        ((string)jValue).ToLowerInvariant().Contains("success") ||
                        ((string)jValue).ToLowerInvariant().Contains("true"),
                    _ => true
                };
                //dataArray = result[dataKey ?? string.Empty] as JArray ?? new JArray();
                var dataToken = string.IsNullOrEmpty(dataKey) ? result : result[dataKey];

                if (dataToken is JArray array)
                {
                    dataArray = array;
                }
                else if (dataToken != null && dataToken.Type == JTokenType.Object)
                {
                    dataArray = new JArray(dataToken); // wrap object in array
                }
                else
                {
                    // If no Data key or not an object/array, treat entire result as single item
                    dataArray = new JArray(result);
                }
            }
            else if (result.Type == JTokenType.Array)
            {
                isSuccess = true;
                dataArray = result;
            }

            if (isSuccess)
            {
                foreach (var data in dataArray)
                {
                    var dataJObject = JObject.FromObject(data);
                    var mappedObject = new JObject();
                    var waButtons = new List<WAButton>();
                    if (templateResponseMapping != null && templateResponseMapping.ContainsKey("Buttons"))
                    {
                        var buttonKeyName = templateResponseMapping["Buttons"];
                        var buttonArray = dataJObject[buttonKeyName] as JArray ?? new JArray();
                        int orderRank = 1;
                        foreach (var button in buttonArray)
                        {
                            string buttonKeyNmae = buttonPropertyMapping["Name"];
                            string buttonKeyType = buttonPropertyMapping["Type"];
                            string buttonKeyValue = buttonPropertyMapping["Value"];
                            //string buttonCountryCode = buttonPropertyMapping["CountryCode"];
                            buttonPropertyMapping.TryGetValue("CountryCode", out string? buttonCountryCode);

                            var type = Enum.TryParse<WAButtonType>(button[buttonKeyType]?.ToString(), out var parsedType)
                            ? parsedType : WAButtonType.None;

                            waButtons.Add(new WAButton
                            {
                                OrderRank = orderRank,
                                Name = button[buttonKeyNmae]?.ToString(),
                                Value = (string.IsNullOrEmpty(buttonCountryCode)
                                    ? button[buttonKeyValue]?.ToString()
                                    : (button[buttonCountryCode] + button[buttonKeyValue]?.ToString()))
                                    ?? string.Empty,
                                Type = type,
                            });
                            orderRank++;

                        }
                    }
                    //var buttonKeyName = templateResponseMapping["Buttons"];
                    //var buttonArray = dataJObject[buttonKeyName] as JArray ?? new JArray();
                    //int orderRank = 1;
                    //var waButtons = new List<WAButton>();
                    //foreach (var button in buttonArray)
                    //{
                    //    string buttonKeyNmae = buttonPropertyMapping["Name"];
                    //    string buttonKeyType = buttonPropertyMapping["Type"];
                    //    string buttonKeyValue = buttonPropertyMapping["Value"];
                    //    //string buttonCountryCode = buttonPropertyMapping["CountryCode"];
                    //    buttonPropertyMapping.TryGetValue("CountryCode", out string? buttonCountryCode);

                    //    var type = Enum.TryParse<WAButtonType>(button[buttonKeyType]?.ToString(), out var parsedType)
                    //    ? parsedType : WAButtonType.None;

                    //    waButtons.Add(new WAButton
                    //    {
                    //        OrderRank = orderRank,
                    //        Name = button[buttonKeyNmae]?.ToString(),
                    //        Value = (string.IsNullOrEmpty(buttonCountryCode)
                    //            ? button[buttonKeyValue]?.ToString()
                    //            : (button[buttonCountryCode] + button[buttonKeyValue]?.ToString()))
                    //            ?? string.Empty,
                    //        Type = type,
                    //    });
                    //    orderRank++;

                    //}
                    foreach (var property in dataJObject.Properties())
                    {
                        var originalKey = property.Name;
                        var value = property.Value;

                        var mappedKey = templateMapping.FirstOrDefault(x => x.Value == originalKey).Key;
                        if (!string.IsNullOrEmpty(mappedKey))
                        {
                            var propertyName = templateProperties.FirstOrDefault(x => x.Value == mappedKey).Key;

                            if (!string.IsNullOrEmpty(propertyName))
                            {
                                if (mediaTypeMapping != null)
                                {
                                    if (mappedKey == "MediaType")
                                    {
                                        var mediaType = mediaTypeMapping?.FirstOrDefault(x => x.Value == value?.ToString()).Key;
                                        mappedObject[propertyName] = mediaType ?? null;
                                    }
                                    else
                                    {
                                        mappedObject[propertyName] = value;
                                    }
                                }
                                else
                                {
                                    mappedObject[propertyName] = value;
                                }
                            }
                        }
                    }
                    var template = mappedObject.ToObject<WATemplate>();
                    if (template != null && !string.IsNullOrEmpty(template.Name))
                    {
                        template.WAButtons = waButtons;
                        templates.Add(template);
                    }
                }
            }

            return templates;
        }

        private void SynchronizeWATemplates(List<WATemplate> templates)
        {
            string pattern = @"\{\{\d+\}\}";

            foreach (var template in templates)
            {
                if (template.Message != null || !string.IsNullOrWhiteSpace(template.Message))
                {
                    template.IsEnabled = !(Regex.IsMatch(template.Message, pattern) || Regex.IsMatch(template.Header ?? string.Empty, pattern));

                    if (!string.IsNullOrEmpty(template.MediaURL))
                    {
                        template.HeaderValues = new Dictionary<int, string>
                    {
                        { 1, template.MediaURL }
                    };
                    }
                    else if (string.IsNullOrEmpty(template.MediaType))
                    {
                        template.MediaType = "Text";
                    }
                    template.BodyValues = StringHelper.ExtractVariablesAsDictionary(template.Message);
                }
                else if (template.Name != null || !string.IsNullOrWhiteSpace(template.Name))
                {
                    template.Message = string.Empty;
                    template.IsEnabled = true;
                }

            }
        }
        public Dictionary<string, string> GetPropertyKeyValuePairs<T>(object model)
        {
            var keyValuePairs = new Dictionary<string, string>();
            Type type = typeof(T);
            PropertyInfo[] properties = type.GetProperties();

            foreach (PropertyInfo property in properties)
            {
                string key = property.Name;
                keyValuePairs[key] = $"{key}";
            }

            return keyValuePairs;
        }
    }
}
