﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "DatabaseSettings": {
    "DBProvider": "postgresql",
    //"ConnectionString": "Host=ls-e721c9cb6fe76c20e99b4da2632fd51e40d87358.coluzekxwdtv.ap-south-1.rds.amazonaws.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=************`H9hTJK5ojM)C=$C6w,0;Timeout=300;CommandTimeout=300;"
    "ConnectionString": "Host=dev-lrb.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=************`H9hTJK5ojM)C=$C6w,0;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;"
  }
}