﻿using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.ListingManagement.Web.Specs;
using Lrb.Application.Property.Web;
using Lrb.Application.PropertyRefrenceInfomation.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Data;
using System.Text.RegularExpressions;
using Lrb.Application.Property.Web;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Domain.Entities.AmenitiesAttributes;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task PropertyHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            BulkPropertyUploadTracker? propertyUploadTracker = await _bulkPropertyUploadTrackerRepo.GetByIdAsync(input.TrackerId);
            try
            {
                if (propertyUploadTracker != null)
                {
                    try
                    {
                        propertyUploadTracker.MappedColumnsData = propertyUploadTracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                        propertyUploadTracker.Status = UploadStatus.Started;
                        propertyUploadTracker.LastModifiedBy = input.CurrentUserId;
                        propertyUploadTracker.CreatedBy = input.CurrentUserId;
                        await _bulkPropertyUploadTrackerRepo.UpdateAsync(propertyUploadTracker);
                        Console.WriteLine($"handler() -> BulkLeadUploadTracker Updated Status: {propertyUploadTracker.Status} \n {JsonConvert.SerializeObject(propertyUploadTracker)}");
                        #region Fetch all required MasterData and Other data
                        var propetyTypes = new List<MasterPropertyType>(await _propertyTypeRepo.ListAsync(cancellationToken));
                        var propertyAttributes = new List<CustomMasterAttribute>(await _masterPropertyAttributeRepo.ListAsync(cancellationToken));
                        var areaUnits = new List<MasterAreaUnit>(await _masterAreaUnitRepo.ListAsync(cancellationToken));
                        List<Lrb.Domain.Entities.Project> projects = new(await _newProjectRepo.ListAsync(cancellationToken));
                        projects = projects.DistinctBy(i => i.Name).ToList();
                        var globalSettingInfo = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                        var listingSources = await _customListingSource.ListAsync(new GetAllListingSourceNameAndIdSpecs());
                        var lrbUsers = await _userViewRepo.ListAsync(new GetUserForReferenceIdAssignmentSpec());
                        var listingSourceAddresses = await _listingSourceAddressRepo.ListAsync(new GetAllListingSourceAddressesForBulkUploadSpecs());
                        #endregion
                        #region Convert file to Datatable
                        Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", propertyUploadTracker?.S3BucketKey ?? string.Empty);
                        DataTable dataTable = new();
                        if (propertyUploadTracker?.S3BucketKey?.Split('.')?.LastOrDefault() == "csv")
                        {
                            using MemoryStream memoryStream = new();
                            fileStream.CopyTo(memoryStream);
                            dataTable = CSVHelper.CSVToDataTable(memoryStream);
                        }
                        else
                        {
                            dataTable = Lrb.Application.Property.Web.EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, propertyUploadTracker?.SheetName);
                        }
                        int totalRows = dataTable.Rows.Count;
                        for (int i = totalRows - 1; i >= 0; i--)
                        {
                            var row = dataTable.Rows[i];
                            if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                            {
                                row.Delete();
                            }
                        }
                        if (dataTable.Rows.Count <= 0)
                        {
                            throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                        }
                        Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
                        #endregion
                        var unMappedColumns = dataTable.GetUnmappedColumnNames(propertyUploadTracker?.MappedColumnsData);

                        var properties = dataTable.ConvertToProperty(propertyUploadTracker?.MappedColumnsData, unMappedColumns, propetyTypes, areaUnits, propertyAttributes, projects, globalSettingInfo, listingSources: listingSources, users: lrbUsers, _blobStorageService: _blobStorageService, listingSourceAddresses: listingSourceAddresses);
                        properties.ForEach(lead => lead.SetProperty(propertyUploadTracker?.MappedColumnsData, input.CurrentUserId));
                        var invalidDate = Lrb.Application.Property.Web.BulkUploadHelper.GetInvalidProperties(properties, globalSettingInfo.ShouldEnablePropertyListing);
                        propertyUploadTracker.Status = UploadStatus.InProgress;
                        propertyUploadTracker.TotalCount = totalRows;
                        propertyUploadTracker.InvalidCount = invalidDate.Count();
                        if (invalidDate.Any())
                        {
                            byte[] bytes = ExcelHelper.CreateExcelFromList(invalidDate).ToArray();
                            string fileName = $"InvalidProperties-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                            string folder = "Properties";
                            var key = await _blobStorageService?.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                            propertyUploadTracker.InvalidDataS3BucketKey = key;
                        }
                        propertyUploadTracker.LastModifiedBy = input.CurrentUserId;
                        propertyUploadTracker.CreatedBy = input.CurrentUserId;
                        await _bulkPropertyUploadTrackerRepo.UpdateAsync(propertyUploadTracker);
                        if (properties.Count > 0)
                        {
                            int leadsPerchunk = properties.Count > 5000 ? 5000 : properties.Count;
                            var chunks = properties.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Property>(i));
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);
                            var chunkIndex = 1;
                            foreach (var chunk in chunks.ToList())
                            {
                                var backgroundDto = new BulkUploadbackgroundDto()
                                {
                                    TrackerId = propertyUploadTracker?.Id ?? Guid.Empty,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Properties = new(chunk)
                                };
                                if (chunkIndex == chunks.Count())
                                {
                                    backgroundDto.IsLastChunk = true;
                                }
                                await ExecuteDBOperationsAsync(backgroundDto);
                                chunkIndex++;
                            }
                            propertyUploadTracker.TotalUploadedCount = properties.Count();
                            propertyUploadTracker.Status = UploadStatus.Completed;
                            propertyUploadTracker.LastModifiedBy = input.CurrentUserId;
                            propertyUploadTracker.CreatedBy = input.CurrentUserId;
                            await _bulkPropertyUploadTrackerRepo.UpdateAsync(propertyUploadTracker);
                        }
                        else
                        {
                            propertyUploadTracker.Status = UploadStatus.Completed;
                            propertyUploadTracker.LastModifiedBy = input.CurrentUserId;
                            propertyUploadTracker.CreatedBy = input.CurrentUserId;
                            await _bulkPropertyUploadTrackerRepo.UpdateAsync(propertyUploadTracker);
                        }
                    }
                    catch (Exception ex)
                    {
                        propertyUploadTracker.Status = UploadStatus.Failed;
                        propertyUploadTracker.Message = ex.Message;
                        propertyUploadTracker.LastModifiedBy = input.CurrentUserId;
                        propertyUploadTracker.CreatedBy = input.CurrentUserId;
                        await _bulkPropertyUploadTrackerRepo.UpdateAsync(propertyUploadTracker);
                        throw;
                    }
                }
                else
                {
                    Console.WriteLine($"handler() -> tracker not found by the Id : {input.TrackerId}");
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> PropertyHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }
        public async Task ExecuteDBOperationsAsync(BulkUploadbackgroundDto dto)
        {
            BulkPropertyUploadTracker? tracker = new();
            tracker = await _bulkPropertyUploadTrackerRepo.GetByIdAsync(dto.TrackerId);
            try
            {
                await _propertyRepo.AddRangeAsync(dto.Properties);

                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.Properties.Count;
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _bulkPropertyUploadTrackerRepo.UpdateAsync(tracker);
                    await _dapperRepository.UpdateLatModifiedDateAsync(dto?.TenantInfoDto?.Id, (int)EntityType.Property);

                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                tracker.Status = UploadStatus.Failed;
                tracker.Message = e?.InnerException?.Message ?? e?.Message;
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _bulkPropertyUploadTrackerRepo.UpdateAsync(tracker);
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = " FunctionEntryPoint -> ExecuteDBOperationsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
    }
}
