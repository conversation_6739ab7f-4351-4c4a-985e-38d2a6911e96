﻿using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Reports.Web;
using Lrb.Application.UserDetails.Web;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Domain.Entities.ErrorModule;
using Mapster;
using Newtonsoft.Json;

namespace Lrb.Application.Team.Web
{
    public class GetTeamByIdRequest : PaginationFilter,IRequest<PagedResponse<GetTeamByIdDto,string>>
    {
        public Guid Id { get; set; }
        public string? SearchByName {  get; set; }

        public class GetByIdTeamRequestHandler : IRequestHandler<GetTeamByIdRequest, PagedResponse<GetTeamByIdDto, string>>
        {
            private readonly IUserService _userService;
            private readonly IReadRepository<Domain.Entities.Team> _teamRepository;
            private readonly IReadRepository<Domain.Entities.Prospect> _prospectRepo;
            private readonly IReadRepository<Domain.Entities.Lead> _leadRepo;
            private readonly IReadRepository<Domain.Entities.UserView> _userView;
            private readonly ILeadRepositoryAsync _leadRepositoryAsync;
            private readonly IDapperRepository _dapperRepository;
            private readonly ICurrentUser _currentUser;
            public GetByIdTeamRequestHandler(IUserService userService,
                IReadRepository<Domain.Entities.Team> teamRepository,
                ILeadRepositoryAsync leadRepositoryAsync,
                IReadRepository<Domain.Entities.Lead> leadRepo,
                IReadRepository<Domain.Entities.UserView> userView,
                IReadRepository<Domain.Entities.Prospect> prospectRepo,
                IDapperRepository dapperRepository,
                ICurrentUser currentUser)
            {
                _teamRepository = teamRepository;
                _userService = userService;
                _leadRepositoryAsync = leadRepositoryAsync;
                _leadRepo = leadRepo;
                _userView = userView;
                _prospectRepo = prospectRepo;
                _dapperRepository = dapperRepository;
                _currentUser = currentUser;
            }

            public async Task<PagedResponse<GetTeamByIdDto,string>> Handle(GetTeamByIdRequest request, CancellationToken cancellationToken)
            {
                try
                {
                    var tenantId = _currentUser.GetTenant();
                    Domain.Entities.Team? team = (await _teamRepository.FirstOrDefaultAsync(new TeamByIdSpec(request.Id), cancellationToken)) ?? throw new NotFoundException("No team found by this id!");
                    var teamUserIds = team.UserIds ?? new List<Guid>();

                    var users = (await _userView.ListAsync(new UserByIdsSpec(teamUserIds, request)))
                        .OrderBy(u => teamUserIds.IndexOf(u.Id))  
                        .ToList();
                    var manager = await _userView.FirstOrDefaultAsync(new UserViewByIdSpec(team.Manager ?? Guid.Empty));
                    GetTeamByIdDto getByIdTeamDto = new GetTeamByIdDto();
                    getByIdTeamDto = team.Adapt<GetTeamByIdDto>();
                    getByIdTeamDto.TotalMembers = team.UserIds != null ? team.UserIds.Count() : 0;  
                    getByIdTeamDto.LeadsAssigned = await _leadRepo.CountAsync(new LeadsCountByUserIdSpec(team.UserIds ?? new List<Guid>()), cancellationToken);
                    getByIdTeamDto.DataAssigned = await _prospectRepo.CountAsync(new GetAssignedProspectByUserIdSpec(team.UserIds ?? new List<Guid>()));
                    var usersWithCounts = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<UserWithCountDto>("LeadratBlack", "Users_Affiliated_Count", new
                      {
                          pagesize = request.PageSize,
                          pagenumber = request.PageNumber,
                          tenantid = tenantId,
                          userids = users?.Select(i => i.Id)?.ToList()
                      }))?.ToList() ?? new List<UserWithCountDto>();

                    var usersDto = users?.Adapt<List<TeamUserDto>>();
                    foreach (var user in usersWithCounts)
                    {
                        var userDto = usersDto?.FirstOrDefault(i => i.Id == user.Id);
                        if (userDto != null)
                        {
                            userDto.LeadsCount = user.LeadsCount;
                            userDto.ProspectsCount = user.ProspectsCount;
                        }
                    }
                    getByIdTeamDto.Users = usersDto ?? new List<TeamUserDto>();
                    var totalUsers = team.UserIds != null ? team.UserIds.Count() : 0;
                    getByIdTeamDto.Manager = manager?.Adapt<TeamUserDto>();
                    List<GetTeamByIdDto> result = new List<GetTeamByIdDto>();
                    result.Add(getByIdTeamDto);
                    return new PagedResponse<GetTeamByIdDto, string>(result, totalUsers);
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "GetByIdTeamRequestHandler -> Handle()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                    throw new NotFoundException("No team found by this Id!");
                }
            }
        }
    }
}
