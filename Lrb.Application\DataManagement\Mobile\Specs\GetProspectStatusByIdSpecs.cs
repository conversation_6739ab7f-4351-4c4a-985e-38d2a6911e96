﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Mobile.Specs
{
    public class GetProspectStatusByIdSpecs : Specification<CustomProspectStatus>
    {
        public GetProspectStatusByIdSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.BaseId == id);
        }
    }

    public class GetProspectStatusByNameSpecs : Specification<CustomProspectStatus>
    {
        public GetProspectStatusByNameSpecs(string? name)
        {
            Query.Where(i => !i.IsDeleted && i.DisplayName.Trim().ToLower().Replace(" ", "").Contains(name.Trim().ToLower().Replace(" ","")));
        }
    }
    public class GetProspectStatusForMobileSpecs : Specification<CustomProspectStatus>
    {
        public GetProspectStatusForMobileSpecs()
        {
            Query.Where(i => !i.Is<PERSON>ele<PERSON>);
        }
    }
}
