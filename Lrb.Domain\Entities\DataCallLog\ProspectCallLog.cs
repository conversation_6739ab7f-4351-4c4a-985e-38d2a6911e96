﻿using System.Text.Json.Serialization;

namespace Lrb.Domain.Entities
{
    public class ProspectCallLog : AuditableEntity, IAggregateRoot
    {
        public DateTime CallStartTime { get; set; }
        public DateTime CallEndTime { get; set; }
        public string? CallDuration { get; set; }
        public string? Notes { get; set; }
        public CallDirection CallDirection { get; set; }
        public Guid UserId { get; set; }
        public Guid ProspectId { get; set; }
        public CallStatus CallStatus { get; set; }
        [JsonIgnore]
        public Prospect Prospect { get; set; }
        public string? CallRecordingUrl { get; set; }
    }
}
