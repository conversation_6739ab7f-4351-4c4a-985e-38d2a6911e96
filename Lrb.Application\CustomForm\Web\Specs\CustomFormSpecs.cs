using Lrb.Application.CustomForm.Web.Requests;

namespace Lrb.Application.CustomForm.Web.Specs
{
    public class GetCustomFormValueByIdSpecs : Specification<Domain.Entities.CustomFieldValue>
    {
        public GetCustomFormValueByIdSpecs(Guid fieldId)
        {
            Query.Where(i => i.FormFieldId == fieldId && !i.IsDeleted);
        }
    }
    public class GetAllCustomFormSpecs : EntitiesByPaginationFilterSpec<Domain.Entities.CustomFormFields>
    {
        public GetAllCustomFormSpecs(GetAllCustomFormRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                 .OrderBy(i => i.LastModifiedOn);
        }
    }
    public class GetAllCustomFormCountSpecs : Specification<Domain.Entities.CustomFormFields>
    {
        public GetAllCustomFormCountSpecs(GetAllCustomFormRequest request)
        {
            Query.Where(i => !i.IsDeleted);


        }
    }
}
