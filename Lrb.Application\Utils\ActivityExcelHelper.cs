﻿using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Reports.Web;
using Lrb.Application.Reports.Web.Dtos.ExportTrackerDto;
using Lrb.Application.Reports.Web.Dtos.FiltersName;
using Lrb.Application.Utils;
using Lrb.Shared.Extensions;
using Microsoft.AspNetCore.Http;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System;
using System.Data;
using System.Globalization;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Lrb.Application.Activity.Utils
{
    public static class ActivityExcelHelper
    {
        private static List<string> mergedCells = new();

        public static bool IsValidFile(IFormFile file, out string ext)
        {
            List<string> extList = new List<string> { "xls", "xlsx", "csv" };
            ext = file.FileName.Split(".").LastOrDefault();
            if (extList.Contains(ext.ToLowerInvariant()))
            {
                return true;
            }
            return false;
        }
        public static DataTable ExcelToDataTable(IFormFile file)
        {
            DataTable dataTable = new DataTable();
            MemoryStream stream = new MemoryStream();
            file.CopyToAsync(stream);
            using SpreadsheetDocument doc = SpreadsheetDocument.Open(stream, false);
            WorkbookPart workbookPart = doc.WorkbookPart;
            Sheets sheets = workbookPart.Workbook.GetFirstChild<Sheets>();
            Sheet sheet = sheets.GetFirstChild<Sheet>();
            Worksheet worksheet = ((WorksheetPart)workbookPart.GetPartById(sheet.Id)).Worksheet;
            SheetData sheetData = worksheet.GetFirstChild<SheetData>();
            int rowIndex = 1;
            foreach (Row row in sheetData)
            {
                if (rowIndex < 2)
                {
                    int cellIndex = 1;
                    char currentCellReference = 'A';
                    char previousCellReference = (char)(currentCellReference - 1);
                    foreach (Cell cell in row)
                    {
                        var cellValue = GetCellValue(cell, workbookPart).Trim();
                        currentCellReference = char.ToUpper(cell.CellReference.InnerText[0]);
                        int cellDiferrence = currentCellReference - previousCellReference;
                        if (cellDiferrence == 1)
                        {
                            dataTable.Columns.Add(new DataColumn(cellValue));
                            cellIndex++;
                        }
                        else
                        {
                            for (int i = 1; i < cellDiferrence; i++)
                            {
                                dataTable.Columns.Add(new DataColumn($"ColumnNo.{cellIndex + i - 1}"));
                            }
                            dataTable.Columns.Add(new DataColumn(cellValue));
                            cellIndex += cellDiferrence;
                        }
                        previousCellReference = currentCellReference;
                    }
                }
                else
                {
                    var dataRow = dataTable.NewRow();
                    int cellIndex = 0;
                    char currentCellReference = 'A';
                    char previousCellReference = (char)(currentCellReference - 1);
                    foreach (Cell cell in row)
                    {
                        var cellValue = GetCellValue(cell, workbookPart).Trim();

                        currentCellReference = char.ToUpper(cell.CellReference.InnerText[0]);
                        int cellDiferrence = currentCellReference - previousCellReference;
                        if (cellDiferrence == 1 && cellIndex <= dataTable.Columns.Count)
                        {
                            dataRow[dataTable.Columns[cellIndex]] = cellValue;
                            cellIndex++;
                        }
                        else
                        {
                            for (int i = 1; i < cellDiferrence; i++)
                            {
                                if (cellIndex + i <= dataTable.Columns.Count)
                                {
                                    dataRow[dataTable.Columns[cellIndex + i - 1]] = string.Empty;
                                }
                            }
                            cellIndex += cellDiferrence;
                            if (cellIndex <= dataTable.Columns.Count)
                            {
                                dataRow[dataTable.Columns[cellIndex - 1]] = cellValue;
                            }
                        }
                        previousCellReference = currentCellReference;
                    }
                    dataTable.Rows.Add(dataRow);
                }
                rowIndex++;
            }
            return dataTable;
        }
        public static DataTable ExcelToDataTable(Stream fileStream)
        {
            DataTable dataTable = new DataTable();
            using SpreadsheetDocument doc = SpreadsheetDocument.Open(fileStream, false);
            WorkbookPart workbookPart = doc.WorkbookPart;
            Sheets sheets = workbookPart.Workbook.GetFirstChild<Sheets>();
            Sheet sheet = sheets.GetFirstChild<Sheet>();
            Worksheet worksheet = ((WorksheetPart)workbookPart.GetPartById(sheet.Id)).Worksheet;
            SheetData sheetData = worksheet.GetFirstChild<SheetData>();
            int rowIndex = 1;
            foreach (Row row in sheetData)
            {
                if (rowIndex < 2)
                {
                    int cellIndex = 1;
                    char currentCellReference = 'A';
                    char previousCellReference = (char)(currentCellReference - 1);
                    foreach (Cell cell in row)
                    {
                        var cellValue = GetCellValue(cell, workbookPart).Trim();
                        currentCellReference = char.ToUpper(cell.CellReference.InnerText[0]);
                        int cellDiferrence = currentCellReference - previousCellReference;
                        if (cellDiferrence == 1)
                        {
                            dataTable.Columns.Add(new DataColumn(cellValue));
                            cellIndex++;
                        }
                        else
                        {
                            for (int i = 1; i < cellDiferrence; i++)
                            {
                                dataTable.Columns.Add(new DataColumn($"ColumnNo.{cellIndex + i - 1}"));
                            }
                            dataTable.Columns.Add(new DataColumn(cellValue));
                            cellIndex += cellDiferrence;
                        }
                        previousCellReference = currentCellReference;
                    }
                }
                else
                {
                    var dataRow = dataTable.NewRow();
                    int cellIndex = 0;
                    char currentCellReference = 'A';
                    char previousCellReference = (char)(currentCellReference - 1);
                    foreach (Cell cell in row)
                    {
                        var cellValue = GetCellValue(cell, workbookPart).Trim();

                        currentCellReference = char.ToUpper(cell.CellReference.InnerText[0]);
                        int cellDiferrence = currentCellReference - previousCellReference;
                        if (cellDiferrence == 1 && cellIndex <= dataTable.Columns.Count)
                        {
                            dataRow[dataTable.Columns[cellIndex]] = cellValue;
                            cellIndex++;
                        }
                        else
                        {
                            for (int i = 1; i < cellDiferrence; i++)
                            {
                                if (cellIndex + i <= dataTable.Columns.Count)
                                {
                                    dataRow[dataTable.Columns[cellIndex + i - 1]] = string.Empty;
                                }
                            }
                            cellIndex += cellDiferrence;
                            if (cellIndex <= dataTable.Columns.Count)
                            {
                                dataRow[dataTable.Columns[cellIndex - 1]] = cellValue;
                            }
                        }
                        previousCellReference = currentCellReference;
                    }
                    dataTable.Rows.Add(dataRow);
                }
                rowIndex++;
            }
            return dataTable;
        }
        public static async Task<List<string>> GetExcelColumnsAsync(IFormFile file)
        {
            DataTable dataTable = new DataTable();
            MemoryStream
            stream = new MemoryStream();
            // The stream now contains the file contents
            await file.CopyToAsync(stream);

            if (stream.Length == 0)
            {
                throw new Exception("The stream is empty");
            }
            if (stream.Position != 0)
            {
                stream.Position = 0;
            }
            using SpreadsheetDocument doc = SpreadsheetDocument.Open(stream, false);
            WorkbookPart workbookPart = doc.WorkbookPart;
            Sheets sheets = workbookPart.Workbook.GetFirstChild<Sheets>();
            Sheet sheet = sheets.GetFirstChild<Sheet>();
            Worksheet worksheet = ((WorksheetPart)workbookPart.GetPartById(sheet.Id)).Worksheet;
            SheetData sheetData = worksheet.GetFirstChild<SheetData>();
            var firstRow = sheetData.GetFirstChild<Row>();
            int cellIndex = 1;
            char currentCellReference = 'A';
            char previousCellReference = (char)(currentCellReference - 1);
            foreach (Cell cell in firstRow)
            {
                var cellValue = GetCellValue(cell, workbookPart).Trim();
                currentCellReference = char.ToUpper(cell.CellReference.InnerText[0]);
                int cellDiferrence = currentCellReference - previousCellReference;
                if (cellDiferrence == 1)
                {
                    dataTable.Columns.Add(new DataColumn(cellValue));
                    cellIndex++;
                }
                else
                {
                    for (int i = 1; i < cellDiferrence; i++)
                    {
                        dataTable.Columns.Add(new DataColumn($"ColumnNo.{cellIndex + i - 1}"));
                    }
                    dataTable.Columns.Add(new DataColumn(cellValue));
                    cellIndex += cellDiferrence;
                }
                previousCellReference = currentCellReference;
            }

            List<string> excelColumns = new List<string>();
            foreach (DataColumn xlClmn in dataTable.Columns)
            {
                excelColumns.Add(xlClmn.ColumnName.Trim());
            }

            return excelColumns;
        }
        public static List<string> GetExcelColumns(MemoryStream stream)
        {
            DataTable dataTable = new DataTable();
            using SpreadsheetDocument doc = SpreadsheetDocument.Open(stream, false);
            WorkbookPart workbookPart = doc.WorkbookPart;
            Sheets sheets = workbookPart.Workbook.GetFirstChild<Sheets>();
            Sheet sheet = sheets.GetFirstChild<Sheet>();
            Worksheet worksheet = ((WorksheetPart)workbookPart.GetPartById(sheet.Id)).Worksheet;
            SheetData sheetData = worksheet.GetFirstChild<SheetData>();
            var firstRow = sheetData.GetFirstChild<Row>();
            int cellIndex = 1;
            char currentCellReference = 'A';
            char previousCellReference = (char)(currentCellReference - 1);
            foreach (Cell cell in firstRow)
            {
                var cellValue = GetCellValue(cell, workbookPart).Trim();
                currentCellReference = char.ToUpper(cell.CellReference.InnerText[0]);
                int cellDiferrence = currentCellReference - previousCellReference;
                if (cellDiferrence == 1)
                {
                    dataTable.Columns.Add(new DataColumn(cellValue));
                    cellIndex++;
                }
                else
                {
                    for (int i = 1; i < cellDiferrence; i++)
                    {
                        dataTable.Columns.Add(new DataColumn($"ColumnNo.{cellIndex + i - 1}"));
                    }
                    dataTable.Columns.Add(new DataColumn(cellValue));
                    cellIndex += cellDiferrence;
                }
                previousCellReference = currentCellReference;
            }

            List<string> excelColumns = new List<string>();
            foreach (DataColumn xlClmn in dataTable.Columns)
            {
                excelColumns.Add(xlClmn.ColumnName.Trim());
            }

            return excelColumns;
        }

        public static MemoryStream CreateExcelFromList<T>(List<T> items)
        {
            using MemoryStream stream = new();
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook);
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();
            workbookpart.Workbook = new Workbook();
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet(new SheetData());
            Sheets? sheets = spreadsheetDocument?.WorkbookPart?.Workbook.AppendChild(new Sheets());
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument?.WorkbookPart?.GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = "Report"
            };
            sheets?.Append(sheet);
            Worksheet worksheet = worksheetPart.Worksheet;
            SheetData? sheetData = worksheet?.GetFirstChild<SheetData>();
            PropertyInfo[] properties = typeof(T).GetProperties();
            Row firstRow = new Row();
            foreach (PropertyInfo property in properties)
            {
                Cell cell1 = new()
                {
                    CellValue = new CellValue(property.Name),
                    DataType = CellValues.String,
                };
                firstRow.Append(cell1);
            }
            sheetData?.Append(firstRow);
            foreach (var item in items)
            {
                var row = new Row();
                foreach (PropertyInfo property in properties)
                {
                    Cell cell = new()
                    {
                        CellValue = new CellValue(property?.GetValue(item)?.ToString() ?? string.Empty),
                        DataType = property?.GetValue(item)?.GetType() == typeof(int) || property?.GetValue(item)?.GetType() == typeof(long) || property?.GetValue(item)?.GetType() == typeof(double) ? CellValues.Number : property?.GetValue(item)?.GetType() == typeof(DateTime) ? CellValues.Date : CellValues.String,
                    };
                    row.Append(cell);
                }
                sheetData?.Append(row);
            }
            worksheetPart.Worksheet.Save();
            workbookpart.Workbook.Save();
            spreadsheetDocument?.Close();
            return stream;
        }
        public static byte[] CreateExcelFromListAsByteArray<T>(List<T> items)
        {
            using MemoryStream stream = new();
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook);
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();
            workbookpart.Workbook = new Workbook();
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet(new SheetData());
            Sheets? sheets = spreadsheetDocument?.WorkbookPart?.Workbook.AppendChild(new Sheets());
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument?.WorkbookPart?.GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = "Report"
            };
            sheets?.Append(sheet);
            Worksheet worksheet = worksheetPart.Worksheet;
            SheetData? sheetData = worksheet?.GetFirstChild<SheetData>();
            PropertyInfo[] properties = typeof(T).GetProperties();
            Row firstRow = new Row();
            foreach (PropertyInfo property in properties)
            {
                Cell cell1 = new()
                {
                    CellValue = new CellValue(property.Name),
                    DataType = CellValues.String,
                };
                firstRow.Append(cell1);
            }
            sheetData?.Append(firstRow);
            foreach (var item in items)
            {
                var row = new Row();
                foreach (PropertyInfo property in properties)
                {
                    Cell cell = new()
                    {
                        CellValue = new CellValue(property?.GetValue(item)?.ToString() ?? string.Empty),
                        DataType = property?.GetValue(item)?.GetType() == typeof(int) || property?.GetValue(item)?.GetType() == typeof(long) || property?.GetValue(item)?.GetType() == typeof(double) ? CellValues.Number : property?.GetValue(item)?.GetType() == typeof(DateTime) ? CellValues.Date : CellValues.String,
                    };
                    row.Append(cell);
                }
                sheetData?.Append(row);
            }
            worksheetPart.Worksheet.Save();
            workbookpart.Workbook.Save();
            spreadsheetDocument?.Close();
            return stream.ToArray();
        }
        public static MemoryStream CreateExcelFromList<T>(List<T> items, List<string> mappingProperties, List<string> unMappingProperties)
        {
            using MemoryStream stream = new();
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook);
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();
            workbookpart.Workbook = new Workbook();
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet(new SheetData());
            Sheets? sheets = spreadsheetDocument?.WorkbookPart?.Workbook.AppendChild(new Sheets());
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument?.WorkbookPart?.GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = "Report"
            };
            sheets?.Append(sheet);
            Worksheet worksheet = worksheetPart.Worksheet;
            SheetData? sheetData = worksheet?.GetFirstChild<SheetData>();
            PropertyInfo[] properties = (typeof(T).GetProperties());
            if (mappingProperties.Any())
            {
                properties = properties.Where(i => mappingProperties.Contains(i.Name)).ToArray();
            }
            if (unMappingProperties.Any())
            {
                properties = properties.Where(i => !unMappingProperties.Contains(i.Name)).ToArray();
            }
            Row firstRow = new Row();
            foreach (PropertyInfo property in properties)
            {
                Cell cell1 = new()
                {
                    CellValue = new CellValue(property.Name),
                    DataType = CellValues.String,
                };
                firstRow.Append(cell1);
            }
            sheetData?.Append(firstRow);
            foreach (var item in items)
            {
                var row = new Row();
                foreach (PropertyInfo property in properties)
                {
                    if (property.PropertyType == typeof(DateTime?) || property.PropertyType == typeof(DateTime))
                    {
                        DateTime? value = (DateTime?)property.GetValue(item);
                        if (value != null)
                        {
                            property.SetValue(item, TimeZoneInfo.ConvertTimeFromUtc(value.Value, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")));
                        }
                    }
                    Cell cell = new()
                    {
                        CellValue = new CellValue(property?.GetValue(item)?.ToString() ?? string.Empty),
                        DataType = property?.GetValue(item)?.GetType() == typeof(int) || property?.GetValue(item)?.GetType() == typeof(long) || property?.GetValue(item)?.GetType() == typeof(double) ? CellValues.Number : property?.GetValue(item)?.GetType() == typeof(DateTime) ? CellValues.Date : CellValues.String,
                    };
                    row.Append(cell);
                }
                sheetData?.Append(row);
            }
            worksheetPart.Worksheet.Save();
            workbookpart.Workbook.Save();
            spreadsheetDocument?.Close();
            return stream;
        }
        public static MemoryStream CreateExcelFromListEPPlus<T>(List<T> items, List<string> mappingProperties, List<string> unMappingProperties)
        {
            MemoryStream stream = new MemoryStream();
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using (var package = new ExcelPackage(stream))
            {
                var worksheet = package.Workbook.Worksheets.Add("Report");
                PropertyInfo[] properties = (typeof(T).GetProperties());
                if (mappingProperties.Any())
                {
                    properties = properties.Where(i => mappingProperties.Contains(i.Name)).ToArray();
                }
                if (unMappingProperties.Any())
                {
                    properties = properties.Where(i => !unMappingProperties.Contains(i.Name)).ToArray();
                }

                for (int col = 0; col < properties.Length; col++)
                {
                    worksheet.Cells[1, col + 1].Value = properties[col].Name.SplitCamelCase();
                }
                for (int row = 0; row < items.Count; row++)
                {
                    for (int col = 0; col < properties.Length; col++)
                    {
                        var cell = worksheet.Cells[row + 2, col + 1];
                        var value = properties[col].GetValue(items[row]);
                        var property = properties[col];
                        if ((property.PropertyType == typeof(DateTime?) || property.PropertyType == typeof(DateTime)) && value != null)
                        {
                            value = TimeZoneInfo.ConvertTimeFromUtc((DateTime)value, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")).ToString("MM/dd/yyyy HH:mm:ss");
                        }
                        cell.Value = value;

                    }
                }

                package.Save();
            }
            return stream;
        }
        private static string GetCellValue(Cell currentCell, WorkbookPart workbookPart)
        {
            if (currentCell.DataType != null && currentCell.DataType == CellValues.SharedString)
            {
                int id;
                if (int.TryParse(currentCell.InnerText, out id))
                {
                    SharedStringItem item = workbookPart.SharedStringTablePart.SharedStringTable.Elements<SharedStringItem>().ElementAt(id);
                    if (item.Text != null) return item.Text.Text;
                    else return "";
                }
            }
            return currentCell.InnerText;
        }
        public static string SplitCamelCase(this string input)
        {
            return Regex.Replace(input, "(?<=[a-z])([A-Z])", " $1", RegexOptions.Compiled).Trim();
        }


        public static MemoryStream CreateExcelFromDictionaryEPPlus(List<Dictionary<string, object>> items, List<string> headers)
        {
            MemoryStream stream = new MemoryStream();
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using (var package = new ExcelPackage(stream))
            {
                var workbook = package.Workbook;
                var worksheet = workbook.Worksheets.Add("Data");

                // Add headers
                int column = 1;
                foreach (var header in headers)
                {
                    worksheet.Cells[1, column].Value = header;
                    column++;
                }

                // Add data
                int row = 2;
                foreach (var rowData in items)
                {
                    column = 1;
                    foreach (var header in headers)
                    {
                        // Check if the header exists as a key in the rowData dictionary
                        if (rowData.ContainsKey(header))
                        {
                            worksheet.Cells[row, column].Value = rowData[header];
                        }
                        else
                        {
                            worksheet.Cells[row, column].Value = string.Empty;
                        }
                        column++;
                    }
                    row++;
                }

                // Save the file
                package.Save();
            }
            return stream;
        }

        private static int FormatHeaderRow(ExcelWorksheet ws, List<string> headers, FiltersDto? filtersDto, ExportTrackerDto exportTrackerDto, string reportName, List<Dictionary<string, int>> displynameAndCount)
        {
            int headerRowCount = AddFilters(ws, filtersDto, exportTrackerDto);

            AddHeader(ws, $"A${headerRowCount + 1}", reportName, 16, ExcelHorizontalAlignment.Center, true, 30);
            int column = 1;



            foreach (var header in headers.ToList())
            {
                if (header != null)
                {
                    if (header == "All")
                    {
                        var index = headers.IndexOf(header);
                        if (index != -1)
                        {
                            headers[index] = "All (Active)";
                        }
                    }
                    else if (header == "Active")
                    {
                        headers.Remove(header);
                        continue;
                    }

                    var count = 0;
                    var displayName = string.Empty;

                    var item = displynameAndCount.FirstOrDefault(d => d.TryGetValue(header, out count));
                    if (item != null)
                    {
                        displayName = item.First().Key;
                    }

                    FormatMergedCell(displayName, count);

                    void FormatMergedCell(string header, int count)
                    {
                        header = header?.Trim();

                        if (count > 0)
                        {
                            var mergedCell = ws.Cells[headerRowCount + 2, column, headerRowCount + 2, column + count - 1];
                            mergedCell.Merge = true;

                            mergedCell.Value = header;
                            mergedCell.Style.Fill.PatternType = ExcelFillStyle.Solid;
                            mergedCell.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
                            mergedCell.Style.Font.Color.SetColor(System.Drawing.Color.White);
                            mergedCell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            ApplyCellBorderStyle(mergedCell, ExcelBorderStyle.Thin, System.Drawing.Color.White);
                            var totalRow = ws.Cells[headerRowCount + 2, ws.Dimension.Start.Column, headerRowCount + 2, ws.Dimension.End.Column];
                            totalRow.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        }
                        else if (!string.IsNullOrEmpty(header))
                        {
                            var cell = ws.Cells[headerRowCount + 3, column];
                            cell.Value = header;
                            cell.Style.Fill.PatternType = ExcelFillStyle.Solid;
                            cell.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
                            cell.Style.Font.Color.SetColor(System.Drawing.Color.White);
                            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            ApplyCellBorderStyle(cell, ExcelBorderStyle.Thin, System.Drawing.Color.White);
                            column++;
                        }

                    }

                    if (string.IsNullOrEmpty(displayName))
                    {
                        var cell = ws.Cells[headerRowCount + 3, column];
                        cell.Value = header;
                        cell.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        cell.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
                        cell.Style.Font.Color.SetColor(System.Drawing.Color.White);
                        ApplyCellBorderStyle(cell, ExcelBorderStyle.Thin, System.Drawing.Color.White);
                        cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                        column++;
                    }
                }
            }
            int lastColumn = column - 1;
            var headerRange = ws.Cells[headerRowCount + 1, 1, headerRowCount + 1, lastColumn];
            headerRange.Merge = true;
            ApplyCellBorderStyle(ws.Cells[headerRowCount + 1, 1, headerRowCount + 1, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.Black);
            ws.Cells[ws.Dimension.Address].AutoFitColumns();
            return headerRowCount + 2;
        }


        private static void AddHeader(ExcelWorksheet ws, string cellAddress, string text, int fontSize, ExcelHorizontalAlignment alignment, bool isBold, int rowHeight)
        {
            var cell = ws.Cells[cellAddress];
            cell.Value = text;
            cell.Style.Font.Size = fontSize;
            cell.Style.Font.Bold = isBold;
            cell.Style.HorizontalAlignment = alignment;

            ws.Row(cell.Start.Row).Height = rowHeight;

            cell.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
        }
        private static int AddFilters(ExcelWorksheet ws, FiltersDto? filtersDto, ExportTrackerDto exportTrackerDto)
        {

            int startRow = AddExportTracker(ws, exportTrackerDto, 1);
            var filtersRow = startRow + 1;


            var properties = typeof(FiltersDto).GetProperties();

            for (int i = 0; i < properties.Length; i++)
            {
                var property = properties[i];
                var value = property.GetValue(filtersDto);

                ws.Cells[filtersRow + i, 1].Value = SplitCamelCase(property.Name) + ":";
                if (property.Name == "FromDate" && value is DateTime fromDate)
                {
                    ws.Cells[filtersRow + i, 2].Value = fromDate.ToString("dd-MM-yyyy");
                }
                else if (property.Name == "ToDate" && value is DateTime todate)
                {
                    ws.Cells[filtersRow + i, 2].Value = todate.ToString("dd-MM-yyyy");
                }
                else if (value != null)
                {
                    ws.Cells[filtersRow + i, 2].Value = value.ToString();
                }
                else
                {
                    ws.Cells[filtersRow + i, 2].Value = "N/A";
                }
            }
            var filterRange = ws.Cells[filtersRow, 1, filtersRow + properties.Length - 1, 2];
            var filterStyle = filterRange.Style;
            filterStyle.Font.Bold = true;
            filterStyle.Fill.PatternType = ExcelFillStyle.Solid;
            filterStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            ApplyCellBorderStyle(filterRange, ExcelBorderStyle.Thin, System.Drawing.Color.Black);

            return properties.Length + startRow;
        }
        private static void ApplyCellBorderStyle(ExcelRangeBase range, ExcelBorderStyle borderStyle, System.Drawing.Color borderColor)
        {
            range.Style.Border.Left.Style = borderStyle;
            range.Style.Border.Left.Color.SetColor(borderColor);
            range.Style.Border.Top.Style = borderStyle;
            range.Style.Border.Top.Color.SetColor(borderColor);
            range.Style.Border.Bottom.Style = borderStyle;
            range.Style.Border.Bottom.Color.SetColor(borderColor);
            range.Style.Border.Right.Style = borderStyle;
            range.Style.Border.Right.Color.SetColor(borderColor);
        }
        private static int AddExportTracker(ExcelWorksheet ws, ExportTrackerDto exportTrackerDto, int startRow)
        {
            var filtersRow = startRow;
            var properties = typeof(ExportTrackerDto).GetProperties();

            for (int i = 0; i < properties.Length; i++)
            {
                var property = properties[i];
                var value = property.GetValue(exportTrackerDto);
                ws.Cells[filtersRow + i, 1].Value = SplitCamelCase(property.Name) + ":";

                if (property.Name == "Type" && value != null)
                {
                    ws.Cells[filtersRow + i, 2].Value = GetReportType(value.ToString());
                }
                else if (property.Name == "CreatedOn" && value is DateTime createdOn)
                {
                    ws.Cells[filtersRow + i, 2].Value = createdOn.ToString("dd-MM-yyyy");
                }
                else if (value != null)
                {
                    ws.Cells[filtersRow + i, 2].Value = value.ToString();
                }
                else
                {
                    ws.Cells[filtersRow + i, 2].Value = "N/A";
                }
            }

            var filterRange = ws.Cells[filtersRow, 1, filtersRow + properties.Length - 1, 2];
            var filterStyle = filterRange.Style;
            filterStyle.Font.Bold = true;
            filterStyle.Fill.PatternType = ExcelFillStyle.Solid;
            filterStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            ApplyCellBorderStyle(filterRange, ExcelBorderStyle.Thin, System.Drawing.Color.Black);

            return properties.Length;
        }
        private static string GetReportType(string name)
        {
            var reportTypes = new Dictionary<string, string>()
            {
                {"substatusreportbyuser","Sub Status Report By User" },
                {"substatusreportbysubsource","Sub Status Report By Subsource"},
                {"projectreportbysubstatus","Project Report By Substatus" },
            };
            return reportTypes[name];
        }

        public static byte[] GenerateExcel(List<ModifiedAllReportDto> modifiedSubStatusReportDtos, List<string> headers, FiltersDto filtersDto, ExportTrackerDto? exportTrackerDto, string reportName, List<Dictionary<string, int>> displynameAndCount)
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using MemoryStream memoryStream = new();
            using var package = new ExcelPackage(memoryStream);
            var ws = package.Workbook.Worksheets.Add("Sheet");
            FormatHeaderRow(ws, headers, filtersDto, exportTrackerDto, reportName, displynameAndCount);
            PopulateDataRows(ws, modifiedSubStatusReportDtos, headers, filtersDto, exportTrackerDto, reportName, displynameAndCount);
            CalculateColumnSum(ws, modifiedSubStatusReportDtos, headers, filtersDto, exportTrackerDto, reportName, displynameAndCount);
            package.Save();
            ws.Cells[ws.Dimension.Address].AutoFitColumns();
            return memoryStream.ToArray();
        }
        private static void CalculateColumnSum(ExcelWorksheet ws, List<ModifiedAllReportDto> items, List<string> headers, FiltersDto filtersDto, ExportTrackerDto exportTrackerDto, string reportName, List<Dictionary<string, int>> displynamecount)
        {
            int headerRowCount = FormatHeaderRow(ws, headers, filtersDto, exportTrackerDto, reportName, displynamecount);

            int startRow = headerRowCount + 2;
            int lastRow = startRow + items.Count - 1;
            int column = 1;

            foreach (var header in headers)
            {
                bool skipHeader = displynamecount.Any(displayCount => displayCount.TryGetValue(header, out var count) && count > 0);

                if (skipHeader)
                {
                    continue;
                }

                double sum = 0;

                for (int row = startRow; row <= lastRow; row++)
                {
                    object cellValue = ws.Cells[row, column].Value;

                    if (cellValue != null && double.TryParse(cellValue.ToString(), out double numericValue))
                    {
                        sum += numericValue;
                    }
                }

                string headerText = headers[column - 1];
                if (headerText != "User Name" && headerText != "Project Title" && headerText != "Sub Source")
                {
                    if (headerText == "Sl No")
                    {
                        ws.Cells[lastRow + 1, column].Value = "Total";
                    }
                    else if (headerText == "All (Active)")
                    {
                        int allValue = items.Sum(item => Convert.ToInt32(item.BaseStatusWithSubStatusCount?.GetValueOrDefault("All") ?? 0));
                        int activeValue = items.Sum(item => Convert.ToInt32(item.BaseStatusWithSubStatusCount?.GetValueOrDefault("Active") ?? 0));

                        ws.Cells[lastRow + 1, column].Value = $"{allValue} ({activeValue})";
                        ws.Cells[lastRow + 1, column].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    }
                    else
                    {
                        ws.Cells[lastRow + 1, column].Value = sum;
                    }
                }
                ws.Cells[lastRow + 1, column].Style.Font.Bold = true;
                ws.Cells[lastRow + 1, column].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                ws.Cells[lastRow + 1, column].Style.Fill.PatternType = ExcelFillStyle.Solid;
                ws.Cells[lastRow + 1, column].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Yellow);
                ApplyCellBorderStyle(ws.Cells[lastRow + 1, column], ExcelBorderStyle.Thin, System.Drawing.Color.Black);

                column++;
            }
        }

        private static void PopulateDataRows(ExcelWorksheet ws, List<ModifiedAllReportDto> modifiedSubStatusReportDtos, List<string> headers, FiltersDto filtersDto, ExportTrackerDto exportTrackerDto, string reportName, List<Dictionary<string, int>> displynameAndCount)
        {
            int headerRowCount = FormatHeaderRow(ws, headers, filtersDto, exportTrackerDto, reportName, displynameAndCount);

            int startRow = headerRowCount + 2;

            foreach (var item in modifiedSubStatusReportDtos)
            {
                int column = 1;

                foreach (var header in headers)
                {
                    bool skipHeader = displynameAndCount.Any(displayCount => displayCount.ContainsKey(header) && displayCount[header] > 0);

                    if (skipHeader)
                    {
                        continue;
                    }

                    string displayedValue = "0";

                    if (header == "All (Active)")
                    {
                        int allValue = item.BaseStatusWithSubStatusCount != null && item.BaseStatusWithSubStatusCount.ContainsKey("All") ? Convert.ToInt32(item.BaseStatusWithSubStatusCount["All"]) : 0;
                        int activeVal = item.BaseStatusWithSubStatusCount != null && item.BaseStatusWithSubStatusCount.ContainsKey("Active") ? Convert.ToInt32(item.BaseStatusWithSubStatusCount["Active"]) : 0;
                        displayedValue = $"{allValue} ({activeVal})";
                    }
                    else
                    {

                        var value = item.BaseStatusWithSubStatusCount != null && item.BaseStatusWithSubStatusCount.ContainsKey(header) ? item.BaseStatusWithSubStatusCount[header] : null;
                        if (value is Dictionary<string, int> res)
                        {
                            var result = res.Select(i => i.Value).Sum(j => j);
                            value = result;
                        }
                        if (value == null)
                        {
                            if (item.BaseStatusWithSubStatusCount != null)
                            {
                                foreach (var item1 in item.BaseStatusWithSubStatusCount.Select(i => i.Value).ToList())
                                {
                                    var result = item1 as Dictionary<string, int>;

                                    if (result != null && result.ContainsKey(header))
                                    {
                                        value = result[header];
                                    }
                                }
                            }

                        }
                        displayedValue = value?.ToString() ?? "0";
                    }

                    ws.Cells[startRow, column].Value = displayedValue;
                    ws.Cells[startRow, column].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    ApplyCellBorderStyle(ws.Cells[startRow, column], ExcelBorderStyle.Thin, System.Drawing.Color.Black);

                    column++;
                }

                startRow++;
            }
        }
    }

    public static class ActivityExcelHelper<T>
    {
        private static List<string> mergedCells = new();

        public static byte[] GenerateExcel(List<T> dtos, string reportName, FiltersDto? filtersDto, ExportTrackerDto exportTrackerDto, List<string>? flags, string? timeZoneId, TimeSpan baseUTcOffset)
        {

            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using MemoryStream memoryStream = new();

            using var package = new ExcelPackage(memoryStream);
            var ws = package.Workbook.Worksheets.Add("Sheet");

            var properties = typeof(T).GetProperties();
            var value = FormatHeaderRow(ws, properties, filtersDto, exportTrackerDto, reportName, flags);
            PopulateDataRows(ws, dtos, properties, filtersDto, exportTrackerDto, reportName, value, flags);
            CalculateColumnSum(ws, dtos, properties, filtersDto, exportTrackerDto, reportName, value, flags);
            ws.Cells[ws.Dimension.Address].AutoFitColumns();

            package.Save();

            return memoryStream.ToArray();
        }
        public static int FormatHeaderRow(ExcelWorksheet ws, PropertyInfo[] properties, FiltersDto filters, ExportTrackerDto exportTrackerDto, string reportName, List<string>? flags)
        {
            bool hasComplexProperty = properties.Any(IsComplexProperty);

            if (hasComplexProperty)
            {
                return FormatHeaderRowWithComplexProperties(ws, properties, filters, exportTrackerDto, reportName, flags);
            }
            else
            {
                return FormatHeaderRowWithSimpleProperties(ws, properties, filters, exportTrackerDto, reportName);
            }
        }
        private static int FormatHeaderRowWithComplexProperties(ExcelWorksheet ws, PropertyInfo[] properties, FiltersDto filters, ExportTrackerDto exportTrackerDto, string reportName, List<string>? flags)
        {
            mergedCells.Clear();
            var headerNames = new List<string>();
            var subHeaderNames = new List<string>();
            int headerRowCount = AddFilters(ws, filters, exportTrackerDto);
            AddHeader(ws, $"A${headerRowCount + 1}", reportName, 16, ExcelHorizontalAlignment.Center, true, 30);

            foreach (var property in properties)
            {

                if (IsSimpleProperty(property))
                {
                    if (property.Name != "Active")
                    {
                        headerNames.Add("");
                    }
                    if (property.Name == "All")
                    {
                        subHeaderNames.Add("All (Active)");
                    }
                    else if (property.Name == "Active")
                    {
                        subHeaderNames.Remove("Active");
                    }
                    else
                    {
                        subHeaderNames.Add(SplitCamelCase(property.Name));
                    }
                }
                else if (IsComplexProperty(property))
                {
                    if (property.Name.Equals("flags", StringComparison.InvariantCultureIgnoreCase))
                    {
                        if (flags?.Any() ?? false)
                        {
                            mergedCells.Add($"{ColumnLetter(subHeaderNames.Count + 1)}${headerRowCount + 2}:{ColumnLetter(subHeaderNames.Count + flags?.Count ?? 0)}${headerRowCount + 2}");
                            if (!string.IsNullOrEmpty(property.Name))
                            {
                                subHeaderNames.Remove(property.Name);
                                headerNames.Add(property.Name);
                                headerNames.Add("");
                            }
                            foreach (var subProperty in flags)
                            {
                                if (!string.IsNullOrEmpty(subProperty))
                                {
                                    subHeaderNames.Add(SplitCamelCase(subProperty));
                                }
                            }
                        }
                    }
                    else
                    {
                        var subProperties = property.PropertyType.GetProperties();
                        int subPropertyCount = CountSubProperties(property);
                        mergedCells.Add($"{ColumnLetter(headerNames.Count + 1)}${headerRowCount + 2}:{ColumnLetter(headerNames.Count + subPropertyCount)}${headerRowCount + 2}");

                        for (int i = 0; i < subPropertyCount; i++)
                        {
                            headerNames.Add(SplitCamelCase(property.Name));
                        }

                        foreach (var subProperty in subProperties)
                        {
                            if (IsSimpleProperty(subProperty))
                            {
                                subHeaderNames.Add(SplitCamelCase(subProperty.Name));
                            }
                        }
                    }
                }
                else
                {
                    headerNames.Add("");
                    subHeaderNames.Add(SplitCamelCase(property.Name));
                }
            }

            foreach (var mergedCell in mergedCells)
            {
                ws.Cells[mergedCell].Merge = true;
            }

            //string headerRange = $"A${headerRowCount + 2}:{ColumnLetter(headerNames.Count)}${headerRowCount + 3}";
            //ws.Cells[$"A${headerRowCount + 1}:{ColumnLetter(headerNames.Count)}${headerRowCount + 1}"].Merge = true;
            //ws.Cells[headerRange].LoadFromArrays(new List<object[]> { headerNames.ToArray(), subHeaderNames.ToArray() });

            string headerRange = $"A${headerRowCount + 2}:{ColumnLetter((subHeaderNames.Count))}${headerRowCount + 3}";
            ws.Cells[$"A${headerRowCount + 1}:{ColumnLetter(subHeaderNames.Count)}${headerRowCount + 1}"].Merge = true;
            ws.Cells[headerRange].LoadFromArrays(new List<object[]> { headerNames.ToArray(), subHeaderNames.ToArray() });

            var headerStyle = ws.Cells[headerRange].Style;
            headerStyle.Fill.PatternType = ExcelFillStyle.Solid;
            headerStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
            headerStyle.Font.Size = 12;
            headerStyle.Font.Color.SetColor(System.Drawing.Color.White);
            headerStyle.HorizontalAlignment = ExcelHorizontalAlignment.Center;

            ApplyCellBorderStyle(ws.Cells[headerRowCount + 2, 1, headerRowCount + 3, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.White);
            ApplyCellBorderStyle(ws.Cells[headerRowCount + 1, 1, headerRowCount + 1, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.Black);

            return headerRowCount + 4;
        }

        private static int FormatHeaderRowWithSimpleProperties(ExcelWorksheet ws, PropertyInfo[] properties, FiltersDto filters, ExportTrackerDto exportTrackerDto, string reportName)
        {
            var headerNames = new List<string>();
            int headerRowCount = AddFilters(ws, filters, exportTrackerDto);

            AddHeader(ws, $"A${headerRowCount + 1}", reportName, 16, ExcelHorizontalAlignment.Center, true, 30);

            foreach (var property in properties)
            {
                if (property.Name != "Active")
                {

                    if (property.Name == "All")
                    {
                        headerNames.Add("All (Active)");
                    }
                    else if (property.Name == "Active")
                    {
                        headerNames.Remove("Active");

                    }

                    else
                    {
                        headerNames.Add(SplitCamelCase(property.Name));
                    }
                }
            }
            string headerRange = $"A${headerRowCount + 2}:{ColumnLetter(headerNames.Count)}{headerRowCount + 2}";

            ws.Cells[$"A${headerRowCount + 1}:{ColumnLetter(headerNames.Count)}${headerRowCount + 1}"].Merge = true;

            ws.Cells[headerRange].LoadFromArrays(new List<object[]> { headerNames.ToArray() });

            var headerStyle = ws.Cells[headerRange].Style;
            headerStyle.Fill.PatternType = ExcelFillStyle.Solid;
            headerStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
            headerStyle.Font.Size = 12;
            headerStyle.Font.Color.SetColor(System.Drawing.Color.White);
            headerStyle.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            ApplyCellBorderStyle(ws.Cells[headerRowCount + 2, 1, headerRowCount + 2, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.White);
            ApplyCellBorderStyle(ws.Cells[headerRowCount + 1, 1, headerRowCount + 1, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.Black);
            return headerRowCount + 3;

        }

        private static void AddHeader(ExcelWorksheet ws, string cellAddress, string text, int fontSize, ExcelHorizontalAlignment alignment, bool isBold, int rowHeight)
        {
            var cell = ws.Cells[cellAddress];
            cell.Value = text;
            cell.Style.Font.Size = fontSize;
            cell.Style.Font.Bold = isBold;
            cell.Style.HorizontalAlignment = alignment;

            ws.Row(cell.Start.Row).Height = rowHeight;

            cell.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
        }

        private static void ApplyCellBorderStyle(ExcelRangeBase range, ExcelBorderStyle borderStyle, System.Drawing.Color borderColor)
        {
            range.Style.Border.Left.Style = borderStyle;
            range.Style.Border.Left.Color.SetColor(borderColor);
            range.Style.Border.Top.Style = borderStyle;
            range.Style.Border.Top.Color.SetColor(borderColor);
            range.Style.Border.Bottom.Style = borderStyle;
            range.Style.Border.Bottom.Color.SetColor(borderColor);
            range.Style.Border.Right.Style = borderStyle;
            range.Style.Border.Right.Color.SetColor(borderColor);
        }
        private static void PopulateDataRows(ExcelWorksheet ws, List<T> data, PropertyInfo[] properties, FiltersDto filters, ExportTrackerDto exportTrackerDto, string reportName, int? headerRowCount, List<string>? flags)
        {
            //int headerRowCount = FormatHeaderRow(ws, properties, filters, exportTrackerDto, reportName);
            int row = headerRowCount ?? 0;
            int id = 1;

            foreach (var item in data)
            {
                int col = 1;

                ws.Cells[row, col].Value = id;
                var cell1 = ws.Cells[row, col];
                cell1.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                ApplyCellBorderStyle(cell1, ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                id++;

                foreach (var property in properties)
                {
                    if (property.Name != "Active")
                    {
                        var value = property.GetValue(item);

                        if (value != null)
                        {
                            if (IsComplexProperty(property))
                            {
                                if (property.Name.Equals("flags", StringComparison.InvariantCultureIgnoreCase))
                                {
                                    PropertyInfo? dataProperty = item?.GetType()?.GetProperty("Flags");
                                    if (flags?.Any() ?? false)
                                    {
                                        foreach (var subHeader in flags)
                                        {
                                            var dataValue = dataProperty?.GetValue(item);
                                            if (dataProperty != null && dataValue != null)
                                            {
                                                var matchingItem = FindItemByPropertyValue(dataValue, "Name", subHeader);
                                                var newValue = GetCountFromDynamicObject(matchingItem);
                                                var uniqueValue = GetUniqueLeadsCountFromDynamicObject(matchingItem);
                                                var cell = ws.Cells[row, col];
                                                cell.Value = $"{newValue.ToString()} ({uniqueValue.ToString()})";
                                                cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                                                ApplyCellBorderStyle(cell, ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                                                col++;
                                            }
                                            else
                                            {
                                                var cell = ws.Cells[row, col];
                                                cell.Value = "0";
                                                cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                                                ApplyCellBorderStyle(cell, ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                                                col++;
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    if (property.Name == "Callback" || property.Name == "MeetingScheduled" || property.Name == "SiteVisitScheduled" || property.Name == "NotInterested" || property.Name == "Dropped" || property.Name == "MeetingStatus" || property.Name == "SiteVisitStatus"
                                        || property.Name == "Incoming" || property.Name == "Outgoing")
                                    {
                                        PopulateComplexProperty(ws, row, col, value, property.Name);
                                        col++;
                                    }
                                    else
                                    {
                                        var cell = ws.Cells[row, col];
                                        cell.Value = property.Name;
                                        cell.Style.Font.Bold = true;
                                        cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                                        ApplyCellBorderStyle(cell, ExcelBorderStyle.Thin, System.Drawing.Color.Black);

                                        col++;
                                    }
                                }
                            }
                            else
                            {
                                var cell = ws.Cells[row, col];

                                if (property.Name == "All")
                                {
                                    var activeProperty = typeof(T).GetProperty("Active");
                                    var activeValue = activeProperty.GetValue(item);
                                    if (activeValue != null)
                                    {
                                        cell.Value = $"{value} ({activeValue})";
                                    }
                                }

                                else
                                {
                                    cell.Value = value.ToString();
                                }

                                cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                                ApplyCellBorderStyle(cell, ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                                col++;
                            }
                        }
                        else
                        {
                            col++;
                        }
                    }
                }

                row++;
            }
        }
        static object? FindItemByPropertyValue(object collection, string propertyName, string propertyValue)
        {
            var result = ((IEnumerable<object>)collection)
                ?.Where(item => GetPropertyValue<string>(item, propertyName) == propertyValue)?.ToList();
            return result?.FirstOrDefault() ?? default;
        }
        static T GetPropertyValue<T>(object obj, string propertyName)
        {
            try
            {
                Type type = obj.GetType();
                PropertyInfo? property = type.GetProperty(propertyName);

                if (property != null)
                {
                    object value = property.GetValue(obj);
                    if (value is T)
                    {
                        return (T)value;
                    }
                }
            }
            catch (Exception ex) { }
            return default;
        }
        static long GetCountFromDynamicObject(object? dynamicObject)
        {
            PropertyInfo? countProperty = dynamicObject?.GetType().GetProperty("Count");
            if (countProperty != null)
            {
                object? countValue = countProperty.GetValue(dynamicObject);
                if (countValue != null && countValue is long)
                {
                    return (long)countValue;
                }
            }
            return 0;
        }
        static long GetUniqueLeadsCountFromDynamicObject(object? dynamicObject)
        {
            PropertyInfo? countProperty = dynamicObject?.GetType().GetProperty("UniqueLeadsCount");
            if (countProperty != null)
            {
                object? countValue = countProperty.GetValue(dynamicObject);
                if (countValue != null && countValue is long)
                {
                    return (long)countValue;
                }
            }
            return 0;
        }
        private static void PopulateComplexProperty(ExcelWorksheet ws, int row, int col, object value, string propertyName)
        {
            var subProperties = value.GetType().GetProperties();


            foreach (var subProperty in subProperties)
            {
                var subValue = subProperty.GetValue(value);
                var cell = ws.Cells[row, col];
                cell.Value = subValue;
                cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                ApplyCellBorderStyle(cell, ExcelBorderStyle.Thin, System.Drawing.Color.Black);


                var cellStyle = cell.Style;
                cellStyle.Fill.PatternType = ExcelFillStyle.Solid;

                if (propertyName == "Callback")
                {

                    cellStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.FromArgb(255, 255, 204, 255));
                }
                else if (propertyName == "MeetingScheduled")
                {

                    cellStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.SkyBlue);
                }
                else if (propertyName == "Dropped")
                {

                    cellStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.FromArgb(173, 216, 230));
                }
                else if (propertyName == "SiteVisitScheduled")
                {

                    cellStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.FromArgb(204, 255, 204));
                }
                else if (propertyName == "NotInterested")
                {

                    cellStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.FromArgb(211, 211, 211));
                }
                else if (propertyName == "MeetingStatus")
                {

                    cellStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.FromArgb(255, 228, 225));
                }
                else if (propertyName == "SiteVisitStatus")
                {

                    cellStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.FromArgb(230, 230, 250));
                }
                else
                {
                    cellStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.FromArgb(255, 255, 255));
                }

                col++;
            }
        }

        private static int CountSubProperties(PropertyInfo property)
        {
            var subProperties = property.PropertyType.GetProperties();
            return subProperties.Length;
        }

        private static void PopulateSubProperties(ExcelWorksheet ws, int row, int col, object value)
        {
            var subProperties = value.GetType().GetProperties();

            foreach (var subProperty in subProperties)
            {
                var subValue = subProperty.GetValue(value);
                ws.Cells[row, col].Value = subValue;
                col++;
            }
        }
        private static string ColumnLetter(int columnNumber)
        {
            int dividend = columnNumber;
            string columnLetter = String.Empty;

            while (dividend > 0)
            {
                int modulo = (dividend - 1) % 26;
                columnLetter = Convert.ToChar('A' + modulo) + columnLetter;
                dividend = (dividend - modulo) / 26;
            }

            return columnLetter;
        }


        private static string SplitCamelCase(string input)
        {
            return Regex.Replace(input, "(?<=[a-z])([A-Z])", " $1", RegexOptions.Compiled).Trim();
        }

        private static bool IsSimpleProperty(PropertyInfo property)
        {
            Type propertyType = property.PropertyType;

            return propertyType.IsPrimitive || propertyType == typeof(string) || propertyType == typeof(TimeOnly?);

        }
        private static bool IsComplexProperty(PropertyInfo property)
        {
            Type propertyType = property.PropertyType;

            return !IsSimpleProperty(property) && propertyType.IsClass && propertyType != typeof(string);
        }

        private static void CalculateColumnSum(ExcelWorksheet ws, List<T> data, PropertyInfo[] properties, FiltersDto filters, ExportTrackerDto exportTrackerDto, string reportName, int? headerRowCount, List<string>? flags)
        {
            //int headerRowCount = FormatHeaderRow(ws, properties, filters, exportTrackerDto, reportName);
            int row = data.Count + headerRowCount ?? 0;

            long allSum = 0;
            long activeSum = 0;

            foreach (var item in data)
            {
                var allValue = typeof(T).GetProperty("All")?.GetValue(item) as long?;
                var activeValue = typeof(T).GetProperty("Active")?.GetValue(item) as long?;


                if (allValue.HasValue)
                {
                    allSum += allValue.Value;
                }

                if (activeValue.HasValue)
                {
                    activeSum += activeValue.Value;
                }
            }
            var totalRow = ws.Cells[row, ws.Dimension.Start.Column, row, ws.Dimension.End.Column];
            totalRow.Style.Fill.PatternType = ExcelFillStyle.Solid;
            totalRow.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Yellow);
            ApplyCellBorderStyle(totalRow, ExcelBorderStyle.Thin, System.Drawing.Color.Black);
            foreach (var property in properties)
            {
                var propertyType = property.PropertyType;

                if (IsSimpleProperty(property) && (propertyType == typeof(long) || (propertyType == typeof(string) && property.Name.Contains("TalkTime"))))
                {
                    List<TimeSpan> timeSpans = new();
                    long sum = 0;
                    foreach (var item in data)
                    {
                        if (propertyType == typeof(string) && (property.GetValue(item)?.ToString()?.IsTimeSpan() ?? false))
                        {
                            var value = property.GetValue(item) as string;
                            if (value != null)
                            {
                                timeSpans.Add(TimeSpan.Parse(value));
                            }
                        }
                        else
                        {
                            var value = property.GetValue(item) as long?;

                            if (value != null)
                            {
                                sum += (long)value;
                            }
                        }
                    }

                    ws.Cells[row, GetColumnIndex(ws, "SlNo", properties, filters, exportTrackerDto)].Value = "TOTAL";
                    ws.Cells[row, GetColumnIndex(ws, "SlNo", properties, filters, exportTrackerDto)].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    var totalCell = ws.Cells[row, GetColumnIndex(ws, "SlNo", properties, filters, exportTrackerDto)];
                    totalCell.Style.Font.Bold = true;
                    if ((property.Name == "All") || (property.Name == "Active"))
                    {
                        ws.Cells[row, GetColumnIndex(ws, "All", properties, filters, exportTrackerDto)].Value = $"{allSum} ({activeSum})";
                        ws.Cells[row, GetColumnIndex(ws, "All", properties, filters, exportTrackerDto)].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    }
                    else
                    {
                        if (propertyType == typeof(long))
                        {
                            ws.Cells[row, GetColumnIndex(ws, property.Name, properties, filters, exportTrackerDto)].Value = sum;
                        }
                        else if (timeSpans.Any())
                        {
                            TimeSpan totalTime = timeSpans.Aggregate(TimeSpan.Zero, (acc, time) => acc + time);
                            ws.Cells[row, GetColumnIndex(ws, property.Name, properties, filters, exportTrackerDto)].Value = totalTime.ToString(@"hh\:mm\:ss");
                        }
                        ws.Cells[row, GetColumnIndex(ws, property.Name, properties, filters, exportTrackerDto)].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    }
                }

                else if (IsComplexProperty(property))
                {
                    if (property.Name.Equals("flags", StringComparison.InvariantCultureIgnoreCase))
                    {
                        if (flags?.Any() ?? false)
                        {
                            foreach (var subHeader in flags)
                            {
                                long? sum = 0;
                                long? uniqSum = 0;
                                foreach (var item in data)
                                {
                                    PropertyInfo? dataProperty = item?.GetType()?.GetProperty("Flags");
                                    var dataValue = dataProperty?.GetValue(item);
                                    if (dataProperty != null && dataValue != null)
                                    {
                                        var matchingItem = FindItemByPropertyValue(dataValue, "Name", subHeader);
                                        var newValue = GetCountFromDynamicObject(matchingItem);
                                        var uniqueValue = GetUniqueLeadsCountFromDynamicObject(matchingItem);
                                        if (long.TryParse(newValue.ToString(), out long val))
                                        {
                                            sum += val;
                                        }
                                        if (long.TryParse(uniqueValue.ToString(), out long val1))
                                        {
                                            uniqSum += val1;
                                        }
                                    }
                                }
                                ws.Cells[row, GetColumnIndex(ws, subHeader, properties, filters, exportTrackerDto)].Value = $"{sum.ToString()} ({uniqSum.ToString()})";
                                ws.Cells[row, GetColumnIndex(ws, subHeader, properties, filters, exportTrackerDto)].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            }
                        }
                    }

                    var subProperties = propertyType.GetProperties();

                    foreach (var subProperty in subProperties)
                    {
                        if (IsSimpleProperty(subProperty) && subProperty.PropertyType == typeof(long))
                        {
                            long sum = 0;

                            foreach (var item in data)
                            {
                                var complexValue = property.GetValue(item);
                                if (complexValue != null)
                                {
                                    var subValue = subProperty.GetValue(complexValue);
                                    if (subValue != null)
                                    {
                                        sum += (long)subValue;
                                    }
                                }
                            }
                            int counter = 0;
                            var columnIndex = GetColumnIndex(ws, subProperty.Name, properties, filters, exportTrackerDto, counter);
                            while (ws.Cells[row, columnIndex].Value != null)
                            {
                                counter++;
                                columnIndex = GetColumnIndex(ws, subProperty.Name, properties, filters, exportTrackerDto, counter);
                            }
                            ws.Cells[row, columnIndex].Value = sum;
                            ws.Cells[row, columnIndex].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            //ws.Cells[row, GetColumnIndex(ws, subProperty.Name, properties, filters, exportTrackerDto)].Value = sum;
                            //ws.Cells[row, GetColumnIndex(ws, subProperty.Name, properties, filters, exportTrackerDto)].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        }
                    }
                }
            }



            ws.Cells[row + 1, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

        }
        private static int GetColumnIndex(ExcelWorksheet ws, string columnName, PropertyInfo[] properties, FiltersDto filters, ExportTrackerDto exportTrackerDto, int? counter = null)
        {
            int headerRowCount = AddFilters(ws, filters, exportTrackerDto);
            bool hasComplexProperty = properties.Any(IsComplexProperty);
            int headerRow = hasComplexProperty ? headerRowCount + 3 : headerRowCount + 2;
            counter ??= 0;
            for (int col = 1; col <= ws.Dimension.Columns; col++)
            {
                string columnHeader = ws.Cells[headerRow, col].Text.Trim();

                if (columnHeader == SplitCamelCase(columnName) && counter == 0)
                {
                    return col;
                }
                else if (columnHeader == SplitCamelCase(columnName))
                {
                    counter--;
                }
            }
            return 3;
        }
        private static int AddFilters(ExcelWorksheet ws, FiltersDto filters, ExportTrackerDto exportTrackerDto)
        {
            int startRow = AddExportTracker(ws, exportTrackerDto, 1);
            var filtersRow = startRow + 1;


            var properties = typeof(FiltersDto).GetProperties();

            for (int i = 0; i < properties.Length; i++)
            {
                var property = properties[i];
                var value = property.GetValue(filters);

                ws.Cells[filtersRow + i, 1].Value = SplitCamelCase(property.Name) + ":";
                if (property.Name == "FromDate" && value is DateTime fromDate)
                {
                    ws.Cells[filtersRow + i, 2].Value = fromDate.ToString("dd-MM-yyyy");
                }
                else if (property.Name == "ToDate" && value is DateTime todate)
                {
                    ws.Cells[filtersRow + i, 2].Value = todate.ToString("dd-MM-yyyy");
                }
                else if (value != null)
                {
                    ws.Cells[filtersRow + i, 2].Value = value.ToString();
                }
                else
                {
                    ws.Cells[filtersRow + i, 2].Value = "N/A";
                }
            }

            var filterRange = ws.Cells[filtersRow, 1, filtersRow + properties.Length - 1, 2];
            var filterStyle = filterRange.Style;
            filterStyle.Font.Bold = true;
            filterStyle.Fill.PatternType = ExcelFillStyle.Solid;
            filterStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            ApplyCellBorderStyle(filterRange, ExcelBorderStyle.Thin, System.Drawing.Color.Black);

            return properties.Length + startRow;
        }
        private static int AddExportTracker(ExcelWorksheet ws, ExportTrackerDto exportTrackerDto, int startRow)
        {
            var filtersRow = startRow;
            var properties = typeof(ExportTrackerDto).GetProperties();

            for (int i = 0; i < properties.Length; i++)
            {
                var property = properties[i];
                var value = property.GetValue(exportTrackerDto);
                ws.Cells[filtersRow + i, 1].Value = SplitCamelCase(property.Name) + ":";

                if (property.Name == "Type" && value != null)
                {
                    ws.Cells[filtersRow + i, 2].Value = GetReportType(value.ToString());
                }
                else if (property.Name == "CreatedOn" && value is DateTime createdOn)
                {
                    ws.Cells[filtersRow + i, 2].Value = createdOn.ToString("dd-MM-yyyy");
                }
                else if (value != null)
                {
                    ws.Cells[filtersRow + i, 2].Value = value.ToString();
                }
                else
                {
                    ws.Cells[filtersRow + i, 2].Value = "N/A";
                }
            }

            var filterRange = ws.Cells[filtersRow, 1, filtersRow + properties.Length - 1, 2];
            var filterStyle = filterRange.Style;
            filterStyle.Font.Bold = true;
            filterStyle.Fill.PatternType = ExcelFillStyle.Solid;
            filterStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            ApplyCellBorderStyle(filterRange, ExcelBorderStyle.Thin, System.Drawing.Color.Black);

            return properties.Length;
        }


        private static string GetReportType(string name)
        {
            var reportTypes = new Dictionary<string, string>()
            {
                {"statusreportbysubsource", "Status report by Sub Source" },
                {"statusreportbyproject","Status Report By Project" },
                {"meetingandvisitreport","Meeting And Visit Report"},
                {"statusreportbysource","Status Report By source" },
                {"statusreportbyuser","Status Report By User" },
                {"statusreportbyagency","Status Report By Agency" },
                {"substatusreportbyuser","Sub Status Report By User" },
                {"substatusreportbysubsource","Sub Status Report By Subsource"},
                {"useractivityreport","User Activity Report" },
                {"projectreportbysubstatus","Project Report By Substatus" },
                {"exportproperties","Export Properties" },
                {"calllogreportbyuser","Call Log Report" },
                {"exportteam","Team Export" }
            };
            return reportTypes[name];
        }

        public static byte[] GenerateExcel<T, TFiltersDto, TExportTrackerDto>(List<T> dtos, string reportName, TFiltersDto? filtersDto, TExportTrackerDto exportTrackerDto, string? timeZoneId, TimeSpan baseUTcOffset)
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using MemoryStream memoryStream = new();

            using var package = new ExcelPackage(memoryStream);
            var ws = package.Workbook.Worksheets.Add("Sheet");

            var properties = typeof(T).GetProperties();
            FormatHeaderRow(ws, properties, filtersDto, exportTrackerDto, reportName,timeZoneId,baseUTcOffset);
            PopulateDataRows(ws, dtos, properties, filtersDto, exportTrackerDto, reportName,timeZoneId,baseUTcOffset);
            package.Save();

            return memoryStream.ToArray();
        }
        private static void PopulateDataRows<T, TFiltersDto, TExportTrackerDto>(ExcelWorksheet ws, List<T> data, PropertyInfo[] properties, TFiltersDto? filters, TExportTrackerDto? exportTrackerDto, string reportName, string? timeZoneId, TimeSpan baseUTcOffset)
        {
            int headerRowCount = FormatHeaderRow(ws, properties, filters, exportTrackerDto, reportName,timeZoneId,baseUTcOffset);
            int row = headerRowCount;
            int id = 1;

            foreach (var item in data)
            {
                int col = 1;

                ws.Cells[row, col].Value = id;
                var cell1 = ws.Cells[row, col];
                cell1.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                id++;

                foreach (var property in properties)
                {
                    var value = property.GetValue(item);

                    if (value != null)
                    {
                        var cell = ws.Cells[row, col];

                        if (property != null && property.Name == "PossessionDate" && value != null)
                        {
                            if (DateTime.TryParseExact(value.ToString(), "MM-dd-yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDate))
                            {
                                cell.Value = parsedDate.ToString("dd/MM/yyyy");
                            }
                            else if (property.Name == "PossessionDate" && value is DateTime possessionDate)

                            {
                                cell.Value = possessionDate.ToString("dd-MM-yyyy");
                            }
                        }

                        else if (property.Name == "LastModifiedOn" && value is DateTime lastModifiedOn)
                        {
                            cell.Value = lastModifiedOn.ToString("dd-MM-yyyy");
                        }
                        else if (property.Name == "ReceivedOn" && value is DateTime receivedOn)
                        {
                            cell.Value = receivedOn.ToString("dd-MM-yyyy");
                        }
                        else if (property.Name == "ScheduledDate" && value is DateTime scheduledDate)
                        {
                            cell.Value = scheduledDate.ToString("dd-MM-yyyy");
                        }
                        else if (property.Name == "RevertDate" && value is DateTime revertDate)
                        {
                            cell.Value = revertDate.ToString("dd-MM-yyyy");
                        }
                        else if (property.Name == "PostponedDate" && value is DateTime postponedDate)
                        {
                            cell.Value = postponedDate.ToString("dd-MM-yyyy");
                        }

                        else
                        {
                            cell.Value = value;
                        }
                        cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        col++;
                    }
                    else
                    {
                        col++;
                    }
                }
                row++;
            }
        }

        private static int FormatHeaderRow<TFiltersDto, TExportTrackerDto>(ExcelWorksheet ws, PropertyInfo[] properties, TFiltersDto? filters, TExportTrackerDto? exportTrackerDto, string reportName, string? timeZoneId, TimeSpan baseUTcOffset)
        {

            bool hasComplexProperty = properties.Any(IsComplexProperty);

            if (hasComplexProperty)
            {
                return FormatHeaderRowWithComplexProperties(ws, properties, filters, exportTrackerDto, reportName,timeZoneId,baseUTcOffset);
            }
            else
            {
                return FormatHeaderRowWithSimpleProperties(ws, properties, filters, exportTrackerDto, reportName, timeZoneId,baseUTcOffset);
            }

        }

        private static int FormatHeaderRowWithSimpleProperties<TFiltersDto, TExportTrackerDto>(ExcelWorksheet ws, PropertyInfo[] properties, TFiltersDto? filters, TExportTrackerDto? exportTrackerDto, string reportName, string? timeZoneId, TimeSpan baseUTcOffset)
        {

            var headerNames = new List<string>();
            int headerRowCount = AddFilters(ws, filters, exportTrackerDto,timeZoneId,baseUTcOffset);

            AddHeader(ws, $"A${headerRowCount + 1}", reportName, 16, ExcelHorizontalAlignment.Center, true, 30);

            foreach (var property in properties)
            {
                headerNames.Add(SplitCamelCase(property.Name));
            }
            string headerRange = $"A${headerRowCount + 2}:{ColumnLetter(headerNames.Count)}{headerRowCount + 2}";

            ws.Cells[$"A${headerRowCount + 1}:{ColumnLetter(headerNames.Count)}${headerRowCount + 1}"].Merge = true;
            ws.Cells[headerRange].LoadFromArrays(new List<object[]> { headerNames.ToArray() });
            var headerStyle = ws.Cells[headerRange].Style;
            headerStyle.Fill.PatternType = ExcelFillStyle.Solid;
            headerStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
            headerStyle.Font.Size = 12;
            headerStyle.Font.Color.SetColor(System.Drawing.Color.White);
            headerStyle.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            ApplyCellBorderStyle(ws.Cells[headerRowCount + 2, 1, headerRowCount + 2, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.White);
            ApplyCellBorderStyle(ws.Cells[headerRowCount + 1, 1, headerRowCount + 1, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.Black);
            return headerRowCount + 3;
        }


        private static int AddFilters<TFiltersDto, TExportTrackerDto>(ExcelWorksheet ws, TFiltersDto? filters, TExportTrackerDto? exportTrackerDto, string? timeZoneId, TimeSpan baseUTcOffset)
        {
            int startRow = AddExportTracker(ws, exportTrackerDto, 1,timeZoneId,baseUTcOffset);
            var filtersRow = startRow + 1;
            var properties = typeof(TFiltersDto).GetProperties();

            for (int i = 0; i < properties.Length; i++)
            {
                var property = properties[i];
                var value = property.GetValue(filters);

                ws.Cells[filtersRow + i, 1].Value = SplitCamelCase(property.Name) + ":";
                if (property.Name == "FromDate" && value is DateTime fromDate)
                {
                    ws.Cells[filtersRow + i, 2].Value = fromDate.ToParticularTimeZone(timeZoneId, baseUTcOffset).ToString("dd-MM-yyyy");
                }
                else if (property.Name == "ToDate" && value is DateTime todate)
                {
                    ws.Cells[filtersRow + i, 2].Value = todate.ToParticularTimeZone(timeZoneId, baseUTcOffset).ToString("dd-MM-yyyy");
                }
                else if (value != null)
                {
                    ws.Cells[filtersRow + i, 2].Value = value.ToString();
                }
                else
                {
                    ws.Cells[filtersRow + i, 2].Value = "N/A";
                }
            }

            var filterRange = ws.Cells[filtersRow, 1, filtersRow + properties.Length - 1, 2];
            var filterStyle = filterRange.Style;
            filterStyle.Font.Bold = true;
            filterStyle.Fill.PatternType = ExcelFillStyle.Solid;
            filterStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            ApplyCellBorderStyle(filterRange, ExcelBorderStyle.Thin, System.Drawing.Color.Black);

            return properties.Length + startRow;
        }

        private static int AddExportTracker<TExportTrackerDto>(ExcelWorksheet ws, TExportTrackerDto? exportTrackerDto, int startRow, string? timeZoneId, TimeSpan baseUTcOffset)
        {
            var filtersRow = startRow;
            var properties = typeof(ExportTrackerDto).GetProperties();

            for (int i = 0; i < properties.Length; i++)
            {
                var property = properties[i];
                var value = property.GetValue(exportTrackerDto);
                ws.Cells[filtersRow + i, 1].Value = SplitCamelCase(property.Name) + ":";

                if (property.Name == "Type" && value != null)
                {
                    ws.Cells[filtersRow + i, 2].Value = GetReportType(value.ToString());
                }
                else if (property.Name == "CreatedOn" && value is DateTime createdOn)
                {
                    ws.Cells[filtersRow + i, 2].Value = createdOn.ToParticularTimeZone(timeZoneId, baseUTcOffset).ToString("dd-MM-yyyy");
                }
                else if (value != null)
                {
                    ws.Cells[filtersRow + i, 2].Value = value.ToString();
                }
                else
                {
                    ws.Cells[filtersRow + i, 2].Value = "N/A";
                }
            }

            var filterRange = ws.Cells[filtersRow, 1, filtersRow + properties.Length - 1, 2];
            var filterStyle = filterRange.Style;
            filterStyle.Font.Bold = true;
            filterStyle.Fill.PatternType = ExcelFillStyle.Solid;
            filterStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            ApplyCellBorderStyle(filterRange, ExcelBorderStyle.Thin, System.Drawing.Color.Black);

            return properties.Length;


        }

        private static int FormatHeaderRowWithComplexProperties<TFiltersDto, TExportTrackerDto>(ExcelWorksheet ws, PropertyInfo[] properties, TFiltersDto? filters, TExportTrackerDto? exportTrackerDto, string reportName, string? timeZoneId, TimeSpan baseUTcOffset)
        {

            mergedCells.Clear();
            var headerNames = new List<string>();
            var subHeaderNames = new List<string>();
            int headerRowCount = AddFilters(ws, filters, exportTrackerDto,timeZoneId,baseUTcOffset);
            AddHeader(ws, $"A${headerRowCount + 1}", reportName, 16, ExcelHorizontalAlignment.Center, true, 30);

            foreach (var property in properties)
            {

                if (IsSimpleProperty(property))
                {
                    subHeaderNames.Add(SplitCamelCase(property.Name));

                }
                else if (IsComplexProperty(property))
                {
                    var subProperties = property.PropertyType.GetProperties();
                    int subPropertyCount = CountSubProperties(property);
                    mergedCells.Add($"{ColumnLetter(headerNames.Count + 1)}${headerRowCount + 2}:{ColumnLetter(headerNames.Count + subPropertyCount)}${headerRowCount + 2}");

                    for (int i = 0; i < subPropertyCount; i++)
                    {
                        headerNames.Add(SplitCamelCase(property.Name));
                    }

                    foreach (var subProperty in subProperties)
                    {
                        if (IsSimpleProperty(subProperty))
                        {
                            subHeaderNames.Add(SplitCamelCase(subProperty.Name));
                        }
                    }
                }
            }

            foreach (var mergedCell in mergedCells)
            {
                ws.Cells[mergedCell].Merge = true;
            }

            string headerRange = $"A${headerRowCount + 2}:{ColumnLetter(headerNames.Count)}${headerRowCount + 3}";
            ws.Cells[$"A${headerRowCount + 1}:{ColumnLetter(headerNames.Count)}${headerRowCount + 1}"].Merge = true;
            ws.Cells[headerRange].LoadFromArrays(new List<object[]> { headerNames.ToArray(), subHeaderNames.ToArray() });

            var headerStyle = ws.Cells[headerRange].Style;
            headerStyle.Fill.PatternType = ExcelFillStyle.Solid;
            headerStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
            headerStyle.Font.Size = 12;
            headerStyle.Font.Color.SetColor(System.Drawing.Color.White);
            headerStyle.HorizontalAlignment = ExcelHorizontalAlignment.Center;

            ApplyCellBorderStyle(ws.Cells[headerRowCount + 2, 1, headerRowCount + 3, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.White);
            ApplyCellBorderStyle(ws.Cells[headerRowCount + 1, 1, headerRowCount + 1, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.Black);

            return headerRowCount + 4;
        }
        

    }
}









