﻿using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Agency.Web;
using Lrb.Application.Common.TimeZone;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Newtonsoft.Json;
using PhoneNumbers;
using System.Data;
using System.Globalization;
using System.Text.RegularExpressions;
using ThirdParty.Json.LitJson;
using MasterItems = Lrb.Application.Lead.Web.Dtos.MasterItems;

namespace Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation
{
    public static class LeadMigrateHelper
    {
        public static async Task<List<Domain.Entities.Lead>> MigrateAsync(
            this DataTable table,
            Dictionary<DataColumns, string>? mappedColumnsData,
            List<string> unMappedColumns,
            List<MasterPropertyType> propertyTypes,
            List<CustomMasterLeadStatus> leadStatuses,
            List<UserDetailsDto> users,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<Domain.Entities.Agency> agencyRepo,
            List<SourceDto> subSources,
             List<Domain.Entities.Agency> agencies,
             LeadMigrateTracker? leadMigrateTracker,
             Domain.Entities.GlobalSettings globalSettings,
             List<CustomFlag> flags,
             List<Flag> flag,
             List<Domain.Entities.ChannelPartner> channelPartners,
              List<Domain.Entities.Campaign> campaigns,
             Guid? currentUserId = null,
             string jsonData = null
          )
        {
            List<Domain.Entities.Lead> leads = new();
            if (mappedColumnsData == null)
            {
                return leads;
            }
            else
            {
                foreach (DataRow row in table.Rows)
                {
                    Domain.Entities.Lead lead = await ConstructLeadAsync(mappedColumnsData, unMappedColumns, propertyTypes, leadStatuses, users, propertyRepo, projectRepo, row, agencyRepo, subSources, agencies, leadMigrateTracker, globalSettings, flags, flag, channelPartners, campaigns, currentUserId,jsonData);
                    leads.Add(lead);
                }
            }

            return leads;
        }

        private static async Task<Domain.Entities.Lead> ConstructLeadAsync(Dictionary<DataColumns, string> mappedColumnsData, List<string> unMappedColumns, List<MasterPropertyType> propertyTypes,
            List<CustomMasterLeadStatus> leadStatuses, List<UserDetailsDto> users, IRepositoryWithEvents<Domain.Entities.Property> propertyRepo, IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
            DataRow row, IRepositoryWithEvents<Domain.Entities.Agency> agencyRepo, List<SourceDto> subSources, List<Domain.Entities.Agency> agencies, LeadMigrateTracker? leadMigrateTracker,
            Domain.Entities.GlobalSettings globalSettings, List<CustomFlag> flags, List<Flag> flag, List<Domain.Entities.ChannelPartner> channelPartners, List<Domain.Entities.Campaign> campaigns, Guid? currentUserId = null,string jsonData = null)
        {
            string? lowerBudget = string.Empty;
            string? upperBudget = string.Empty;
            var name = mappedColumnsData.GetStringValue(DataColumns.Name, row);
            name = string.IsNullOrWhiteSpace(name?.Trim()) ? "Unknown" : name.Trim();
            var leadTags = GetLeadTags(mappedColumnsData, row);
            var customflags = !mappedColumnsData.ContainsKey(DataColumns.Tag) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Tag]) ? string.Empty : row[mappedColumnsData[DataColumns.Tag]].ToString();
            var projects = await mappedColumnsData.GetProjectsAsync(DataColumns.Project, row, projectRepo);
            var properties = await mappedColumnsData.GetPropertiesAsync(DataColumns.Property, row, propertyRepo);
            string? enquiryFor = !mappedColumnsData.ContainsKey(DataColumns.EnquiredFor) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.EnquiredFor]) ? string.Empty : row[mappedColumnsData[DataColumns.EnquiredFor]].ToString();
            string? bhkType = !mappedColumnsData.ContainsKey(DataColumns.BHKType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BHKType]) ? string.Empty : row[mappedColumnsData[DataColumns.BHKType]].ToString();
            var beds = !mappedColumnsData.ContainsKey(DataColumns.Beds) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Beds]) ? string.Empty : row[mappedColumnsData[DataColumns.Beds]].ToString();
            var baths = !mappedColumnsData.ContainsKey(DataColumns.Baths) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Baths]) ? string.Empty : row[mappedColumnsData[DataColumns.Baths]].ToString();
            var floors = !mappedColumnsData.ContainsKey(DataColumns.PreferredFloor) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.PreferredFloor]) ? string.Empty : row[mappedColumnsData[DataColumns.PreferredFloor]].ToString();
            var furnishStatus = !mappedColumnsData.ContainsKey(DataColumns.FurnishStatus) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.FurnishStatus]) ? string.Empty : row[mappedColumnsData[DataColumns.FurnishStatus]].ToString();
            string? noOfBhks = !mappedColumnsData.ContainsKey(DataColumns.NoOfBHK) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.NoOfBHK]) ? string.Empty : row[mappedColumnsData[DataColumns.NoOfBHK]].ToString();
            string? baseProperty = !mappedColumnsData.ContainsKey(DataColumns.BasePropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BasePropertyType]) ? string.Empty : row[mappedColumnsData[DataColumns.BasePropertyType]].ToString();
            string? subProperty = !mappedColumnsData.ContainsKey(DataColumns.SubPropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SubPropertyType]) ? string.Empty : row[mappedColumnsData[DataColumns.SubPropertyType]].ToString();
            string leadSource = !mappedColumnsData.ContainsKey(DataColumns.Source) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Source]) ? string.Empty : row[mappedColumnsData[DataColumns.Source]].ToString();
            string budget = !mappedColumnsData.ContainsKey(DataColumns.Budget) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Budget]) ? string.Empty : row[mappedColumnsData[DataColumns.Budget]].ToString();
            var subSource = mappedColumnsData.GetStringValue(DataColumns.SubSource, row);
            string currecncy = !mappedColumnsData.ContainsKey(DataColumns.Currency) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Currency]) ? string.Empty : row[mappedColumnsData[DataColumns.Currency]].ToString();
            var propertyInfo = GetPropertyType(baseProperty, subProperty, propertyTypes, bhkType, noOfBhks, beds, baths, furnishStatus, floors);
            var leadStatus = mappedColumnsData.GetLeadStatus(row, leadStatuses);
            var user = mappedColumnsData.GetStringValue(DataColumns.AssignToUser, row);
            var assignToUser = users.FirstOrDefault(u => u.UserName.ToLower() == user.ToLower().Trim().Replace(" ", "") && !u.IsDeleted);
            var scheduledDate = mappedColumnsData.GetScheduledDate(DataColumns.ScheduledDate, row, jsonData);
            string countryCode = !mappedColumnsData.ContainsKey(DataColumns.CountryCode) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CountryCode]) ? string.Empty : row[mappedColumnsData[DataColumns.CountryCode]].ToString();
            string altCountryCode = !mappedColumnsData.ContainsKey(DataColumns.AlternativeNoCountryCode) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AlternativeNoCountryCode]) ? string.Empty : row[mappedColumnsData[DataColumns.AlternativeNoCountryCode]].ToString();
            var offeringType = !mappedColumnsData.ContainsKey(DataColumns.OfferingType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.OfferingType]) ? string.Empty : row[mappedColumnsData[DataColumns.OfferingType]].ToString();
            var purpose = !mappedColumnsData.ContainsKey(DataColumns.Purpose) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Purpose]) ? string.Empty : row[mappedColumnsData[DataColumns.Purpose]].ToString();
            var createdon = mappedColumnsData.GetCreatedDate(DataColumns.CreatedDate, row, jsonData);

            //var currencycode = mappedColumnsData.GetCurrencySymbol1(row, currecncy);
            var currencycode = mappedColumnsData.GetCurrencySymbol1Migrate(row, currecncy, globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency);

            if (budget?.Contains('-') ?? false)
            {
                string[] budgets = budget.Split('-');
                lowerBudget = budgets.FirstOrDefault();
                upperBudget = budgets.LastOrDefault();
            }
            else
            {
                lowerBudget = budget;
                upperBudget = budget;
            }
            var lowerBudgetInfo = BudgetHelper.ConvertBuget(lowerBudget ?? string.Empty);
            var upperBudgetinfo = BudgetHelper.ConvertBuget(upperBudget ?? string.Empty);
            //var enquiryInfo = GetEnquiryForInfo(enquiredFor ?? string.Empty);
            //var leadsourceInfo = BulkUploadHelper.GetLeadSourceInfo(leadSource ?? string.Empty);
            //var subSourceInfo = BulkUploadHelper.GetSubSource(subSources, leadsourceInfo.IsValidInfo ? leadsourceInfo.LeadSource : null, subSource ?? string.Empty);
            //var enquiryTypesInfo = BulkUploadHelper.GetEnquiryTypesInfo(enquiryFor ?? string.Empty);
            var offeringTypeInfo = BulkUploadHelper.GetOfferingTypesInfo(offeringType ?? string.Empty);
            var purposees = BulkUploadHelper.GetPurposeInfo(purpose ?? string.Empty);
            var agencyName = !mappedColumnsData.ContainsKey(DataColumns.AgencyName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AgencyName]) ? null : row[mappedColumnsData[DataColumns.AgencyName]].ToString();
            var agencyToAdd = agencies.FirstOrDefault(i => i.Name.Trim().ToLower().Equals(agencyName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
            var siteVisitDone = mappedColumnsData.GetIntValue(DataColumns.SiteVisitDoneCount, row);
            var siteVisitNotDone = mappedColumnsData.GetIntValue(DataColumns.SiteVisitNotDoneCount, row);
            var meetingDone = mappedColumnsData.GetIntValue(DataColumns.MeetingDoneCount, row);
            var meetingNotDone = mappedColumnsData.GetIntValue(DataColumns.MeetingNotDoneCount, row);
            var cpName = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerName]) ? null : row[mappedColumnsData[DataColumns.ChannelPartnerName]].ToString();
            var channelPartenr = channelPartners.FirstOrDefault(i => i.FirmName.Trim().ToLower().Equals(cpName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
            var campaignName = !mappedColumnsData.ContainsKey(DataColumns.CampaignName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CampaignName]) ? null : row[mappedColumnsData[DataColumns.CampaignName]].ToString();
            var campaignToAdd = campaigns.FirstOrDefault(i => i.Name.Trim().ToLower().Equals(agencyName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
            //   var note= GetUnmappedColumnsString(row, unMappedColumns);
            Domain.Entities.Lead lead = new()
            {

                Name = name,
                ContactNo = !mappedColumnsData.ContainsKey(DataColumns.ContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ContactNo]) ? string.Empty : row[mappedColumnsData[DataColumns.ContactNo]].ToString(),
                Email = !mappedColumnsData.ContainsKey(DataColumns.Email) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Email]) ? string.Empty : row[mappedColumnsData[DataColumns.Email]].ToString(),
                Rating = !mappedColumnsData.ContainsKey(DataColumns.Rating) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Rating]) ? string.Empty : row[mappedColumnsData[DataColumns.Rating]].ToString(),
                //CustomLeadStatus = newStatus,
                //AgencyName = !mappedColumnsData.ContainsKey(DataColumns.AgencyName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AgencyName]) ? null : row[mappedColumnsData[DataColumns.AgencyName]].ToString(),
                Agencies = agencyToAdd != null
                            ? new List<Domain.Entities.Agency>() { agencyToAdd }
                             : !string.IsNullOrEmpty(agencyName)
                             ? new List<Domain.Entities.Agency>()
                                  {
                                        new Domain.Entities.Agency()
                                        {
                                            Name = agencyName,
                                            CreatedBy = leadMigrateTracker.CreatedBy,
                                            LastModifiedBy = leadMigrateTracker.LastModifiedBy
                                        }
                                  } : null,
                ReferralName = !mappedColumnsData.ContainsKey(DataColumns.ReferralName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ReferralName]) ? null : row[mappedColumnsData[DataColumns.ReferralName]].ToString(),
                ReferralContactNo = !mappedColumnsData.ContainsKey(DataColumns.ReferralContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ReferralContactNo]) ? null : row[mappedColumnsData[DataColumns.ReferralContactNo]].ToString(),
                ReferralEmail = !mappedColumnsData.ContainsKey(DataColumns.ReferralEmail) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ReferralEmail]) ? null : row[mappedColumnsData[DataColumns.ReferralEmail]].ToString(),
                AssignTo = assignToUser?.Id ?? Guid.Empty,
                Enquiries = new List<LeadEnquiry>() {
                    new() {
                        IsPrimary = true,
                        PropertyType = propertyInfo.IsValidInfo ? propertyInfo.PropertyType : default,
                        PropertyTypes = propertyInfo.IsValidInfo ? propertyInfo.PropertyTypes : default,
                        //LeadSource = leadsourceInfo.IsValidInfo ? leadsourceInfo.LeadSource : default,
                        SubSource = subSource,
                        UpperBudget = upperBudgetinfo.IsValidInfo ? upperBudgetinfo.Budget : default,
                        LowerBudget = lowerBudgetInfo.IsValidInfo ? lowerBudgetInfo.Budget : default,
                        Currency = currencycode ?? "INR",
                        BHKTypes = propertyInfo.IsValidInfo ? propertyInfo.BHKTypes : default,
                        BHKs = propertyInfo.IsValidInfo ? propertyInfo.BHKs : default,
                        //EnquiryTypes = enquiryTypesInfo.IsValidInfo ? enquiryTypesInfo.EnquiryTypes : default,
                        Addresses = new()
                        {
                            new()
                            {
                                City = !mappedColumnsData.ContainsKey(DataColumns.City) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.City]) ? string.Empty : row[mappedColumnsData[DataColumns.City]].ToString(),
                                State = !mappedColumnsData.ContainsKey(DataColumns.State) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.State]) ? string.Empty : row[mappedColumnsData[DataColumns.State]].ToString(),
                                SubLocality = !mappedColumnsData.ContainsKey(DataColumns.Location) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Location]) ? string.Empty : row[mappedColumnsData[DataColumns.Location]].ToString(),
                                Community = !mappedColumnsData.ContainsKey(DataColumns.Community) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Community]) ? string.Empty : row[mappedColumnsData[DataColumns.Community]].ToString(),
                                SubCommunity = !mappedColumnsData.ContainsKey(DataColumns.SubCommunity) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SubCommunity]) ? string.Empty : row[mappedColumnsData[DataColumns.SubCommunity]].ToString(),
                                TowerName = !mappedColumnsData.ContainsKey(DataColumns.TowerName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.TowerName]) ? string.Empty : row[mappedColumnsData[DataColumns.TowerName]].ToString(),
                                Country = !mappedColumnsData.ContainsKey(DataColumns.Country) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Country]) ? string.Empty : row[mappedColumnsData[DataColumns.Country]].ToString(),

                            }
                        },
                        OfferType = offeringTypeInfo.IsValidInfo ? offeringTypeInfo.OfferingType :OfferType.None,
                        Purpose = purposees.IsValidInfo ? purposees.Purpose :Purpose.None

                    } },
                Projects = projects,
                TagInfo = leadTags,
                Properties = properties,
                AlternateContactNo = !mappedColumnsData.ContainsKey(DataColumns.AlternateContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AlternateContactNo]) ? null : row[mappedColumnsData[DataColumns.AlternateContactNo]].ToString().Trim(),
                Notes = unMappedColumns.Any() ? string.Join(", \n", unMappedColumns.Select(column => !string.IsNullOrEmpty(row[column].ToString()) ? column + " - " + row[column] : null).Where(i => i != null)) : string.Empty,
                Designation = !mappedColumnsData.ContainsKey(DataColumns.Designation) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Designation]) ? string.Empty : row[mappedColumnsData[DataColumns.Designation]].ToString(),
                ChannelPartnerExecutiveName = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerExecutiveName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerExecutiveName]) ? string.Empty : row[mappedColumnsData[DataColumns.ChannelPartnerExecutiveName]].ToString(),
                ChannelPartnerName = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerName]) ? string.Empty : row[mappedColumnsData[DataColumns.ChannelPartnerName]].ToString(),
                ChannelPartnerContactNo = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerContactNo]) ? null : row[mappedColumnsData[DataColumns.ChannelPartnerContactNo]].ToString().Trim(),
                ScheduledDate = scheduledDate,
                CreatedOn = (DateTime)createdon,
                CustomLeadStatus = leadStatus ?? leadStatuses.FirstOrDefault(i => i.Status == "new"),
                CompanyName = !mappedColumnsData.ContainsKey(DataColumns.CompanyName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CompanyName]) ? string.Empty : row[mappedColumnsData[DataColumns.CompanyName]].ToString(),
                Appointments = new List<LeadAppointment>(),
                CustomFlags = GetCustomFlagsByFlagNames(customflags, flag),
                ChannelPartners = channelPartenr != null
                            ? new List<Domain.Entities.ChannelPartner>() { channelPartenr }
                             : !string.IsNullOrEmpty(cpName)
                             ? new List<Domain.Entities.ChannelPartner>()
                                  {
                                        new Domain.Entities.ChannelPartner()
                                        {
                                            FirmName = cpName,
                                            CreatedBy = leadMigrateTracker.CreatedBy,
                                            LastModifiedBy = leadMigrateTracker.LastModifiedBy
                                        }
                                  } : null,
                Campaigns = campaignToAdd != null
                            ? new List<Domain.Entities.Campaign>() { campaignToAdd }
                             : !string.IsNullOrEmpty(campaignName)
                             ? new List<Domain.Entities.Campaign>()
                                  {
                                        new Domain.Entities.Campaign()
                                        {
                                            Name = campaignName,
                                            CreatedBy = leadMigrateTracker.CreatedBy,
                                            LastModifiedBy = leadMigrateTracker.LastModifiedBy
                                        }
                                  } : null,
                BulkCategory = BulkType.BulkMigrate,
                CountryCode = countryCode ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",
                AltCountryCode = altCountryCode ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",

            };
            for (int i = 0; i < siteVisitDone; i++)
            {
                lead.Appointments.Add(new LeadAppointment
                {
                    IsDone = true,
                    Type = AppointmentType.SiteVisit,
                    CreatedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                    UserId = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                    LastModifiedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty

                });
            }
            for (int i = 0; i < siteVisitNotDone; i++)
            {
                lead.Appointments.Add(new LeadAppointment
                {
                    IsDone = false,
                    Type = AppointmentType.SiteVisit,
                    CreatedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                    UserId = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                    LastModifiedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty
                });
            }
            for (int i = 0; i < meetingDone; i++)
            {
                lead.Appointments.Add(new LeadAppointment
                {
                    IsDone = true,
                    Type = AppointmentType.Meeting,
                    CreatedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                    UserId = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                    LastModifiedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty
                });
            }
            for (int i = 0; i < meetingNotDone; i++)
            {
                lead.Appointments.Add(new LeadAppointment
                {
                    IsDone = false,
                    Type = AppointmentType.Meeting,
                    CreatedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                    UserId = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                    LastModifiedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty
                });
            }
            if (!propertyInfo.IsValidInfo)
            {
                lead.Notes += !string.IsNullOrEmpty(propertyInfo.BasePropertyType) ? " \n" + "BaseProperty" + " - " + propertyInfo.BasePropertyType : string.Empty;
                lead.Notes += !string.IsNullOrEmpty(propertyInfo.SubPropertyType) ? ", \n" + "SubProperty" + " - " + propertyInfo.SubPropertyType : string.Empty;
                lead.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidNoOfBHK) ? ", \n" + "NoOfBHK" + " - " + propertyInfo.InvalidNoOfBHK : string.Empty;
                lead.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidBHKType) ? ", \n" + "BHKType" + " - " + propertyInfo.InvalidBHKType : string.Empty;
            }
            if (!upperBudgetinfo.IsValidInfo)
            {
                lead.Notes += !string.IsNullOrEmpty(upperBudgetinfo.Invalidbudget) ? ", \n" + "UpperBudget" + " - " + upperBudgetinfo.Invalidbudget : string.Empty;
            }
            if (!lowerBudgetInfo.IsValidInfo)
            {
                lead.Notes += !string.IsNullOrEmpty(lowerBudgetInfo.Invalidbudget) ? ", \n" + "LowerBudget" + " - " + lowerBudgetInfo.Invalidbudget : string.Empty;
            }
            //if (!leadsourceInfo.IsValidInfo)
            //{
            //    lead.Notes += !string.IsNullOrEmpty(leadsourceInfo.InvalidLeadSource) ? ", \n" + "LeadSource" + " - " + leadsourceInfo.InvalidLeadSource : string.Empty;
            //}
            //if (!enquiryTypesInfo.IsValidInfo)
            //{
            //    lead.Notes += !string.IsNullOrEmpty(enquiryTypesInfo.InvalidEnquiredFor) ? ", \n" + "EnquiryFOr" + " - " + enquiryTypesInfo.InvalidEnquiredFor : string.Empty;
            //}
            //if (!subSourceInfo.IsValidInfo)
            //{
            //    lead.Notes += !string.IsNullOrEmpty(subSourceInfo.SubSource) ? ", \n" + "subSource" + " - " + subSourceInfo.SubSource : string.Empty;
            //}
            if (!leadStatus.IsValidStatus(leadStatuses))
            {
                var baseStatusString = mappedColumnsData.GetStringValue(DataColumns.BaseStatus, row);
                var subStatusString = mappedColumnsData.GetStringValue(DataColumns.SubStatus, row);

                lead.Notes += !string.IsNullOrWhiteSpace(baseStatusString) ? $"\n{mappedColumnsData[DataColumns.BaseStatus]} - {baseStatusString}" : string.Empty;
                lead.Notes += !string.IsNullOrWhiteSpace(subStatusString) ? $"\n{mappedColumnsData[DataColumns.SubStatus]} - {subStatusString}" : string.Empty;
            }

            return lead;
        }

        public static string GetUnmappedColumnsString(Dictionary<string, object> row, List<string> unMappedColumns)
        {
            if (unMappedColumns.Any())
            {
                return string.Join(", \n", unMappedColumns.Select(column =>
                {
                    var value = row[column];
                    if (value != null)
                    {
                        // Add single quote initially to the column value
                        var valueString = $"'{value}'";

                        if (value is DateTime dateValue)
                        {
                            return $"{column} - '{dateValue:dd-MM-yyyy}'";
                        }
                        else if (double.TryParse(value.ToString(), out double numericValue) && numericValue >= 0)
                        {
                            try
                            {
                                // Convert numeric date value to DateTime
                                DateTime dateFromExcel = DateTime.FromOADate(numericValue);
                                return $"{column} - '{dateFromExcel:dd-MM-yyyy}'";
                            }
                            catch (ArgumentException)
                            {
                                // Handle invalid OLE Automation date
                                return $"{column} - {valueString}";
                            }
                        }
                        else if (DateTime.TryParse(value.ToString(), out DateTime parsedDate))
                        {
                            return $"{column} - '{parsedDate:dd-MM-yyyy}'";
                        }
                        else
                        {
                            return $"{column} - {valueString}";
                        }
                    }
                    return null;
                }).Where(i => i != null));
            }
            else
            {
                return string.Empty;
            }
        }

        public static List<CustomFlag> GetCustomFlagsByFlagNames(string flagNames, List<Flag> flags)
        {
            var flagNameArray = flagNames.Split(',').Select(name => name.ToLower().Trim()).ToArray();
            var matchingFlags = flags.Where(f => flagNameArray.Contains(f.Name.ToLower().Trim()));
            var customFlagList = new List<CustomFlag>();
            if (matchingFlags?.Any() ?? false)
            {
                foreach (var item in matchingFlags)
                {
                    customFlagList.Add(new CustomFlag() { Flag = item, FlagId = item.Id,IsSelected=true });
                }
            }
            return customFlagList;
        }

        public static List<string> GetAgencyNamesFromDataTable(
DataTable table,
Dictionary<DataColumns, string> mappedColumnsData)
        {
            var agencyNames = new HashSet<string>();

            foreach (DataRow row in table.Rows)
            {

                if (mappedColumnsData.TryGetValue(DataColumns.AgencyName, out var agencyColumn) &&
                    !string.IsNullOrEmpty(agencyColumn))
                {
                    string agencyName = row[agencyColumn].ToString();
                    if (!string.IsNullOrEmpty(agencyName))
                    {
                        agencyNames.Add(agencyName);
                    }
                }
            }

            return agencyNames.Distinct().ToList();
        }

        public static List<string> GetCpNamesFromDataTable(
  DataTable table,
  Dictionary<DataColumns, string> mappedColumnsData)
        {
            var cpNames = new HashSet<string>();

            foreach (DataRow row in table.Rows)
            {

                if (mappedColumnsData.TryGetValue(DataColumns.ChannelPartnerName, out var Cpname) &&
                    !string.IsNullOrEmpty(Cpname))
                {
                    string agencyName = row[Cpname].ToString();
                    if (!string.IsNullOrEmpty(agencyName))
                    {
                        cpNames.Add(agencyName);
                    }
                }
            }

            return cpNames.Distinct().ToList();
        }
        public static DateTime? GetCreatedDate(this Dictionary<DataColumns, string> mappedColumnsData, DataColumns column, DataRow row, string jsonData = null)
        {
            try
            {
                // Get the date string from the mapped columns or row
                var dateString = GetStringValue(mappedColumnsData, column, row);

                // If the date string is empty or null, handle default cases
                if (string.IsNullOrEmpty(dateString))
                {
                    return column == DataColumns.CreatedDate ? DateTime.UtcNow : (DateTime?)null;
                }

                // Try to parse the date string
                DateTime? date = null;
                CommonTimeZoneDto? commonTimeZoneDto = null;

                // Deserialize JSON data if provided
                if (!string.IsNullOrWhiteSpace(jsonData))
                {
                    commonTimeZoneDto = JsonConvert.DeserializeObject<CommonTimeZoneDto>(jsonData);
                }

                // Handle Excel date format (OLE Automation date)
                if (double.TryParse(dateString, out var excelDateValue))
                {
                    date = DateTime.FromOADate(excelDateValue);
                }
                // Handle regular date strings
                else
                {
                    // Try parsing with the default format
                    if (!DateTime.TryParse(dateString, out var parsedDate))
                    {
                        // Fallback to a specific format if the default parsing fails
                        date = DateTime.ParseExact(dateString, formats, CultureInfo.InvariantCulture);
                    }
                    else
                    {
                        string formattedDate = parsedDate.ToString(CultureInfo.CurrentCulture);
                        date = DateTime.Parse(formattedDate, CultureInfo.CurrentCulture);
                    }
                }

                // Convert the date to the specified time zone if JSON data is provided
                if (commonTimeZoneDto != null)
                {
                    date = date?.ConvertDateTime(commonTimeZoneDto.TimeZoneId ?? string.Empty, commonTimeZoneDto.BaseUTcOffset);
                }

                return date;
            }
            catch (Exception ex)
            {
                // Return UTC time as a fallback in case of any errors
                return DateTime.UtcNow;
            }
        }
        /*
                public static string GetUnmappedColumnsString(Dictionary<string, object> row, List<string> unMappedColumns)
                {
                    if (unMappedColumns.Any())
                    {
                        return string.Join(", \n", unMappedColumns
                            .Select(column =>
                            {
                                if (row.ContainsKey(column) && row[column] != null)
                                {
                                    return $"{column} - {row[column]}";
                                }
                                return null;
                            })
                            .Where(i => i != null));
                    }
                    else
                    {
                        return string.Empty;
                    }
                }*/
        public static DateTime? GetScheduledDate(this Dictionary<DataColumns, string> mappedColumnsData, DataColumns column, DataRow row, string jsonData = null)
        {
            try
            {
                // Get the date string from the mapped columns or row
                var dateString = GetStringValue(mappedColumnsData, column, row);

                // If the date string is empty or null, handle default cases
                if (string.IsNullOrEmpty(dateString))
                {
                    return column == DataColumns.ScheduledDate ? (DateTime?)null : DateTime.UtcNow;
                }

                // Try to parse the date string
                DateTime? date = null;
                CommonTimeZoneDto? commonTimeZoneDto = null;

                // Deserialize JSON data if provided
                if (!string.IsNullOrWhiteSpace(jsonData))
                {
                    commonTimeZoneDto = JsonConvert.DeserializeObject<CommonTimeZoneDto>(jsonData);
                }

                // Handle Excel date format (OLE Automation date)
                if (double.TryParse(dateString, out var excelDateValue))
                {
                    date = DateTime.FromOADate(excelDateValue);
                }
                // Handle regular date strings
                else
                {
                    if (!DateTime.TryParse(dateString, out var parsedDate))
                    {
                        // Fallback to a specific format if the default parsing fails
                        date = DateTime.ParseExact(dateString, formats, CultureInfo.InvariantCulture);
                    }
                    else
                    {
                        string formattedDate = parsedDate.ToString(CultureInfo.CurrentCulture);
                        date = DateTime.Parse(formattedDate, CultureInfo.CurrentCulture);
                    }
                }

                // Convert the date to the specified time zone if JSON data is provided
                if (commonTimeZoneDto != null)
                {
                    date = date?.ConvertDateTime(commonTimeZoneDto.TimeZoneId ?? string.Empty, commonTimeZoneDto.BaseUTcOffset);
                }

                return date;
            }
            catch
            {
                // Return null in case of any errors
                return null;
            }
        }
        public static PropertyTypeInfo GetPropertyType(string basePropertyType, string subPropertyType, List<MasterPropertyType> propertyTypes, string bhkType, string? noOfBHK, string? beds = null, string? baths = null, string? furnishedstatus = null, string? floors = null)
        {
            List<MasterPropertyType> propertyType = new();
            List<string> proprtyTypes = propertyTypes?.Select(i => i.DisplayName.ToLower()).ToList();

            List<BHKType> bhkTypes = new();
            List<double> listOfBHKs = new();
            List<int> listOfBeds = new();
            List<int> listOfBaths = new();
            FurnishStatus FurnishStatus = new();
            List<string> listOfFloors = new();
            List<string> subPropertyTypes = new();
            if (!string.IsNullOrEmpty(subPropertyType))
            {
                foreach (string subProp in subPropertyType.Split(','))
                {
                    if (!string.IsNullOrWhiteSpace(subProp))
                    {
                        subPropertyTypes.Add(subProp);
                    }
                }
            }
            if (!string.IsNullOrEmpty(bhkType))
            {
                foreach (string bhk in bhkType.Split(','))
                {
                    if (Enum.TryParse<BHKType>(bhk, true, out BHKType type))
                    {
                        bhkTypes.Add(type);
                    }
                }
            }
            if (!string.IsNullOrEmpty(noOfBHK))
            {
                foreach (string bhk in noOfBHK.Split(','))
                {
                    double bHK = CreateLeadHelper.GetNoOfBHK(bhk);
                    if (bHK != 0)
                    {
                        listOfBHKs.Add(bHK);
                    }
                }
            }
            if (!string.IsNullOrEmpty(floors))
            {
                foreach (string floor in floors.Split(','))
                {
                    if (!string.IsNullOrWhiteSpace(floor))
                    {
                        listOfFloors.Add(floor);
                    }
                }


            }
            if (!string.IsNullOrWhiteSpace(furnishedstatus))
            {
                foreach (string furnished in furnishedstatus.Split(','))
                {
                    if (Enum.TryParse<FurnishStatus>(furnished, true, out FurnishStatus type))
                    {
                        FurnishStatus = type;
                    }
                }
            }
            if (!string.IsNullOrWhiteSpace(beds))
            {
                foreach (string bed in beds.Split(','))
                {
                    int noBed = CreateLeadHelper.Get(bed);
                    listOfBeds.Add(noBed);
                }
            }
            if (!string.IsNullOrWhiteSpace(baths))
            {
                foreach (string bath in baths.Split(','))
                {
                    int noBaths = CreateLeadHelper.Get(bath);
                    if (noBaths != 0)
                    {
                        listOfBaths.Add(noBaths);
                    }
                }
            }

            if (string.IsNullOrEmpty(basePropertyType) && string.IsNullOrEmpty(subPropertyType))
            {
                return new PropertyTypeInfo() { IsValidInfo = false };
            }

            string subProperty = string.Empty;
            string baseProperty = string.Empty;
            List<string> subProperties = new();

            if (!string.IsNullOrEmpty(subPropertyType))
            {
                foreach (var subPrope in subPropertyTypes)
                {
                    if (!string.IsNullOrEmpty(subPrope))
                    {
                        if (subPrope.ToLower() == "apartment" || subPrope.ToLower() == "studio apartment" || subPrope.ToLower() == "1rk" || subPrope.ToLower() == "serviced apartment" || subPrope.ToLower() == "penthouse")
                        {
                            subProperty = "flat";
                            subProperties.Add(subProperty);

                        }
                        else if (subPrope.ToLower() == "independent" || subPrope.ToLower() == "builder floor" || subPrope.ToLower() == "farmhouse")
                        {
                            subPropertyType = "independent house";
                            subProperties.Add(subProperty);

                        }
                        else if (subPrope.ToLower() == "office" || subPrope.ToLower() == "office in it park")
                        {
                            subPropertyType = "office space";
                            subProperties.Add(subProperty);

                        }
                        else if (subPrope.ToLower() == "retail")
                        {
                            subPropertyType = "shop";
                            subProperties.Add(subProperty);

                        }
                        else if (subPrope.ToLower() == "storage")
                        {
                            subPropertyType = "basement";
                            subProperties.Add(subProperty);

                        }
                        else if (subPrope.ToLower() == "industry")
                        {
                            subPropertyType = "industrial space";
                            subProperties.Add(subProperty);

                        }
                        else if (subPrope.ToLower() == "hospitality")
                        {
                            subPropertyType = "hotel space";
                            subProperties.Add(subProperty);

                        }
                        else if (subPrope.ToLower() == "warehouse")
                        {
                            subPropertyType = "godown";
                            subProperties.Add(subProperty);
                        }

                    }
                }
            }
            else
            {
                if (!string.IsNullOrEmpty(basePropertyType))
                {
                    if (basePropertyType.ToLower().Contains("residential") || basePropertyType.ToLower() == "r")
                    {
                        subProperty = "flat";
                        subProperties.Add(subProperty);
                    }
                    if (basePropertyType.ToLower().Contains("commercial") || basePropertyType.ToLower() == "c")
                    {
                        subProperty = "Plot";
                        subProperties.Add(subProperty);
                    }
                    if (basePropertyType.ToLower().Contains("agricultural") || basePropertyType.ToLower() == "a")
                    {
                        subProperty = "land";
                        subProperties.Add(subProperty);
                    }
                }
            }

            if (!string.IsNullOrEmpty(basePropertyType) && !basePropertyType.ToLower().Contains("agricultural") && basePropertyType.ToLower() != "a" && subProperty.ToLower() == "land")
            {
                subProperty = "plot";
            }

            if (string.IsNullOrEmpty(basePropertyType) && subPropertyTypes.Any())
            {
                var sub = subPropertyTypes?.FirstOrDefault();
                if (propertyTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") && i.DisplayName.ToLower() == sub.ToLower() && !i.IsDeleted) != null)
                {
                    basePropertyType = "residential";
                }
                else if (propertyTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") && i.DisplayName.ToLower() == sub.ToLower() && !i.IsDeleted) != null)
                {
                    basePropertyType = "commercial";
                }
                else
                {
                    basePropertyType = "agricultural";
                }
            }


            if (!string.IsNullOrEmpty(basePropertyType) && (basePropertyType?.ToLower() == "commercial" || basePropertyType?.ToLower() == "c"))
            {
                foreach (var sub in subPropertyTypes)
                {
                    var propertyTypeadd = propertyTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") && i.DisplayName.ToLower() == sub.ToLower() && !i.IsDeleted);
                    propertyType.Add(propertyTypeadd);
                }
            }
            else if (string.IsNullOrEmpty(basePropertyType) || (basePropertyType?.ToLower() == "residential" || basePropertyType?.ToLower() == "r"))
            {
                foreach (var sub in subPropertyTypes)
                {
                    var propertyTypeadd = propertyTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") && i.DisplayName.ToLower() == sub.ToLower() && !i.IsDeleted);
                    if (propertyTypeadd != null)
                    {
                        propertyType.Add(propertyTypeadd);
                    }
                }
            }
            else
            {
                foreach (var sub in subPropertyTypes)
                {
                    var propertyTypeadd = propertyTypes.FirstOrDefault(i => i.DisplayName.ToLower() == sub.ToLower());
                    propertyType.Add(propertyTypeadd);
                }
            }
            return new PropertyTypeInfo()
            {
                PropertyTypes = propertyType,
                PropertyType = propertyType?.FirstOrDefault(),
                BHKTypes = bhkTypes,
                BHKs = listOfBHKs,
                Beds = listOfBeds,
                Baths = listOfBaths,
                Furnished = FurnishStatus,
                Floors = listOfFloors,
                IsValidInfo = true
            };
        }
        public static double GetNumber(string number)
        {
            try
            {
                Regex regex = new Regex(@"^\d+");
                Match match = regex.Match(number);
                double integer = 0;

                if (match.Success)
                {
                    integer = int.Parse(match.Value);
                }
                return integer;
            }
            catch (Exception e)
            {
                throw;
            }

        }

        public static string GetCurrencySymbol1Migrate(this Dictionary<DataColumns, string> mappedColumnsData, DataRow row, string? currency, string? defaultcurrency)
        {
            if (string.IsNullOrWhiteSpace(currency))
            {
                return defaultcurrency;
            }
            currency = currency.Replace(" ", "").ToUpper();
            var currencies = ISO3166.Country.List
                .SelectMany(country => new Nager.Country.CountryProvider().GetCountries()
                    .Where(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase))
                    .SelectMany(c => c.Currencies)
                    .Select(currency => currency.IsoCode))
                .ToList();
            bool isCurrencyCode = !currencies.Contains(currency);
            if (isCurrencyCode)
            {
                string symbol;
                if (TryGetCurrencySymbol(currency, out symbol))
                {
                    return symbol;
                }
                else
                {
                    return defaultcurrency;
                }
            }
            if (GetValidCurrencySymbols().Contains(currency))
            {
                return currency;
            }

            else
            {
                return defaultcurrency;
            }
        }
        public static bool TryGetCurrencySymbol(string currencySymbol, out string isoCurrencyCode)
        {
            isoCurrencyCode = CultureInfo
        .GetCultures(CultureTypes.AllCultures)
        .Where(c => !c.IsNeutralCulture)
        .Select(culture =>
        {
            try
            {
                return new RegionInfo(culture.Name);
            }
            catch
            {
                return null;
            }
        })
        .Where(ri => ri != null && ri.CurrencySymbol.Equals(currencySymbol, StringComparison.OrdinalIgnoreCase))
        .Select(ri => ri.ISOCurrencySymbol)
        .FirstOrDefault();

            return isoCurrencyCode != null;

        }




        public static List<string> GetValidCurrencySymbols()
        {
            var symbols = new List<string>();
            var countries = ISO3166.Country.List;

            var currencyProvider = new Nager.Country.CountryProvider();

            foreach (var country in countries)
            {
                var matchingCountry = currencyProvider.GetCountries()
                    .FirstOrDefault(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase));

                if (matchingCountry != null)
                {
                    foreach (var currency in matchingCountry.Currencies)
                    {
                        var symbol = currency.IsoCode;
                        symbols.Add(symbol);
                    }
                }
            }

            return symbols;
        }
        private static int GetIntValue(this Dictionary<DataColumns, string> mappedColumnsData, DataColumns column, DataRow row)
        {
            if (!mappedColumnsData.ContainsKey(column) || string.IsNullOrEmpty(mappedColumnsData[column]))
            {
                return 0;
            }

            var value = row[mappedColumnsData[column]]?.ToString();
            if (int.TryParse(value, out int result))
            {
                return result;
            }

            return 0;
        }



        private static string GetStringValue(this Dictionary<DataColumns, string> mappedColumnsData, DataColumns column, DataRow row)
        {
            var data = !mappedColumnsData.ContainsKey(column)
                    || string.IsNullOrEmpty(mappedColumnsData[column])
                    ? string.Empty
                    : row[mappedColumnsData[column]]?.ToString();
            return data?.Trim() ?? string.Empty;
        }

        private static DateTime? GetDataTimeValue(this Dictionary<DataColumns, string> mappedColumnsData, DataColumns column, DataRow row)
        {
            try
            {
                var dateString = GetStringValue(mappedColumnsData, column, row);
                if (string.IsNullOrEmpty(dateString))
                {
                    return null;
                }
                else
                {
                    DateTime? date = null;
                    try
                    {
                        if (double.TryParse(dateString, out var value))
                        {
                            date = DateTime.FromOADate(value);
                        }
                        else
                        {
                            date = Convert.ToDateTime(dateString, CultureInfo.GetCultureInfo("hi-IN"));
                        }
                        if (date.HasValue)
                        {
                            return date.Value.ToUniversalTime();
                        }
                    }
                    catch
                    {
                        return date;
                    }
                    return date;
                }
            }
            catch
            {
                return DateTime.UtcNow;
            }

        }
        private static (long Min, long Max) GetBudgetValues(this Dictionary<DataColumns, string> mappedDataColumns, DataRow row)
        {
            var minBudgetString = mappedDataColumns.GetStringValue(DataColumns.LowerBudget, row);
            var maxBudgetString = mappedDataColumns.GetStringValue(DataColumns.UpperBudget, row);
            var budgetStrings = mappedDataColumns.GetStringValue(DataColumns.Budget, row)?.Split("-")?.ToList() ?? new();
            long minBudget = (long)ConvertBuget(minBudgetString ?? budgetStrings?.FirstOrDefault() ?? string.Empty);
            long maxBudget = (long)ConvertBuget(maxBudgetString ?? budgetStrings?.LastOrDefault() ?? string.Empty);
            return (minBudget, maxBudget);
        }
        private static bool TryGetPriceValue(this Dictionary<DataColumns, string> mappedDataColumns, DataColumns column, DataRow row, out double value)
        {
            var budgetString = mappedDataColumns.GetStringValue(column, row);
            if (string.IsNullOrEmpty(budgetString))
            {
                value = 0;
                return default;
            }
            else
            {
                value = double.TryParse(budgetString, out value) ? value : ConvertBuget(budgetString);
                if (value != default)
                {
                    return true;
                }
            }
            return false;
        }
        private static T? GetEnumValue<T>(this Dictionary<DataColumns, string> mappedDataColumns, DataColumns column, DataRow row) where T : struct
        {
            var enumString = mappedDataColumns.GetStringValue(column, row);
            if (Enum.TryParse(enumString, true, out T value))
            {
                return value;
            }
            else
            {
                return null;
            }
        }
        private static double ConvertBuget(string budget)
        {
            if (double.TryParse(budget, out double result))
            {
                return result;
            }
            var lakhRegex = new Regex(@"\d+\s*(lak|lac|lack|lacks|lakh|l)s?", RegexOptions.IgnoreCase);
            var lakhMatch = lakhRegex.Match(budget);

            if (lakhMatch.Success)
            {
                var lakhString = lakhMatch.Value.ToLower();
                var croreRegex = new Regex(@"\d+\s*(crore|crores|cr|crs|c)", RegexOptions.IgnoreCase);
                var croreMatch = croreRegex.Match(budget);
                var crore = 0D;

                if (croreMatch.Success)
                {
                    crore = double.Parse(croreMatch.Value.ToLower().Replace("crore", "").Replace("crores", "").Replace("cr", "").Replace("crs", "").Replace("c", "").Trim());
                }
                var thousandRegex = new Regex(@"\d+\s*(thousand|thousands|tho|thosand|k)s?", RegexOptions.IgnoreCase);
                var thousandMatch = thousandRegex.Match(budget);
                var thousand = 0D;
                if (thousandMatch.Success)
                {
                    thousand = double.Parse(thousandMatch.Value.ToLower().Replace("thousand", "").Replace("thousands", "").Replace("thousands", "").Replace("tho", "").Replace("k", "").Trim());
                }
                var lakh = double.Parse(Regex.Replace(lakhString, @"[^\d]", "")) * 100000;
                result = crore * 10000000 + lakh + thousand * 1000;
            }
            else
            {
                var thousandRegex = new Regex(@"\d+\s*(thousand|thousands|tho|thosand|k)s?", RegexOptions.IgnoreCase);
                var thousandMatch = thousandRegex.Match(budget);

                if (thousandMatch.Success)
                {
                    var thousandString = thousandMatch.Value.ToLower();
                    var croreRegex = new Regex(@"\d+\s*(crore|crores|cr|crs)", RegexOptions.IgnoreCase);
                    var croreMatch = croreRegex.Match(budget);
                    var crore = 0D;

                    if (croreMatch.Success)
                    {
                        crore = double.Parse(croreMatch.Value.ToLower().Replace("crore", "").Replace("crores", "").Replace("cr", "").Replace("crs", "").Trim());
                    }

                    var thousand = double.Parse(Regex.Replace(thousandString, @"[^\d]", "")) * 1000;
                    result = crore * 10000000 + thousand;
                }
                else
                {
                    var croreRegex = new Regex(@"\d+\s*(crore|crores|cr|crs)", RegexOptions.IgnoreCase);
                    var croreMatch = croreRegex.Match(budget);

                    if (croreMatch.Success)
                    {
                        var croreString = croreMatch.Value.ToLower();
                        var millionRegex = new Regex(@"\d+\s*(million|millions)", RegexOptions.IgnoreCase);
                        var millionMatch = millionRegex.Match(budget);
                        var million = 0D;
                        if (millionMatch.Success)
                        {
                            million = double.Parse(millionMatch.Value.ToLower().Replace("million", "").Replace("millions", "").Trim());
                        }

                        var crore = double.Parse(Regex.Replace(croreString, @"[^\d]", "")) * 10000000;
                        result = crore + million * 1000000;
                    }
                }
            }
            return result;
        }

        private static async Task<List<Domain.Entities.Property>> GetPropertiesAsync(this Dictionary<DataColumns, string> mappedDataColumns,
            DataColumns column,
            DataRow row,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo)
        {
            var propertyString = mappedDataColumns.GetStringValue(column, row);
            var propertyNames = propertyString.Split(",")?.ToList();
            propertyNames = (propertyNames?.Any() ?? false) ? propertyNames.Where(i => !string.IsNullOrWhiteSpace(i))?.ToList() : null;
            var leadProperties = new List<Domain.Entities.Property>();
            if (propertyNames != null && propertyNames.Any())
            {
                foreach (var propertyName in propertyNames)
                {
                    var existingProperty = await propertyRepo.FirstOrDefaultAsync(new GetPropertyByTitleSpec(propertyName.Trim().ToLower()), default);
                    if (existingProperty != null)
                    {
                        leadProperties.Add(existingProperty);
                    }
                    else
                    {
                        var newProperty = new Domain.Entities.Property() { Title = propertyName };
                        newProperty = await propertyRepo.AddAsync(newProperty, default);
                        leadProperties.Add(newProperty);
                    }
                }
            }
            return leadProperties;
        }
        private static async Task<List<Lrb.Domain.Entities.Project>> GetProjectsAsync(this Dictionary<DataColumns, string> mappedDataColumns,
            DataColumns column,
            DataRow row,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo)
        {
            var projectString = mappedDataColumns.GetStringValue(column, row);
            var projectNames = projectString.Split(",")?.ToList();
            projectNames = (projectNames?.Any() ?? false) ? projectNames.Where(i => !string.IsNullOrWhiteSpace(i))?.ToList() : null;
            var leadProjects = new List<Lrb.Domain.Entities.Project>();
            if (projectNames != null && projectNames.Any())
            {
                foreach (var projectName in projectNames)
                {
                    var existingProject = await projectRepo.FirstOrDefaultAsync(new GetNewProjectsByIdV2Spec(projectName.Trim().ToLower()), default);
                    if (existingProject != null)
                    {
                        leadProjects.Add(existingProject);
                    }
                    else
                    {
                        var newProjects = new Lrb.Domain.Entities.Project() { Name = projectName };
                        newProjects = await projectRepo.AddAsync(newProjects, default);
                        leadProjects.Add(newProjects);
                    }
                }
            }
            return leadProjects;
        }
        private static (MasterPropertyType? PropertyType, BHKType? BHKType, double? NoOfBHK) GetPropertyTypeInfo(this Dictionary<DataColumns, string> mappedDataColumns,
            DataRow row, List<MasterPropertyType> propertyTypes)
        {
            string basePropertyType = mappedDataColumns.GetStringValue(DataColumns.BasePropertyType, row);
            string subPropertyType = mappedDataColumns.GetStringValue(DataColumns.SubPropertyType, row);
            string bhkType = mappedDataColumns.GetStringValue(DataColumns.BHKType, row);
            string noOfBHK = mappedDataColumns.GetStringValue(DataColumns.NoOfBHK, row);

            MasterPropertyType? propertyType = null;
            BHKType? bHKType = Enum.TryParse(bhkType, true, out BHKType value) ? value : null;
            double bHK = CreateLeadHelper.GetNoOfBHK(noOfBHK);

            if (!string.IsNullOrEmpty(subPropertyType))
            {
                if (!string.IsNullOrEmpty(basePropertyType))
                {
                    if (basePropertyType.ToLower().Contains("residential"))
                    {
                        subPropertyType = "flat";
                    }
                    if (basePropertyType.ToLower().Contains("commercial"))
                    {
                        subPropertyType = "Plot";
                    }
                    if (basePropertyType.ToLower().Contains("agricultural"))
                    {
                        subPropertyType = "land";
                    }
                }
                else
                {
                    if (bHKType != null && bHK > 0)
                    {
                        subPropertyType = "flat";
                    }
                }
                propertyType = propertyTypes.FirstOrDefault(i =>
                            i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")
                            && i.DisplayName != null && !string.IsNullOrEmpty(subPropertyType)
                            && i.DisplayName.ToLower() == subPropertyType.ToLower() && !i.IsDeleted);
                return (propertyType, bHKType, bHK);
            }
            else if (basePropertyType != null)
            {
                if (basePropertyType.ToLower().Contains("residential"))
                {
                    propertyType = propertyTypes.FirstOrDefault(i =>
                            i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")
                            && i.DisplayName != null && !string.IsNullOrEmpty(subPropertyType)
                            && i.DisplayName.ToLower() == subPropertyType.ToLower() && !i.IsDeleted);
                }
                if (basePropertyType.ToLower().Contains("commercial"))
                {
                    propertyType = propertyTypes.FirstOrDefault(i =>
                            i.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")
                            && i.DisplayName != null && !string.IsNullOrEmpty(subPropertyType)
                            && i.DisplayName.ToLower() == subPropertyType.ToLower() && !i.IsDeleted);
                }
                if (basePropertyType.ToLower().Contains("agricultural"))
                {
                    propertyType = propertyTypes.FirstOrDefault(i => i.DisplayName != null && i.DisplayName.ToLower() == subPropertyType.ToLower() && !i.IsDeleted);
                }
            }
            propertyType ??= propertyTypes.FirstOrDefault(i => i.DisplayName != null && i.DisplayName.ToLower() == subPropertyType.ToLower() && !i.IsDeleted);
            return (propertyType, bHKType, bHK);
        }

        private static CustomMasterLeadStatus? GetLeadStatus(this Dictionary<DataColumns, string> mappedDataColumns,
            DataRow row, List<CustomMasterLeadStatus> leadStatuses)
        {
            CustomMasterLeadStatus? leadStatus = null;
            var baseStatusString = mappedDataColumns.GetStringValue(DataColumns.BaseStatus, row);
            var subStatusString = mappedDataColumns.GetStringValue(DataColumns.SubStatus, row);
            if (!string.IsNullOrWhiteSpace(subStatusString))
            {
                if (!string.IsNullOrWhiteSpace(baseStatusString))
                {
                    var subStatuses = leadStatuses.Where(i => !i.IsDeleted && ((i.DisplayName != null && i.DisplayName.ToLower().Replace(" ", "") == subStatusString.ToLower().Replace(" ", "")) || i.Status == subStatusString.Replace(" ", "_").ToLower())).ToList();
                    var baseStatus = leadStatuses.FirstOrDefault(i => !i.IsDeleted && ((i.DisplayName != null && i.DisplayName.ToLower().Replace(" ", "") == baseStatusString.ToLower().Replace(" ", "")) || i.Status == baseStatusString.Replace(" ", "_").ToLower()));
                    if (baseStatus != null)
                    {
                        leadStatus = subStatuses.FirstOrDefault(i => i.BaseId == baseStatus.Id);
                    }
                }
                leadStatus ??= leadStatuses.FirstOrDefault(i => !i.IsDeleted && ((i.DisplayName != null && i.DisplayName.ToLower().Replace(" ", "") == subStatusString.ToLower().Replace(" ", "")) || i.Status == subStatusString.Replace(" ", "_").ToLower()));
            }
            else
            {
                if (!string.IsNullOrWhiteSpace(baseStatusString))
                {
                    leadStatus = leadStatuses.FirstOrDefault(i => !i.IsDeleted && ((i.DisplayName != null && i.DisplayName.ToLower().Replace(" ", "").Contains(baseStatusString.ToLower().Replace(" ", ""))) || i.Status == baseStatusString.Replace(" ", "_").ToLower()));
                }
            }
            return leadStatus != null && leadStatus.IsValidStatus(leadStatuses) ? leadStatus : null;
        }
        private static bool IsValidStatus(this CustomMasterLeadStatus leadStatus, List<CustomMasterLeadStatus> leadStatuses)
        {
            if (leadStatus == null)
            {
                return false;
            }
            else
            {
                if (leadStatus.BaseId == default || leadStatus.BaseId == null)
                {
                    return !leadStatuses.Any(i => i.BaseId == leadStatus.Id);
                }
                else
                {
                    return leadStatuses.Any(i => i.Id == leadStatus.BaseId);
                }
            }
        }

        private static string GetAdditionalDataAsNote(this Dictionary<DataColumns, string> mappedDataColumns,
            DataRow row,
            List<string> unMappedColumns,
            MasterPropertyType? propertyType,
            BHKType? bHKType,
            double? noOfBHK,
            LeadSource? source,
            EnquiryType? enquiredFor,
            long minBudget,
            long maxBudget,
            CustomMasterLeadStatus? leadStatus)
        {
            var notes = unMappedColumns.Any()
                ? string.Join(", \n", unMappedColumns.Select(column => !string.IsNullOrEmpty(row[column].ToString())
                                                                       ? column + " - " + row[column]
                                                                       : null).Where(i => i != null))
                : string.Empty;

            if (propertyType == null)
            {
                string basePropertyType = mappedDataColumns.GetStringValue(DataColumns.BasePropertyType, row);
                string subPropertyType = mappedDataColumns.GetStringValue(DataColumns.SubPropertyType, row);
                notes += !string.IsNullOrWhiteSpace(basePropertyType) ? $"\n{mappedDataColumns[DataColumns.BasePropertyType]} - {basePropertyType}" : string.Empty;
                notes += !string.IsNullOrWhiteSpace(subPropertyType) ? $"\n{mappedDataColumns[DataColumns.SubPropertyType]} - {subPropertyType}" : string.Empty;
            }
            if (bHKType == null || bHKType == (BHKType)default)
            {
                string bhkTypeString = mappedDataColumns.GetStringValue(DataColumns.BHKType, row);
                notes += !string.IsNullOrWhiteSpace(bhkTypeString) ? $"\n{mappedDataColumns[DataColumns.BHKType]} - {bhkTypeString}" : string.Empty;
            }
            if (noOfBHK == null || noOfBHK <= 0)
            {
                string noOfBHKString = mappedDataColumns.GetStringValue(DataColumns.NoOfBHK, row);
                notes += !string.IsNullOrWhiteSpace(noOfBHKString) ? $"\n{mappedDataColumns[DataColumns.NoOfBHK]} - {noOfBHKString}" : string.Empty;
            }
            if ((minBudget < 0 && maxBudget < 0) || (minBudget > maxBudget))
            {
                var budgetString = mappedDataColumns.GetStringValue(DataColumns.Budget, row);
                string? minBudgetString = mappedDataColumns.GetStringValue(DataColumns.LowerBudget, row);
                string? maxBudgetString = mappedDataColumns.GetStringValue(DataColumns.LowerBudget, row);
                notes += !string.IsNullOrWhiteSpace(budgetString) ? $"\n{mappedDataColumns[DataColumns.Budget]} - {budgetString}" : string.Empty;
                notes += !string.IsNullOrWhiteSpace(minBudgetString) ? $"\n{mappedDataColumns[DataColumns.LowerBudget]} - {minBudgetString}" : string.Empty;
                notes += !string.IsNullOrWhiteSpace(maxBudgetString) ? $"\n{mappedDataColumns[DataColumns.UpperBudget]} - {maxBudgetString}" : string.Empty;
            }
            if (source == null)
            {
                var sourceString = mappedDataColumns.GetStringValue(DataColumns.Source, row);
                notes += !string.IsNullOrWhiteSpace(sourceString) ? $"\n{mappedDataColumns[DataColumns.Source]} - {sourceString}" : string.Empty;
            }
            if (enquiredFor == null)
            {
                var enquiredForString = mappedDataColumns.GetStringValue(DataColumns.EnquiredFor, row);
                notes += !string.IsNullOrWhiteSpace(enquiredForString) ? $"\n{mappedDataColumns[DataColumns.EnquiredFor]} - {enquiredForString}" : string.Empty;
            }
            if (leadStatus == null)
            {
                var baseStatusString = mappedDataColumns.GetStringValue(DataColumns.BaseStatus, row);
                var subStatusString = mappedDataColumns.GetStringValue(DataColumns.SubStatus, row);
                notes += !string.IsNullOrWhiteSpace(baseStatusString) ? $"\n{mappedDataColumns[DataColumns.BaseStatus]} - {baseStatusString}" : string.Empty;
                notes += !string.IsNullOrWhiteSpace(subStatusString) ? $"\n{mappedDataColumns[DataColumns.SubStatus]} - {subStatusString}" : string.Empty;
            }
            notes += $"\nAdded to leadrat on GMT - {DateTime.UtcNow}";
            return notes;
        }

        private static LeadTag GetLeadTags(this Dictionary<DataColumns, string> mappedDataColumns, DataRow row)
        {
            var leadTags = new LeadTag();
            try
            {
                var tagString = GetStringValue(mappedDataColumns, DataColumns.Tag, row);
                if (!string.IsNullOrWhiteSpace(tagString))
                {
                    var properties = typeof(LeadTag).GetProperties().Where(i => i.PropertyType == typeof(bool));
                    var enabledTag = properties.FirstOrDefault(i => i.Name.ToLowerInvariant().Contains(tagString.ToLower()));
                    if (enabledTag != null)
                    {
                        enabledTag.SetValue(leadTags, true);
                    }
                }
            }
            catch { }
            return leadTags;
        }

        private static async Task<List<Domain.Entities.Agency>> GetAgenciesAsync(this Dictionary<DataColumns, string> mappedDataColumns,
            DataColumns column,
            DataRow row,
            IRepositoryWithEvents<Domain.Entities.Agency> agencyRepo)
        {
            var agencyString = mappedDataColumns.GetStringValue(column, row);
            var agencyNames = agencyString.Split(",")?.ToList();
            agencyNames = (agencyNames?.Any() ?? false) ? agencyNames.Where(i => !string.IsNullOrWhiteSpace(i))?.ToList() : null;
            var leadAgencies = new List<Domain.Entities.Agency>();
            if (agencyNames != null && agencyNames.Any())
            {
                foreach (var agencyName in agencyNames)
                {
                    var existingAgency = await agencyRepo.FirstOrDefaultAsync(new GetAgencyByNameSpec(agencyName.Trim().ToLower()), default);
                    if (existingAgency != null)
                    {
                        leadAgencies.Add(existingAgency);
                    }
                    else
                    {
                        var newAgency = new Domain.Entities.Agency() { Name = agencyName };
                        newAgency = await agencyRepo.AddAsync(newAgency, default);
                        leadAgencies.Add(newAgency);
                    }
                }
            }
            return leadAgencies;
        }

        public static (List<Domain.Entities.Lead>, List<InvalidData>) MigrateAsyncV2(
            this DataTable table,
            Dictionary<DataColumns, string>? mappedColumnsData,
            List<string> unMappedColumns,
            MasterItems masterItems,
            LeadMigrateTracker? leadMigrateTracker,
            IRepositoryWithEvents<Lrb.Domain.Entities.Source> _sourceRepo,
            Guid? currentUserId = null,
            string jsonData = null)
        {
            List<Domain.Entities.Lead> leads = new();
            List<InvalidData> invalidLeads = new();
            PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
            if (mappedColumnsData == null)
            {
                return (leads, invalidLeads);
            }
            else
            {
                Guid? defaultUnitId = Guid.TryParse(masterItems?.GlobalSettings?.DefaultValues?.FirstOrDefault().Value, out Guid parsedGuid) ? parsedGuid : (Guid?)null;

                foreach (DataRow row in table.Rows)
                {
                    string lowerBudget = !mappedColumnsData.ContainsKey(DataColumns.LowerBudget) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.LowerBudget]) ? string.Empty : row[mappedColumnsData[DataColumns.LowerBudget]].ToString();
                    string upperBudget = !mappedColumnsData.ContainsKey(DataColumns.UpperBudget) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.UpperBudget]) ? string.Empty : row[mappedColumnsData[DataColumns.UpperBudget]].ToString();
                    var name = mappedColumnsData.GetStringValue(DataColumns.Name, row);
                    name = string.IsNullOrWhiteSpace(name?.Trim()) ? "Unknown" : name.Trim();
                    //var leadTags = GetLeadTags(mappedColumnsData, row);
                    var customflags = !mappedColumnsData.ContainsKey(DataColumns.Tag) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Tag]) ? string.Empty : row[mappedColumnsData[DataColumns.Tag]].ToString();
                    var projectName = !mappedColumnsData.ContainsKey(DataColumns.Project) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Project]) ? null : row[mappedColumnsData[DataColumns.Project]].ToString();
                    var projectNames = projectName?.Split(',').Select(p => p.Trim()).Where(p => !string.IsNullOrWhiteSpace(p)).ToList();
                    var projects = masterItems.Projects?
                                    .Where(i => projectNames != null && projectNames.Any(name =>
                                        i.Name.Trim().ToLower().Equals(name.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase)))
                                    .ToList();
                    var propertyName = !mappedColumnsData.ContainsKey(DataColumns.Property) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Property]) ? null : row[mappedColumnsData[DataColumns.Property]].ToString();
                    var propertyNames = propertyName?.Split(',').Select(p => p.Trim()).Where(p => !string.IsNullOrWhiteSpace(p)).ToList();
                    var properties = masterItems.Properties?
                                    .Where(i => propertyNames != null && propertyNames.Any(title =>
                                        i.Title.Trim().ToLower().Equals(title.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase)))
                                    .ToList();
                    string? enquiryFor = !mappedColumnsData.ContainsKey(DataColumns.EnquiredFor) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.EnquiredFor]) ? string.Empty : row[mappedColumnsData[DataColumns.EnquiredFor]].ToString();
                    string? bhkType = !mappedColumnsData.ContainsKey(DataColumns.BHKType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BHKType]) ? string.Empty : row[mappedColumnsData[DataColumns.BHKType]].ToString();
                    var beds = !mappedColumnsData.ContainsKey(DataColumns.Beds) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Beds]) ? string.Empty : row[mappedColumnsData[DataColumns.Beds]].ToString();
                    var baths = !mappedColumnsData.ContainsKey(DataColumns.Baths) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Baths]) ? string.Empty : row[mappedColumnsData[DataColumns.Baths]].ToString();
                    var floors = !mappedColumnsData.ContainsKey(DataColumns.PreferredFloor) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.PreferredFloor]) ? string.Empty : row[mappedColumnsData[DataColumns.PreferredFloor]].ToString();
                    var furnishStatus = !mappedColumnsData.ContainsKey(DataColumns.FurnishStatus) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.FurnishStatus]) ? string.Empty : row[mappedColumnsData[DataColumns.FurnishStatus]].ToString();
                    string? noOfBhks = !mappedColumnsData.ContainsKey(DataColumns.NoOfBHK) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.NoOfBHK]) ? string.Empty : row[mappedColumnsData[DataColumns.NoOfBHK]].ToString();
                    string? baseProperty = !mappedColumnsData.ContainsKey(DataColumns.BasePropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BasePropertyType]) ? string.Empty : row[mappedColumnsData[DataColumns.BasePropertyType]].ToString();
                    string? subProperty = !mappedColumnsData.ContainsKey(DataColumns.SubPropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SubPropertyType]) ? string.Empty : row[mappedColumnsData[DataColumns.SubPropertyType]].ToString();
                    string leadSource = !mappedColumnsData.ContainsKey(DataColumns.Source) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Source]) ? string.Empty : row[mappedColumnsData[DataColumns.Source]].ToString();
                    string budget = !mappedColumnsData.ContainsKey(DataColumns.Budget) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Budget]) ? string.Empty : row[mappedColumnsData[DataColumns.Budget]].ToString();
                    var subSource = mappedColumnsData.GetStringValue(DataColumns.SubSource, row);
                    string currecncy = !mappedColumnsData.ContainsKey(DataColumns.Currency) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Currency]) ? string.Empty : row[mappedColumnsData[DataColumns.Currency]].ToString();
                    var propertyInfo = BulkUploadHelper.GetPropertyType(baseProperty, subProperty, masterItems.PropetyTypes ?? new(), bhkType, noOfBhks, beds, baths, furnishStatus, floors);
                    var leadStatus = mappedColumnsData.GetLeadStatus(row, masterItems.LeadStatuses ?? new());
                    var user = mappedColumnsData.GetStringValue(DataColumns.AssignToUser, row);
                    var assignToUser = masterItems.Users?.FirstOrDefault(u => u.UserName.ToLower() == user.ToLower().Trim().Replace(" ", "") && !u.IsDeleted);
                    var secondaryUser = mappedColumnsData.GetStringValue(DataColumns.AssignToSecondaryUser, row);
                    var assignToSecondaryUser = masterItems.Users?.FirstOrDefault(u => u.UserName.ToLower() == secondaryUser.ToLower().Trim().Replace(" ", "") && !u.IsDeleted);
                    var scheduledDate = mappedColumnsData.GetScheduledDate(DataColumns.ScheduledDate, row, jsonData);
                    string countryCode = !mappedColumnsData.ContainsKey(DataColumns.CountryCode) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CountryCode]) ? string.Empty : row[mappedColumnsData[DataColumns.CountryCode]].ToString();
                    string altCountryCode = !mappedColumnsData.ContainsKey(DataColumns.AlternativeNoCountryCode) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AlternativeNoCountryCode]) ? string.Empty : row[mappedColumnsData[DataColumns.AlternativeNoCountryCode]].ToString();
                    var offeringType = !mappedColumnsData.ContainsKey(DataColumns.OfferingType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.OfferingType]) ? string.Empty : row[mappedColumnsData[DataColumns.OfferingType]].ToString();
                    var purpose = !mappedColumnsData.ContainsKey(DataColumns.Purpose) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Purpose]) ? string.Empty : row[mappedColumnsData[DataColumns.Purpose]].ToString();
                    var createdon = mappedColumnsData.GetCreatedDate(DataColumns.CreatedDate, row, jsonData);
                    var currencycode = mappedColumnsData.GetCurrencySymbol1Migrate(row, currecncy, masterItems.GlobalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency);
                    string? carpetArea = !mappedColumnsData.ContainsKey(DataColumns.CarpetArea) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CarpetArea]) ? string.Empty : row[mappedColumnsData[DataColumns.CarpetArea]].ToString();
                    string? carpetAreaUnit = !mappedColumnsData.ContainsKey(DataColumns.CarpetAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CarpetAreaUnit]) ? string.Empty : row[mappedColumnsData[DataColumns.CarpetAreaUnit]].ToString();
                    var carpetarea = GetUnitDetails(carpetArea ?? string.Empty, masterItems.AreaUnits, carpetAreaUnit ?? string.Empty, defaultUnitId);
                    string? propertyArea = !mappedColumnsData.ContainsKey(DataColumns.PropertyArea) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.PropertyArea]) ? string.Empty : row[mappedColumnsData[DataColumns.PropertyArea]].ToString();
                    string? propertyAreaUnit = !mappedColumnsData.ContainsKey(DataColumns.PropertyAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.PropertyAreaUnit]) ? string.Empty : row[mappedColumnsData[DataColumns.PropertyAreaUnit]].ToString();
                    var propertyArea1 = GetUnitDetails(propertyArea ?? string.Empty, masterItems.AreaUnits, propertyAreaUnit ?? string.Empty, defaultUnitId);
                    string? netArea = !mappedColumnsData.ContainsKey(DataColumns.NetArea) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.NetArea]) ? string.Empty : row[mappedColumnsData[DataColumns.NetArea]].ToString();
                    string? netAreaUnit = !mappedColumnsData.ContainsKey(DataColumns.NetAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.NetAreaUnit]) ? string.Empty : row[mappedColumnsData[DataColumns.NetAreaUnit]].ToString();
                    var netArea1 = GetUnitDetails(netArea ?? string.Empty, masterItems.AreaUnits, netAreaUnit ?? string.Empty, defaultUnitId);
                    string? builtUpArea = !mappedColumnsData.ContainsKey(DataColumns.BuiltUpArea) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BuiltUpArea]) ? string.Empty : row[mappedColumnsData[DataColumns.BuiltUpArea]].ToString();
                    string? builtUpAreaUnit = !mappedColumnsData.ContainsKey(DataColumns.BuiltUpAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BuiltUpAreaUnit]) ? string.Empty : row[mappedColumnsData[DataColumns.BuiltUpAreaUnit]].ToString();
                    var builtUpArea1 = GetUnitDetails(builtUpArea ?? string.Empty, masterItems.AreaUnits, builtUpAreaUnit ?? string.Empty, defaultUnitId);
                    string? SaleableArea = !mappedColumnsData.ContainsKey(DataColumns.SaleableArea) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SaleableArea]) ? string.Empty : row[mappedColumnsData[DataColumns.SaleableArea]].ToString();
                    string? SaleableAreaUnit = !mappedColumnsData.ContainsKey(DataColumns.SaleableAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SaleableAreaUnit]) ? string.Empty : row[mappedColumnsData[DataColumns.SaleableAreaUnit]].ToString();
                    var SaleableArea1 = GetUnitDetails(SaleableArea ?? string.Empty, masterItems.AreaUnits, SaleableAreaUnit ?? string.Empty, defaultUnitId);
                    var campaignName = !mappedColumnsData.ContainsKey(DataColumns.CampaignName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CampaignName]) ? null : row[mappedColumnsData[DataColumns.CampaignName]].ToString();
                    var campaign = masterItems.Campaigns?.FirstOrDefault(i => i.Name.Trim().ToLower().Equals(campaignName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                    if (lowerBudget?.Contains('-') ?? false)
                    {
                        string[] lowerbudgets = lowerBudget.Split('-');
                        lowerBudget = lowerbudgets.FirstOrDefault();
                    }
                    if (upperBudget?.Contains('-') ?? false)
                    {
                        string[] upperbudgets = upperBudget.Split('-');
                        upperBudget = upperbudgets.LastOrDefault();
                    }
                    var lowerBudgetInfo = BudgetHelper.ConvertBuget(lowerBudget ?? string.Empty);
                    var upperBudgetinfo = BudgetHelper.ConvertBuget(upperBudget ?? string.Empty);
                    var leadsourceInfo = BulkUploadHelper.GetLeadSourceInfo(leadSource ?? string.Empty, _sourceRepo);
                    SubSourceInfo? subSourceInfo = new();
                    if (leadsourceInfo?.Result != null)
                    {
                        subSourceInfo = BulkUploadHelper.GetSubSource(masterItems.SubSources ?? new(), leadsourceInfo.Result.IsValidInfo ? leadsourceInfo.Result.LeadSource : null, subSource ?? string.Empty);
                    }
                    var enquiryTypesInfo = BulkUploadHelper.GetEnquiryTypesInfo(enquiryFor ?? string.Empty);
                    var offeringTypeInfo = BulkUploadHelper.GetOfferingTypesInfo(offeringType ?? string.Empty);
                    var purposees = BulkUploadHelper.GetPurposeInfo(purpose ?? string.Empty);
                    var siteVisitDone = mappedColumnsData.GetIntValue(DataColumns.SiteVisitDoneCount, row);
                    var siteVisitNotDone = mappedColumnsData.GetIntValue(DataColumns.SiteVisitNotDoneCount, row);
                    var meetingDone = mappedColumnsData.GetIntValue(DataColumns.MeetingDoneCount, row);
                    var meetingNotDone = mappedColumnsData.GetIntValue(DataColumns.MeetingNotDoneCount, row);
                    var agencyName = !mappedColumnsData.ContainsKey(DataColumns.AgencyName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AgencyName]) ? null : row[mappedColumnsData[DataColumns.AgencyName]].ToString();
                    var agencyToAdd = masterItems.Agencies?.FirstOrDefault(i => i.Name.Replace(" ", "").Trim().Equals(agencyName?.Replace(" ", "").Trim(), StringComparison.InvariantCultureIgnoreCase));
                    var cpName = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerName]) ? null : row[mappedColumnsData[DataColumns.ChannelPartnerName]].ToString();
                    var channelPartenr = masterItems.ChannelPartners?.FirstOrDefault(i => i.FirmName.Trim().ToLower().Equals(cpName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                    var sourcinguser = mappedColumnsData.GetStringValue(DataColumns.SourcingManager, row);
                    var sourcinguserName = masterItems.Users?.FirstOrDefault(u => u.UserName.ToLower() == sourcinguser.ToLower().Trim().Replace(" ", "") && !u.IsDeleted);
                    var ClosingUser = mappedColumnsData.GetStringValue(DataColumns.ClosingManager, row);
                    var ClosingUserName = masterItems.Users?.FirstOrDefault(u => u.UserName.ToLower() == ClosingUser.ToLower().Trim().Replace(" ", "") && !u.IsDeleted);
                    var profession = !mappedColumnsData.ContainsKey(DataColumns.Profession) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Profession]) ? string.Empty : row[mappedColumnsData[DataColumns.Profession]].ToString();
                    var validProfession = BulkUploadHelper.GetProfessionInfo(profession ?? string.Empty);
                    var possesionType = !mappedColumnsData.ContainsKey(DataColumns.PossesionType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.PossesionType]) ? string.Empty : row[mappedColumnsData[DataColumns.PossesionType]].ToString();
                    var possessionDate = mappedColumnsData.GetPossessionDate(DataColumns.PossessionDate, row, jsonData);
                    bool isValidPossessionDate = possessionDate.HasValue;
                    DateTime? dateTime = possessionDate ;
                    if (possesionType != "None" && possesionType != "CustomDate")
                    {
                        DateTime currentUtcTime = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, DateTime.DaysInMonth(DateTime.UtcNow.Year, DateTime.UtcNow.Month));
                        possessionDate = currentUtcTime;
                        isValidPossessionDate = true;

                        if (possesionType == "UnderConstruction")
                        {
                            dateTime = null;
                        }
                        else if (possesionType == "SixMonth")
                        {
                            dateTime = currentUtcTime.AddMonths(6);
                        }
                        else if (possesionType == "Year")
                        {
                            dateTime = currentUtcTime.AddYears(1);
                        }
                        else if (possesionType == "TwoYears")
                        {
                            dateTime = currentUtcTime.AddYears(2);
                        }
                    }
                    if (dateTime.HasValue && (possesionType == null || possesionType == "None"|| string.IsNullOrWhiteSpace(possesionType)))
                    {
                        possesionType = PossesionType.CustomDate.ToString();
                    }
                    string landline = !mappedColumnsData.ContainsKey(DataColumns.LandLine) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.LandLine]) ? string.Empty : row[mappedColumnsData[DataColumns.LandLine]].ToString();
                    var gender = !mappedColumnsData.ContainsKey(DataColumns.Gender) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Gender]) ? string.Empty : row[mappedColumnsData[DataColumns.Gender]].ToString();
                    var validGenderType = BulkUploadHelper.GetGenderInfo(gender ?? string.Empty);
                    var maritalStatus = !mappedColumnsData.ContainsKey(DataColumns.MaritalStatus) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.MaritalStatus]) ? string.Empty : row[mappedColumnsData[DataColumns.MaritalStatus]].ToString();
                    var validMaritalStatusType = BulkUploadHelper.GetMaritalStatusInfo(maritalStatus ?? string.Empty);
                    var dateOdBirth = !mappedColumnsData.ContainsKey(DataColumns.DateOfBirth) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.DateOfBirth]) ? string.Empty : row[mappedColumnsData[DataColumns.DateOfBirth]].ToString();
                    var validDateOfBirth = mappedColumnsData.GetDateOfBirth(DataColumns.DateOfBirth, row, jsonData);
                    Domain.Entities.Lead lead = new()
                    {
                        Id = Guid.NewGuid(),
                        Name = name,
                        ContactNo = !mappedColumnsData.ContainsKey(DataColumns.ContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ContactNo]) ? string.Empty : row[mappedColumnsData[DataColumns.ContactNo]].ToString(),
                        Email = !mappedColumnsData.ContainsKey(DataColumns.Email) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Email]) ? string.Empty : row[mappedColumnsData[DataColumns.Email]].ToString(),
                        Rating = !mappedColumnsData.ContainsKey(DataColumns.Rating) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Rating]) ? string.Empty : row[mappedColumnsData[DataColumns.Rating]].ToString(),
                        Agencies = agencyToAdd != null ? new List<Domain.Entities.Agency>() { agencyToAdd } : null,
                        ReferralName = !mappedColumnsData.ContainsKey(DataColumns.ReferralName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ReferralName]) ? null : row[mappedColumnsData[DataColumns.ReferralName]].ToString(),
                        ReferralContactNo = !mappedColumnsData.ContainsKey(DataColumns.ReferralContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ReferralContactNo]) ? null : row[mappedColumnsData[DataColumns.ReferralContactNo]].ToString(),
                        ReferralEmail = !mappedColumnsData.ContainsKey(DataColumns.ReferralEmail) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ReferralEmail]) ? null : row[mappedColumnsData[DataColumns.ReferralEmail]].ToString(),
                        AssignTo = assignToUser?.Id ?? Guid.Empty,
                        SecondaryUserId = assignToSecondaryUser?.Id ?? Guid.Empty,
                        UploadType = UploadType.Migration,
                        UploadTypeName = leadMigrateTracker?.S3BucketKey,
                        Enquiries = new List<LeadEnquiry>() {
                        new() {
                            Id = Guid.NewGuid(),
                            IsPrimary = true,
                            PropertyType = propertyInfo.IsValidInfo ? propertyInfo.PropertyType : default,
                            PropertyTypes = propertyInfo.IsValidInfo ? propertyInfo.PropertyTypes : default,
                            //LeadSource = leadsourceInfo.Result.IsValidInfo ? leadsourceInfo.Result.LeadSource : default,
                            //SubSource = subSourceInfo.IsValidInfo? (!string.IsNullOrWhiteSpace(subSourceInfo.SubSource) ? subSourceInfo.SubSource : null): null,
                            UpperBudget = upperBudgetinfo.IsValidInfo ? upperBudgetinfo.Budget : default,
                            LowerBudget = lowerBudgetInfo.IsValidInfo ? lowerBudgetInfo.Budget : default,
                            Currency = currencycode ?? "INR",
                            BHKTypes = propertyInfo.IsValidInfo ? propertyInfo.BHKTypes : default,
                            BHKs = propertyInfo.IsValidInfo ? propertyInfo.BHKs : default,
                            Beds=propertyInfo.IsValidInfo ? propertyInfo.Beds : default,
                            Baths=propertyInfo.IsValidInfo ? propertyInfo.Baths : default,
                            Furnished=propertyInfo.IsValidInfo ? propertyInfo.Furnished : default,
                            Floors =propertyInfo.IsValidInfo ? propertyInfo.Floors :default,
                            EnquiryTypes = enquiryTypesInfo.IsValidInfo ? enquiryTypesInfo.EnquiryTypes : default,
                            Addresses = new()
                            {
                                new()
                                {
                                    Id = Guid.NewGuid(),
                                    City = !mappedColumnsData.ContainsKey(DataColumns.City) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.City]) ? string.Empty : row[mappedColumnsData[DataColumns.City]].ToString(),
                                    State = !mappedColumnsData.ContainsKey(DataColumns.State) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.State]) ? string.Empty : row[mappedColumnsData[DataColumns.State]].ToString(),
                                    SubLocality = !mappedColumnsData.ContainsKey(DataColumns.Location) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Location]) ? string.Empty : row[mappedColumnsData[DataColumns.Location]].ToString(),
                                    Community = !mappedColumnsData.ContainsKey(DataColumns.Community) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Community]) ? string.Empty : row[mappedColumnsData[DataColumns.Community]].ToString(),
                                    SubCommunity = !mappedColumnsData.ContainsKey(DataColumns.SubCommunity) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SubCommunity]) ? string.Empty : row[mappedColumnsData[DataColumns.SubCommunity]].ToString(),
                                    TowerName = !mappedColumnsData.ContainsKey(DataColumns.TowerName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.TowerName]) ? string.Empty : row[mappedColumnsData[DataColumns.TowerName]].ToString(),
                                    Country = !mappedColumnsData.ContainsKey(DataColumns.Country) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Country]) ? string.Empty : row[mappedColumnsData[DataColumns.Country]].ToString(),
                                    PostalCode = !mappedColumnsData.ContainsKey(DataColumns.PostalCode) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.PostalCode]) ? string.Empty : row[mappedColumnsData[DataColumns.PostalCode]].ToString(),
                                }
                            },
                            OfferType = offeringTypeInfo.IsValidInfo ? offeringTypeInfo.OfferingType :OfferType.None,
                            CarpetArea = carpetarea.Item1,
                            CarpetAreaUnitId = carpetarea.Item2,
                            PropertyArea =  propertyArea1.Item1,
                            PropertyAreaUnitId = propertyArea1.Item2,
                            NetArea =  netArea1.Item1,
                            NetAreaUnitId = netArea1.Item2,
                            BuiltUpArea =  builtUpArea1.Item1,
                            BuiltUpAreaUnitId = builtUpArea1.Item2,
                            SaleableArea =  SaleableArea1.Item1,
                            SaleableAreaUnitId = SaleableArea1.Item2,
                            UnitName = !mappedColumnsData.ContainsKey(DataColumns.UnitName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.UnitName]) ? null : row[mappedColumnsData[DataColumns.UnitName]].ToString(),
                            ClusterName = !mappedColumnsData.ContainsKey(DataColumns.ClusterName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ClusterName]) ? null : row[mappedColumnsData[DataColumns.ClusterName]].ToString(),
                            Purpose = purposees.IsValidInfo ? purposees.Purpose :Purpose.None,
                            PossessionDate = isValidPossessionDate  ? dateTime?.ToUniversalTime() : null,
                            }
                        },
                        Projects = projects,
                        Properties = properties,
                        AlternateContactNo = !mappedColumnsData.ContainsKey(DataColumns.AlternateContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AlternateContactNo]) ? null : row[mappedColumnsData[DataColumns.AlternateContactNo]].ToString().Trim(),
                        Notes = unMappedColumns.Any() ? string.Join(", \n", unMappedColumns.Select(column => !string.IsNullOrEmpty(row[column].ToString()) ? column + " - " + row[column] : null).Where(i => i != null)) : string.Empty,
                        Designation = !mappedColumnsData.ContainsKey(DataColumns.Designation) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Designation]) ? string.Empty : row[mappedColumnsData[DataColumns.Designation]].ToString(),
                        ChannelPartnerExecutiveName = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerExecutiveName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerExecutiveName]) ? string.Empty : row[mappedColumnsData[DataColumns.ChannelPartnerExecutiveName]].ToString(),
                        ChannelPartnerName = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerName]) ? string.Empty : row[mappedColumnsData[DataColumns.ChannelPartnerName]].ToString(),
                        ChannelPartnerContactNo = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerContactNo]) ? null : row[mappedColumnsData[DataColumns.ChannelPartnerContactNo]].ToString().Trim(),
                        ScheduledDate = scheduledDate,
                        CreatedOn = createdon ?? DateTime.UtcNow,
                        CustomLeadStatus = leadStatus ?? masterItems.LeadStatuses?.FirstOrDefault(i => i.Status == "new"),
                        CompanyName = !mappedColumnsData.ContainsKey(DataColumns.CompanyName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CompanyName]) ? string.Empty : row[mappedColumnsData[DataColumns.CompanyName]].ToString(),
                        CustomFlags = GetCustomFlagsByFlagNames(customflags, masterItems.Flags ?? new()),
                        ChannelPartners = channelPartenr != null ? new List<Domain.Entities.ChannelPartner>() { channelPartenr } : null,
                        BulkCategory = BulkType.BulkMigrate,
                        CountryCode = countryCode ?? masterItems.GlobalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",
                        AltCountryCode = altCountryCode ?? masterItems.GlobalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",
                        Nationality = !mappedColumnsData.ContainsKey(DataColumns.Nationality) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Nationality]) ? null : row[mappedColumnsData[DataColumns.Nationality]].ToString(),
                        Campaigns = campaign != null ? new List<Domain.Entities.Campaign>() { campaign } : null,
                        Address = ((mappedColumnsData.ContainsKey(DataColumns.CustomerCity) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerCity]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerCity]].ToString()))
                           || (mappedColumnsData.ContainsKey(DataColumns.CustomerState) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerState]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerState]].ToString()))
                           || (mappedColumnsData.ContainsKey(DataColumns.CustomerLocation) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerLocation]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerLocation]].ToString()))
                           || (mappedColumnsData.ContainsKey(DataColumns.CustomerCommunity) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerCommunity]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerCommunity]].ToString()))
                           || (mappedColumnsData.ContainsKey(DataColumns.CustomerSubCommunity) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerSubCommunity]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerSubCommunity]].ToString()))
                           || (mappedColumnsData.ContainsKey(DataColumns.CustomerTowerName) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerTowerName]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerTowerName]].ToString()))
                           || (mappedColumnsData.ContainsKey(DataColumns.CustomerCountry) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerCountry]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerCountry]].ToString()))
                           || (mappedColumnsData.ContainsKey(DataColumns.CustomerPostalCode) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerPostalCode]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerPostalCode]].ToString()))) ?
                            new Address
                            {
                                Id = Guid.NewGuid(),
                                City = !mappedColumnsData.ContainsKey(DataColumns.CustomerCity) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerCity]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerCity]].ToString(),
                                State = !mappedColumnsData.ContainsKey(DataColumns.CustomerState) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerState]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerState]].ToString(),
                                SubLocality = !mappedColumnsData.ContainsKey(DataColumns.CustomerLocation) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerLocation]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerLocation]].ToString(),
                                Community = !mappedColumnsData.ContainsKey(DataColumns.CustomerCommunity) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerCommunity]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerCommunity]].ToString(),
                                SubCommunity = !mappedColumnsData.ContainsKey(DataColumns.CustomerSubCommunity) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerSubCommunity]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerSubCommunity]].ToString(),
                                TowerName = !mappedColumnsData.ContainsKey(DataColumns.CustomerTowerName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerTowerName]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerTowerName]].ToString(),
                                Country = !mappedColumnsData.ContainsKey(DataColumns.CustomerCountry) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerCountry]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerCountry]].ToString(),
                                PostalCode = !mappedColumnsData.ContainsKey(DataColumns.CustomerPostalCode) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerPostalCode]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerPostalCode]].ToString(),
                            } : null,
                        SourcingManager = sourcinguserName?.Id ?? Guid.Empty,
                        ClosingManager = ClosingUserName?.Id ?? Guid.Empty,
                        Profession = validProfession.IsValidInfo ? validProfession.Profession : Profession.None,
                        PossesionType = Enum.TryParse(possesionType, true, out PossesionType parsedType) && (parsedType != PossesionType.CustomDate || isValidPossessionDate) ? parsedType : default,
                        LandLine = (!string.IsNullOrWhiteSpace(landline) && Regex.IsMatch(Regex.Replace(landline, @"[^0-9\-]", ""), @"^[0-9\-]{6,16}$")) ? Regex.Replace(landline, @"[^0-9\-]", "") : string.Empty,
                        Gender = validGenderType.IsValidInfo ? validGenderType.Gender : Gender.NotMentioned,
                        MaritalStatus = validMaritalStatusType.IsValidInfo ? validMaritalStatusType.MaritalStatusType : MaritalStatusType.NotMentioned,
                        DateOfBirth = validDateOfBirth ?? null
                    };
                    InvalidData invalidLead = null;
                    bool isInvalidContact = false;
                    if (!string.IsNullOrEmpty(lead.ContactNo))
                    {
                        var contactNo = phoneUtil.V2ConcatenatePhoneNumber(lead.CountryCode, lead.ContactNo, masterItems.GlobalSettings ?? new());
                        if (string.IsNullOrEmpty(contactNo))
                        {
                            invalidLead = lead.Adapt<InvalidData>();
                            invalidLead.Errors = "Invalid ContactNo";
                            invalidLead.AssignTo = assignToUser != null ? assignToUser.FirstName + " " + assignToUser.LastName : string.Empty;
                            invalidLead.Source = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.LeadSource.ToString() ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                            invalidLead.SubSource = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                            invalidLead.Created = lead.CreatedOn.Date;
                            invalidLeads.Add(invalidLead);
                            isInvalidContact = true;
                        }
                        lead.ContactNo = contactNo;
                    }
                    else
                    {
                        invalidLead = lead.Adapt<InvalidData>();
                        invalidLead.Errors = "Invalid ContactNo";
                        invalidLead.AssignTo = assignToUser != null ? assignToUser.FirstName + " " + assignToUser.LastName : string.Empty;
                        invalidLead.Source = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.LeadSource.ToString() ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                        invalidLead.SubSource = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                        invalidLead.Created = lead.CreatedOn.Date;
                        invalidLeads.Add(invalidLead);
                        isInvalidContact = true;
                    }
                    lead.AlternateContactNo = !string.IsNullOrEmpty(lead.AlternateContactNo) ? phoneUtil.V2ConcatenatePhoneNumber(lead.AltCountryCode, lead.AlternateContactNo, masterItems.GlobalSettings ?? new()) : null;
                    lead.ReferralContactNo = !string.IsNullOrEmpty(lead.AlternateContactNo) ? phoneUtil.V2ConcatenatePhoneNumber(null, lead.ReferralContactNo, masterItems.GlobalSettings ?? new()) : null;
                    lead.ChannelPartnerContactNo = !string.IsNullOrEmpty(lead.ChannelPartnerContactNo) ? phoneUtil.V2ConcatenatePhoneNumber(null, lead.ChannelPartnerContactNo, masterItems.GlobalSettings ?? new()) : null;
                    if (leadMigrateTracker?.MigrationType == LeadMigrationType.None || leadMigrateTracker?.MigrationType == LeadMigrationType.CreateDuplicateLead)
                    {
                        lead.LastModifiedOn = (DateTime)createdon;
                    }
                    else
                    {
                        lead.LastModifiedOn = DateTime.UtcNow;
                    }
                    if (leadsourceInfo?.Result == null && !isInvalidContact)
                    {
                        invalidLead = lead.Adapt<InvalidData>();
                        invalidLead.Errors = "Invalid Source";
                        invalidLead.Source = leadSource;
                        invalidLead.SubSource = subSource;
                        invalidLead.Created = lead.CreatedOn.Date;
                        invalidLeads.Add(invalidLead);
                    }
                    else if(leadsourceInfo?.Result != null)
                    {
                        lead.Enquiries.FirstOrDefault().LeadSource = leadsourceInfo.Result.IsValidInfo ? leadsourceInfo.Result.LeadSource : default;
                        lead.Enquiries.FirstOrDefault().SubSource = subSourceInfo.IsValidInfo ? (!string.IsNullOrWhiteSpace(subSourceInfo.SubSource) ? subSourceInfo.SubSource.ToLower() : null) : null;
                    }

                    // Set OriginalOwner to the assigned user when first assigned
                    if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
                    {
                        lead.OriginalOwner = lead.AssignTo;
                    }

                    var appointments = new List<LeadAppointment>();

                    // Add site visit appointments
                    appointments.AddRange(Enumerable.Repeat(new LeadAppointment
                    {
                        IsDone = true,
                        Type = AppointmentType.SiteVisit,
                        CreatedOn = DateTime.UtcNow,
                        CreatedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                        UserId = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                        LastModifiedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                    }, siteVisitDone));

                    appointments.AddRange(Enumerable.Repeat(new LeadAppointment
                    {
                        IsDone = false,
                        Type = AppointmentType.SiteVisit,
                        CreatedOn = DateTime.UtcNow,
                        CreatedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                        UserId = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                        LastModifiedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                    }, siteVisitNotDone));

                    // Add meeting appointments
                    appointments.AddRange(Enumerable.Repeat(new LeadAppointment
                    {
                        IsDone = true,
                        Type = AppointmentType.Meeting,
                        CreatedOn = DateTime.UtcNow,
                        CreatedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                        UserId = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                        LastModifiedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                    }, meetingDone));

                    appointments.AddRange(Enumerable.Repeat(new LeadAppointment
                    {
                        IsDone = false,
                        Type = AppointmentType.Meeting,
                        CreatedOn = DateTime.UtcNow,
                        CreatedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                        UserId = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                        LastModifiedBy = assignToUser?.Id ?? currentUserId ?? Guid.Empty,
                    }, meetingNotDone));

                    if (appointments.Any())
                    {
                        lead.Appointments = appointments;
                    }
                    if (!propertyInfo.IsValidInfo)
                    {
                        lead.Notes += !string.IsNullOrEmpty(propertyInfo.BasePropertyType) ? " \n" + "BaseProperty" + " - " + propertyInfo.BasePropertyType : string.Empty;
                        lead.Notes += !string.IsNullOrEmpty(propertyInfo.SubPropertyType) ? ", \n" + "SubProperty" + " - " + propertyInfo.SubPropertyType : string.Empty;
                        lead.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidNoOfBHK) ? ", \n" + "NoOfBHK" + " - " + propertyInfo.InvalidNoOfBHK : string.Empty;
                        lead.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidBHKType) ? ", \n" + "BHKType" + " - " + propertyInfo.InvalidBHKType : string.Empty;
                    }
                    if (!upperBudgetinfo.IsValidInfo)
                    {
                        lead.Notes += !string.IsNullOrEmpty(upperBudgetinfo.Invalidbudget) ? ", \n" + "UpperBudget" + " - " + upperBudgetinfo.Invalidbudget : string.Empty;
                    }
                    if (!lowerBudgetInfo.IsValidInfo)
                    {
                        lead.Notes += !string.IsNullOrEmpty(lowerBudgetInfo.Invalidbudget) ? ", \n" + "LowerBudget" + " - " + lowerBudgetInfo.Invalidbudget : string.Empty;
                    }
                    if (leadsourceInfo?.Result != null && !leadsourceInfo.Result.IsValidInfo)
                    {
                        lead.Notes += !string.IsNullOrEmpty(leadsourceInfo.Result.InvalidLeadSource) ? ", \n" + "LeadSource" + " - " + leadsourceInfo.Result.InvalidLeadSource : string.Empty;
                    }
                    if (!enquiryTypesInfo.IsValidInfo)
                    {
                        lead.Notes += !string.IsNullOrEmpty(enquiryTypesInfo.InvalidEnquiredFor) ? ", \n" + "EnquiryFOr" + " - " + enquiryTypesInfo.InvalidEnquiredFor : string.Empty;
                    }
                    if (!subSourceInfo.IsValidInfo)
                    {
                        lead.Notes += !string.IsNullOrEmpty(subSourceInfo.SubSource) ? ", \n" + "subSource" + " - " + subSourceInfo.SubSource : string.Empty;
                    }
                    if (!leadStatus.IsValidStatus(masterItems.LeadStatuses))
                    {
                        var baseStatusString = mappedColumnsData.GetStringValue(DataColumns.BaseStatus, row);
                        var subStatusString = mappedColumnsData.GetStringValue(DataColumns.SubStatus, row);

                        lead.Notes += !string.IsNullOrWhiteSpace(baseStatusString) ? $"\n{mappedColumnsData[DataColumns.BaseStatus]} - {baseStatusString}" : string.Empty;
                        lead.Notes += !string.IsNullOrWhiteSpace(subStatusString) ? $"\n{mappedColumnsData[DataColumns.SubStatus]} - {subStatusString}" : string.Empty;
                    }
                     var existingContacts = leads.SelectMany(i => new[] { i.ContactNo, i.AlternateContactNo })
                    .Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                    if (invalidLead == null && (!existingContacts.Contains(lead.ContactNo) && !existingContacts.Contains(lead.AlternateContactNo)))
                    {
                        leads.Add(lead);
                    }
                    else if((existingContacts.Contains(lead.ContactNo) || existingContacts.Contains(lead.AlternateContactNo)))
                    {
                        invalidLead = lead.Adapt<InvalidData>();
                        invalidLead.Errors = "Duplicate ContactNo";
                        invalidLead.Source = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.LeadSource.ToString() ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                        invalidLead.SubSource = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                        invalidLead.Created = lead.CreatedOn.Date;
                        invalidLeads.Add(invalidLead);
                    }
                }
            }
            return (leads, invalidLeads);
        }
        public static (double, Guid) GetUnitDetails(string unitAreaSize, List<MasterAreaUnit> areaUnits, string unit, Guid? areaUnitId = null)
        {
            var unitArea = GetArea(unitAreaSize);
            if (areaUnits.Count > 0)
            {
                var masterUnits = areaUnits?.ConvertAll(i => i?.Unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty)?.ToList();
                var unitOfArea = Regex.Replace(unitAreaSize, "[a-zA-Z]", string.Empty).Trim();
                if (!string.IsNullOrWhiteSpace(unitOfArea) && !string.IsNullOrWhiteSpace(unit) && (masterUnits?.Any(i => i.Contains(unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty)) ?? false))
                {
                    var normalizedUnit = unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty;
                    var unitId = areaUnits?.FirstOrDefault(i => (i?.Unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty).Contains(normalizedUnit))?.Id ?? Guid.Empty;
                    return (unitArea, unitId);
                }
                else if (areaUnitId != null)
                {
                    return (unitArea, areaUnitId ?? Guid.Empty);
                }
                else if (string.IsNullOrWhiteSpace(unit))
                {
                    var unitId = areaUnits?.FirstOrDefault(i => i.Unit == "Sq. Feet")?.Id ?? Guid.Empty;
                    return (unitArea, unitId);
                }
            }
            return (unitArea, Guid.Empty);
        }
        public static double GetArea(string number)
        {
            try
            {
                Regex regex = new Regex(@"^\d+(\.\d+)?$");
                Match match = regex.Match(number);
                double res = 0;

                if (match.Success)
                {
                    res = double.Parse(match.Value);
                }
                return res;
            }
            catch (Exception e)
            {
                throw;
            }

        }

        public static DateTime? GetPossessionDate(this Dictionary<DataColumns, string> mappedColumnsData, DataColumns column, DataRow row, string jsonData = null)
        {
            try
            {
                var dateString = GetStringValue(mappedColumnsData, column, row);
                if (string.IsNullOrEmpty(dateString))
                {
                    return column == DataColumns.PossessionDate ? (DateTime?)null : DateTime.UtcNow;
                }
                DateTime? date = null;
                CommonTimeZoneDto? commonTimeZoneDto = null;
                if (!string.IsNullOrWhiteSpace(jsonData))
                {
                    commonTimeZoneDto = JsonConvert.DeserializeObject<CommonTimeZoneDto>(jsonData);
                }
                if (double.TryParse(dateString, out var excelDateValue))
                {
                    date = DateTime.FromOADate(excelDateValue);
                }
                else
                {
                    if (!DateTime.TryParseExact(dateString, formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedDate))
                    {
                        date = DateTime.ParseExact(dateString, formats, CultureInfo.InvariantCulture);
                    }
                    else
                    {
                        date = parsedDate;
                    }
                }
                if (commonTimeZoneDto != null)
                {
                    date = date?.ConvertDateTime(commonTimeZoneDto.TimeZoneId ?? string.Empty, commonTimeZoneDto.BaseUTcOffset);
                }

                return date;
            }
            catch
            {
                return null;
            }
        }
        public static DateTime? GetDateOfBirth(this Dictionary<DataColumns, string> mappedColumnsData, DataColumns column, DataRow row, string jsonData = null)
        {
            try
            {
                // Get the date string from the mapped columns or row
                var dateString = GetStringValue(mappedColumnsData, column, row);

                // If the date string is empty or null, handle default cases
                if (string.IsNullOrEmpty(dateString))
                {
                    return null;
                }

                // Try to parse the date string
                DateTime? date = null;

                // Handle Excel date format (OLE Automation date)
                if (double.TryParse(dateString, out var excelDateValue))
                {
                    date = DateTime.FromOADate(excelDateValue).Date;
                }
                // Handle regular date strings
                else
                {
                    // Try parsing with the default format
                    if (!DateTime.TryParse(dateString, out var parsedDate))
                    {
                        // Fallback to a specific format if the default parsing fails
                        date = DateTime.ParseExact(dateString, formats, CultureInfo.InvariantCulture).Date;
                    }
                    else
                    {
                        string formattedDate = parsedDate.ToString(CultureInfo.CurrentCulture);
                        date = DateTime.Parse(formattedDate, CultureInfo.CurrentCulture).Date;
                    }
                }

                if (date.HasValue && date.Value > DateTime.UtcNow.Date)
                {
                    return null;
                }

                return date;
            }
            catch (Exception ex)
            {
                // Return UTC time as a fallback in case of any errors
                return null;
            }
        }

        public static string[] formats = {
                // Month-Day-Year formats
                "MM/dd/yyyy HH:mm:ss",       // 10/24/2024 14:30:00
                "MM/dd/yyyy hh:mm:ss tt",    // 10/24/2024 02:30:00 PM
                "M/d/yyyy hh:mm:ss tt",
                "MM-dd-yyyy HH:mm:ss",       // 10-24-2024 14:30:00
                "MM-dd-yyyy hh:mm:ss tt",    // 10-24-2024 02:30:00 PM

                // Day-Month-Year formats
                "dd-MM-yyyy HH:mm:ss",       // 24-10-2024 14:30:00
                "dd-MM-yyyy hh:mm:ss tt",    // 24-10-2024 02:30:00 PM
                "dd/MM/yyyy HH:mm:ss",       // 24/10/2024 14:30:00
                "dd/MM/yyyy hh:mm:ss tt",    // 24/10/2024 02:30:00 PM
                "dd.MM.yyyy HH:mm:ss",       // 24.10.2024 14:30:00
                "dd.MM.yyyy hh:mm:ss tt",    // 24.10.2024 02:30:00 PM

                // Year-Month-Day formats
                "yyyy/MM/dd HH:mm:ss",       // 2024/10/24 14:30:00
                "yyyy-MM-dd HH:mm:ss",       // 2024-10-24 14:30:00
                "yyyy.MM.dd HH:mm:ss",       // 2024.10.24 14:30:00
                "yyyy/MM/dd hh:mm:ss tt",    // 2024/10/24 02:30:00 PM
                "yyyy-MM-dd hh:mm:ss tt",    // 2024-10-24 02:30:00 PM
                "yyyy.MM.dd hh:mm:ss tt",    // 2024.10.24 02:30:00 PM

                // ISO 8601 formats
                "yyyy-MM-ddTHH:mm:ss.fffffffZ",  // 2024-10-24T14:30:00.0000000Z
                "yyyy-MM-ddTHH:mm:ssZ",          // 2024-10-24T14:30:00Z
                "yyyy-MM-ddTHH:mm:ss.fffZ",      // 2024-10-24T14:30:00.000Z
                "yyyy-MM-ddTHH:mm:ss",           // 2024-10-24T14:30:00
                "yyyy-MM-ddTHH:mm:ssK",          // 2024-10-24T14:30:00+00:00

                // Custom formats
                "yyyyMMddHHmmss",             // 20241024143000
                "ddMMyyyyHHmmss",             // 24102024143000
                "yyyyMMdd",                   // 20241024

                // Optional seconds
                "MM/dd/yyyy HH:mm",           // 10/24/2024 14:30
                "dd/MM/yyyy HH:mm",           // 24/10/2024 14:30
                "yyyy-MM-ddTHH:mm",           // 2024-10-24T14:30
                "yyyyMMddHHmm",               // 202410241430

                // Optional milliseconds
                "MM/dd/yyyy HH:mm:ss.fff",    // 10/24/2024 14:30:00.000
                "dd/MM/yyyy HH:mm:ss.fff",    // 24/10/2024 14:30:00.000
                "yyyy-MM-dd HH:mm:ss.fff",    // 2024-10-24 14:30:00.000
                "yyyy-MM-ddTHH:mm:ss.fff",    // 2024-10-24T14:30:00.000

                "dd/MM/yyyy hh:mm tt",   // 29/01/2025 07:00 PM
                "MM/dd/yyyy hh:mm tt",  // 01/29/2025 07:00 PM (US format)
                 "dd/MM/yyyy", 
                 "dd/MM/yyyy",
                 "dd-MM-yyyy HH:mm:ss"


        };
    }
}
