﻿using Lrb.Application.Agency.Web.Dtos;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Project;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.Project.Web.Requests.Bulk_Upload;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Data;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task ProjectHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            BulkProjectUploadTracker? projectUploadTracker = await _bulkProjectUploadTrackerRepo.GetByIdAsync(input.TrackerId);
            try
            {
                if (projectUploadTracker != null)
                {
                    try
                    {
                        projectUploadTracker.MappedColumnsData = projectUploadTracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                        projectUploadTracker.Status = UploadStatus.Started;
                        projectUploadTracker.LastModifiedBy = input.CurrentUserId;
                        projectUploadTracker.CreatedBy = input.CurrentUserId;
                        await _bulkProjectUploadTrackerRepo.UpdateAsync(projectUploadTracker);
                        Console.WriteLine($"handler() -> ProjectHandler Updated Status: {projectUploadTracker.Status} \n {JsonConvert.SerializeObject(projectUploadTracker)}");
                        #region Fetch all required MasterData and Other data
                        var projectTypes = new List<MasterProjectType>(await _projectTypeRepo.ListAsync(cancellationToken));
                        var projectAmenities = new List<CustomMasterAmenity>(await _projectAmenityRepo.ListAsync(cancellationToken));
                        var areaUnits = new List<MasterAreaUnit>(await _masterAreaUnitRepo.ListAsync(cancellationToken));
                        var associateBanks = new List<MasterAssociatedBank>(await _associatesBankRepo.ListAsync(cancellationToken));
                        var globalSettingInfo = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                        #endregion
                        #region Convert file to Datatable
                        Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", projectUploadTracker?.S3BucketKey ?? string.Empty);
                        DataTable dataTable = new();
                        if (projectUploadTracker?.S3BucketKey?.Split('.')?.LastOrDefault() == "csv")
                        {
                            using MemoryStream memoryStream = new();
                            fileStream.CopyTo(memoryStream);
                            dataTable = CSVHelper.CSVToDataTable(memoryStream);
                        }
                        else
                        {
                            dataTable = Lrb.Application.Property.Web.EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, projectUploadTracker?.SheetName);
                        }
                        int totalRows = dataTable.Rows.Count;
                        for (int i = totalRows - 1; i >= 0; i--)
                        {
                            var row = dataTable.Rows[i];
                            if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                            {
                                row.Delete();
                            }
                        }
                        if (dataTable.Rows.Count <= 0)
                        {
                            throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                        }
                        Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
                        #endregion
                        var unMappedColumns = dataTable.GetUnmappedColumnNames(projectUploadTracker?.MappedColumnsData);
                        var projects = dataTable.ConvertToProject(projectUploadTracker?.MappedColumnsData, unMappedColumns, projectTypes, areaUnits, associateBanks, projectAmenities, globalSettingInfo, _blobStorageService: _blobStorageService);
                        projects.ForEach(i => i.SetProject(projectUploadTracker?.MappedColumnsData, input.CurrentUserId));
                        var projectNames = projects.Select(lead => lead.Name).ToList();
                        var existingprojects = await _newProjectRepo.ListAsync(new GetAllProjectByNamesSpec(projectNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
                        var invalidDate = BulkProjectUploadHelper.GetInvalidProjects(projects, existingprojects);
                        projects = projects
                          .Where(age => age.Name != string.Empty)
                          .DistinctBy(project => project.Name)
                          .ToList();
                        var distinctCount = projects.Count();
                        projects.RemoveAll(i => invalidDate.Select(i => i.Name).Contains(i.Name));
                        projectUploadTracker.Status = UploadStatus.InProgress;
                        projectUploadTracker.TotalCount = totalRows;
                        projectUploadTracker.DistinctLeadCount = distinctCount;
                        projectUploadTracker.InvalidCount = invalidDate.Count();
                        if (invalidDate.Any())
                        {
                            byte[] bytes = ExcelHelper.CreateExcelFromList(invalidDate).ToArray();
                            string fileName = $"InvalidProjects-{DateTime.Now:dd_MM_yyyy-HH_mm_ss}.xlsx";
                            string folder = "Projects";
                            var key = await _blobStorageService?.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                            projectUploadTracker.InvalidDataS3BucketKey = key;
                        }
                        projectUploadTracker.LastModifiedBy = input.CurrentUserId;
                        projectUploadTracker.CreatedBy = input.CurrentUserId;
                        await _bulkProjectUploadTrackerRepo.UpdateAsync(projectUploadTracker);
                        if (projects.Count > 0)
                        {
                            int projectsPerchunk = projects.Count > 5000 ? 5000 : projects.Count;
                            var chunks = projects.Chunk(projectsPerchunk).Select(i => new ConcurrentBag<Project>(i));
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);
                            var chunkIndex = 1;
                            foreach (var chunk in chunks.ToList())
                            {
                                var backgroundDto = new BulkProjectUploadbackgroundDto()
                                {
                                    TrackerId = projectUploadTracker?.Id ?? Guid.Empty,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Projects = new(chunk)
                                };
                                if (chunkIndex == chunks.Count())
                                {
                                    backgroundDto.IsLastChunk = true;
                                }
                                await ExecuteDBOperationsAsync(backgroundDto);
                                chunkIndex++;
                            }
                            projectUploadTracker.TotalUploadedCount = projects.Count();
                            projectUploadTracker.Status = UploadStatus.Completed;
                            projectUploadTracker.LastModifiedBy = input.CurrentUserId;
                            projectUploadTracker.CreatedBy = input.CurrentUserId;
                            await _bulkProjectUploadTrackerRepo.UpdateAsync(projectUploadTracker);
                        }
                        else
                        {
                            projectUploadTracker.Status = UploadStatus.Completed;
                            projectUploadTracker.LastModifiedBy = input.CurrentUserId;
                            projectUploadTracker.CreatedBy = input.CurrentUserId;
                            await _bulkProjectUploadTrackerRepo.UpdateAsync(projectUploadTracker);
                        }
                    }
                    catch (Exception ex)
                    {
                        projectUploadTracker.Status = UploadStatus.Failed;
                        projectUploadTracker.Message = ex.Message;
                        projectUploadTracker.LastModifiedBy = input.CurrentUserId;
                        projectUploadTracker.CreatedBy = input.CurrentUserId;
                        await _bulkProjectUploadTrackerRepo.UpdateAsync(projectUploadTracker);
                        throw;
                    }
                }
                else
                {
                    Console.WriteLine($"handler() -> tracker not found by the Id : {input.TrackerId}");
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ProjectHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }
        public async Task ExecuteDBOperationsAsync(BulkProjectUploadbackgroundDto dto)
        {
            BulkProjectUploadTracker? tracker = new();
            tracker = await _bulkProjectUploadTrackerRepo.GetByIdAsync(dto.TrackerId);
            try
            {
                await _projectRepo.AddRangeAsync(dto.Projects);

                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.Projects.Count;
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _bulkProjectUploadTrackerRepo.UpdateAsync(tracker);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                tracker.Status = UploadStatus.Failed;
                tracker.Message = e?.InnerException?.Message ?? e?.Message;
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _bulkProjectUploadTrackerRepo.UpdateAsync(tracker);
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = " FunctionEntryPoint -> ExecuteDBOperationsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
    }
}
