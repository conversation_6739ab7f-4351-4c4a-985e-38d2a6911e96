﻿using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Project.Mobile;
using Lrb.Application.Property.Mobile;
using Lrb.Application.TempProject.Dtos;
using Lrb.Application.UserDetails.Mobile;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;

namespace Lrb.Application.Lead.Mobile
{
    public class GetLeadByIdRequest : IRequest<Response<ViewLeadDto>>
    {
        public Guid Id { get; set; }
        public GetLeadByIdRequest(Guid id) => Id = id;
    }
    public class GetByIdRequestHandler : IRequestHandler<GetLeadByIdRequest, Response<ViewLeadDto>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IUserService _userService;
        private readonly IReadRepository<Domain.Entities.Property> _propertyRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetByIdRequestHandler(

            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IUserService userService,
            IReadRepository<Domain.Entities.Property> propertyRepo,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser)
        {
            _leadRepo = leadRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _userService = userService;
            _propertyRepo = propertyRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<Response<ViewLeadDto>> Handle(GetLeadByIdRequest request, CancellationToken cancellationToken)
        {
            var existingLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.Id), cancellationToken);
            if (existingLead == null)
            {
                throw new NotFoundException("No lead found by this Id");
            }
            existingLead.TagInfo ??= new LeadTag();
            var key = LeadHelper.Find(existingLead);
            var leadDto = existingLead.Adapt<ViewLeadDto>();
            leadDto.ContactRecords = existingLead.ContactRecords;
            leadDto.LeadFilterKey = key;
            List<string> userIds = new List<string>()
                {
                    existingLead.AssignTo.ToString() ?? Guid.Empty.ToString(),
                    existingLead.LastModifiedBy.ToString() ?? Guid.Empty.ToString(),
                    existingLead.AssignedFrom.ToString() ?? Guid.Empty.ToString()
                };
            var users = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
            var assignedUser = users.FirstOrDefault(i => i.Id == existingLead?.AssignTo);
            var assignedFromUser = users.FirstOrDefault(i => i.Id == existingLead?.AssignedFrom);
            var lastModifiedUser = users.FirstOrDefault(i => i.Id == existingLead?.LastModifiedBy);
            if (assignedUser != null)
            {
                leadDto.AssignedUser = assignedUser?.Adapt<UserDto>();
                if(leadDto.AssignedUser != null && assignedUser != null)
                {
                    leadDto.AssignedUser.Name = assignedUser.FirstName + " " + assignedUser.LastName;
                }
            }
            if(assignedFromUser != null)
            {
                leadDto.AssignedFromUser= assignedFromUser?.Adapt<UserDto>();
                if(leadDto.AssignedFromUser != null && assignedFromUser != null)
                {
                    leadDto.AssignedFromUser.Name = assignedFromUser.FirstName + " " + assignedFromUser.LastName;
                }
            }
            if (lastModifiedUser != null)
            {
                leadDto.LastModifiedByUser = lastModifiedUser?.Adapt<UserDto>();
                if(leadDto.LastModifiedByUser!= null && lastModifiedUser != null)
                {
                    leadDto.LastModifiedByUser.Name = lastModifiedUser.FirstName + " " + lastModifiedUser.LastName;
                }
            }
            if (existingLead?.BookedDetails?.Any() ?? false)
            {
                existingLead.BookedDetails = existingLead.BookedDetails.OrderByDescending(i => i.LastModifiedOn).ToList();
                List<LeadBookedDetail> bookedDetails = new();
                var bookedDetail = existingLead.BookedDetails.FirstOrDefault();
                bookedDetails.Add(bookedDetail);
                existingLead.BookedDetails = bookedDetails;

            }
            if (existingLead.BookedDetails?.FirstOrDefault()?.Properties?.Any() ?? false)
            {
                leadDto.BookedDetails.FirstOrDefault().Property = existingLead.BookedDetails.FirstOrDefault().Properties.FirstOrDefault().Adapt<BasicPropertyInfoDto>();
            }
            if (existingLead.BookedDetails?.FirstOrDefault()?.Projects?.Any() ?? false)
            {
                leadDto.BookedDetails.FirstOrDefault().Projects = existingLead.BookedDetails.FirstOrDefault().Projects.FirstOrDefault().Adapt<BasicProjectDto>();
            }
            string? tenantId = _currentUser.GetTenant();
            var res = (await _dapperRepository.GetLeadSourceUpdateStatusAsync(leadDto.Id, tenantId ?? string.Empty));
            leadDto.IsSourceUpdated = res > 1 ? true : false;
            #region Commented Out LeadHistory
            //var leadHistory = await _leadHistoryRepo.GetByIdAsync(leadDto.Id, cancellationToken);
            //if (leadHistory != null)
            //{
            //    var assignedToUsers = leadHistory.AssignedTo;
            //    if (assignedToUsers != null && assignedToUsers.Count > 1)
            //    {
            //        var assignedFromUser = (assignedToUsers.SkipLast(1)).LastOrDefault();
            //        if (!assignedFromUser.IsNullOrEmpty() && assignedFromUser.Value != Guid.Empty)
            //        {
            //            var assignedFromUserDetails = ((await _userService.GetListAsync(cancellationToken)).Where(i => i.Id == assignedFromUser.Value)).FirstOrDefault();
            //            if (assignedFromUserDetails != null)
            //            {
            //                leadDto.AssignedFromUser = assignedFromUserDetails.Adapt<UserDto>();
            //                leadDto.AssignedFromUser.Name = assignedFromUserDetails.FirstName + " " + assignedFromUserDetails.LastName;
            //            }
            //        }
            //    }
            //}
            //var leadHistoryModel = LeadHistoryHelper.FormLeadHistoryViewModelMobile(leadHistory, LeadHistoryDto.LeadHistoryFilterKey.None);
            //leadDto.LeadHistory = leadHistoryModel;
            #endregion
            return new Response<ViewLeadDto>(leadDto);
        }

    }

}
