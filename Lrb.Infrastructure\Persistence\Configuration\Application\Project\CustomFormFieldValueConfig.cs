﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.Project
{
    public class CustomFormFieldValueConfig : IEntityTypeConfiguration<CustomFormValue>
    {
        public void Configure(EntityTypeBuilder<CustomFormValue> builder)
        {
            builder.IsMultiTenant();
        }
    }
}