﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.Project
{
    public class CustomFormFieldValueConfig : IEntityTypeConfiguration<CustomFieldValue>
    {
        public void Configure(EntityTypeBuilder<CustomFieldValue> builder)
        {
            builder.IsMultiTenant();

            // Configure the relationship between CustomFieldValue and CustomFormFields
            builder.HasOne(v => v.Fields)
                   .WithMany()
                   .HasForeignKey(v => v.FormFieldId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}