﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.Project
{
    public class CustomFormFieldValueConfig : IEntityTypeConfiguration<CustomFormValue>
    {
        public void Configure(EntityTypeBuilder<CustomFormValue> builder)
        {
            builder.IsMultiTenant();

            builder.HasOne(x => x.Fields)
                   .WithMany()
                   .HasForeignKey(x => x.CustomFieldIds)
                   .HasConstraintName("FK_CustomFormValue_CustomForm_FieldsId")
                   .OnDelete(DeleteBehavior.Cascade);

            builder.Property(x => x.CustomFieldIds)
                   .HasColumnName("FieldsId");
        }
    }
}