﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Common.WA
{
    public interface IWAService : ITransientService
    {
        Task<List<WATemplate>> RetrieveWATemplatesAsync();
        Task<List<WATemplate>> RetriveWATemaplteByName(string? templateName, WAApiInfo getTemplateApiInfo, WAPayloadMapping payloadmapping);
    }
}
