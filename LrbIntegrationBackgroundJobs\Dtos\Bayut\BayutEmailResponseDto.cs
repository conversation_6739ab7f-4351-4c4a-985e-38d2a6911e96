﻿namespace LrbIntegrationBackgroundJobs.Dtos.Bayut
{
    public class BayutEmailResponseDto
    {
        public string property_id { get; set; }
        public string lead_id { get; set; }
        public string property_reference { get; set; }
        public string current_type { get; set; }
        public string date_time { get; set; }
        public string message { get; set; }
        public string client_name { get; set; }
        public string client_email { get; set; }
        public string client_phone { get; set; }

        public LrbIntegrationPostDto MapToLrbIngrDto()
        {
            var dto = new LrbIntegrationPostDto()
            {
                Name = client_name,
                Mobile = client_phone,
                Email = client_email,
                Notes = message,
                RefrenceNo = property_reference
            };
            return dto;
        }
    }
}
