﻿using System.Text.RegularExpressions;

namespace Lrb.Shared.Extensions
{
    public static class StringExtentions
    {
        public static string LrbNormalize(this string str)
        {
            return string.IsNullOrWhiteSpace(str)
                ? string.Empty
                : Regex.Replace(str, @"[-_ .]", string.Empty)
                .ToLowerInvariant()
                .Replace(",,", ",")
                .Normalize()
                .Trim();
        }
        public static bool IsTimeSpan(this string input)
        {
            string pattern = @"^(?:[0-9]|0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$";
            return Regex.IsMatch(input, pattern);
        }
        public const string NotAllowed  = "Sorry! Login Not Allowed Outside Shift Timing.";

        public const string UserLocked = "Sorry! Account is locked, Please reset your password";
    }
}
