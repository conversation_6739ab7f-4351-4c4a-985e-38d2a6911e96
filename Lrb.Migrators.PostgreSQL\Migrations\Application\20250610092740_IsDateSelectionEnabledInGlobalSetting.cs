﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class IsDateSelectionEnabledInGlobalSetting : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsPastDateSelectionEnabled",
                schema: "LeadratBlack",
                table: "GlobalSettings",
                type: "boolean",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsPastDateSelectionEnabled",
                schema: "LeadratBlack",
                table: "GlobalSettings");
        }
    }
}
