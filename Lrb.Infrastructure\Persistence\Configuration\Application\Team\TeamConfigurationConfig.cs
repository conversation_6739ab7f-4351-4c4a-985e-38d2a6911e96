﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.Team
{
    public class TeamConfigurationConfig : IEntityTypeConfiguration<TeamConfiguration>
    {
        public void Configure(EntityTypeBuilder<TeamConfiguration> builder)
        {
            builder.IsMultiTenant();
            builder.HasOne(i => i.Team)
                .WithMany(i => i.Configurations)
                .HasForeignKey(i => i.TeamId);
        }
    }
}
