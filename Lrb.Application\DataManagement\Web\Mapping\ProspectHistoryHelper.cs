﻿using Lrb.Application.Agency.Web;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Property.Web;
using Lrb.Application.Team.Web;
using Lrb.Domain.Entities.MasterData;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Lrb.Application.DataManagement.Web.Mapping
{
    public static class ProspectHistoryHelper
    {
        public static List<ProspectHistory> V2CreateProspectHistoryForVM(ViewProspectDto newprospect, ViewProspectDto? oldProspect, UserDetailsDto? user, int version, List<CustomProspectStatus> statuses, List<MasterPropertyType> propertiesType, List<MasterProspectSource> prospectSources, IUserService userService, CancellationToken cancellationToken)
        {
            List<ProspectHistory> prospectHistories = new();
            PropertyInfo[]? propertyInfo = newprospect.GetType().GetProperties();
            var groupKey = Guid.NewGuid();
            foreach (var property in propertyInfo)
            {
                if (property != null)
                {
                    if (ShouldIncludePropperty(property))
                    {
                        if (property.GetValue(newprospect) != null)
                        {
                            if (IsComplexTypeProperty(property.PropertyType))
                            {
                                var complexItems = V2GetProspectRelatedChildEntityItemsForVM(property, newprospect, oldProspect, statuses, propertiesType, prospectSources, groupKey, version, user, cancellationToken);
                                prospectHistories.AddRange(complexItems);
                            }
                            else
                            {
                                if (property?.GetValue(newprospect) != null && property?.GetValue(newprospect)?.ToString() != string.Empty)
                                {
                                    if (property?.GetValue(newprospect)?.ToString() != "0" && property?.GetValue(newprospect)?.ToString() != "None" && property?.GetValue(newprospect)?.ToString() != "00000000-0000-0000-0000-000000000000")
                                    {
                                        ProspectHistory prospectHistory = new();
                                        prospectHistory.ProspectId = newprospect?.Id ?? Guid.Empty;
                                        if (newprospect?.LastModifiedBy != null)
                                        {
                                            var modifiedBy = newprospect.LastModifiedBy;
                                            try
                                            {
                                                prospectHistory.ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty);
                                            }
                                            catch (NotFoundException ex) { }
                                            prospectHistory.LastModifiedById = user?.Id ?? Guid.Empty;
                                        }
                                        prospectHistory.ModifiedOn = newprospect?.LastModifiedOn;
                                        prospectHistory.GroupKey = groupKey;
                                        prospectHistory.Version = version;
                                        prospectHistory.FieldName = Regex.Replace(property?.Name ?? string.Empty, "([A-Z])", " $1").Trim();
                                        prospectHistory.FieldType = property?.PropertyType.Name.ToString() ?? string.Empty;
                                        if (prospectHistory.FieldType.Contains("Nullable"))
                                        {
                                            var type = Nullable.GetUnderlyingType(property.PropertyType)?.Name;
                                            prospectHistory.FieldType = type ?? string.Empty;
                                        }
                                        prospectHistory.NewValue = (property?.GetValue(newprospect))?.ToString() ?? null;
                                        prospectHistory.OldValue = default;
                                        prospectHistories.Add(prospectHistory);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if (oldProspect != null)
            {
                var userName = string.Empty;
                try
                {
                    userName = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty);
                }
                catch (NotFoundException ex)
                {
                }
                AssignAuditStamps(prospectHistories, userName, version);
            }
            return prospectHistories;
        }
        public static async Task<List<ProspectHistory>> CreateProspectHistoryForVM(ViewProspectDto newprospect, ViewProspectDto? oldProspect, Guid userId, int version, List<CustomProspectStatus> statuses, List<MasterPropertyType> propertiesType, List<MasterProspectSource> prospectSources, IUserService userService, CancellationToken cancellationToken)
        {
            List<ProspectHistory> prospectHistories = new();
            PropertyInfo[]? propertyInfo = newprospect.GetType().GetProperties();
            var groupKey = Guid.NewGuid();
            var modifiedBy = newprospect.LastModifiedBy;
            UserDetailsDto? user = null;
            try
            {
                user = await userService.GetAsync(modifiedBy.ToString(), cancellationToken);
            }
            catch
            {
            }
            foreach (var property in propertyInfo)
            {
                if (property != null)
                {

                    if (ShouldIncludePropperty(property))
                    {
                        if (property.GetValue(newprospect) != null)
                        {
                            if (IsComplexTypeProperty(property.PropertyType))
                            {
                                var complexItems = await GetProspectRelatedChildEntityItemsForVM(property, newprospect, oldProspect, statuses, propertiesType, prospectSources, groupKey, version, userService, cancellationToken, user: user);
                                if (complexItems != null)
                                {
                                    prospectHistories.AddRange(complexItems);
                                }
                            }
                            else
                            {
                                if (property?.GetValue(newprospect) != null && property?.GetValue(newprospect)?.ToString() != string.Empty)
                                {
                                    if (property?.GetValue(newprospect)?.ToString() != "0" && property?.GetValue(newprospect)?.ToString() != "None" && property?.GetValue(newprospect)?.ToString() != "00000000-0000-0000-0000-000000000000")
                                    {
                                        ProspectHistory prospectHistory = new();
                                        prospectHistory.ProspectId = newprospect?.Id ?? Guid.Empty;
                                        if (newprospect?.LastModifiedBy != null)
                                        {
                                            try
                                            {
                                                prospectHistory.ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty);
                                            }
                                            catch (NotFoundException ex) { }
                                            prospectHistory.LastModifiedById = user?.Id ?? Guid.Empty;
                                        }
                                        prospectHistory.ModifiedOn = newprospect?.LastModifiedOn;
                                        prospectHistory.GroupKey = groupKey;
                                        prospectHistory.Version = version;
                                        prospectHistory.FieldName = Regex.Replace(property?.Name ?? string.Empty, "([A-Z])", " $1").Trim();
                                        prospectHistory.FieldType = property?.PropertyType.Name.ToString() ?? string.Empty;
                                        if (prospectHistory.FieldType.Contains("Nullable"))
                                        {
                                            var type = Nullable.GetUnderlyingType(property.PropertyType)?.Name;
                                            prospectHistory.FieldType = type ?? string.Empty;
                                        }
                                        prospectHistory.NewValue = (property?.GetValue(newprospect))?.ToString() ?? null;
                                        prospectHistory.OldValue = default;
                                        prospectHistories.Add(prospectHistory);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if (oldProspect != null)
            {
                var userName = string.Empty;
                try
                {
                    var users = await userService.GetAsync(userId.ToString(), cancellationToken);
                    userName = (users.FirstName ?? string.Empty) + " " + (users.LastName ?? string.Empty);
                }
                catch (NotFoundException ex)
                {
                }
                AssignAuditStamps(prospectHistories, userName, version);
            }
            return prospectHistories;
        }

        public static async Task<List<ProspectHistory>> UpdateProspectHistoryForVM(ViewProspectDto newprospect, ViewProspectDto? oldProspect, Guid userId, int version, List<CustomProspectStatus> statuses, List<MasterPropertyType> propertiesType, List<MasterProspectSource> prospectSources, IUserService userService, CancellationToken cancellationToken, Guid? UserId = null)
        {
            statuses ??= new List<CustomProspectStatus>();
            propertiesType ??= new List<MasterPropertyType>();
            List<ProspectHistory> prospectHistories = new();
            PropertyInfo[]? propertyInfo = newprospect.GetType().GetProperties();
            var groupKey = Guid.NewGuid();
            var modifiedBy = newprospect.LastModifiedBy;
            var AssigntoUser = newprospect.AssignTo;
            UserDetailsDto? user = null;
            try
            {
                user = await userService.GetAsync(modifiedBy.ToString(), cancellationToken);
            }
            catch (NotFoundException ex) { }
            foreach (var property in propertyInfo)
            {
                if (property != null)
                {
                    if (ShouldIncludePropperty(property))
                    {
                        if (property.GetValue(newprospect) != null || property.GetValue(oldProspect) != null)
                        {
                            if (IsComplexTypeProperty(property.PropertyType))
                            {
                                var complexItems = await GetProspectRelatedChildEntityItemsForVM(property, newprospect, oldProspect, statuses, propertiesType, prospectSources, groupKey, version, userService, cancellationToken, user: user);
                                prospectHistories.AddRange(complexItems);
                            }
                            else
                            {
                                bool statusChanged = oldProspect?.Status?.Id != newprospect?.Status?.Id;
                                bool notesChanged = oldProspect?.Notes != newprospect?.Notes;
                                bool scheduleDateChanged = oldProspect?.ScheduleDate != newprospect?.ScheduleDate;

                                if (newprospect != null && oldProspect != null)
                                {
                                    if ((property?.GetValue(oldProspect)?.ToString() != property?.GetValue(newprospect)?.ToString() || (statusChanged && property?.Name == "ScheduleDate") || (property?.Name == "Notes" && (statusChanged || notesChanged || scheduleDateChanged))) && !(property?.Name == "Notes" && string.IsNullOrWhiteSpace(newprospect?.Notes)))
                                    {
                                        if (property?.GetValue(newprospect)?.ToString() != "0" && property?.GetValue(newprospect)?.ToString() != "None" && property?.GetValue(newprospect)?.ToString() != "00000000-0000-0000-0000-000000000000" && property?.GetValue(newprospect)?.ToString() != null)
                                        {
                                            ProspectHistory prospectHistory = new();
                                            prospectHistory.ProspectId = newprospect?.Id ?? Guid.Empty;
                                            if (newprospect?.LastModifiedBy != null)
                                            {
                                                prospectHistory.ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty);
                                                prospectHistory.LastModifiedById = user?.Id ?? Guid.Empty;
                                            }
                                            if (newprospect?.AssignTo != null)
                                            {
                                                prospectHistory.UserId = UserId;
                                            }
                                            prospectHistory.ModifiedOn = newprospect?.LastModifiedOn;
                                            prospectHistory.GroupKey = groupKey;
                                            prospectHistory.Version = version;
                                            prospectHistory.FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim();
                                            prospectHistory.FieldType = property?.PropertyType.Name.ToString() ?? string.Empty;
                                            if (prospectHistory.FieldType.Contains("Nullable"))
                                            {
                                                var type = Nullable.GetUnderlyingType(property.PropertyType)?.Name;
                                                prospectHistory.FieldType = type ?? string.Empty;
                                            }
                                            prospectHistory.NewValue = (property?.GetValue(newprospect))?.ToString() ?? null;
                                            prospectHistory.OldValue = property?.GetValue(oldProspect)?.ToString() ?? default!;
                                            prospectHistories.Add(prospectHistory);
                                        }
                                        else if (property?.GetValue(oldProspect)?.ToString() != "0" && property?.GetValue(oldProspect)?.ToString() != "None" && property?.GetValue(oldProspect)?.ToString() != "00000000-0000-0000-0000-000000000000" && property?.GetValue(oldProspect)?.ToString() != null)
                                        {
                                            ProspectHistory prospectHistory = new();
                                            prospectHistory.ProspectId = newprospect?.Id ?? Guid.Empty;
                                            if (newprospect?.LastModifiedBy != null)
                                            {
                                                prospectHistory.ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty);
                                                prospectHistory.LastModifiedById = user?.Id ?? Guid.Empty;
                                            }
                                            if (newprospect?.AssignTo != null)
                                            {
                                                prospectHistory.UserId = UserId;
                                            }
                                            prospectHistory.ModifiedOn = newprospect?.LastModifiedOn;
                                            prospectHistory.GroupKey = groupKey;
                                            prospectHistory.Version = version;
                                            prospectHistory.FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim();
                                            prospectHistory.FieldType = property?.PropertyType.Name.ToString() ?? string.Empty;
                                            if (prospectHistory.FieldType.Contains("Nullable"))
                                            {
                                                var type = Nullable.GetUnderlyingType(property.PropertyType)?.Name;
                                                prospectHistory.FieldType = type ?? string.Empty;
                                            }
                                            prospectHistory.NewValue = (property?.GetValue(newprospect))?.ToString() ?? null;
                                            prospectHistory.OldValue = property?.GetValue(oldProspect)?.ToString() ?? default!;
                                            prospectHistories.Add(prospectHistory);
                                        }
                                    }
                                    else if (property?.GetValue(newprospect)?.ToString() == null && property?.GetValue(newprospect)?.ToString() == string.Empty)
                                    {
                                        if (property?.GetValue(newprospect)?.ToString() != "0" && property?.GetValue(newprospect)?.ToString() != "None" && property?.GetValue(newprospect)?.ToString() != "00000000-0000-0000-0000-000000000000")
                                        {
                                            ProspectHistory prospectHistory = new();
                                            prospectHistory.ProspectId = newprospect?.Id ?? Guid.Empty;
                                            if (newprospect?.LastModifiedBy != null)
                                            {

                                                prospectHistory.ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty);

                                                prospectHistory.LastModifiedById = user?.Id ?? Guid.Empty;
                                            }
                                            if (newprospect?.AssignTo != null)
                                            {
                                                prospectHistory.UserId = UserId;
                                            }
                                            prospectHistory.ModifiedOn = newprospect?.LastModifiedOn;
                                            prospectHistory.GroupKey = groupKey;
                                            prospectHistory.Version = version;
                                            prospectHistory.FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim();
                                            prospectHistory.FieldType = property?.PropertyType.Name.ToString() ?? string.Empty;
                                            if (prospectHistory.FieldType.Contains("Nullable"))
                                            {
                                                var type = Nullable.GetUnderlyingType(property.PropertyType)?.Name;
                                                prospectHistory.FieldType = type ?? string.Empty;
                                            }
                                            prospectHistory.NewValue = (property?.GetValue(newprospect))?.ToString() ?? string.Empty;
                                            prospectHistory.OldValue = default;
                                            prospectHistories.Add(prospectHistory);
                                        }
                                    }
                                }
                                else
                                {
                                    if (property?.GetValue(newprospect)?.ToString() != "0" && property?.GetValue(newprospect)?.ToString() != "None" && property?.GetValue(newprospect)?.ToString() != "00000000-0000-0000-0000-000000000000")
                                    {
                                        ProspectHistory prospectHistory = new();
                                        prospectHistory.ProspectId = newprospect?.Id ?? Guid.Empty;
                                        if (newprospect?.LastModifiedBy != null)
                                        {
                                            prospectHistory.ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty);
                                            prospectHistory.LastModifiedById = user?.Id ?? Guid.Empty;
                                        }
                                        if (newprospect?.AssignTo != null)
                                        {
                                            prospectHistory.UserId = UserId;
                                        }
                                        prospectHistory.ModifiedOn = newprospect?.LastModifiedOn;
                                        prospectHistory.GroupKey = groupKey;
                                        prospectHistory.Version = version;
                                        prospectHistory.FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim();
                                        prospectHistory.FieldType = property?.PropertyType.Name.ToString() ?? string.Empty;
                                        if (prospectHistory.FieldType.Contains("Nullable"))
                                        {
                                            var type = Nullable.GetUnderlyingType(property.PropertyType)?.Name;
                                            prospectHistory.FieldType = type ?? string.Empty;
                                        }
                                        prospectHistory.NewValue = (property?.GetValue(newprospect))?.ToString() ?? string.Empty;
                                        prospectHistory.OldValue = default;
                                        prospectHistories.Add(prospectHistory);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if (oldProspect != null)
            {
                var userName = string.Empty;
                try
                {
                    var user1 = await userService.GetAsync(userId.ToString(), cancellationToken);
                    userName = (user1.FirstName ?? string.Empty) + " " + (user1.LastName ?? string.Empty);
                }
                catch (NotFoundException ex) { }
                AssignAuditStamps(prospectHistories, userName, version);
            }
            return prospectHistories;
        }

        public static bool IsComplexTypeProperty(this Type type)
        {
            var data = Nullable.GetUnderlyingType(type)?.Name;
            if (type.Name == typeof(long).Name || type.Name == typeof(int).Name || type.Name == typeof(short).Name || type.Name == typeof(UIntPtr).Name || type?.Name == typeof(uint).Name || type.Name == typeof(byte).Name || type.Name == typeof(char).Name)
            {
                return false;
            }
            else if (type.Name == typeof(Half).Name || type.Name == typeof(float).Name || type.Name == typeof(double).Name || type.Name == typeof(decimal).Name)
            {
                return false;
            }
            else if (type.Name == typeof(bool).Name)
            {
                return false;
            }
            else if (type.Name == typeof(string).Name || type.Name == typeof(Guid).Name || type.Name == typeof(DateTime).Name)
            {
                return false;
            }
            else if (type.IsEnum || type.IsValueType)
            {
                return false;
            }
            else if (type.IsGenericType || type.IsNested || type.IsClass)
            {
                return true;
            }
            return false;
        }
        private static List<ProspectHistory> V2GetProspectRelatedChildEntityItemsForVM(PropertyInfo property, ViewProspectDto newProspect, ViewProspectDto? oldProspect, List<CustomProspectStatus> statuses, List<MasterPropertyType> propertyTypes, List<MasterProspectSource> sources, Guid groupKey, int version, UserDetailsDto? user, CancellationToken cancellationToken)
        {
            var items = new List<ProspectHistory>();
            if (property != null)
            {
                var type = property.PropertyType;
                var typeName = type?.Name ?? default;
                if (typeName == typeof(CustomProspectStatusDto).Name)
                {
                    var oldStatus = oldProspect?.Status;
                    var newStatus = newProspect?.Status;
                    if (oldStatus != null && newStatus != null)
                    {
                        var oldParentStatus = statuses.FirstOrDefault(i => i.Id == (oldStatus?.BaseId ?? default));
                        var newParentStatus = statuses.FirstOrDefault(i => i.Id == (newStatus?.BaseId ?? default));
                        if (oldParentStatus != null && newParentStatus != null)
                        {
                            if (oldParentStatus.Status != newParentStatus.Status)
                            {
                                var parentStatusItems = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldParentStatus?.DisplayName ?? default,
                                    NewValue = newParentStatus?.DisplayName ?? default,
                                    FieldType = property.PropertyType.Name,
                                    RelatedEntityId = newParentStatus?.Id ?? default,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityName = typeof(CustomProspectStatusDto).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(CustomProspectStatusDto).Name}"
                                };
                                items.Add(parentStatusItems);
                                if (newParentStatus?.Level > 0)
                                {
                                    var childStatusItems = new ProspectHistory()
                                    {
                                        FieldName = "Reason",
                                        OldValue = oldStatus?.DisplayName ?? default,
                                        NewValue = default,
                                        FieldType = property.PropertyType.Name,
                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                        GroupKey = groupKey,
                                        Version = version,
                                        RelatedEntityId = newStatus?.Id ?? default,
                                        RelatedEntityName = typeof(CustomProspectStatusDto).Name,
                                        RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(CustomProspectStatusDto).Name}"
                                    };
                                    items.Add(childStatusItems);
                                }
                            }
                        }
                        else
                        {
                            if (oldStatus != null && newStatus != null)
                            {
                                if (oldStatus.Status != newStatus.Status)
                                {
                                    var parentStatusItems = new ProspectHistory()
                                    {
                                        FieldName = property.Name,
                                        OldValue = oldStatus?.DisplayName ?? default,
                                        NewValue = newStatus?.DisplayName ?? default,
                                        FieldType = property.PropertyType.Name,
                                        RelatedEntityId = newParentStatus?.Id ?? default,
                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                        GroupKey = groupKey,
                                        Version = version,
                                        RelatedEntityName = typeof(CustomProspectStatusDto).Name,
                                        RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(CustomProspectStatusDto).Name}"
                                    };
                                    items.Add(parentStatusItems);
                                }
                            }
                            else if (newStatus != null)
                            {
                                var parentStatusItems = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = default,
                                    NewValue = newStatus?.DisplayName ?? default,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newStatus?.Id ?? default,
                                    RelatedEntityName = typeof(CustomProspectStatusDto).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(CustomProspectStatusDto).Name}"
                                };
                                items.Add(parentStatusItems);
                            }
                            else if (oldStatus != null)
                            {
                                var parentStatusItems = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldStatus?.DisplayName ?? default,
                                    NewValue = default,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newStatus?.Id ?? default,
                                    RelatedEntityName = typeof(CustomProspectStatusDto).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(CustomProspectStatusDto).Name}"
                                };
                                items.Add(parentStatusItems);
                            }
                        }
                    }
                }
                else if (typeName == typeof(AddressDto).Name)
                {
                    var oldAddress = oldProspect?.AddressDto;
                    var newAddress = newProspect?.AddressDto;
                    var addressProperties = typeof(AddressDto).GetProperties();
                    foreach (var addressProperty in addressProperties)
                    {
                        if (addressProperty != null)
                        {
                            if (oldAddress != null && newAddress != null)
                            {
                                if (addressProperty?.GetValue(newAddress)?.ToString() != addressProperty?.GetValue(oldAddress)?.ToString())
                                {
                                    if (addressProperty?.Name == "City" || addressProperty?.Name == "State" || addressProperty?.Name == "Country"
                                       || addressProperty?.Name == "Community" || addressProperty?.Name == "SubCommunity" || addressProperty?.Name == "TowerName"
                                       || addressProperty?.Name == "Locality" || addressProperty?.Name == "SubLocality" || addressProperty?.Name == "District")
                                    {
                                        var addressItem = new ProspectHistory()
                                        {
                                            FieldName = "Data" + " " + property.Name.Replace("Dto", ""),
                                            OldValue = addressProperty?.GetValue(oldAddress)?.ToString() ?? default,
                                            NewValue = addressProperty?.GetValue(newAddress)?.ToString() ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newAddress?.Id ?? default,
                                            RelatedEntityName = typeof(AddressDto).Name,
                                            RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(AddressDto).Name}"
                                        };
                                        items.Add(addressItem);
                                    }
                                }
                            }
                            else
                            {
                                if (addressProperty?.Name == "City" || addressProperty?.Name == "State" || addressProperty?.Name == "Country"
                                       || addressProperty?.Name == "Community" || addressProperty?.Name == "SubCommunity" || addressProperty?.Name == "TowerName"
                                        || addressProperty?.Name == "Locality" || addressProperty?.Name == "SubLocality" || addressProperty?.Name == "District")
                                {
                                    if (addressProperty.GetValue(newAddress) != null)
                                    {
                                        var addressItem = new ProspectHistory()
                                        {
                                            FieldName = "Data" + " " + property.Name.Replace("Dto", ""),
                                            OldValue = default,
                                            NewValue = addressProperty?.GetValue(newAddress)?.ToString() ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newAddress?.Id ?? default,
                                            RelatedEntityName = typeof(AddressDto).Name,
                                            RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(AddressDto).Name}"
                                        };
                                        items.Add(addressItem);
                                    }
                                }
                            }
                        }
                    }
                }
                else if (typeName == typeof(UserDto).Name)
                {
                    if (property.Name == "AssignedUser")
                    {
                        if (oldProspect?.AssignedUser != null && newProspect?.AssignedUser != null)
                        {
                            var newUser = newProspect.AssignedUser;
                            var oldUser = oldProspect.AssignedUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var assignedUser = new ProspectHistory()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newUser?.Id ?? default,
                                    RelatedEntityName = typeof(UserDto).Name,
                                    RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                                };
                                items.Add(assignedUser);
                            }
                        }
                        else if (newProspect?.AssignedUser != null)
                        {
                            var newUser = newProspect.AssignedUser;
                            var assignedUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = newUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(assignedUser);
                        }
                        else if (oldProspect?.AssignedUser != null)
                        {
                            var oldUser = oldProspect.AssignedUser;
                            var assignedUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = oldUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(assignedUser);
                        }
                    }
                    if (property.Name == "AssignedFromUser")
                    {
                        if (oldProspect?.AssignedFromUser != null && newProspect?.AssignedFromUser != null)
                        {
                            var newUser = newProspect.AssignedFromUser;
                            var oldUser = oldProspect.AssignedFromUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var assignedFromUser = new ProspectHistory()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newUser?.Id ?? default,
                                    RelatedEntityName = typeof(UserDto).Name,
                                    RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                                };
                                items.Add(assignedFromUser);
                            }
                        }
                        else if (newProspect?.AssignedFromUser != null)
                        {
                            var newUser = newProspect.AssignedFromUser;
                            var assignedFromUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = newUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(assignedFromUser);
                        }
                        else if (oldProspect?.AssignedFromUser != null)
                        {
                            var oldUser = oldProspect.AssignedFromUser;
                            var assignedFromUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = oldUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(assignedFromUser);
                        }
                    }
                    else if (property.Name == "LastModifiedUser")
                    {
                        if (oldProspect?.LastModifiedUser != null && newProspect?.LastModifiedUser != null)
                        {
                            var newUser = newProspect.LastModifiedUser;
                            var oldUser = oldProspect.LastModifiedUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var lastModifiedUser = new ProspectHistory()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newUser?.Id ?? default,
                                    RelatedEntityName = typeof(UserDto).Name,
                                    RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                                };
                                items.Add(lastModifiedUser);
                            }
                        }
                        else if (newProspect?.LastModifiedUser != null)
                        {
                            var newUser = newProspect.LastModifiedUser;
                            var lastModifiedUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = newUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(lastModifiedUser);
                        }
                        else if (oldProspect?.LastModifiedUser != null)
                        {
                            var oldUser = oldProspect.LastModifiedUser;
                            var lastModifiedUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = oldUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(lastModifiedUser);
                        }
                    }
                    else if (property.Name == "CreatedByUser")
                    {
                        if (oldProspect?.CreatedByUser != null && newProspect?.CreatedByUser != null)
                        {
                            var newUser = newProspect.CreatedByUser;
                            var oldUser = oldProspect.CreatedByUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var createdByUser = new ProspectHistory()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newUser?.Id ?? default,
                                    RelatedEntityName = typeof(UserDto).Name,
                                    RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                                };
                                items.Add(createdByUser);
                            }
                        }
                        else if (newProspect?.CreatedByUser != null)
                        {
                            var newUser = newProspect.CreatedByUser;
                            var createdByUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = newUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(createdByUser);
                        }
                        else if (oldProspect?.CreatedByUser != null)
                        {
                            var oldUser = oldProspect.CreatedByUser;
                            var createdByUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = oldUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(createdByUser);
                        }
                    }
                    else if (property.Name == "SourcingManagerUser")
                    {
                        if (oldProspect?.SourcingManagerUser != null && newProspect?.SourcingManagerUser != null)
                        {
                            var newUser = newProspect.SourcingManagerUser;
                            var oldUser = oldProspect.SourcingManagerUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var sourcingManagerUser = new ProspectHistory()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newUser?.Id ?? default,
                                    RelatedEntityName = typeof(UserDto).Name,
                                    RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                                };
                                items.Add(sourcingManagerUser);
                            }
                        }
                        else if (newProspect?.SourcingManagerUser != null)
                        {
                            var newUser = newProspect.SourcingManagerUser;
                            var sourcingManagerUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = newUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(sourcingManagerUser);
                        }
                        else if (oldProspect?.SourcingManagerUser != null)
                        {
                            var oldUser = oldProspect.SourcingManagerUser;
                            var sourcingManagerUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = oldUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(sourcingManagerUser);
                        }
                    }
                    else if (property.Name == "ClosingManagerUser")
                    {
                        if (oldProspect?.ClosingManagerUser != null && newProspect?.ClosingManagerUser != null)
                        {
                            var newUser = newProspect.ClosingManagerUser;
                            var oldUser = oldProspect.ClosingManagerUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var closingManagerUser = new ProspectHistory()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newUser?.Id ?? default,
                                    RelatedEntityName = typeof(UserDto).Name,
                                    RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                                };
                                items.Add(closingManagerUser);
                            }
                        }
                        else if (newProspect?.ClosingManagerUser != null)
                        {
                            var newUser = newProspect.ClosingManagerUser;
                            var closingManagerUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = newUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(closingManagerUser);
                        }
                        else if (oldProspect?.ClosingManagerUser != null)
                        {
                            var oldUser = oldProspect.ClosingManagerUser;
                            var closingManagerUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = oldUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(closingManagerUser);
                        }
                    }
                    else if (property.Name == "ConvertedByUser")
                    {
                        if (oldProspect?.ConvertedByUser != null && newProspect?.ConvertedByUser != null)
                        {
                            var newUser = newProspect.ConvertedByUser;
                            var oldUser = oldProspect.ConvertedByUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var convertedByUser = new ProspectHistory()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newUser?.Id ?? default,
                                    RelatedEntityName = typeof(UserDto).Name,
                                    RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                                };
                                items.Add(convertedByUser);
                            }
                        }
                        else if (newProspect?.ConvertedByUser != null)
                        {
                            var newUser = newProspect.ConvertedByUser;
                            var convertedByUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = newUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(convertedByUser);
                        }
                        else if (oldProspect?.ConvertedByUser != null)
                        {
                            var oldUser = oldProspect.ConvertedByUser;
                            var convertedByUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = oldUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(convertedByUser);
                        }
                    }
                }
                else if (typeName == typeof(ViewProspectEnquiryDto).Name)
                {
                    var oldPrimaryEnquiry = oldProspect?.Enquiry;
                    var newPrimaryEnquiry = newProspect?.Enquiry;
                    var enquiryProperties = typeof(ViewProspectEnquiryDto).GetProperties();
                    foreach (var enquiryProperty in enquiryProperties)
                    {

                        if (ShouldIncludePropperty(enquiryProperty))
                        {
                            if (enquiryProperty.GetValue(newPrimaryEnquiry) != null && enquiryProperty.GetValue(newPrimaryEnquiry) != default)
                            {
                                if (enquiryProperty.Name == "PropertyTypes")

                                {
                                    var oldParentPropertyType = propertyTypes?.Where(pt => oldPrimaryEnquiry?.PropertyTypes?.Any(ope => ope.BaseId == pt.Id || ope.Id == pt.Id) ?? false).Select(pt => pt.DisplayName.ToString()).FirstOrDefault();
                                    var newParentPropertyType = propertyTypes?.Where(pt => newPrimaryEnquiry?.PropertyTypes?.Any(ope => ope.BaseId == pt.Id || ope.Id == pt.Id) ?? false).Select(pt => pt.DisplayName.ToString()).FirstOrDefault();
                                    var childOldType = oldPrimaryEnquiry?.PropertyTypes?.Select(i => i.ChildType?.DisplayName).ToList();
                                    var childNewType = newPrimaryEnquiry?.PropertyTypes?.Select(i => i.ChildType?.DisplayName).ToList();
                                    string childOldTypes = null;
                                    string childNewTypes = null;
                                    if (childOldType != null)
                                    {
                                        childOldTypes = string.Join(",", childOldType);
                                    }
                                    if (childNewType != null)
                                    {
                                        childNewTypes = string.Join(",", childNewType);
                                    }
                                    if (oldParentPropertyType != null && newParentPropertyType != null)
                                    {
                                        if (oldParentPropertyType != newParentPropertyType)
                                        {
                                            var parentPropertyTypeItems = new ProspectHistory()
                                            {
                                                FieldName = enquiryProperty.Name,
                                                OldValue = oldParentPropertyType ?? default,
                                                NewValue = newParentPropertyType ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                            };
                                            items.Add(parentPropertyTypeItems);
                                            var childPropertyTypeItems = new ProspectHistory()
                                            {
                                                FieldName = "SubPropertyType",
                                                OldValue = childOldTypes ?? default,
                                                NewValue = childNewTypes ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                            };
                                            items.Add(childPropertyTypeItems);
                                        }
                                    }
                                    else if (oldParentPropertyType != null)
                                    {
                                        var parentPropertyTypeItems = new ProspectHistory()
                                        {
                                            FieldName = enquiryProperty.Name,
                                            OldValue = oldParentPropertyType ?? default,
                                            NewValue = newParentPropertyType ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                        };
                                        items.Add(parentPropertyTypeItems);
                                        var childPropertyTypeItems = new ProspectHistory()
                                        {
                                            FieldName = "SubPropertyType",
                                            OldValue = childOldTypes ?? default,
                                            NewValue = default,
                                            FieldType = property.PropertyType.Name,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                        };
                                        items.Add(childPropertyTypeItems);
                                    }
                                    else if (newParentPropertyType != null)
                                    {
                                        var parentPropertyTypeItem = new ProspectHistory()
                                        {
                                            FieldName = enquiryProperty.Name,
                                            OldValue = default,
                                            NewValue = newParentPropertyType ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                        };
                                        items.Add(parentPropertyTypeItem);
                                        var childPropertyTypeItem = new ProspectHistory()
                                        {
                                            FieldName = "SubPropertyType",
                                            OldValue = default,
                                            NewValue = childNewTypes ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                        };
                                        items.Add(childPropertyTypeItem);
                                    }
                                    else
                                    {
                                        var childPropertyTypeItem = new ProspectHistory()
                                        {
                                            FieldName = enquiryProperty.Name,
                                            OldValue = oldParentPropertyType ?? default,
                                            NewValue = newParentPropertyType ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                        };
                                    }
                                }
                                else if (enquiryProperty.PropertyType.Name == typeof(MasterProspectSourceDto).Name)
                                {
                                    var oldProspectSource = oldPrimaryEnquiry?.ProspectSource;
                                    var newProspectSource = newPrimaryEnquiry?.ProspectSource;
                                    if (newProspectSource != null && oldProspectSource != null)
                                    {
                                        if (oldProspectSource?.DisplayName != newProspectSource?.DisplayName)
                                        {
                                            var prospectSource = new ProspectHistory()
                                            {
                                                FieldName = enquiryProperty.Name,
                                                OldValue = oldProspectSource?.DisplayName ?? default,
                                                NewValue = newProspectSource?.DisplayName ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(MasterProspectSourceDto).Name}"
                                            };
                                            items.Add(prospectSource);
                                        }
                                    }
                                    else
                                    {
                                        var prospectSource = new ProspectHistory()
                                        {
                                            FieldName = enquiryProperty.Name,
                                            OldValue = default,
                                            NewValue = newProspectSource?.DisplayName ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(MasterProspectSourceDto).Name}"
                                        };
                                        items.Add(prospectSource);
                                    }

                                }
                                else if (enquiryProperty.PropertyType.Name.Contains("List`1"))
                                {
                                    if (enquiryProperty.Name == "EnquiryTypes" || enquiryProperty.Name == "BHKs" || enquiryProperty.Name == "BHKTypes" || enquiryProperty.Name == "Beds"
|| enquiryProperty.Name == "Baths" || enquiryProperty.Name == "Floors")
                                    {
                                        (string propertyName, string propertyType, string oldValue, string newValue) historyProperties = GetPropertiesForProspectHistory(oldPrimaryEnquiry, newPrimaryEnquiry, enquiryProperty.Name);
                                        if (historyProperties.oldValue != historyProperties.newValue)
                                        {
                                            var prospectSource = new ProspectHistory()
                                            {
                                                FieldName = historyProperties.propertyName,
                                                OldValue = historyProperties.oldValue,
                                                NewValue = historyProperties.newValue,
                                                FieldType = historyProperties.propertyType,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(MasterProspectSourceDto).Name}"
                                            };
                                            items.Add(prospectSource);
                                        }

                                    }
                                    else if (enquiryProperty.Name == "Addresses")
                                    {
                                        var addressProperties = typeof(AddressDto).GetProperties();
                                        foreach (var addressProperty in addressProperties)
                                        {
                                            if (addressProperty.Name == "City" ||
                                        addressProperty.Name == "State" || addressProperty.Name == "SubLocality" || addressProperty.Name == "Country")
                                            {
                                                (string propertyName, string propertyType, string oldValue, string newValue) historyProperties = GetPropertiesForProspectHistory(oldPrimaryEnquiry, newPrimaryEnquiry, addressProperty.Name);
                                                if (historyProperties.oldValue != historyProperties.newValue)
                                                {
                                                    var prospectSource = new ProspectHistory()
                                                    {
                                                        FieldName = historyProperties.propertyName,
                                                        OldValue = historyProperties.oldValue,
                                                        NewValue = historyProperties.newValue,
                                                        FieldType = historyProperties.propertyType,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(MasterProspectSourceDto).Name}"
                                                    };
                                                    items.Add(prospectSource);
                                                }
                                            }
                                        }
                                    }
                                }

                                else if (enquiryProperty.PropertyType.Name == typeof(AddressDto).Name)
                                {
                                    var oldEnquiredAddress = oldPrimaryEnquiry?.Address;
                                    var newEnquiredAddress = newPrimaryEnquiry?.Address;
                                    var addressProperties = typeof(AddressDto).GetProperties();



                                    foreach (var addressProperty in addressProperties)
                                    {
                                        if (addressProperty != null)
                                        {
                                            if (oldEnquiredAddress != null && newEnquiredAddress != null)
                                            {
                                                if (addressProperty?.GetValue(newEnquiredAddress)?.ToString() != addressProperty?.GetValue(oldEnquiredAddress)?.ToString())
                                                {
                                                    if (addressProperty?.Name == "City" || addressProperty?.Name == "State" || addressProperty?.Name == "Country")
                                                    {
                                                        var addressItem = new ProspectHistory()
                                                        {
                                                            FieldName = "Enquiry" + addressProperty.Name,
                                                            OldValue = default,
                                                            NewValue = addressProperty?.GetValue(newEnquiredAddress)?.ToString() ?? default,
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newEnquiredAddress?.Id ?? default,
                                                            RelatedEntityName = typeof(AddressDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(AddressDto).Name}"
                                                        };
                                                        items.Add(addressItem);
                                                    }
                                                }
                                            }

                                            else
                                            {
                                                if (addressProperty?.Name == "City" || addressProperty?.Name == "State" || addressProperty?.Name == "Country")
                                                {
                                                    if (addressProperty.GetValue(newEnquiredAddress) != null)
                                                    {
                                                        var addressItem = new ProspectHistory()
                                                        {
                                                            FieldName = "Enquiry" + addressProperty.Name,
                                                            OldValue = default,
                                                            NewValue = addressProperty?.GetValue(newEnquiredAddress)?.ToString() ?? default,
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newEnquiredAddress?.Id ?? default,
                                                            RelatedEntityName = typeof(AddressDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(AddressDto).Name}"
                                                        };
                                                        items.Add(addressItem);
                                                    }
                                                }
                                            }

                                        }
                                    }
                                }
                                else
                                {
                                    if (newPrimaryEnquiry != null && oldPrimaryEnquiry != null)
                                    {
                                        if (enquiryProperty.GetValue(newPrimaryEnquiry)?.ToString() != enquiryProperty.GetValue(oldPrimaryEnquiry)?.ToString())
                                        {
                                            if (enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() != "None" && enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() != "00000000-0000-0000-0000-000000000000")
                                            {
                                                if (enquiryProperty?.Name == "CarpetArea")
                                                {
                                                    var oldParentCarpetArea = oldPrimaryEnquiry?.CarpetArea;
                                                    var newParentCarpetArea = newPrimaryEnquiry?.CarpetArea;
                                                    var oldParentCarpetAreaUnit = oldPrimaryEnquiry?.CarpetAreaUnit;
                                                    var newParentCarpetAreaUnit = newPrimaryEnquiry?.CarpetAreaUnit;
                                                    var oldParentCarpetAreawithUnit = oldParentCarpetArea + " " + oldParentCarpetAreaUnit;
                                                    var newParentCarpetAreawithUnit = newParentCarpetArea + " " + newParentCarpetAreaUnit;


                                                    if (oldParentCarpetAreawithUnit != newParentCarpetAreawithUnit)
                                                    {
                                                        var carpetarea = new ProspectHistory()
                                                        {
                                                            FieldName = enquiryProperty.Name,
                                                            OldValue = oldParentCarpetAreawithUnit.Trim(),
                                                            NewValue = newParentCarpetAreawithUnit.Trim(),
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + "  " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                        };
                                                        items.Add(carpetarea);

                                                    }

                                                }
                                                else if (enquiryProperty?.Name == "BuiltUpArea")
                                                {
                                                    var oldParentArea = oldPrimaryEnquiry?.BuiltUpArea;
                                                    var newParentArea = newPrimaryEnquiry?.BuiltUpArea;
                                                    var oldParentUnit = oldPrimaryEnquiry?.BuiltUpAreaUnit;
                                                    var newParentCarpetAreaUnit = newPrimaryEnquiry?.BuiltUpAreaUnit;
                                                    var oldParentCarpetAreawithUnit = oldParentArea + " " + oldParentUnit;
                                                    var newParentCarpetAreawithUnit = newParentArea + " " + newParentCarpetAreaUnit;


                                                    if (oldParentCarpetAreawithUnit != newParentCarpetAreawithUnit)
                                                    {
                                                        var carpetarea = new ProspectHistory()
                                                        {
                                                            FieldName = enquiryProperty.Name,
                                                            OldValue = oldParentCarpetAreawithUnit.Trim(),
                                                            NewValue = newParentCarpetAreawithUnit.Trim(),
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + "  " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                        };
                                                        items.Add(carpetarea);

                                                    }

                                                }
                                                else if (enquiryProperty?.Name == "SaleableArea")
                                                {
                                                    var oldParentArea = oldPrimaryEnquiry?.SaleableArea;
                                                    var newParentArea = newPrimaryEnquiry?.SaleableArea;
                                                    var oldParentAreaUnit = oldPrimaryEnquiry?.SaleableAreaUnit;
                                                    var newParentAreaUnit = newPrimaryEnquiry?.SaleableAreaUnit;
                                                    var oldParentwithUnit = oldParentArea + " " + oldParentAreaUnit;
                                                    var newParentAreawithUnit = newParentArea + " " + newParentAreaUnit;


                                                    if (oldParentwithUnit != newParentAreawithUnit)
                                                    {
                                                        var carpetarea = new ProspectHistory()
                                                        {
                                                            FieldName = enquiryProperty.Name,
                                                            OldValue = oldParentwithUnit.Trim(),
                                                            NewValue = newParentAreawithUnit.Trim(),
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + "  " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                        };
                                                        items.Add(carpetarea);

                                                    }

                                                }
                                                else if (enquiryProperty.Name == "UpperBudget")
                                                {
                                                    var oldParentUpperBudget = oldPrimaryEnquiry?.UpperBudget;
                                                    var newParentUpperBudget = newPrimaryEnquiry?.UpperBudget;
                                                    if (oldParentUpperBudget != newParentUpperBudget)
                                                    {
                                                        var budgetHistory = new ProspectHistory()
                                                        {
                                                            FieldName = enquiryProperty.Name,
                                                            OldValue = oldParentUpperBudget?.ToString(),
                                                            NewValue = newParentUpperBudget?.ToString(),
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                        };
                                                        items.Add(budgetHistory);

                                                    }
                                                }
                                                else if (enquiryProperty.Name == "LowerBudget")
                                                {
                                                    var oldParentLowerBudget = oldPrimaryEnquiry?.LowerBudget;
                                                    var newParentLowerBudget = newPrimaryEnquiry?.LowerBudget;
                                                    if (oldParentLowerBudget != newParentLowerBudget)
                                                    {
                                                        var budgetHistory = new ProspectHistory()
                                                        {
                                                            FieldName = enquiryProperty.Name,
                                                            OldValue = oldParentLowerBudget?.ToString(),
                                                            NewValue = newParentLowerBudget?.ToString(),
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                        };
                                                        items.Add(budgetHistory);

                                                    }
                                                }
                                                else if (enquiryProperty?.Name == "NetArea")
                                                {
                                                    var oldParentArea = oldPrimaryEnquiry?.NetArea;
                                                    var newParentArea = newPrimaryEnquiry?.NetArea;
                                                    var oldParentAreaUnit = oldPrimaryEnquiry?.NetAreaUnit;
                                                    var newParentCarpetAreaUnit = newPrimaryEnquiry?.NetAreaUnit;
                                                    var oldParentCarpetAreawithUnit = oldParentArea + " " + oldParentAreaUnit;
                                                    var newParentCarpetAreawithUnit = newParentArea + " " + newParentCarpetAreaUnit;


                                                    if (oldParentCarpetAreawithUnit != newParentCarpetAreawithUnit)
                                                    {
                                                        var carpetarea = new ProspectHistory()
                                                        {
                                                            FieldName = enquiryProperty.Name,
                                                            OldValue = oldParentCarpetAreawithUnit.Trim(),
                                                            NewValue = newParentCarpetAreawithUnit.Trim(),
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + "  " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                        };
                                                        items.Add(carpetarea);

                                                    }

                                                }
                                                else if (enquiryProperty?.Name == "PropertyArea")
                                                {
                                                    var oldParentArea = oldPrimaryEnquiry?.PropertyArea;
                                                    var newParentArea = newPrimaryEnquiry?.PropertyArea;
                                                    var oldParentAreaUnit = oldPrimaryEnquiry?.PropertyAreaUnit;
                                                    var newParentAreaUnit = newPrimaryEnquiry?.PropertyAreaUnit;
                                                    var oldParentwithUnit = oldParentArea + " " + oldParentAreaUnit;
                                                    var newParentAreawithUnit = newParentArea + " " + newParentAreaUnit;


                                                    if (oldParentwithUnit != newParentAreawithUnit)
                                                    {
                                                        var carpetarea = new ProspectHistory()
                                                        {
                                                            FieldName = enquiryProperty.Name,
                                                            OldValue = oldParentwithUnit.Trim(),
                                                            NewValue = newParentAreawithUnit.Trim(),
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + "  " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                        };
                                                        items.Add(carpetarea);

                                                    }

                                                }
                                                else if (enquiryProperty?.Name != "CarpetAreaUnit" && enquiryProperty?.Name != "BuiltUpAreaUnit" && enquiryProperty?.Name != "SaleableAreaUnit" && enquiryProperty?.Name != "NetAreaUnit" && enquiryProperty?.Name != "PropertyAreaUnit" && enquiryProperty?.Name != "PropertyType")
                                                {
                                                    var enquiryItems = new ProspectHistory()
                                                    {
                                                        FieldName = Regex.Replace(enquiryProperty?.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                                        OldValue = enquiryProperty?.GetValue(oldPrimaryEnquiry)?.ToString() ?? null,
                                                        NewValue = enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() ?? default,
                                                        FieldType = enquiryProperty?.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}"
                                                    };

                                                    if (enquiryItems.FieldType.Contains("Nullable"))
                                                    {
                                                        var enquiryType = Nullable.GetUnderlyingType(enquiryProperty.PropertyType)?.Name;
                                                        enquiryItems.FieldType = enquiryType ?? string.Empty;
                                                    }
                                                    items.Add(enquiryItems);

                                                }


                                            }
                                        }
                                    }
                                    else if (enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() != null && enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() != string.Empty)
                                    {

                                        if (enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() != "None" && enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() != "00000000-0000-0000-0000-000000000000")
                                        {
                                            if (enquiryProperty?.Name == "CarpetArea" && newPrimaryEnquiry?.CarpetArea != 0)
                                            {
                                                var oldParentCarpetArea = oldPrimaryEnquiry?.CarpetArea;
                                                var newParentCarpetArea = newPrimaryEnquiry?.CarpetArea;
                                                var oldParentCarpetAreaUnit = oldPrimaryEnquiry?.CarpetAreaUnit;
                                                var newParentCarpetAreaUnit = newPrimaryEnquiry?.CarpetAreaUnit;
                                                var oldParentCarpetAreawithUnit = oldParentCarpetArea + " " + oldParentCarpetAreaUnit;
                                                var newParentCarpetAreawithUnit = newParentCarpetArea + " " + newParentCarpetAreaUnit;
                                                if (oldParentCarpetAreawithUnit != newParentCarpetAreawithUnit)
                                                {
                                                    var carpetarea = new ProspectHistory()
                                                    {
                                                        FieldName = enquiryProperty.Name,
                                                        OldValue = oldParentCarpetAreawithUnit.Trim(),
                                                        NewValue = newParentCarpetAreawithUnit.Trim(),
                                                        FieldType = property.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                    };
                                                    items.Add(carpetarea);

                                                }
                                            }
                                            else if (enquiryProperty?.Name == "BuiltUpArea" && newPrimaryEnquiry?.BuiltUpArea != 0)
                                            {
                                                var oldParentBuiltUpArea = oldPrimaryEnquiry?.BuiltUpArea;
                                                var newParentBuiltUpArea = newPrimaryEnquiry?.BuiltUpArea;
                                                var oldParentBuiltUpAreaUnit = oldPrimaryEnquiry?.BuiltUpAreaUnit;
                                                var newParentBuiltUpAreaUnit = newPrimaryEnquiry?.BuiltUpAreaUnit;
                                                var oldParentBuiltUpAreawithUnit = oldParentBuiltUpArea + " " + oldParentBuiltUpAreaUnit;
                                                var newParentBuiltUpAreawithUnit = newParentBuiltUpArea + " " + newParentBuiltUpAreaUnit;

                                                if (oldParentBuiltUpAreawithUnit != newParentBuiltUpAreawithUnit)
                                                {
                                                    var carpetarea = new ProspectHistory()
                                                    {
                                                        FieldName = enquiryProperty.Name,
                                                        OldValue = oldParentBuiltUpAreawithUnit.Trim(),
                                                        NewValue = newParentBuiltUpAreawithUnit.Trim(),
                                                        FieldType = property.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                    };
                                                    items.Add(carpetarea);
                                                }
                                            }
                                            else if (enquiryProperty?.Name == "SaleableArea" && newPrimaryEnquiry?.SaleableArea != 0)
                                            {
                                                var oldParentSaleableArea = oldPrimaryEnquiry?.SaleableArea;
                                                var newParentSaleableArea = newPrimaryEnquiry?.SaleableArea;
                                                var oldParentSaleableAreaUnit = oldPrimaryEnquiry?.SaleableAreaUnit;
                                                var newParentSaleableAreaUnit = newPrimaryEnquiry?.SaleableAreaUnit;
                                                var oldParentSaleableAreawithUnit = oldParentSaleableArea + " " + oldParentSaleableAreaUnit;
                                                var newParentSaleableAreawithUnit = newParentSaleableArea + " " + newParentSaleableAreaUnit;

                                                if (oldParentSaleableAreawithUnit != newParentSaleableAreawithUnit)
                                                {
                                                    var carpetarea = new ProspectHistory()
                                                    {
                                                        FieldName = enquiryProperty.Name,
                                                        OldValue = oldParentSaleableAreawithUnit.Trim(),
                                                        NewValue = newParentSaleableAreawithUnit.Trim(),
                                                        FieldType = property.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                    };
                                                    items.Add(carpetarea);

                                                }
                                            }
                                            else if (enquiryProperty?.Name == "NetArea" && newPrimaryEnquiry?.NetArea != 0)
                                            {
                                                var oldParentArea = oldPrimaryEnquiry?.NetArea;
                                                var newParentArea = newPrimaryEnquiry?.NetArea;
                                                var oldParentAreaUnit = oldPrimaryEnquiry?.NetAreaUnit;
                                                var newParentCarpetAreaUnit = newPrimaryEnquiry?.NetAreaUnit;
                                                var oldParentCarpetAreawithUnit = oldParentArea + " " + oldParentAreaUnit;
                                                var newParentCarpetAreawithUnit = newParentArea + " " + newParentCarpetAreaUnit;


                                                if (oldParentCarpetAreawithUnit != newParentCarpetAreawithUnit)
                                                {
                                                    var carpetarea = new ProspectHistory()
                                                    {
                                                        FieldName = enquiryProperty.Name,
                                                        OldValue = oldParentCarpetAreawithUnit.Trim(),
                                                        NewValue = newParentCarpetAreawithUnit.Trim(),
                                                        FieldType = property.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + "  " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                    };
                                                    items.Add(carpetarea);

                                                }

                                            }
                                            else if (enquiryProperty?.Name == "PropertyArea" && newPrimaryEnquiry?.PropertyArea != 0)
                                            {
                                                var oldParentArea = oldPrimaryEnquiry?.PropertyArea;
                                                var newParentArea = newPrimaryEnquiry?.PropertyArea;
                                                var oldParentAreaUnit = oldPrimaryEnquiry?.PropertyAreaUnit;
                                                var newParentAreaUnit = newPrimaryEnquiry?.PropertyAreaUnit;
                                                var oldParentwithUnit = oldParentArea + " " + oldParentAreaUnit;
                                                var newParentAreawithUnit = newParentArea + " " + newParentAreaUnit;


                                                if (oldParentwithUnit != newParentAreawithUnit)
                                                {
                                                    var carpetarea = new ProspectHistory()
                                                    {
                                                        FieldName = enquiryProperty.Name,
                                                        OldValue = oldParentwithUnit.Trim(),
                                                        NewValue = newParentAreawithUnit.Trim(),
                                                        FieldType = property.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + "  " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                    };
                                                    items.Add(carpetarea);

                                                }

                                            }

                                            else if (enquiryProperty?.Name == "UpperBudget" && newPrimaryEnquiry?.UpperBudget != 0)
                                            {
                                                var oldParentUpperBudget = oldPrimaryEnquiry?.UpperBudget;
                                                var newParentUpperBudget = newPrimaryEnquiry?.UpperBudget;
                                                if (oldParentUpperBudget != newParentUpperBudget)
                                                {
                                                    var budgetHistory = new ProspectHistory()
                                                    {
                                                        FieldName = enquiryProperty.Name,
                                                        OldValue = oldParentUpperBudget?.ToString(),
                                                        NewValue = newParentUpperBudget?.ToString(),
                                                        FieldType = property.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                    };
                                                    items.Add(budgetHistory);

                                                }
                                            }
                                            else if (enquiryProperty?.Name == "LowerBudget" && newPrimaryEnquiry?.LowerBudget != 0)
                                            {
                                                var oldParentLowerBudget = oldPrimaryEnquiry?.LowerBudget;
                                                var newParentLowerBudget = newPrimaryEnquiry?.LowerBudget;
                                                if (oldParentLowerBudget != newParentLowerBudget)
                                                {
                                                    var budgetHistory = new ProspectHistory()
                                                    {
                                                        FieldName = enquiryProperty.Name,
                                                        OldValue = oldParentLowerBudget?.ToString(),
                                                        NewValue = newParentLowerBudget?.ToString(),
                                                        FieldType = property.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                    };
                                                    items.Add(budgetHistory);

                                                }
                                            }
                                            else if (enquiryProperty?.Name != "CarpetAreaUnit" && enquiryProperty?.Name != "BuiltUpAreaUnit" && enquiryProperty?.Name != "SaleableAreaUnit" && enquiryProperty?.Name != "NetAreaUnit" && enquiryProperty?.Name != "PropertyAreaUnit"
                                                && enquiryProperty?.Name != "PropertyType" && newPrimaryEnquiry?.CarpetArea != 0 && newPrimaryEnquiry?.BuiltUpArea != 0 && newPrimaryEnquiry?.SaleableArea != 0 && newPrimaryEnquiry?.PropertyArea != 0 && newPrimaryEnquiry?.NetArea != 0
                                                && newPrimaryEnquiry?.UpperBudget != 0 && newPrimaryEnquiry?.LowerBudget != 0)
                                            {
                                                var enquiryItems = new ProspectHistory()
                                                {
                                                    FieldName = Regex.Replace(enquiryProperty?.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                                    OldValue = default,
                                                    NewValue = enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() ?? default,
                                                    FieldType = enquiryProperty?.PropertyType.Name,
                                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                    GroupKey = groupKey,
                                                    Version = version,
                                                    RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                    RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                    RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}"
                                                };

                                                if (enquiryItems.FieldType.Contains("Nullable"))
                                                {
                                                    var enquiryType = Nullable.GetUnderlyingType(enquiryProperty.PropertyType)?.Name;
                                                    enquiryItems.FieldType = enquiryType ?? string.Empty;
                                                }
                                                items.Add(enquiryItems);


                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else if (typeName == typeof(PropertyDto).Name)
                {
                    var oldPropNames = string.Join(",", oldProspect?.Properties?.Select(i => i.Title)?.ToList() ?? new());
                    var newPropNames = string.Join(",", newProspect.Properties?.Select(i => i.Title)?.ToList() ?? new());
                    if (newPropNames != null && oldPropNames != null)
                    {
                        if (newPropNames != oldPropNames)
                        {
                            var propItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = oldPropNames ?? default,
                                NewValue = oldPropNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(PropertyDto).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(PropertyDto).Name}"
                            };
                            items.Add(propItem);
                        }
                    }
                    else
                    {
                        var propItem = new ProspectHistory()
                        {
                            FieldName = property.Name,
                            OldValue = oldPropNames ?? default,
                            NewValue = oldPropNames ?? default,
                            FieldType = property.PropertyType.Name,
                            ProspectId = newProspect?.Id ?? Guid.Empty,
                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                            LastModifiedById = user?.Id ?? Guid.Empty,
                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                            GroupKey = groupKey,
                            Version = version,
                            RelatedEntityId = default,
                            RelatedEntityName = typeof(PropertyDto).Name,
                            RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(PropertyDto).Name}"
                        };
                        items.Add(propItem);
                    }
                }
                else if (typeName == typeof(ChannelPartnerDto).Name)
                {
                    var oldChannelPartnerNames = string.Join(",", oldProspect?.ChannelPartners?.Select(i => i.FirmName)?.ToList() ?? new());
                    var newChannelPartnerNames = string.Join(",", newProspect.ChannelPartners?.Select(i => i.FirmName)?.ToList() ?? new());
                    if (oldChannelPartnerNames != null && newChannelPartnerNames != null)
                    {
                        if (oldChannelPartnerNames != newChannelPartnerNames)
                        {
                            var channelPartnerItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = oldChannelPartnerNames ?? default,
                                NewValue = newChannelPartnerNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.ChannelPartner).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.ChannelPartner).Name}"
                            };
                            items.Add(channelPartnerItem);
                        }
                    }
                    else
                    {
                        var channelPartnerItem = new ProspectHistory()
                        {
                            FieldName = property.Name,
                            OldValue = default,
                            NewValue = newChannelPartnerNames ?? default,
                            FieldType = property.PropertyType.Name,
                            ProspectId = newProspect?.Id ?? Guid.Empty,
                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                            LastModifiedById = user?.Id ?? Guid.Empty,
                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                            GroupKey = groupKey,
                            Version = version,
                            RelatedEntityId = default,
                            RelatedEntityName = typeof(Lrb.Domain.Entities.ChannelPartner).Name,
                            RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.ChannelPartner).Name}"
                        };
                        items.Add(channelPartnerItem);
                    }
                }
                else if (typeName == typeof(ProjectDto).Name)
                {
                    var oldProjNames = string.Join(",", oldProspect?.Projects?.Select(i => i.Name)?.ToList() ?? new());
                    var newProjNames = string.Join(",", newProspect.Projects?.Select(i => i.Name)?.ToList() ?? new());
                    if (oldProjNames != null && newProjNames != null)
                    {
                        if (oldProjNames != newProjNames)
                        {
                            var projItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = oldProjNames ?? default,
                                NewValue = newProjNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.Project).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Project).Name}"
                            };
                            items.Add(projItem);
                        }
                    }
                    else
                    {
                        var projItem = new ProspectHistory()
                        {
                            FieldName = property.Name,
                            OldValue = oldProjNames ?? default,
                            NewValue = newProjNames ?? default,
                            FieldType = property.PropertyType.Name,
                            ProspectId = newProspect?.Id ?? Guid.Empty,
                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                            LastModifiedById = user?.Id ?? Guid.Empty,
                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                            GroupKey = groupKey,
                            Version = version,
                            RelatedEntityId = default,
                            RelatedEntityName = typeof(Lrb.Domain.Entities.Project).Name,
                            RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Project).Name}"
                        };
                        items.Add(projItem);
                    }

                }
                else if (type.IsGenericType)
                {
                    if (type.FullName == typeof(IList<ProspectEnquiry>).FullName || type.FullName == typeof(List<ProspectEnquiry>).FullName)
                    {
                        var oldPrimaryEnquiry = oldProspect?.Enquiry;
                        var newPrimaryEnquiry = newProspect?.Enquiry;
                        var enquiryProperties = typeof(ProspectEnquiry).GetProperties();
                        foreach (var enquiryProperty in enquiryProperties)
                        {

                            if (ShouldIncludePropperty(enquiryProperty))
                            {
                                if (enquiryProperty.GetValue(newPrimaryEnquiry) != null)
                                {

                                    if (enquiryProperty.Name == "PropertyTypes")
                                    {
                                        var oldParentPropertyType = propertyTypes?.Where(pt => oldPrimaryEnquiry?.PropertyTypes?.Any(ope => ope.BaseId == pt.Id || ope.Id == pt.Id) ?? false).Select(pt => pt.DisplayName.ToString()).FirstOrDefault();
                                        var newParentPropertyType = propertyTypes?.Where(pt => newPrimaryEnquiry?.PropertyTypes?.Any(ope => ope.BaseId == pt.Id || ope.Id == pt.Id) ?? false).Select(pt => pt.DisplayName.ToString()).FirstOrDefault();
                                        var childOldType = oldPrimaryEnquiry?.PropertyTypes?.Select(i => i.ChildType?.DisplayName).ToList();
                                        var childNewType = newPrimaryEnquiry?.PropertyTypes?.Select(i => i.ChildType?.DisplayName).ToList();
                                        string childOldTypes = null;
                                        string childNewTypes = null;
                                        if (childOldType != null)
                                        {
                                            childOldTypes = string.Join(",", childOldType);
                                        }
                                        if (childNewType != null)
                                        {
                                            childNewTypes = string.Join(",", childNewType);
                                        }
                                        if (oldParentPropertyType != null && newParentPropertyType != null)
                                        {
                                            var parentPropertyTypeItems = new ProspectHistory()
                                            {
                                                FieldName = enquiryProperty.Name,
                                                OldValue = oldParentPropertyType ?? default,
                                                NewValue = newParentPropertyType ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ProspectEnquiry).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterPropertyType).Name}"
                                            };
                                            items.Add(parentPropertyTypeItems);
                                            var childPropertyTypeItems = new ProspectHistory()
                                            {
                                                FieldName = "SubPropertyType",
                                                OldValue = childOldTypes ?? default,
                                                NewValue = childNewTypes ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ProspectEnquiry).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterPropertyType).Name}"
                                            };
                                            items.Add(childPropertyTypeItems);
                                        }
                                        else if (oldParentPropertyType != null)
                                        {
                                            var parentPropertyTypeItems = new ProspectHistory()
                                            {
                                                FieldName = enquiryProperty.Name,
                                                OldValue = oldParentPropertyType ?? default,
                                                NewValue = newParentPropertyType ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ProspectEnquiry).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterPropertyType).Name}"
                                            };
                                            items.Add(parentPropertyTypeItems);
                                            var childPropertyTypeItems = new ProspectHistory()
                                            {
                                                FieldName = "SubPropertyType",
                                                OldValue = childOldTypes ?? default,
                                                NewValue = default,
                                                FieldType = property.PropertyType.Name,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ProspectEnquiry).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterPropertyType).Name}"
                                            };
                                            items.Add(childPropertyTypeItems);
                                        }
                                        else if (newParentPropertyType != null)
                                        {
                                            var parentPropertyTypeItem = new ProspectHistory()
                                            {
                                                FieldName = enquiryProperty.Name,
                                                OldValue = default,
                                                NewValue = newParentPropertyType ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ProspectEnquiry).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterPropertyType).Name}"
                                            };
                                            items.Add(parentPropertyTypeItem);
                                            var childPropertyTypeItem = new ProspectHistory()
                                            {
                                                FieldName = "SubPropertyType",
                                                OldValue = default,
                                                NewValue = childNewTypes ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ProspectEnquiry).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterPropertyType).Name}"
                                            };
                                            items.Add(childPropertyTypeItem);
                                        }
                                        else
                                        {
                                            var childPropertyTypeItem = new ProspectHistory()
                                            {
                                                FieldName = enquiryProperty.Name,
                                                OldValue = oldParentPropertyType ?? default,
                                                NewValue = newParentPropertyType ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ProspectEnquiry).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterPropertyType).Name}"
                                            };
                                        }
                                    }
                                    else if (enquiryProperty.PropertyType.Name == typeof(MasterProspectSource).Name)
                                    {
                                        var oldProspectSource = oldPrimaryEnquiry?.ProspectSource;
                                        var newProspectSource = newPrimaryEnquiry?.ProspectSource;
                                        var prospectSource = new ProspectHistory()
                                        {
                                            FieldName = enquiryProperty.Name,
                                            OldValue = oldProspectSource?.DisplayName ?? default,
                                            NewValue = newProspectSource?.DisplayName ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(MasterProspectSource).Name,
                                            RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterProspectSource).Name}"
                                        };
                                        items.Add(prospectSource);
                                    }
                                    else if (enquiryProperty.PropertyType.Name == typeof(Address).Name)
                                    {
                                        var oldEnquiredAddress = oldPrimaryEnquiry?.Address?.ToString();
                                        var newEnquiredAddress = newPrimaryEnquiry?.Address?.ToString();

                                        if (oldEnquiredAddress != null && newEnquiredAddress != null)
                                        {
                                            var enquiredAddressItem = new ProspectHistory()
                                            {
                                                FieldName = enquiryProperty.Name,
                                                OldValue = oldEnquiredAddress ?? default,
                                                NewValue = newEnquiredAddress ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Address?.Id ?? default,
                                                RelatedEntityName = typeof(Address).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(Address).Name}"
                                            };
                                            items.Add(enquiredAddressItem);
                                        }

                                    }
                                    else
                                    {
                                        var enquiryItems = new ProspectHistory()
                                        {
                                            FieldName = enquiryProperty.Name,
                                            NewValue = enquiryProperty.GetValue(newPrimaryEnquiry)?.ToString() ?? default,
                                            FieldType = enquiryProperty.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(ProspectEnquiry).Name,
                                            RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}"
                                        };
                                        if (oldPrimaryEnquiry != null)
                                        {
                                            enquiryItems.OldValue = enquiryProperty.GetValue(oldPrimaryEnquiry)?.ToString() ?? null;
                                        }
                                        items.Add(enquiryItems);
                                    }
                                }
                            }
                        }
                    }
                    else if (type.FullName == typeof(IList<ProjectDto>).FullName || type.FullName == typeof(List<ProjectDto>).FullName)
                    {
                        var oldProjNames = string.Join(",", oldProspect?.Projects?.Select(i => i.Name)?.ToList() ?? new());
                        var newProjNames = string.Join(",", newProspect.Projects?.Select(i => i.Name)?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldProjNames) && !string.IsNullOrEmpty(newProjNames))
                        {
                            if (oldProjNames != newProjNames)
                            {
                                var projItem = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldProjNames ?? default,
                                    NewValue = newProjNames ?? default,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = default,
                                    RelatedEntityName = typeof(Lrb.Domain.Entities.Project).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Project).Name}"
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newProjNames))
                        {
                            var projItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newProjNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.Project).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Project).Name}"
                            };
                            items.Add(projItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<AgencyDto>).FullName || type.FullName == typeof(List<AgencyDto>).FullName)
                    {
                        var oldAgencyNames = string.Join(",", oldProspect?.Agencies?.Select(i => i.Name)?.ToList() ?? new());
                        var newAgencyNames = string.Join(",", newProspect.Agencies?.Select(i => i.Name)?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldAgencyNames) && !string.IsNullOrEmpty(newAgencyNames))
                        {
                            if (oldAgencyNames != newAgencyNames)
                            {
                                var projItem = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldAgencyNames ?? default,
                                    NewValue = newAgencyNames ?? default,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user.FirstName ?? string.Empty) + " " + (user.LastName ?? string.Empty),
                                    LastModifiedById = user.Id,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = default,
                                    RelatedEntityName = typeof(Lrb.Domain.Entities.Agency).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Agency).Name}"
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newAgencyNames))
                        {
                            var projItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newAgencyNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user.FirstName ?? string.Empty) + " " + (user.LastName ?? string.Empty),
                                LastModifiedById = user.Id,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.Agency).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Agency).Name}"
                            };
                            items.Add(projItem);
                        }
                        else if (!string.IsNullOrEmpty(oldAgencyNames))
                        {
                            var projItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = oldAgencyNames,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user.FirstName ?? string.Empty) + " " + (user.LastName ?? string.Empty),
                                LastModifiedById = user.Id,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.Agency).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Agency).Name}"
                            };
                            items.Add(projItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<PropertyDto>).FullName || type.FullName == typeof(List<PropertyDto>).FullName)
                    {
                        var oldPropNames = string.Join(",", oldProspect?.Properties?.Select(i => i.Title)?.ToList() ?? new());
                        var newPropNames = string.Join(",", newProspect.Properties?.Select(i => i.Title)?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(newPropNames) && !string.IsNullOrEmpty(oldPropNames))
                        {
                            if (newPropNames != oldPropNames)
                            {
                                var propItem = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldPropNames ?? default,
                                    NewValue = newPropNames ?? default,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = default,
                                    RelatedEntityName = typeof(PropertyDto).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(PropertyDto).Name}"
                                };
                                items.Add(propItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newPropNames))
                        {
                            var propItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newPropNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(PropertyDto).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(PropertyDto).Name}"
                            };
                            items.Add(propItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<ChannelPartnerDto>).FullName || type.FullName == typeof(List<ChannelPartnerDto>).FullName)
                    {
                        var oldChannelPartnerNames = string.Join(",", oldProspect?.ChannelPartners?.Select(i => i.FirmName)?.ToList() ?? new());
                        var newChannelPartnerNames = string.Join(",", newProspect.ChannelPartners?.Select(i => i.FirmName)?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldChannelPartnerNames) && !string.IsNullOrEmpty(newChannelPartnerNames))
                        {
                            if (oldChannelPartnerNames != newChannelPartnerNames)
                            {
                                var channelPartnerItem = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldChannelPartnerNames ?? default,
                                    NewValue = newChannelPartnerNames ?? default,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = default,
                                    RelatedEntityName = typeof(Lrb.Domain.Entities.ChannelPartner).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.ChannelPartner).Name}"
                                };
                                items.Add(channelPartnerItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newChannelPartnerNames))
                        {
                            var channelPartnerItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newChannelPartnerNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.ChannelPartner).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.ChannelPartner).Name}"
                            };
                            items.Add(channelPartnerItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<CampaignDto>).FullName || type.FullName == typeof(List<CampaignDto>).FullName)
                    {
                        var oldCampaignNames = string.Join(",", oldProspect?.Campaigns?.Select(i => i.Name)?.ToList() ?? new());
                        var newCampaignNames = string.Join(",", newProspect.Campaigns?.Select(i => i.Name)?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldCampaignNames) && !string.IsNullOrEmpty(newCampaignNames))
                        {
                            if (oldCampaignNames != newCampaignNames)
                            {
                                var camItem = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldCampaignNames ?? default,
                                    NewValue = newCampaignNames ?? default,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user.FirstName ?? string.Empty) + " " + (user.LastName ?? string.Empty),
                                    LastModifiedById = user.Id,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = default,
                                    RelatedEntityName = typeof(Lrb.Domain.Entities.Campaign).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Campaign).Name}"
                                };
                                items.Add(camItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newCampaignNames))
                        {
                            var projItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newCampaignNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user.FirstName ?? string.Empty) + " " + (user.LastName ?? string.Empty),
                                LastModifiedById = user.Id,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.Campaign).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Campaign).Name}"
                            };
                            items.Add(projItem);
                        }
                        else if (!string.IsNullOrEmpty(oldCampaignNames))
                        {
                            var projItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = oldCampaignNames,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user.FirstName ?? string.Empty) + " " + (user.LastName ?? string.Empty),
                                LastModifiedById = user.Id,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.Campaign).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Campaign).Name}"
                            };
                            items.Add(projItem);
                        }
                    }
                    else if (type.FullName == typeof(Dictionary<ContactType, int>).FullName || type.FullName == typeof(Dictionary<ContactType, int>).FullName)
                    {
                        var oldContactType = oldProspect?.ContactRecords?.LastOrDefault().Key.ToString();
                        var newContactType = newProspect.ContactRecords?.LastOrDefault().Key.ToString();

                        if (newContactType != null && oldContactType != null && newProspect.ContactRecords.Any(entry => entry.Value > 1) && (oldProspect?.ContactRecords?.Any() ?? false))
                        {
                            var changedValues = newProspect.ContactRecords
                            .Where(newEntry => oldProspect.ContactRecords.ContainsKey(newEntry.Key)
                              && oldProspect.ContactRecords[newEntry.Key] != newEntry.Value)
                            .Select(newEntry => new ProspectHistory
                            {
                                FieldName = property.Name,
                                OldValue = null,
                                NewValue = newEntry.Key.ToString(),
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(ProspectCommunication).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectCommunication).Name}"
                            });
                            items.AddRange(changedValues.ToList());
                            if (!changedValues?.Any() ?? false)
                            {
                                var newEntries = newProspect.ContactRecords
                                .Where(newEntry => oldProspect?.ContactRecords == null ||
                                                   !oldProspect.ContactRecords.ContainsKey(newEntry.Key))
                                .Select(newEntry => new ProspectHistory
                                {
                                    FieldName = property.Name,
                                    OldValue = null,
                                    NewValue = newEntry.Key.ToString(),
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = default,
                                    RelatedEntityName = typeof(ProspectCommunication).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectCommunication).Name}"
                                });
                                items.AddRange(newEntries.ToList());
                            }
                        }
                        else
                        {
                            var contactTypeItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = null,
                                NewValue = newContactType ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(ProspectCommunication).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectCommunication).Name}"
                            };
                            items.Add(contactTypeItem);
                        }
                    }
                }
            }
            return items;
        }
        private static async Task<List<ProspectHistory>> GetProspectRelatedChildEntityItemsForVM(PropertyInfo property, ViewProspectDto newProspect, ViewProspectDto? oldProspect, List<CustomProspectStatus> statuses, List<MasterPropertyType> propertyTypes, List<MasterProspectSource> sources, Guid groupKey, int version, IUserService userService, CancellationToken cancellationToken, UserDetailsDto? user = null)
        {
            var items = new List<ProspectHistory>();
            var modifiedBy = newProspect.LastModifiedBy;
            //UserDetailsDto? user = null;
            try
            {
                user ??= await userService.GetAsync(modifiedBy.ToString(), cancellationToken);
            }
            catch (NotFoundException ex) { }
            if (property != null)
            {
                var type = property.PropertyType;
                var typeName = type?.Name ?? default;
                if (typeName == typeof(CustomProspectStatusDto).Name)
                {
                    var oldStatus = oldProspect?.Status;
                    var newStatus = newProspect?.Status;
                    if (oldStatus != null && newStatus != null)
                    {
                        var oldParentStatus = statuses?.FirstOrDefault(i => i.Id == (oldStatus?.BaseId ?? default));
                        var newParentStatus = statuses?.FirstOrDefault(i => i.Id == (newStatus?.BaseId ?? default));
                        if (oldParentStatus != null && newParentStatus != null)
                        {
                            if (oldParentStatus.Status != newParentStatus.Status)
                            {
                                var parentStatusItems = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldParentStatus?.DisplayName ?? default,
                                    NewValue = newParentStatus?.DisplayName ?? default,
                                    FieldType = property.PropertyType.Name,
                                    RelatedEntityId = newParentStatus?.Id ?? default,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityName = typeof(CustomProspectStatusDto).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(CustomProspectStatusDto).Name}"
                                };
                                items.Add(parentStatusItems);
                                if (newParentStatus?.Level > 0)
                                {
                                    var childStatusItems = new ProspectHistory()
                                    {
                                        FieldName = "Reason",
                                        OldValue = oldStatus?.DisplayName ?? default,
                                        NewValue = default,
                                        FieldType = property.PropertyType.Name,
                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                        GroupKey = groupKey,
                                        Version = version,
                                        RelatedEntityId = newStatus?.Id ?? default,
                                        RelatedEntityName = typeof(CustomProspectStatusDto).Name,
                                        RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(CustomProspectStatusDto).Name}"
                                    };
                                    items.Add(childStatusItems);
                                }
                            }
                        }
                        else
                        {
                            if (oldStatus != null && newStatus != null)
                            {
                                if (oldStatus.Status != newStatus.Status)
                                {

                                    var parentStatusItems = new ProspectHistory()
                                    {
                                        FieldName = property.Name,
                                        OldValue = oldStatus?.DisplayName ?? default,
                                        NewValue = newStatus?.DisplayName ?? default,
                                        FieldType = property.PropertyType.Name,
                                        RelatedEntityId = newParentStatus?.Id ?? default,
                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                        GroupKey = groupKey,
                                        Version = version,
                                        RelatedEntityName = typeof(CustomProspectStatusDto).Name,
                                        RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(CustomProspectStatusDto).Name}"
                                    };
                                    items.Add(parentStatusItems);
                                }
                            }
                            else if (newStatus != null)
                            {

                                var parentStatusItems = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = default,
                                    NewValue = newStatus?.DisplayName ?? default,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newStatus?.Id ?? default,
                                    RelatedEntityName = typeof(CustomProspectStatusDto).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(CustomProspectStatusDto).Name}"
                                };
                                items.Add(parentStatusItems);
                            }
                            else if (oldStatus != null)
                            {

                                var parentStatusItems = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldStatus?.DisplayName ?? default,
                                    NewValue = default,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newStatus?.Id ?? default,
                                    RelatedEntityName = typeof(CustomProspectStatusDto).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(CustomProspectStatusDto).Name}"
                                };
                                items.Add(parentStatusItems);
                            }
                        }
                    }
                }
                else if (typeName == typeof(AddressDto).Name)
                {
                    var oldAddress = oldProspect?.AddressDto;
                    var newAddress = newProspect?.AddressDto;
                    var addressProperties = typeof(AddressDto).GetProperties();
                    foreach (var addressProperty in addressProperties)
                    {
                        if (addressProperty != null)
                        {
                            if (oldAddress != null && newAddress != null)
                            {
                                if (addressProperty?.GetValue(newAddress)?.ToString() != addressProperty?.GetValue(oldAddress)?.ToString())
                                {
                                    if (addressProperty?.Name == "City" || addressProperty?.Name == "State" || addressProperty?.Name == "Country"
                                         || addressProperty?.Name == "Community" || addressProperty?.Name == "SubCommunity" || addressProperty?.Name == "TowerName"
                                         || addressProperty?.Name == "SubLocality")
                                    {

                                        var addressItem = new ProspectHistory()
                                        {
                                            FieldName = $"Customer Address {addressProperty?.Name}",
                                            OldValue = addressProperty?.GetValue(oldAddress)?.ToString() ?? default,
                                            NewValue = addressProperty?.GetValue(newAddress)?.ToString() ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newAddress?.Id ?? default,
                                            RelatedEntityName = typeof(AddressDto).Name,
                                            RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(AddressDto).Name}"
                                        };
                                        items.Add(addressItem);
                                    }
                                }
                            }
                            else
                            {
                                if (addressProperty?.Name == "City" || addressProperty?.Name == "State" || addressProperty?.Name == "Country"
                                       || addressProperty?.Name == "Community" || addressProperty?.Name == "SubCommunity" || addressProperty?.Name == "TowerName"
                                       || addressProperty?.Name == "SubLocality")
                                {
                                    if (addressProperty.GetValue(newAddress) != null)
                                    {

                                        var addressItem = new ProspectHistory()
                                        {
                                            FieldName = $"Customer Address {addressProperty?.Name}",
                                            OldValue = default,
                                            NewValue = addressProperty?.GetValue(newAddress)?.ToString() ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newAddress?.Id ?? default,
                                            RelatedEntityName = typeof(AddressDto).Name,
                                            RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(AddressDto).Name}"
                                        };
                                        items.Add(addressItem);
                                    }
                                }
                            }
                        }
                    }
                }
                else if (typeName == typeof(UserDto).Name)
                {

                    if (property.Name == "AssignedUser")
                    {
                        if (oldProspect?.AssignedUser != null && newProspect?.AssignedUser != null)
                        {
                            var newUser = newProspect.AssignedUser;
                            var oldUser = oldProspect.AssignedUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var assignedUser = new ProspectHistory()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newUser?.Id ?? default,
                                    RelatedEntityName = typeof(UserDto).Name,
                                    RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                                };
                                items.Add(assignedUser);
                            }
                        }
                        else if (newProspect?.AssignedUser != null)
                        {
                            var newUser = newProspect.AssignedUser;
                            var assignedUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = newUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(assignedUser);
                        }
                        else if (oldProspect?.AssignedUser != null)
                        {
                            var oldUser = oldProspect.AssignedUser;
                            var assignedUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = oldUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(assignedUser);
                        }
                    }
                    if (property.Name == "AssignedFromUser")
                    {
                        if (oldProspect?.AssignedFromUser != null && newProspect?.AssignedFromUser != null)
                        {
                            var newUser = newProspect.AssignedFromUser;
                            var oldUser = oldProspect.AssignedFromUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var assignedFromUser = new ProspectHistory()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newUser?.Id ?? default,
                                    RelatedEntityName = typeof(UserDto).Name,
                                    RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                                };
                                items.Add(assignedFromUser);
                            }
                        }
                        else if (newProspect?.AssignedFromUser != null)
                        {
                            var newUser = newProspect.AssignedFromUser;
                            var assignedFromUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = newUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(assignedFromUser);
                        }
                        else if (oldProspect?.AssignedFromUser != null)
                        {
                            var oldUser = oldProspect.AssignedFromUser;
                            var assignedFromUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = oldUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(assignedFromUser);
                        }
                    }
                    else if (property.Name == "LastModifiedUser")
                    {
                        if (oldProspect?.LastModifiedUser != null && newProspect?.LastModifiedUser != null)
                        {
                            var newUser = newProspect.LastModifiedUser;
                            var oldUser = oldProspect.LastModifiedUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var lastModifiedUser = new ProspectHistory()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newUser?.Id ?? default,
                                    RelatedEntityName = typeof(UserDto).Name,
                                    RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                                };
                                items.Add(lastModifiedUser);
                            }
                        }
                        else if (newProspect?.LastModifiedUser != null)
                        {
                            var newUser = newProspect.LastModifiedUser;
                            var lastModifiedUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = newUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(lastModifiedUser);
                        }
                        else if (oldProspect?.LastModifiedUser != null)
                        {
                            var oldUser = oldProspect.LastModifiedUser;
                            var lastModifiedUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = oldUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(lastModifiedUser);
                        }
                    }
                    else if (property.Name == "CreatedByUser")
                    {
                        if (oldProspect?.CreatedByUser != null && newProspect?.CreatedByUser != null)
                        {
                            var newUser = newProspect.CreatedByUser;
                            var oldUser = oldProspect.CreatedByUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var createdByUser = new ProspectHistory()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newUser?.Id ?? default,
                                    RelatedEntityName = typeof(UserDto).Name,
                                    RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                                };
                                items.Add(createdByUser);
                            }
                        }
                        else if (newProspect?.CreatedByUser != null)
                        {
                            var newUser = newProspect.CreatedByUser;
                            var createdByUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = newUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(createdByUser);
                        }
                        else if (oldProspect?.CreatedByUser != null)
                        {
                            var oldUser = oldProspect.CreatedByUser;
                            var createdByUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = oldUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(createdByUser);
                        }
                    }
                    else if (property.Name == "SourcingManagerUser")
                    {
                        if (oldProspect?.SourcingManagerUser != null && newProspect?.SourcingManagerUser != null)
                        {
                            var newUser = newProspect.SourcingManagerUser;
                            var oldUser = oldProspect.SourcingManagerUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var sourcingManagerUser = new ProspectHistory()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newUser?.Id ?? default,
                                    RelatedEntityName = typeof(UserDto).Name,
                                    RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                                };
                                items.Add(sourcingManagerUser);
                            }
                        }
                        else if (newProspect?.SourcingManagerUser != null)
                        {
                            var newUser = newProspect.SourcingManagerUser;
                            var sourcingManagerUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = newUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(sourcingManagerUser);
                        }
                        else if (oldProspect?.SourcingManagerUser != null)
                        {
                            var oldUser = oldProspect.SourcingManagerUser;
                            var sourcingManagerUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = oldUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(sourcingManagerUser);
                        }
                    }
                    else if (property.Name == "ClosingManagerUser")
                    {
                        if (oldProspect?.ClosingManagerUser != null && newProspect?.ClosingManagerUser != null)
                        {
                            var newUser = newProspect.ClosingManagerUser;
                            var oldUser = oldProspect.ClosingManagerUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var closingManagerUser = new ProspectHistory()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newUser?.Id ?? default,
                                    RelatedEntityName = typeof(UserDto).Name,
                                    RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                                };
                                items.Add(closingManagerUser);
                            }
                        }
                        else if (newProspect?.ClosingManagerUser != null)
                        {
                            var newUser = newProspect.ClosingManagerUser;
                            var closingManagerUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = newUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(closingManagerUser);
                        }
                        else if (oldProspect?.ClosingManagerUser != null)
                        {
                            var oldUser = oldProspect.ClosingManagerUser;
                            var closingManagerUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = oldUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(closingManagerUser);
                        }
                    }
                    else if (property.Name == "ConvertedByUser")
                    {
                        if (oldProspect?.ConvertedByUser != null && newProspect?.ConvertedByUser != null)
                        {
                            var newUser = newProspect.ConvertedByUser;
                            var oldUser = oldProspect.ConvertedByUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var convertedByUser = new ProspectHistory()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = newUser?.Id ?? default,
                                    RelatedEntityName = typeof(UserDto).Name,
                                    RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                                };
                                items.Add(convertedByUser);
                            }
                        }
                        else if (newProspect?.ConvertedByUser != null)
                        {
                            var newUser = newProspect.ConvertedByUser;
                            var convertedByUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = newUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(convertedByUser);
                        }
                        else if (oldProspect?.ConvertedByUser != null)
                        {
                            var oldUser = oldProspect.ConvertedByUser;
                            var convertedByUser = new ProspectHistory()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = oldUser?.Id ?? default,
                                RelatedEntityName = typeof(UserDto).Name,
                                RelatedEntityChain = $"{typeof(ViewProspectDto).Name}.{typeof(UserDto).Name}"
                            };
                            items.Add(convertedByUser);
                        }
                    }
                }
                else if (typeName == typeof(ViewProspectEnquiryDto).Name)
                {
                    var oldPrimaryEnquiry = oldProspect?.Enquiry;
                    var newPrimaryEnquiry = newProspect?.Enquiry;
                    var enquiryProperties = typeof(ViewProspectEnquiryDto).GetProperties();
                    foreach (var enquiryProperty in enquiryProperties)
                    {

                        if (ShouldIncludePropperty(enquiryProperty))
                        {

                            if ((enquiryProperty.GetValue(newPrimaryEnquiry) != null && enquiryProperty.GetValue(newPrimaryEnquiry) != default) || (oldPrimaryEnquiry != null && enquiryProperty.GetValue(oldPrimaryEnquiry) != null && enquiryProperty.GetValue(oldPrimaryEnquiry) != default))
                            {

                                if (enquiryProperty.Name == "PropertyTypes")
                                {

                                    var oldParentPropertyType = propertyTypes?.Where(pt => oldPrimaryEnquiry?.PropertyTypes?.Any(ope => ope.BaseId == pt.Id || ope.Id == pt.Id) ?? false).Select(pt => pt.DisplayName.ToString()).FirstOrDefault();
                                    var newParentPropertyType = propertyTypes?.Where(pt => newPrimaryEnquiry?.PropertyTypes?.Any(ope => ope.BaseId == pt.Id || ope.Id == pt.Id) ?? false).Select(pt => pt.DisplayName.ToString()).FirstOrDefault();
                                    var childOldType = oldPrimaryEnquiry?.PropertyTypes?.Select(i => i.ChildType?.DisplayName).ToList();
                                    var childNewType = newPrimaryEnquiry?.PropertyTypes?.Select(i => i.ChildType?.DisplayName).ToList();
                                    string childOldTypes = null;
                                    string childNewTypes = null;
                                    if (childOldType != null)
                                    {
                                        childOldTypes = string.Join(",", childOldType);
                                    }
                                    if (childNewType != null)
                                    {
                                        childNewTypes = string.Join(",", childNewType);
                                    }
                                    if (oldParentPropertyType != null && newParentPropertyType != null)
                                    {
                                        if (oldParentPropertyType != newParentPropertyType)
                                        {
                                            var parentPropertyTypeItems = new ProspectHistory()
                                            {
                                                FieldName = enquiryProperty.Name,
                                                OldValue = oldParentPropertyType ?? default,
                                                NewValue = newParentPropertyType ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                            };
                                            items.Add(parentPropertyTypeItems);

                                            var childPropertyTypeItems = new ProspectHistory()
                                            {
                                                FieldName = "SubPropertyType",
                                                OldValue = childOldTypes ?? default,
                                                NewValue = childNewTypes ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                            };
                                            items.Add(childPropertyTypeItems);
                                        }
                                    }
                                    else if (oldParentPropertyType != null)
                                    {
                                        var parentPropertyTypeItems = new ProspectHistory()
                                        {
                                            FieldName = enquiryProperty.Name,
                                            OldValue = oldParentPropertyType ?? default,
                                            NewValue = newParentPropertyType ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                        };
                                        items.Add(parentPropertyTypeItems);
                                        var childPropertyTypeItems = new ProspectHistory()
                                        {
                                            FieldName = "SubPropertyType",
                                            OldValue = childOldTypes ?? default,
                                            NewValue = default,
                                            FieldType = property.PropertyType.Name,
                                            RelatedEntityId = oldPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}",
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                        };
                                        items.Add(childPropertyTypeItems);
                                    }
                                    else if (newParentPropertyType != null)
                                    {
                                        var parentPropertyTypeItem = new ProspectHistory()
                                        {
                                            FieldName = enquiryProperty.Name,
                                            OldValue = default,
                                            NewValue = newParentPropertyType ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                        };
                                        items.Add(parentPropertyTypeItem);
                                        var childPropertyTypeItem = new ProspectHistory()
                                        {
                                            FieldName = "SubPropertyType",
                                            OldValue = default,
                                            NewValue = childNewTypes ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                        };
                                        items.Add(childPropertyTypeItem);
                                    }
                                    else
                                    {
                                        var childPropertyTypeItem = new ProspectHistory()
                                        {
                                            FieldName = enquiryProperty.Name,
                                            OldValue = oldParentPropertyType ?? default,
                                            NewValue = newParentPropertyType ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                        };
                                    }
                                }

                                else if (enquiryProperty.PropertyType.Name == typeof(MasterProspectSourceDto).Name)
                                {
                                    var oldProspectSource = oldPrimaryEnquiry?.ProspectSource;
                                    var newProspectSource = newPrimaryEnquiry?.ProspectSource;
                                    if (newProspectSource != null && oldProspectSource != null)
                                    {
                                        if (oldProspectSource?.DisplayName != newProspectSource?.DisplayName)
                                        {
                                            var prospectSource = new ProspectHistory()
                                            {
                                                FieldName = enquiryProperty.Name,
                                                OldValue = oldProspectSource?.DisplayName ?? default,
                                                NewValue = newProspectSource?.DisplayName ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(MasterProspectSourceDto).Name}"
                                            };
                                            items.Add(prospectSource);
                                        }
                                    }
                                    else
                                    {
                                        var prospectSource = new ProspectHistory()
                                        {
                                            FieldName = enquiryProperty.Name,
                                            OldValue = default,
                                            NewValue = newProspectSource?.DisplayName ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(MasterProspectSourceDto).Name}"
                                        };
                                        items.Add(prospectSource);
                                    }

                                }
                                else if (enquiryProperty.PropertyType.Name.Contains("List`1"))
                                {
                                    if (enquiryProperty.Name == "EnquiryTypes" || enquiryProperty.Name == "BHKs" || enquiryProperty.Name == "BHKTypes" || enquiryProperty.Name == "Beds"
                                        || enquiryProperty.Name == "Baths" || enquiryProperty.Name == "Floors")
                                    {
                                        (string propertyName, string propertyType, string oldValue, string newValue) historyProperties = GetPropertiesForProspectHistory(oldPrimaryEnquiry, newPrimaryEnquiry, enquiryProperty.Name);
                                        if (historyProperties.oldValue != historyProperties.newValue)
                                        {
                                            var prospectSource = new ProspectHistory()
                                            {
                                                FieldName = historyProperties.propertyName,
                                                OldValue = historyProperties.oldValue,
                                                NewValue = historyProperties.newValue,
                                                FieldType = historyProperties.propertyType,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(MasterProspectSourceDto).Name}"
                                            };
                                            items.Add(prospectSource);
                                        }

                                    }
                                    else if (enquiryProperty.Name == "Addresses")
                                    {
                                        var addressProperties = typeof(AddressDto).GetProperties();
                                        foreach (var addressProperty in addressProperties)
                                        {
                                            if (addressProperty.Name == "City" ||
                                        addressProperty.Name == "State" || addressProperty.Name == "SubLocality" || addressProperty.Name == "Country"
                                                    || addressProperty?.Name == "Community" || addressProperty?.Name == "SubCommunity" || addressProperty?.Name == "TowerName"
                                                    || addressProperty?.Name == "SubLocality")
                                            {
                                                (string propertyName, string propertyType, string oldValue, string newValue) historyProperties = GetPropertiesForProspectHistory(oldPrimaryEnquiry, newPrimaryEnquiry, addressProperty.Name);
                                                if (historyProperties.oldValue != historyProperties.newValue)
                                                {
                                                    var prospectSource = new ProspectHistory()
                                                    {
                                                        FieldName = historyProperties.propertyName,
                                                        OldValue = historyProperties.oldValue,
                                                        NewValue = historyProperties.newValue,
                                                        FieldType = historyProperties.propertyType,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(MasterProspectSourceDto).Name}"
                                                    };
                                                    items.Add(prospectSource);
                                                }
                                            }
                                        }
                                    }
                                }

                                else if (enquiryProperty.PropertyType.Name == typeof(AddressDto).Name)
                                {
                                    var oldEnquiredAddress = oldPrimaryEnquiry?.Address;
                                    var newEnquiredAddress = newPrimaryEnquiry?.Address;
                                    var addressProperties = typeof(AddressDto).GetProperties();



                                    foreach (var addressProperty in addressProperties)
                                    {
                                        if (addressProperty != null)
                                        {
                                            if (oldEnquiredAddress != null && newEnquiredAddress != null)
                                            {
                                                if (addressProperty?.GetValue(newEnquiredAddress)?.ToString() != addressProperty?.GetValue(oldEnquiredAddress)?.ToString())
                                                {
                                                    if (addressProperty?.Name == "City" || addressProperty?.Name == "State" || addressProperty?.Name == "Country"
                                                        || addressProperty?.Name == "Community" || addressProperty?.Name == "SubCommunity" || addressProperty?.Name == "TowerName"
                                                       || addressProperty?.Name == "SubLocality")
                                                    {
                                                        var addressItem = new ProspectHistory()
                                                        {
                                                            FieldName = "Enquiry" + addressProperty.Name,
                                                            OldValue = default,
                                                            NewValue = addressProperty?.GetValue(newEnquiredAddress)?.ToString() ?? default,
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newEnquiredAddress?.Id ?? default,
                                                            RelatedEntityName = typeof(AddressDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(AddressDto).Name}"
                                                        };
                                                        items.Add(addressItem);
                                                    }
                                                }
                                            }

                                            else
                                            {
                                                if (addressProperty?.Name == "City" || addressProperty?.Name == "State" || addressProperty?.Name == "Country"
                                                    || addressProperty?.Name == "Community" || addressProperty?.Name == "SubCommunity" || addressProperty?.Name == "TowerName"
                                                    || addressProperty?.Name == "SubLocality")
                                                {
                                                    if (addressProperty.GetValue(newEnquiredAddress) != null)
                                                    {
                                                        var addressItem = new ProspectHistory()
                                                        {
                                                            FieldName = "Enquiry" + addressProperty.Name,
                                                            OldValue = default,
                                                            NewValue = addressProperty?.GetValue(newEnquiredAddress)?.ToString() ?? default,
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newEnquiredAddress?.Id ?? default,
                                                            RelatedEntityName = typeof(AddressDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(AddressDto).Name}"
                                                        };
                                                        items.Add(addressItem);
                                                    }
                                                }
                                            }

                                        }
                                    }
                                }
                                else
                                {
                                    if (newPrimaryEnquiry != null && oldPrimaryEnquiry != null)
                                    {
                                        if (enquiryProperty.GetValue(newPrimaryEnquiry)?.ToString() != enquiryProperty.GetValue(oldPrimaryEnquiry)?.ToString())
                                        {
                                            if (enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() != "None" && enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() != "00000000-0000-0000-0000-000000000000")
                                            {

                                                if ((enquiryProperty?.Name == "CarpetArea" && newPrimaryEnquiry?.CarpetArea != oldPrimaryEnquiry?.CarpetArea) || (enquiryProperty?.Name == "CarpetAreaUnit" && newPrimaryEnquiry?.CarpetAreaUnit != oldPrimaryEnquiry?.CarpetAreaUnit))
                                                {
                                                    var oldParentCarpetArea = oldPrimaryEnquiry?.CarpetArea;
                                                    var newParentCarpetArea = newPrimaryEnquiry?.CarpetArea;
                                                    var oldParentCarpetAreaUnit = oldPrimaryEnquiry?.CarpetAreaUnit;
                                                    var newParentCarpetAreaUnit = newPrimaryEnquiry?.CarpetAreaUnit;
                                                    var oldParentCarpetAreawithUnit = oldParentCarpetArea + " " + oldParentCarpetAreaUnit;
                                                    var newParentCarpetAreawithUnit = newParentCarpetArea + " " + newParentCarpetAreaUnit;


                                                    if (oldParentCarpetAreawithUnit != newParentCarpetAreawithUnit)
                                                    {
                                                        var carpetarea = new ProspectHistory()
                                                        {
                                                            FieldName = enquiryProperty.Name,
                                                            OldValue = oldParentCarpetAreawithUnit.Trim(),
                                                            NewValue = newParentCarpetAreawithUnit.Trim(),
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + "  " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                        };
                                                        
                                                            items.Add(carpetarea);
                                                        

                                                    }

                                                }

                                                else if ((enquiryProperty?.Name == "BuiltUpArea" && newPrimaryEnquiry?.BuiltUpArea != oldPrimaryEnquiry?.BuiltUpArea) || (enquiryProperty?.Name == "BuiltUpAreaUnit" && newPrimaryEnquiry?.BuiltUpAreaUnit != oldPrimaryEnquiry?.BuiltUpAreaUnit))
                                                {
                                                    var oldParentArea = oldPrimaryEnquiry?.BuiltUpArea;
                                                    var newParentArea = newPrimaryEnquiry?.BuiltUpArea;
                                                    var oldParentUnit = oldPrimaryEnquiry?.BuiltUpAreaUnit;
                                                    var newParentCarpetAreaUnit = newPrimaryEnquiry?.BuiltUpAreaUnit;
                                                    var oldParentCarpetAreawithUnit = oldParentArea + " " + oldParentUnit;
                                                    var newParentCarpetAreawithUnit = newParentArea + " " + newParentCarpetAreaUnit;


                                                    if (oldParentCarpetAreawithUnit != newParentCarpetAreawithUnit)
                                                    {
                                                        var carpetarea = new ProspectHistory()
                                                        {
                                                            FieldName = enquiryProperty.Name,
                                                            OldValue = oldParentCarpetAreawithUnit.Trim(),
                                                            NewValue = newParentCarpetAreawithUnit.Trim(),
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + "  " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                        };
                                                       
                                                            items.Add(carpetarea);
                                                        

                                                    }

                                                }
                                                else if ((enquiryProperty?.Name == "SaleableArea" && newPrimaryEnquiry?.SaleableArea != oldPrimaryEnquiry?.SaleableArea) || (enquiryProperty?.Name == "SaleableAreaUnit" && newPrimaryEnquiry?.SaleableAreaUnit != oldPrimaryEnquiry?.SaleableAreaUnit))
                                                {
                                                    var oldParentArea = oldPrimaryEnquiry?.SaleableArea;
                                                    var newParentArea = newPrimaryEnquiry?.SaleableArea;
                                                    var oldParentAreaUnit = oldPrimaryEnquiry?.SaleableAreaUnit;
                                                    var newParentAreaUnit = newPrimaryEnquiry?.SaleableAreaUnit;
                                                    var oldParentwithUnit = oldParentArea + " " + oldParentAreaUnit;
                                                    var newParentAreawithUnit = newParentArea + " " + newParentAreaUnit;


                                                    if (oldParentwithUnit != newParentAreawithUnit)
                                                    {
                                                        var carpetarea = new ProspectHistory()
                                                        {
                                                            FieldName = enquiryProperty.Name,
                                                            OldValue = oldParentwithUnit.Trim(),
                                                            NewValue = newParentAreawithUnit.Trim(),
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + "  " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                        };
                                                       
                                                            items.Add(carpetarea);
                                                        

                                                    }

                                                }
                                                else if (enquiryProperty.Name == "UpperBudget")
                                                {
                                                    var oldParentUpperBudget = oldPrimaryEnquiry?.UpperBudget;
                                                    var newParentUpperBudget = newPrimaryEnquiry?.UpperBudget;
                                                    if (oldParentUpperBudget != newParentUpperBudget)
                                                    {
                                                        var budgetHistory = new ProspectHistory()
                                                        {
                                                            FieldName = enquiryProperty.Name,
                                                            OldValue = oldParentUpperBudget?.ToString(),
                                                            NewValue = newParentUpperBudget?.ToString(),
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                        };
                                                        items.Add(budgetHistory);

                                                    }
                                                }
                                                else if (enquiryProperty.Name == "LowerBudget")
                                                {
                                                    var oldParentLowerBudget = oldPrimaryEnquiry?.LowerBudget;
                                                    var newParentLowerBudget = newPrimaryEnquiry?.LowerBudget;
                                                    if (oldParentLowerBudget != newParentLowerBudget)
                                                    {
                                                        var budgetHistory = new ProspectHistory()
                                                        {
                                                            FieldName = enquiryProperty.Name,
                                                            OldValue = oldParentLowerBudget?.ToString(),
                                                            NewValue = newParentLowerBudget?.ToString(),
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                        };
                                                        items.Add(budgetHistory);

                                                    }
                                                }
                                                else if ((enquiryProperty?.Name == "NetArea" && newPrimaryEnquiry?.NetArea != oldPrimaryEnquiry?.NetArea) || (enquiryProperty?.Name == "NetAreaUnit" && newPrimaryEnquiry?.NetAreaUnit != oldPrimaryEnquiry?.NetAreaUnit))
                                                {
                                                    var oldParentArea = oldPrimaryEnquiry?.NetArea;
                                                    var newParentArea = newPrimaryEnquiry?.NetArea;
                                                    var oldParentAreaUnit = oldPrimaryEnquiry?.NetAreaUnit;
                                                    var newParentCarpetAreaUnit = newPrimaryEnquiry?.NetAreaUnit;
                                                    var oldParentCarpetAreawithUnit = oldParentArea + " " + oldParentAreaUnit;
                                                    var newParentCarpetAreawithUnit = newParentArea + " " + newParentCarpetAreaUnit;


                                                    if (oldParentCarpetAreawithUnit != newParentCarpetAreawithUnit)
                                                    {
                                                        var carpetarea = new ProspectHistory()
                                                        {
                                                            FieldName = enquiryProperty.Name,
                                                            OldValue = oldParentCarpetAreawithUnit.Trim(),
                                                            NewValue = newParentCarpetAreawithUnit.Trim(),
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + "  " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                        };
                                                        
                                                            items.Add(carpetarea);
                                                        

                                                    }

                                                }
                                                else if ((enquiryProperty?.Name == "PropertyArea" && newPrimaryEnquiry?.PropertyArea != oldPrimaryEnquiry?.PropertyArea) || (enquiryProperty?.Name == "PropertyAreaUnit" && newPrimaryEnquiry?.PropertyAreaUnit != oldPrimaryEnquiry?.PropertyAreaUnit))
                                                {
                                                    var oldParentArea = oldPrimaryEnquiry?.PropertyArea;
                                                    var newParentArea = newPrimaryEnquiry?.PropertyArea;
                                                    var oldParentAreaUnit = oldPrimaryEnquiry?.PropertyAreaUnit;
                                                    var newParentAreaUnit = newPrimaryEnquiry?.PropertyAreaUnit;
                                                    var oldParentwithUnit = oldParentArea + " " + oldParentAreaUnit;
                                                    var newParentAreawithUnit = newParentArea + " " + newParentAreaUnit;


                                                    if (oldParentwithUnit != newParentAreawithUnit)
                                                    {
                                                        var carpetarea = new ProspectHistory()
                                                        {
                                                            FieldName = enquiryProperty.Name,
                                                            OldValue = oldParentwithUnit.Trim(),
                                                            NewValue = newParentAreawithUnit.Trim(),
                                                            FieldType = property.PropertyType.Name,
                                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                                            ModifiedBy = (user?.FirstName ?? string.Empty) + "  " + (user?.LastName ?? string.Empty),
                                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                            GroupKey = groupKey,
                                                            Version = version,
                                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                        };
                                                        
                                                            items.Add(carpetarea);
                                                        

                                                    }

                                                }
                                                else if (enquiryProperty?.Name != "CarpetAreaUnit" && enquiryProperty?.Name != "BuiltUpAreaUnit" && enquiryProperty?.Name != "SaleableAreaUnit" && enquiryProperty?.Name != "NetAreaUnit" && enquiryProperty?.Name != "PropertyAreaUnit" && enquiryProperty?.Name != "PropertyType")
                                                {
                                                    var enquiryItems = new ProspectHistory()
                                                    {
                                                        FieldName = Regex.Replace(enquiryProperty?.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                                        OldValue = enquiryProperty?.GetValue(oldPrimaryEnquiry)?.ToString() ?? null,
                                                        NewValue = enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() ?? default,
                                                        FieldType = enquiryProperty?.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}"
                                                    };

                                                    if (enquiryItems.FieldType.Contains("Nullable"))
                                                    {
                                                        var enquiryType = Nullable.GetUnderlyingType(enquiryProperty.PropertyType)?.Name;
                                                        enquiryItems.FieldType = enquiryType ?? string.Empty;
                                                    }
                                                    items.Add(enquiryItems);

                                                }


                                            }
                                        }
                                    }
                                    else if ((enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() != null && enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() != string.Empty) || (oldPrimaryEnquiry != null && enquiryProperty?.GetValue(oldPrimaryEnquiry)?.ToString() != null && enquiryProperty?.GetValue(oldPrimaryEnquiry)?.ToString() != string.Empty))
                                    {
                                        if (enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() != "None" && enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() != "00000000-0000-0000-0000-000000000000")
                                        {
                                            if (enquiryProperty?.Name == "CarpetArea")
                                            {
                                                var oldParentCarpetArea = oldPrimaryEnquiry?.CarpetArea;
                                                var newParentCarpetArea = newPrimaryEnquiry?.CarpetArea;
                                                var oldParentCarpetAreaUnit = oldPrimaryEnquiry?.CarpetAreaUnit;
                                                var newParentCarpetAreaUnit = newPrimaryEnquiry?.CarpetAreaUnit;
                                                var oldParentCarpetAreawithUnit = oldParentCarpetArea + " " + oldParentCarpetAreaUnit;
                                                var newParentCarpetAreawithUnit = newParentCarpetArea + " " + newParentCarpetAreaUnit;
                                                if (oldParentCarpetAreawithUnit != newParentCarpetAreawithUnit)
                                                {
                                                    var carpetarea = new ProspectHistory()
                                                    {
                                                        FieldName = enquiryProperty.Name,
                                                        OldValue = oldParentCarpetAreawithUnit.Trim(),
                                                        NewValue = newParentCarpetAreawithUnit.Trim(),
                                                        FieldType = property.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                    };
                                                    items.Add(carpetarea);

                                                }
                                            }
                                            else if (enquiryProperty?.Name == "BuiltUpArea")
                                            {
                                                var oldParentBuiltUpArea = oldPrimaryEnquiry?.BuiltUpArea;
                                                var newParentBuiltUpArea = newPrimaryEnquiry?.BuiltUpArea;
                                                var oldParentBuiltUpAreaUnit = oldPrimaryEnquiry?.BuiltUpAreaUnit;
                                                var newParentBuiltUpAreaUnit = newPrimaryEnquiry?.BuiltUpAreaUnit;
                                                var oldParentBuiltUpAreawithUnit = oldParentBuiltUpArea + " " + oldParentBuiltUpAreaUnit;
                                                var newParentBuiltUpAreawithUnit = newParentBuiltUpArea + " " + newParentBuiltUpAreaUnit;

                                                if (oldParentBuiltUpAreawithUnit != newParentBuiltUpAreawithUnit)
                                                {
                                                    var carpetarea = new ProspectHistory()
                                                    {
                                                        FieldName = enquiryProperty.Name,
                                                        OldValue = oldParentBuiltUpAreawithUnit.Trim(),
                                                        NewValue = newParentBuiltUpAreawithUnit.Trim(),
                                                        FieldType = property.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                    };
                                                    items.Add(carpetarea);
                                                }
                                            }
                                            else if (enquiryProperty?.Name == "SaleableArea")
                                            {
                                                var oldParentSaleableArea = oldPrimaryEnquiry?.SaleableArea;
                                                var newParentSaleableArea = newPrimaryEnquiry?.SaleableArea;
                                                var oldParentSaleableAreaUnit = oldPrimaryEnquiry?.SaleableAreaUnit;
                                                var newParentSaleableAreaUnit = newPrimaryEnquiry?.SaleableAreaUnit;
                                                var oldParentSaleableAreawithUnit = oldParentSaleableArea + " " + oldParentSaleableAreaUnit;
                                                var newParentSaleableAreawithUnit = newParentSaleableArea + " " + newParentSaleableAreaUnit;

                                                if (oldParentSaleableAreawithUnit != newParentSaleableAreawithUnit)
                                                {
                                                    var carpetarea = new ProspectHistory()
                                                    {
                                                        FieldName = enquiryProperty.Name,
                                                        OldValue = oldParentSaleableAreawithUnit.Trim(),
                                                        NewValue = newParentSaleableAreawithUnit.Trim(),
                                                        FieldType = property.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                    };
                                                    items.Add(carpetarea);

                                                }
                                            }
                                            else if (enquiryProperty?.Name == "NetArea")
                                            {
                                                var oldParentArea = oldPrimaryEnquiry?.NetArea;
                                                var newParentArea = newPrimaryEnquiry?.NetArea;
                                                var oldParentAreaUnit = oldPrimaryEnquiry?.NetAreaUnit;
                                                var newParentCarpetAreaUnit = newPrimaryEnquiry?.NetAreaUnit;
                                                var oldParentCarpetAreawithUnit = oldParentArea + " " + oldParentAreaUnit;
                                                var newParentCarpetAreawithUnit = newParentArea + " " + newParentCarpetAreaUnit;


                                                if (oldParentCarpetAreawithUnit != newParentCarpetAreawithUnit)
                                                {
                                                    var carpetarea = new ProspectHistory()
                                                    {
                                                        FieldName = enquiryProperty.Name,
                                                        OldValue = oldParentCarpetAreawithUnit.Trim(),
                                                        NewValue = newParentCarpetAreawithUnit.Trim(),
                                                        FieldType = property.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + "  " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                    };
                                                    items.Add(carpetarea);

                                                }

                                            }
                                            else if (enquiryProperty?.Name == "PropertyArea")
                                            {
                                                var oldParentArea = oldPrimaryEnquiry?.PropertyArea;
                                                var newParentArea = newPrimaryEnquiry?.PropertyArea;
                                                var oldParentAreaUnit = oldPrimaryEnquiry?.PropertyAreaUnit;
                                                var newParentAreaUnit = newPrimaryEnquiry?.PropertyAreaUnit;
                                                var oldParentwithUnit = oldParentArea + " " + oldParentAreaUnit;
                                                var newParentAreawithUnit = newParentArea + " " + newParentAreaUnit;


                                                if (oldParentwithUnit != newParentAreawithUnit)
                                                {
                                                    var carpetarea = new ProspectHistory()
                                                    {
                                                        FieldName = enquiryProperty.Name,
                                                        OldValue = oldParentwithUnit.Trim(),
                                                        NewValue = newParentAreawithUnit.Trim(),
                                                        FieldType = property.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + "  " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                    };
                                                    items.Add(carpetarea);

                                                }

                                            }

                                            else if (enquiryProperty?.Name == "UpperBudget")
                                            {
                                                var oldParentUpperBudget = oldPrimaryEnquiry?.UpperBudget;
                                                var newParentUpperBudget = newPrimaryEnquiry?.UpperBudget;
                                                if (oldParentUpperBudget != newParentUpperBudget)
                                                {
                                                    var budgetHistory = new ProspectHistory()
                                                    {
                                                        FieldName = enquiryProperty.Name,
                                                        OldValue = oldParentUpperBudget?.ToString(),
                                                        NewValue = newParentUpperBudget?.ToString(),
                                                        FieldType = property.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                    };
                                                    items.Add(budgetHistory);

                                                }
                                            }
                                            else if (enquiryProperty?.Name == "LowerBudget")
                                            {
                                                var oldParentLowerBudget = oldPrimaryEnquiry?.LowerBudget;
                                                var newParentLowerBudget = newPrimaryEnquiry?.LowerBudget;
                                                if (oldParentLowerBudget != newParentLowerBudget)
                                                {
                                                    var budgetHistory = new ProspectHistory()
                                                    {
                                                        FieldName = enquiryProperty.Name,
                                                        OldValue = oldParentLowerBudget?.ToString(),
                                                        NewValue = newParentLowerBudget?.ToString(),
                                                        FieldType = property.PropertyType.Name,
                                                        ProspectId = newProspect?.Id ?? Guid.Empty,
                                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                                        ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                        GroupKey = groupKey,
                                                        Version = version,
                                                        RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                        RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                        RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}.{typeof(PropertyTypeDto).Name}"
                                                    };
                                                    items.Add(budgetHistory);

                                                }
                                            }
                                            else if (enquiryProperty?.Name != "CarpetAreaUnit" && enquiryProperty?.Name != "BuiltUpAreaUnit" && enquiryProperty?.Name != "SaleableAreaUnit" && enquiryProperty?.Name != "NetAreaUnit" && enquiryProperty?.Name != "PropertyAreaUnit" && enquiryProperty?.Name != "PropertyType")
                                            {
                                                var enquiryItems = new ProspectHistory()
                                                {
                                                    FieldName = Regex.Replace(enquiryProperty?.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                                    OldValue = default,
                                                    NewValue = enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() ?? default,
                                                    FieldType = enquiryProperty?.PropertyType.Name,
                                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                    GroupKey = groupKey,
                                                    Version = version,
                                                    RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                    RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                                    RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}"
                                                };

                                                if (enquiryItems.FieldType.Contains("Nullable"))
                                                {
                                                    var enquiryType = Nullable.GetUnderlyingType(enquiryProperty.PropertyType)?.Name;
                                                    enquiryItems.FieldType = enquiryType ?? string.Empty;
                                                }
                                                items.Add(enquiryItems);


                                            }
                                        }
                                    }

                                }
                            }
                            else
                            {
                                try
                                {
                                    if (enquiryProperty?.Name == "SubSource" && enquiryProperty.GetValue(newPrimaryEnquiry)?.ToString() != enquiryProperty.GetValue(oldPrimaryEnquiry)?.ToString())
                                    {
                                        var enquiryItems = new ProspectHistory()
                                        {
                                            FieldName = Regex.Replace(enquiryProperty?.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                            NewValue = enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() ?? default,
                                            OldValue = enquiryProperty?.GetValue(oldPrimaryEnquiry)?.ToString() ?? default,
                                            FieldType = enquiryProperty?.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(ViewProspectEnquiryDto).Name,
                                            RelatedEntityChain = $"{typeof(ProspectDto).Name}.{typeof(ViewProspectEnquiryDto).Name}"
                                        };

                                        if (enquiryItems.FieldType.Contains("Nullable"))
                                        {
                                            var enquiryType = Nullable.GetUnderlyingType(enquiryProperty.PropertyType)?.Name;
                                            enquiryItems.FieldType = enquiryType ?? string.Empty;
                                        }
                                        items.Add(enquiryItems);
                                    }

                                }
                                catch (Exception ex) { }
                            }
                        }
                    }
                }
                else if (typeName == typeof(PropertyDto).Name || property.Name == "Properties")
                {

                    var oldPropNames = string.Join(",", oldProspect?.Properties?.Select(i => i.Title)?.ToList() ?? new());
                    var newPropNames = string.Join(",", newProspect.Properties?.Select(i => i.Title)?.ToList() ?? new());
                    if (newPropNames != null && oldPropNames != null)
                    {
                        if (newPropNames != oldPropNames)
                        {
                            var propItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = oldPropNames ?? default,
                                NewValue = newPropNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(PropertyDto).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(PropertyDto).Name}"
                            };
                            items.Add(propItem);
                        }
                    }
                    else
                    {
                        var propItem = new ProspectHistory()
                        {
                            FieldName = property.Name,
                            OldValue = oldPropNames ?? default,
                            NewValue = newPropNames ?? default,
                            FieldType = property.PropertyType.Name,
                            ProspectId = newProspect?.Id ?? Guid.Empty,
                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                            LastModifiedById = user?.Id ?? Guid.Empty,
                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                            GroupKey = groupKey,
                            Version = version,
                            RelatedEntityId = default,
                            RelatedEntityName = typeof(PropertyDto).Name,
                            RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(PropertyDto).Name}"
                        };
                        items.Add(propItem);
                    }
                }
                else if (typeName == typeof(ChannelPartnerDto).Name)
                {

                    var oldChannelPartnerNames = string.Join(",", oldProspect?.ChannelPartners?.Select(i => i.FirmName)?.ToList() ?? new());
                    var newChannelPartnerNames = string.Join(",", newProspect.ChannelPartners?.Select(i => i.FirmName)?.ToList() ?? new());
                    if (oldChannelPartnerNames != null && newChannelPartnerNames != null)
                    {
                        if (oldChannelPartnerNames != newChannelPartnerNames)
                        {
                            var channelPartnerItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = oldChannelPartnerNames ?? default,
                                NewValue = newChannelPartnerNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.ChannelPartner).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.ChannelPartner).Name}"
                            };
                            items.Add(channelPartnerItem);
                        }
                    }
                    else
                    {
                        var channelPartnerItem = new ProspectHistory()
                        {
                            FieldName = property.Name,
                            OldValue = default,
                            NewValue = newChannelPartnerNames ?? default,
                            FieldType = property.PropertyType.Name,
                            ProspectId = newProspect?.Id ?? Guid.Empty,
                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                            LastModifiedById = user?.Id ?? Guid.Empty,
                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                            GroupKey = groupKey,
                            Version = version,
                            RelatedEntityId = default,
                            RelatedEntityName = typeof(Lrb.Domain.Entities.ChannelPartner).Name,
                            RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.ChannelPartner).Name}"
                        };
                        items.Add(channelPartnerItem);
                    }
                }
                else if (typeName == typeof(ProjectDto).Name || property.Name == "Projects")
                {

                    var oldProjNames = string.Join(",", oldProspect?.Projects?.Select(i => i.Name)?.ToList() ?? new());
                    var newProjNames = string.Join(",", newProspect.Projects?.Select(i => i.Name)?.ToList() ?? new());
                    if (oldProjNames != null && newProjNames != null)
                    {
                        if (oldProjNames != newProjNames)
                        {
                            var projItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = oldProjNames ?? default,
                                NewValue = newProjNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.Project).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Project).Name}"
                            };
                            items.Add(projItem);
                        }
                    }
                    else
                    {
                        var projItem = new ProspectHistory()
                        {
                            FieldName = property.Name,
                            OldValue = oldProjNames ?? default,
                            NewValue = newProjNames ?? default,
                            FieldType = property.PropertyType.Name,
                            ProspectId = newProspect?.Id ?? Guid.Empty,
                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                            LastModifiedById = user?.Id ?? Guid.Empty,
                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                            GroupKey = groupKey,
                            Version = version,
                            RelatedEntityId = default,
                            RelatedEntityName = typeof(Lrb.Domain.Entities.Project).Name,
                            RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Project).Name}"
                        };
                        items.Add(projItem);
                    }

                }
                else if (type.IsGenericType)
                {
                    if (type.FullName == typeof(IList<ProspectEnquiry>).FullName || type.FullName == typeof(List<ProspectEnquiry>).FullName)
                    {
                        var oldPrimaryEnquiry = oldProspect?.Enquiry;
                        var newPrimaryEnquiry = newProspect?.Enquiry;
                        var enquiryProperties = typeof(ProspectEnquiry).GetProperties();
                        foreach (var enquiryProperty in enquiryProperties)
                        {

                            if (ShouldIncludePropperty(enquiryProperty))
                            {

                                if (enquiryProperty.GetValue(newPrimaryEnquiry) != null)
                                {
                                    if (enquiryProperty.Name == "PropertyTypes")
                                    {
                                        var oldParentPropertyType = propertyTypes?.Where(pt => oldPrimaryEnquiry?.PropertyTypes?.Any(ope => ope.BaseId == pt.Id || ope.Id == pt.Id) ?? false).Select(pt => pt.DisplayName.ToString()).FirstOrDefault();
                                        var newParentPropertyType = propertyTypes?.Where(pt => newPrimaryEnquiry?.PropertyTypes?.Any(ope => ope.BaseId == pt.Id || ope.Id == pt.Id) ?? false).Select(pt => pt.DisplayName.ToString()).FirstOrDefault();
                                        var childOldType = oldPrimaryEnquiry?.PropertyTypes?.Select(i => i.ChildType?.DisplayName).ToList();
                                        var childNewType = newPrimaryEnquiry?.PropertyTypes?.Select(i => i.ChildType?.DisplayName).ToList();
                                        string childOldTypes = null;
                                        string childNewTypes = null;
                                        if (childOldType != null)
                                        {
                                            childOldTypes = string.Join(",", childOldType);
                                        }
                                        if (childNewType != null)
                                        {
                                            childNewTypes = string.Join(",", childNewType);
                                        }
                                        if (oldParentPropertyType != null && newParentPropertyType != null)
                                        {
                                            var parentPropertyTypeItems = new ProspectHistory()
                                            {
                                                FieldName = enquiryProperty.Name,
                                                OldValue = oldParentPropertyType ?? default,
                                                NewValue = newParentPropertyType ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ProspectEnquiry).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterPropertyType).Name}"
                                            };
                                            items.Add(parentPropertyTypeItems);
                                            var childPropertyTypeItems = new ProspectHistory()
                                            {
                                                FieldName = "SubPropertyType",
                                                OldValue = childOldTypes ?? default,
                                                NewValue = childNewTypes ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ProspectEnquiry).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterPropertyType).Name}"
                                            };
                                            items.Add(childPropertyTypeItems);
                                        }
                                        else if (oldParentPropertyType != null)
                                        {
                                            var parentPropertyTypeItems = new ProspectHistory()
                                            {
                                                FieldName = enquiryProperty.Name,
                                                OldValue = oldParentPropertyType ?? default,
                                                NewValue = newParentPropertyType ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ProspectEnquiry).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterPropertyType).Name}"
                                            };
                                            items.Add(parentPropertyTypeItems);
                                            var childPropertyTypeItems = new ProspectHistory()
                                            {
                                                FieldName = "SubPropertyType",
                                                OldValue = childOldTypes ?? default,
                                                NewValue = default,
                                                FieldType = property.PropertyType.Name,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ProspectEnquiry).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterPropertyType).Name}"
                                            };
                                            items.Add(childPropertyTypeItems);
                                        }
                                        else if (newParentPropertyType != null)
                                        {
                                            var parentPropertyTypeItem = new ProspectHistory()
                                            {
                                                FieldName = enquiryProperty.Name,
                                                OldValue = default,
                                                NewValue = newParentPropertyType ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ProspectEnquiry).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterPropertyType).Name}"
                                            };
                                            items.Add(parentPropertyTypeItem);
                                            var childPropertyTypeItem = new ProspectHistory()
                                            {
                                                FieldName = "SubPropertyType",
                                                OldValue = default,
                                                NewValue = childNewTypes ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ProspectEnquiry).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterPropertyType).Name}"
                                            };
                                            items.Add(childPropertyTypeItem);
                                        }
                                        else
                                        {
                                            var childPropertyTypeItem = new ProspectHistory()
                                            {
                                                FieldName = enquiryProperty.Name,
                                                OldValue = oldParentPropertyType ?? default,
                                                NewValue = newParentPropertyType ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                                RelatedEntityName = typeof(ProspectEnquiry).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterPropertyType).Name}"
                                            };
                                        }
                                    }
                                    else if (enquiryProperty.PropertyType.Name == typeof(MasterProspectSource).Name)
                                    {
                                        var oldProspectSource = oldPrimaryEnquiry?.ProspectSource;
                                        var newProspectSource = newPrimaryEnquiry?.ProspectSource;
                                        var prospectSource = new ProspectHistory()
                                        {
                                            FieldName = enquiryProperty.Name,
                                            OldValue = oldProspectSource?.DisplayName ?? default,
                                            NewValue = newProspectSource?.DisplayName ?? default,
                                            FieldType = property.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(MasterProspectSource).Name,
                                            RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(MasterProspectSource).Name}"
                                        };
                                        items.Add(prospectSource);
                                    }
                                    else if (enquiryProperty.PropertyType.Name == typeof(Address).Name)
                                    {
                                        var oldEnquiredAddress = oldPrimaryEnquiry?.Address?.ToString();
                                        var newEnquiredAddress = newPrimaryEnquiry?.Address?.ToString();

                                        if (oldEnquiredAddress != null && newEnquiredAddress != null)
                                        {
                                            var enquiredAddressItem = new ProspectHistory()
                                            {
                                                FieldName = enquiryProperty.Name,
                                                OldValue = oldEnquiredAddress ?? default,
                                                NewValue = newEnquiredAddress ?? default,
                                                FieldType = property.PropertyType.Name,
                                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                                LastModifiedById = user?.Id ?? Guid.Empty,
                                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                                GroupKey = groupKey,
                                                Version = version,
                                                RelatedEntityId = newPrimaryEnquiry?.Address?.Id ?? default,
                                                RelatedEntityName = typeof(Address).Name,
                                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}.{typeof(Address).Name}"
                                            };
                                            items.Add(enquiredAddressItem);
                                        }

                                    }
                                    else
                                    {
                                        var enquiryItems = new ProspectHistory()
                                        {
                                            FieldName = enquiryProperty.Name,
                                            NewValue = enquiryProperty.GetValue(newPrimaryEnquiry)?.ToString() ?? default,
                                            FieldType = enquiryProperty.PropertyType.Name,
                                            ProspectId = newProspect?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version,
                                            RelatedEntityId = newPrimaryEnquiry?.Id ?? default,
                                            RelatedEntityName = typeof(ProspectEnquiry).Name,
                                            RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectEnquiry).Name}"
                                        };
                                        if (oldPrimaryEnquiry != null)
                                        {
                                            enquiryItems.OldValue = enquiryProperty.GetValue(oldPrimaryEnquiry)?.ToString() ?? null;
                                        }
                                        items.Add(enquiryItems);
                                    }
                                }
                            }
                        }
                    }
                    else if (type.FullName == typeof(IList<ProjectDto>).FullName || type.FullName == typeof(List<ProjectDto>).FullName)
                    {

                        var oldProjNames = string.Join(",", oldProspect?.Projects?.Select(i => i.Name)?.ToList() ?? new());
                        var newProjNames = string.Join(",", newProspect.Projects?.Select(i => i.Name)?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldProjNames) && !string.IsNullOrEmpty(newProjNames))
                        {
                            if (oldProjNames != newProjNames)
                            {
                                var projItem = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldProjNames ?? default,
                                    NewValue = newProjNames ?? default,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = default,
                                    RelatedEntityName = typeof(Lrb.Domain.Entities.Project).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Project).Name}"
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newProjNames))
                        {
                            var projItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newProjNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.Project).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Project).Name}"
                            };
                            items.Add(projItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<AgencyDto>).FullName || type.FullName == typeof(List<AgencyDto>).FullName)
                    {

                        var oldAgencyNames = string.Join(",", oldProspect?.Agencies?.Select(i => i.Name)?.ToList() ?? new());
                        var newAgencyNames = string.Join(",", newProspect.Agencies?.Select(i => i.Name)?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldAgencyNames) && !string.IsNullOrEmpty(newAgencyNames))
                        {
                            if (oldAgencyNames != newAgencyNames)
                            {
                                var projItem = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldAgencyNames ?? default,
                                    NewValue = newAgencyNames ?? default,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user.FirstName ?? string.Empty) + " " + (user.LastName ?? string.Empty),
                                    LastModifiedById = user.Id,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = default,
                                    RelatedEntityName = typeof(Lrb.Domain.Entities.Agency).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Agency).Name}"
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newAgencyNames))
                        {
                            var projItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newAgencyNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user.FirstName ?? string.Empty) + " " + (user.LastName ?? string.Empty),
                                LastModifiedById = user.Id,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.Agency).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Agency).Name}"
                            };
                            items.Add(projItem);
                        }
                        else if (!string.IsNullOrEmpty(oldAgencyNames))
                        {
                            var projItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = oldAgencyNames,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user.FirstName ?? string.Empty) + " " + (user.LastName ?? string.Empty),
                                LastModifiedById = user.Id,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.Agency).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Agency).Name}"
                            };
                            items.Add(projItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<PropertyDto>).FullName || type.FullName == typeof(List<PropertyDto>).FullName)
                    {

                        var oldPropNames = string.Join(",", oldProspect?.Properties?.Select(i => i.Title)?.ToList() ?? new());
                        var newPropNames = string.Join(",", newProspect.Properties?.Select(i => i.Title)?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(newPropNames) && !string.IsNullOrEmpty(oldPropNames))
                        {
                            if (newPropNames != oldPropNames)
                            {
                                var propItem = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldPropNames ?? default,
                                    NewValue = newPropNames ?? default,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = default,
                                    RelatedEntityName = typeof(PropertyDto).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(PropertyDto).Name}"
                                };
                                items.Add(propItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newPropNames))
                        {
                            var propItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newPropNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(PropertyDto).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(PropertyDto).Name}"
                            };
                            items.Add(propItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<ChannelPartnerDto>).FullName || type.FullName == typeof(List<ChannelPartnerDto>).FullName)
                    {

                        var oldChannelPartnerNames = string.Join(",", oldProspect?.ChannelPartners?.Select(i => i.FirmName)?.ToList() ?? new());
                        var newChannelPartnerNames = string.Join(",", newProspect.ChannelPartners?.Select(i => i.FirmName)?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldChannelPartnerNames) && !string.IsNullOrEmpty(newChannelPartnerNames))
                        {
                            if (oldChannelPartnerNames != newChannelPartnerNames)
                            {
                                var channelPartnerItem = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldChannelPartnerNames ?? default,
                                    NewValue = newChannelPartnerNames ?? default,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = default,
                                    RelatedEntityName = typeof(Lrb.Domain.Entities.ChannelPartner).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.ChannelPartner).Name}"
                                };
                                items.Add(channelPartnerItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newChannelPartnerNames))
                        {
                            var channelPartnerItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newChannelPartnerNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.ChannelPartner).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.ChannelPartner).Name}"
                            };
                            items.Add(channelPartnerItem);
                        }
                        else if (!string.IsNullOrEmpty(oldChannelPartnerNames))
                        {
                            var channelPartnerItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = oldChannelPartnerNames ?? default,
                                NewValue = newChannelPartnerNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.ChannelPartner).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.ChannelPartner).Name}"
                            };
                            items.Add(channelPartnerItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<CampaignDto>).FullName || type.FullName == typeof(List<CampaignDto>).FullName)
                    {

                        var oldCampaignNames = string.Join(",", oldProspect?.Campaigns?.Select(i => i.Name)?.ToList() ?? new());
                        var newCampaignNames = string.Join(",", newProspect.Campaigns?.Select(i => i.Name)?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldCampaignNames) && !string.IsNullOrEmpty(newCampaignNames))
                        {
                            if (oldCampaignNames != newCampaignNames)
                            {
                                var projItem = new ProspectHistory()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldCampaignNames ?? default,
                                    NewValue = newCampaignNames ?? default,
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user.FirstName ?? string.Empty) + " " + (user.LastName ?? string.Empty),
                                    LastModifiedById = user.Id,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = default,
                                    RelatedEntityName = typeof(Lrb.Domain.Entities.Campaign).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Campaign).Name}"
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newCampaignNames))
                        {
                            var projItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newCampaignNames ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user.FirstName ?? string.Empty) + " " + (user.LastName ?? string.Empty),
                                LastModifiedById = user.Id,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.Campaign).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Campaign).Name}"
                            };
                            items.Add(projItem);
                        }
                        else if (!string.IsNullOrEmpty(oldCampaignNames))
                        {
                            var projItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = oldCampaignNames,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user.FirstName ?? string.Empty) + " " + (user.LastName ?? string.Empty),
                                LastModifiedById = user.Id,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(Lrb.Domain.Entities.Campaign).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(Lrb.Domain.Entities.Campaign).Name}"
                            };
                            items.Add(projItem);
                        }
                    }
                    else if (type.FullName == typeof(Dictionary<ContactType, int>).FullName || type.FullName == typeof(Dictionary<ContactType, int>).FullName)
                    {

                        var oldContactType = oldProspect?.ContactRecords?.LastOrDefault().Key.ToString();
                        var newContactType = newProspect.ContactRecords?.LastOrDefault().Key.ToString();

                        if (newContactType != null && oldContactType != null && (oldProspect?.ContactRecords?.Any() ?? false))
                        {
                            var changedValues = newProspect.ContactRecords
                            .Where(newEntry => oldProspect.ContactRecords.ContainsKey(newEntry.Key)
                              && oldProspect.ContactRecords[newEntry.Key] != newEntry.Value)
                            .Select(newEntry => new ProspectHistory
                            {
                                FieldName = property.Name,
                                OldValue = null,
                                NewValue = newEntry.Key.ToString(),
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(ProspectCommunication).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectCommunication).Name}"
                            });
                            items.AddRange(changedValues.ToList());
                            if (!changedValues?.Any() ?? false)
                            {
                                var newEntries = newProspect.ContactRecords
                                .Where(newEntry => oldProspect?.ContactRecords == null ||
                                                   !oldProspect.ContactRecords.ContainsKey(newEntry.Key))
                                .Select(newEntry => new ProspectHistory
                                {
                                    FieldName = property.Name,
                                    OldValue = null,
                                    NewValue = newEntry.Key.ToString(),
                                    FieldType = property.PropertyType.Name,
                                    ProspectId = newProspect?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version,
                                    RelatedEntityId = default,
                                    RelatedEntityName = typeof(ProspectCommunication).Name,
                                    RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectCommunication).Name}"
                                });

                                items.AddRange(newEntries.ToList());
                            }
                        }
                        else
                        {
                            var contactTypeItem = new ProspectHistory()
                            {
                                FieldName = property.Name,
                                OldValue = null,
                                NewValue = newContactType ?? default,
                                FieldType = property.PropertyType.Name,
                                ProspectId = newProspect?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newProspect?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version,
                                RelatedEntityId = default,
                                RelatedEntityName = typeof(ProspectCommunication).Name,
                                RelatedEntityChain = $"{typeof(Prospect).Name}.{typeof(ProspectCommunication).Name}"
                            };
                            items.Add(contactTypeItem);
                        }
                    }
                }
            }
            return items;
        }

        private static bool ShouldIncludePropperty(PropertyInfo property)
        {
            List<string> propertiesToKeep = new()
            {
                "ProspectId",
                "Prospect",
                "DomainEvents",
                "AddressId",
                "Id"
            };
            return !propertiesToKeep.Contains(property.Name);
        }

        private static void AssignAuditStamps(List<ProspectHistory> items, string user, int lastVersion)
        {
            var timeStamp = DateTime.UtcNow;
            var newVersion = lastVersion + 1;
            items.ForEach(item => { item.Version = newVersion; item.ModifiedOn = timeStamp; item.ModifiedBy = user; });
        }
        public static void V2SetUserViewForProspect(this ViewProspectDto prospectDto, List<UserDetailsDto> users, Guid? currentUserId = null)
        {
            var assignedUser = users.FirstOrDefault(i => i.Id == prospectDto.AssignTo);
            if (assignedUser != null)
            {
                prospectDto.AssignedUser = new()
                {
                    Id = assignedUser?.Id ?? Guid.Empty,
                    ContactNo = assignedUser?.PhoneNumber ?? "",
                    Name = (assignedUser?.FirstName + " " ?? "") + (assignedUser?.LastName ?? "")
                };
            }
            var assignedFromUser = users.FirstOrDefault(i => i.Id == prospectDto.AssignedFrom);
            if (assignedFromUser != null)
            {
                prospectDto.AssignedFromUser = new()
                {
                    Id = assignedFromUser?.Id ?? Guid.Empty,
                    ContactNo = assignedFromUser?.PhoneNumber ?? "",
                    Name = (assignedFromUser?.FirstName + " " ?? "") + (assignedFromUser?.LastName ?? "")
                };

            }
            var lastModifiedUser = users.FirstOrDefault(i => i.Id == (currentUserId ?? prospectDto.LastModifiedBy));
            if (lastModifiedUser != null)
            {
                prospectDto.LastModifiedUser = new()
                {
                    Id = lastModifiedUser?.Id ?? Guid.Empty,
                    ContactNo = lastModifiedUser?.PhoneNumber ?? "",
                    Name = (lastModifiedUser?.FirstName + " " ?? "") + (lastModifiedUser?.LastName ?? "")
                };
            }
            var sourcingManagerUser = users.FirstOrDefault(i => i.Id == prospectDto.SourcingManager);
            if (sourcingManagerUser != null)
            {
                prospectDto.SourcingManagerUser = new()
                {
                    Id = sourcingManagerUser?.Id ?? Guid.Empty,
                    ContactNo = sourcingManagerUser?.PhoneNumber ?? "",
                    Name = (sourcingManagerUser?.FirstName + " " ?? "") + (sourcingManagerUser?.LastName ?? "")
                };
            }

            var closingManagerUser = users.FirstOrDefault(i => i.Id == prospectDto.ClosingManager);
            if (closingManagerUser != null)
            {
                prospectDto.ClosingManagerUser = new()
                {
                    Id = closingManagerUser?.Id ?? Guid.Empty,
                    ContactNo = closingManagerUser?.PhoneNumber ?? "",
                    Name = (closingManagerUser?.FirstName + " " ?? "") + (closingManagerUser?.LastName ?? "")
                };
            }
        }
        public static async Task<ViewProspectDto> SetUserViewForProspect(ViewProspectDto prospect, IUserService userService, CancellationToken cancellationToken, Guid? currentUserId = null, List<UserDetailsDto>? userDetails = null)
        {
            if (prospect == null)
            {
                return null;
            }
            UserDetailsDto assignedUser = null;
            try
            {
                assignedUser = userDetails?.FirstOrDefault(i => i.Id == prospect.AssignTo) ?? await userService.GetAsync(prospect.AssignTo.ToString(), cancellationToken);
            }
            catch (NotFoundException) { }
            if (assignedUser != null)
            {
                prospect.AssignedUser = assignedUser?.Adapt<UserDto>();
                prospect.AssignedUser.Name = assignedUser?.FirstName + " " + assignedUser?.LastName;
            }
            UserDetailsDto? lastModifiedByUser = null;
            try
            {
                lastModifiedByUser = userDetails?.FirstOrDefault(i => i.Id == currentUserId) ?? await userService.GetAsync(currentUserId.ToString() ?? prospect?.LastModifiedBy.ToString() ?? string.Empty, cancellationToken);
            }
            catch (NotFoundException e) { }
            if (lastModifiedByUser != null)
            {
                prospect.LastModifiedUser = lastModifiedByUser?.Adapt<UserDto>();
                prospect.LastModifiedUser.Name = lastModifiedByUser?.FirstName + " " + lastModifiedByUser?.LastName;
            }
            else if (currentUserId != null)
            {
                lastModifiedByUser = userDetails?.FirstOrDefault(i => i.Id == currentUserId) ?? await userService.GetAsync(currentUserId.ToString() ?? string.Empty, cancellationToken);
                prospect.LastModifiedUser = lastModifiedByUser?.Adapt<UserDto>();
                prospect.LastModifiedUser.Name = lastModifiedByUser?.FirstName + " " + lastModifiedByUser?.LastName;
            }
            UserDetailsDto? assignedFromUser = null;
            try
            {
                assignedFromUser = userDetails?.FirstOrDefault(i => i.Id == prospect?.AssignedFrom) ?? await userService.GetAsync(prospect?.AssignedFrom.ToString() ?? string.Empty, cancellationToken);
            }
            catch (NotFoundException e) { }
            if (assignedFromUser != null)
            {
                prospect.AssignedFromUser = assignedFromUser?.Adapt<UserDto>();
                prospect.AssignedFromUser.Name = assignedFromUser?.FirstName + " " + assignedFromUser?.LastName;
            }
            UserDetailsDto? sourcingManager = null;
            try
            {
                sourcingManager = userDetails?.FirstOrDefault(i => i.Id == prospect?.SourcingManager) ?? await userService.GetAsync(prospect?.SourcingManager?.ToString() ?? string.Empty, cancellationToken);
            }
            catch (NotFoundException e) { }
            if (sourcingManager != null)
            {
                prospect.SourcingManagerUser = sourcingManager?.Adapt<UserDto>();
                prospect.SourcingManagerUser.Name = sourcingManager?.FirstName + " " + sourcingManager?.LastName;
            }
            UserDetailsDto? closingManager = null;
            try
            {
                closingManager = userDetails?.FirstOrDefault(i => i.Id == prospect?.ClosingManager) ?? await userService.GetAsync(prospect?.ClosingManager?.ToString() ?? string.Empty, cancellationToken);
            }
            catch (NotFoundException e) { }
            if (closingManager != null)
            {
                prospect.ClosingManagerUser = closingManager?.Adapt<UserDto>();
                prospect.ClosingManagerUser.Name = closingManager?.FirstName + " " + closingManager?.LastName;
            }
            return prospect;
        }
        public static (string, string, string, string) GetPropertiesForProspectHistory(ProspectEnquiryDto oldProspectEnquiryDto, ProspectEnquiryDto newProspectEnquiryDto, string propertyName)
        {
            string updatedPropertyName = string.Empty;
            string updatedPropertyType = string.Empty;
            string oldProperty = string.Empty;
            string newProperty = string.Empty;
            if (propertyName == "SubLocality")
            {
                updatedPropertyName = "Enquiry SubLocalities";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldProspectEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.SubLocality)).Select(i => i.SubLocality).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newProspectEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.SubLocality)).Select(i => i.SubLocality).Distinct().ToList() ?? new());
            }
            if (propertyName == "City")
            {
                updatedPropertyName = "Enquiry Cities";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldProspectEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.City)).Select(i => i.City).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newProspectEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.City)).Select(i => i.City).Distinct().ToList() ?? new());
            }
            if (propertyName == "State")
            {
                updatedPropertyName = "Enquiry States";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldProspectEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.State)).Select(i => i.State).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newProspectEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.State)).Select(i => i.State).Distinct().ToList() ?? new());
            }
            if (propertyName == "Country")
            {
                updatedPropertyName = "Enquiry Countries";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldProspectEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.Country)).Select(i => i.Country).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newProspectEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.Country)).Select(i => i.Country).Distinct().ToList() ?? new());
            }
            if (propertyName == "BHKTypes")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of BHKTypes";
                oldProperty = string.Join(",", oldProspectEnquiryDto?.BHKTypes?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newProspectEnquiryDto?.BHKTypes?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "Beds")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of Beds";
                oldProperty = string.Join(",", oldProspectEnquiryDto?.Beds?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newProspectEnquiryDto?.Beds?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "Baths")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of Baths";
                oldProperty = string.Join(",", oldProspectEnquiryDto?.Baths?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newProspectEnquiryDto?.Baths?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "Floors")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of Floors";
                oldProperty = string.Join(",", oldProspectEnquiryDto?.Floors?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newProspectEnquiryDto?.Floors?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "BHKs")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of BHKs";
                oldProperty = string.Join(",", oldProspectEnquiryDto?.BHKs?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newProspectEnquiryDto?.BHKs?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "EnquiryTypes")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of EnquiryTypes";
                oldProperty = string.Join(",", oldProspectEnquiryDto?.EnquiryTypes?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newProspectEnquiryDto?.EnquiryTypes?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "TowerName")
            {
                updatedPropertyName = "Enquiry TowerName";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldProspectEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.TowerName)).Select(i => i.TowerName).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newProspectEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.TowerName)).Select(i => i.TowerName).Distinct().ToList() ?? new());
            }
            if (propertyName == "SubCommunity")
            {
                updatedPropertyName = "Enquiry SubCommunity";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldProspectEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.SubCommunity)).Select(i => i.SubCommunity).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newProspectEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.SubCommunity)).Select(i => i.SubCommunity).Distinct().ToList() ?? new());
            }
            if (propertyName == "Community")
            {
                updatedPropertyName = "Enquiry Community";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldProspectEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.Community)).Select(i => i.Community).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newProspectEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.Community)).Select(i => i.Community).Distinct().ToList() ?? new());
            }


            return (updatedPropertyName, updatedPropertyType, oldProperty, newProperty);
        }
        public static async Task<ViewProspectDto> SetUserViewForProspectV1(ViewProspectDto prospect, List<UserDetailsDto> users, CancellationToken cancellationToken)
        {
            if (prospect == null)
            {
                return null;
            }
            try
            {
                var assignedUser = users.FirstOrDefault(i => i.Id == prospect.AssignTo);
                if (assignedUser != null)
                {
                    prospect.AssignedUser = assignedUser?.Adapt<UserDto>();
                    prospect.AssignedUser.Name = assignedUser?.FirstName + " " + assignedUser?.LastName;
                }
            }
            catch (NotFoundException) { }
            try
            {
                var lastModifiedByUser = users.FirstOrDefault(i => i.Id == prospect.LastModifiedBy);
                if (lastModifiedByUser != null)
                {
                    prospect.LastModifiedUser = lastModifiedByUser?.Adapt<UserDto>();
                    prospect.LastModifiedUser.Name = lastModifiedByUser?.FirstName + " " + lastModifiedByUser?.LastName;
                }
            }
            catch (NotFoundException e) { }

            try
            {
                var assignedFromUser = users.FirstOrDefault(i => i.Id == prospect.AssignedFrom);
                if (assignedFromUser != null)
                {
                    prospect.AssignedFromUser = assignedFromUser?.Adapt<UserDto>();
                    prospect.AssignedFromUser.Name = assignedFromUser?.FirstName + " " + assignedFromUser?.LastName;
                }
            }
            catch (NotFoundException e) { }


            try
            {
                var sourcingManager = users.FirstOrDefault(i => i.Id == prospect?.SourcingManager);

                if (sourcingManager != null)
                {
                    prospect.SourcingManagerUser = sourcingManager?.Adapt<UserDto>();
                    prospect.SourcingManagerUser.Name = sourcingManager?.FirstName + " " + sourcingManager?.LastName;
                }
            }

            catch (NotFoundException e) { }
            try
            {
                var closingManager = users.FirstOrDefault(i => i.Id == prospect?.ClosingManager);

                if (closingManager != null)
                {
                    prospect.ClosingManagerUser = closingManager?.Adapt<UserDto>();
                    prospect.ClosingManagerUser.Name = closingManager?.FirstName + " " + closingManager?.LastName;
                }
            }
            catch (NotFoundException e) { }

            return prospect;
        }
    }
}

