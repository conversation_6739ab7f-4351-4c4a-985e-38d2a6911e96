using Lrb.Application.Common.Exceptions;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Lrb.Shared.Authorization;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Serilog;
using Serilog.Context;
using System.Net;

namespace Lrb.Infrastructure.Middleware;

internal class ExceptionMiddleware : IMiddleware
{
    private readonly ICurrentUser _currentUser;
    private readonly ISerializerService _jsonSerializer;
    private readonly ILeadRepositoryAsync _dapperRepository;

    public ExceptionMiddleware(
        ICurrentUser currentUser,
        ISerializerService jsonSerializer, 
        ILeadRepositoryAsync dapperRepository)
    {
        _currentUser = currentUser;
        _jsonSerializer = jsonSerializer;
        _dapperRepository = dapperRepository;
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        try
        {
            var tnantId = context.Request.Headers["tenant"].FirstOrDefault()?.Split(" ").Last();
            if (tnantId != null)
            {
                context.Items[ContextKeys.TenantId] = tnantId;
            }
            await next(context);
            await AttachBodyWhenUnauthenticatedAsync(context);
        }
        catch (Exception exception)
        {
            string errorId = Guid.NewGuid().ToString();
            string email = _currentUser.GetUserEmail() is string userEmail ? userEmail : "Anonymous";
            var userId = _currentUser.GetUserId();
            string tenant = _currentUser.GetTenant() ?? string.Empty;

            // Log only necessary information
            Log.Information($"Exception Message: {exception.Message}");
            Log.Information($"Exception StackTrace ->> {exception.StackTrace}");
            if (userId != Guid.Empty) LogContext.PushProperty("UserId", userId);
            LogContext.PushProperty("UserEmail", email);
            if (!string.IsNullOrEmpty(tenant)) LogContext.PushProperty("Tenant", tenant);
            LogContext.PushProperty("ErrorId", errorId);

            var errorResult = new ErrorResult
            {
                Exception = exception.Message.Trim(),
                ErrorId = errorId,
                SupportMessage = $"Provide the ErrorId {errorId} to the support team for further analysis."
            };
            errorResult.Messages.Add(exception.Message);

            // Store error details in database without exposing them in response
            var error = new LrbError()
            {
                ErrorMessage = exception?.Message ?? exception?.InnerException?.Message,
                ErrorSource = exception?.Source,
                StackTrace = exception?.StackTrace,
                InnerException = JsonConvert.SerializeObject(exception?.InnerException, 
                    new JsonSerializerSettings 
                    { 
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore, 
                        Formatting = Formatting.Indented 
                    }),
                ErrorModule = "ExceptionMiddleware -> InvokeAsync()",
            };
            await _dapperRepository.AddErrorAsync(error);

            if (exception is not CustomException && exception?.InnerException != null)
            {
                while (exception.InnerException != null)
                {
                    exception = exception.InnerException;
                }
            }

            switch (exception)
            {
                case CustomException e:
                    errorResult.StatusCode = (int)e.StatusCode;
                    errorResult.ActionCode = (int)e.ActionCode;
                    if (e is NotFoundException)
                    {
                        errorResult.ActionCode = (int)ErrorActionCode.ShowToast;
                    }

                    if (e.ErrorMessages is not null)
                    {
                        errorResult.Messages = e.ErrorMessages;
                    }
                    break;

                case KeyNotFoundException:
                    errorResult.StatusCode = (int)HttpStatusCode.NotFound;
                    break;
                case UnauthorizedAccessException:
                    errorResult.StatusCode = (int)HttpStatusCode.Unauthorized;
                    break;
                default:
                    errorResult.StatusCode = (int)HttpStatusCode.InternalServerError;
                    errorResult.ActionCode = (int)ErrorActionCode.NoOp;
                    break;
            }

            Log.Error($"Request failed with Status Code {context.Response.StatusCode} and Error Id {errorId}");
            var response = context.Response;
            if (!response.HasStarted)
            {
                response.ContentType = "application/json";
                response.StatusCode = errorResult.StatusCode;
                AddCorsHeaders(response);
                await response.WriteAsync(_jsonSerializer.Serialize(errorResult));
            }
            else
            {
                Log.Warning("Can't write error response. Response has already started.");
            }
        }
    }

    private async Task AttachBodyWhenUnauthenticatedAsync(HttpContext context)
    {
        if (context.Response != null)
        {
            var response = context.Response;
            if (!response.HasStarted && response.StatusCode == 401 && response.Headers.ContainsKey("WWW-Authenticate"))
            {
                var headerValue = response.Headers?["WWW-Authenticate"].ToString();

                if (headerValue != null)
                {
                    string errorId = Guid.NewGuid().ToString();
                    var errorResult = new ErrorResult()
                    {
                        ErrorId = errorId,
                        StatusCode = response.StatusCode,
                        SupportMessage = $"Provide the ErrorId {errorId} to the support team for further analysis."
                    };

                    headerValue = headerValue.Replace("Bearer", string.Empty).Trim();

                    if (headerValue?.Contains("invalid_token") ?? false)
                    {
                        headerValue = headerValue.Replace("invalid_token", string.Empty).Trim();
                        if (headerValue.Contains("The signature is invalid") || !headerValue.Contains("The token lifetime is invalid"))
                        {
                            errorResult.Messages.Add("Incorrect Credentials!");
                            errorResult.Exception = "Incorrect Credentials!";
                            errorResult.ActionCode = (int)ErrorActionCode.Logout;
                        }
                        else if (headerValue.Contains("The token lifetime is invalid"))
                        {
                            errorResult.Messages.Add("Session Expired!");
                            errorResult.Exception = "Session Expired!";
                            errorResult.ActionCode = (int)ErrorActionCode.Refresh;
                        }
                        else
                        {
                            errorResult.ActionCode = (int)ErrorActionCode.NoOp;
                        }
                    }
                    else if (string.IsNullOrEmpty(headerValue))
                    {
                        errorResult.ActionCode = (int)ErrorActionCode.Logout;
                        errorResult.Messages.Add("Missing credentials!");
                        errorResult.Exception = "Missing credentials!";
                    }

                    response.ContentType = "application/json";
                    AddCorsHeaders(response);
                    await response.WriteAsync(_jsonSerializer.Serialize(errorResult));
                }
            }
        }
    }

    public static void AddCorsHeaders(HttpResponse response)
    {
        response.Headers.Add("Access-Control-Allow-Origin", "*");
        response.Headers.Add("Access-Control-Allow-Methods", "GET, HEAD, OPTIONS, POST, PUT, DELETE, TRACE, CONNECT");
        response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, Authorization");
    }
}