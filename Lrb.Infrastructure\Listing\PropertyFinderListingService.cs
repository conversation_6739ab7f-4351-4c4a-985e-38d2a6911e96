﻿using Lrb.Application.Common.Listing;
using Lrb.Application.Property.Web.V2.Dtos;
using Lrb.Domain.Entities;
using Newtonsoft.Json;
using RestSharp;

namespace Lrb.Infrastructure.Listing
{
    internal class PropertyFinderListingService : IPropertyFinderListingService
    {
        private readonly string _baseUri = "https://atlas.propertyfinder.com";

        #region Generate Auth Token
        public async Task<string> GeneratePFAuthToken(string apiKey, string secretKey)
        {
            try
            {
                var options = new RestClientOptions(_baseUri ?? string.Empty)
                {
                    MaxTimeout = -1,
                };
                var client = new RestClient(options);
                var request = new RestRequest("/v1/auth/token", Method.Post);
                request.AddHeader("Content-Type", "application/json");
                var body = new PFNewAuthDto()
                {
                    apiKey = apiKey,
                    apiSecret = secretKey
                };
                var jsonBody = JsonConvert.SerializeObject(body);
                request.AddStringBody(jsonBody, DataFormat.Json);
                RestResponse response = await client.PostAsync(request);
                Console.WriteLine(response.Content);
                var data = JsonConvert.DeserializeObject<PFAuthResponseDto>(response?.Content ?? string.Empty);

                return data?.accessToken ?? string.Empty;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to generate Property Finder auth token.", ex);
            }
        }
        #endregion

        #region PF Listing Operations
        public async Task<RootGetAll> SearchListingOnPF(PFListingFilter filter, string apiKey, string secretKey)
        {
            try
            {
                var options = new RestClientOptions(_baseUri)
                {
                    MaxTimeout = -1,
                };
                var client = new RestClient(options);
                var request = new RestRequest();
                if (filter.IsDraft == true && !string.IsNullOrEmpty(filter.Reference))
                {
                    request = new RestRequest($"/v1/listings?perPage={filter.Perpage}&page={filter.Page}&draft=true&filter[reference]={filter.Reference}", Method.Get);
                }
                else if(!string.IsNullOrEmpty(filter.Id))
                {
                    request = new RestRequest($"/v1/listings?perPage=10&page=1&filter[ids]={filter.Id}", Method.Get);
                }
                var token = await GeneratePFAuthToken(apiKey, secretKey);
                request.AddHeader("Authorization", $"Bearer {token}");
                RestResponse response = await client.ExecuteAsync(request);
                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<RootGetAll>(response?.Content ?? string.Empty);
                    return data ?? new();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        public async Task<PropertyResponse?> CreateListingOnPF(CreateListingOnPFRequestDto dto, ListingIntegrationInfo accountInfo)
        {
            try
            {
                var options = new RestClientOptions(accountInfo?.BaseUri ?? _baseUri)
                {
                    MaxTimeout = -1,
                };
                var client = new RestClient(options);
                var request = new RestRequest("/v1/listings", Method.Post);
                var token = await GeneratePFAuthToken(accountInfo?.ApiKey ?? string.Empty, accountInfo?.SecretKey ?? string.Empty);
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("Authorization", $"Bearer {token}");
                var settings = new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                };

                var jsonBody = JsonConvert.SerializeObject(dto, settings);
                request.AddStringBody(jsonBody, DataFormat.Json);
                RestResponse response = await client.ExecuteAsync(request);
                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<PropertyResponse>(response?.Content ?? string.Empty);
                    return data;
                }
                return null;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to create listing on Property Finder.", ex);
            }
        }

        public async Task<PropertyResponse> UpdateListingOnPF(CreateListingOnPFRequestDto dto, string listingId, ListingIntegrationInfo accountInfo)
        {
            try
            {
                var options = new RestClientOptions(accountInfo?.BaseUri ?? _baseUri)
                {
                    MaxTimeout = -1,
                };
                var client = new RestClient(options);
                var request = new RestRequest($"/v1/listings/{listingId}", Method.Put);
                var token = await GeneratePFAuthToken(accountInfo?.ApiKey ?? string.Empty, accountInfo?.SecretKey ?? string.Empty);
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("Authorization", $"Bearer {token}");
                var settings = new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                };
                var jsonBody = JsonConvert.SerializeObject(dto, settings);
                request.AddStringBody(jsonBody, DataFormat.Json);
                RestResponse response = await client.ExecuteAsync(request);
                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<PropertyResponse>(response?.Content ?? string.Empty);
                    return data;
                }
                return null;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to create listing on Property Finder.", ex);
            }
        }

        public async Task<bool> DeleteListingOnPF(string listingId, ListingIntegrationInfo accountInfo)
        {
            try
            {
                var options = new RestClientOptions(accountInfo?.BaseUri ?? _baseUri)
                {
                    MaxTimeout = -1,
                };
                var client = new RestClient(options);
                var request = new RestRequest($"/v1/listings/{listingId}", Method.Delete);
                var token = await GeneratePFAuthToken(accountInfo?.ApiKey ?? string.Empty, accountInfo?.SecretKey ?? string.Empty);
                request.AddHeader("Authorization", $"Bearer {token}");
                request.AddHeader("Content-Type", "application/json");
                RestResponse response = await client.ExecuteAsync(request);
                Console.WriteLine(response.Content);
                if (response.IsSuccessful)
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to delete listing on Property Finder.", ex);
            }
        }

        public async Task<bool> PublishListingOnPF(string listingId, ListingIntegrationInfo accountInfo)
        {
            try
            {
                var options = new RestClientOptions("https://atlas.propertyfinder.com")
                {
                    MaxTimeout = -1,
                };
                var client = new RestClient(options);
                var request = new RestRequest($"/v1/listings/{listingId}/publish", Method.Post);
                var token = await GeneratePFAuthToken(accountInfo?.ApiKey ?? string.Empty, accountInfo?.SecretKey ?? string.Empty);
                request.AddHeader("Authorization", $"Bearer {token}");
                request.AddHeader("Content-Type", "application/json");
                RestResponse response = await client.ExecuteAsync(request);
                Console.WriteLine(response.Content);
                if (response.IsSuccessful)
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to publish listing on Property Finder.", ex);
            }
        }
        #endregion

        #region Location
        public async Task<ApiResponse> GetPFLocation(PFLocationFilter filter, string apiKey, string secretKey)
        {
            try
            {
                var options = new RestClientOptions(_baseUri)
                {
                    MaxTimeout = -1,
                };
                var client = new RestClient(options);
                var request = new RestRequest();
                if (filter.locationId != null)
                {
                    request = new RestRequest($"/v1/locations?perPage=10&page=1&filter[id]={filter.locationId}", Method.Get);
                }
                else
                {
                    request = new RestRequest($"/v1/locations?perPage={filter.PerPage}&page={filter.Page}&search={filter.Search}", Method.Get);
                }
                var token = await GeneratePFAuthToken(apiKey ?? string.Empty, secretKey ?? string.Empty);
                request.AddHeader("Authorization", $"Bearer {token}");
                request.AddHeader("Content-Type", "application/json");
                RestResponse response = await client.ExecuteAsync(request);
                Console.WriteLine(response.Content);
                var data = JsonConvert.DeserializeObject<ApiResponse>(response?.Content ?? string.Empty);
                return data ?? new();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to get Property Finder locations.", ex);
            }
        }
        #endregion

        #region Listing Verification
        public Task<object> CheckListingVerificationEligibility(string listingId, string apiKey, string secretKey)
        {
            throw new NotImplementedException();
        }

        public Task<bool> VerifyListing(string listingId, string userId, object documents, string apiKey, string secretKey)
        {
            throw new NotImplementedException();
        }
        #endregion

        #region User
        public Task<bool> CreateUserOnPF(Guid lrbUserId, string apiKey, string secretKey)
        {
            throw new NotImplementedException();
        }

        public async Task<AgentResponse> ImportPFUserAsLrb(string page, string perpage, string search, string apiKey, string secretKey)
        {
            try
            {
                var options = new RestClientOptions(_baseUri)
                {
                    MaxTimeout = -1,
                };

                var token = await GeneratePFAuthToken(apiKey, secretKey);
                var client = new RestClient(options);
                var request = new RestRequest($"/v1/users/?page={page}&perPage={perpage}&search={search}", Method.Get);
                request.AddHeader("Authorization", $"Bearer {token}");
                request.AddHeader("Content-Type", "application/json");
                RestResponse response = await client.ExecuteAsync(request);
                Console.WriteLine(response.Content);
                var data = JsonConvert.DeserializeObject<AgentResponse>(response?.Content ?? string.Empty);
                return data ?? new AgentResponse();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to import Property Finder user as LRB user.", ex);
            }
        }
        #endregion

        #region WebHook
        public async Task<PFWebhookResponseDto> SubscribeWebhookEvent(PFWebHookRequest dto, string apiKey, string secretKey)
        {
            try
            {
                var options = new RestClientOptions("https://atlas.propertyfinder.com")
                {
                    MaxTimeout = -1,
                };
                var client = new RestClient(options);
                var request = new RestRequest("/v1/webhooks", Method.Post);
                request.AddHeader("Content-Type", "application/json");
                var token = await GeneratePFAuthToken(apiKey, secretKey);
                request.AddHeader("Authorization", $"Bearer {token}");
                var jsonBody = JsonConvert.SerializeObject(dto, Formatting.Indented);
                request.AddStringBody(jsonBody, DataFormat.Json);
                RestResponse response = await client.ExecuteAsync(request);
                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<PFWebhookResponseDto>(response?.Content ?? string.Empty);
                    return data ?? new();
                }
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public async Task<bool> DeleteEventSubscription(string eventId, string apiKey, string secretKey)
        {
            try
            {
                var options = new RestClientOptions("https://atlas.propertyfinder.com")
                {
                    MaxTimeout = -1,
                };
                var client = new RestClient(options);
                var request = new RestRequest($"/v1/webhooks/{eventId}", Method.Delete);
                var token = await GeneratePFAuthToken(apiKey, secretKey);
                request.AddHeader("Authorization", $"Bearer {token}");
                RestResponse response = await client.ExecuteAsync(request);
                if (response.IsSuccessful)
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        #endregion

        #region Auth Dto
        public class PFNewAuthDto
        {
            public string? apiKey { get; set; }
            public string? apiSecret { get; set; }
        }
        #endregion
    }
}
