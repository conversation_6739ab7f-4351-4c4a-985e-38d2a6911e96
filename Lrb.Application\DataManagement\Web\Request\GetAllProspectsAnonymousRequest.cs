﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Lead.Web;

namespace Lrb.Application.DataManagement.Web.Request
{
    public class GetAllProspectsAnonymousRequest : GetAllProspectParameter, IRequest<PagedResponse<PullViewProspectDto, long>>
    {
    }

    public class GetAllProspectsAnonymousRequestHandler : IRequestHandler<GetAllProspectsAnonymousRequest, PagedResponse<PullViewProspectDto, long>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly IDapperRepository _dapperRepository;
        private readonly IProspectRepository _efProspectRepository;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _customProspectRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepository;
        public GetAllProspectsAnonymousRequestHandler(
            ICurrentUser currentUser,
            IDapperRepository dapperRepository,
            IProspectRepository efProspectRepository,
            IRepositoryWithEvents<CustomProspectStatus> customProspectRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepository)
        {
            _currentUser = currentUser;
            _dapperRepository = dapperRepository;
            _efProspectRepository = efProspectRepository;
            _customProspectRepo = customProspectRepo;
            _globalSettingsRepository = globalSettingsRepository;
        }
        public async Task<PagedResponse<PullViewProspectDto, long>> Handle(GetAllProspectsAnonymousRequest request, CancellationToken cancellationToken)
        {
            Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            if (globalSettings?.CanAccessAnonymousApis != true)
            {
                throw new UnauthorizedAccessException("Access to prospects is restricted.");
            }
            var statuses = await _customProspectRepo.ListAsync(cancellationToken);
            var prospects = _efProspectRepository.GetAnonymousProspectForWeb(request,statuses).Result.ToList();
            var count = _efProspectRepository.GetAnonymousProspectCountForWeb(request, statuses).Result;
            var prospectsView = prospects.Adapt<List<PullViewProspectDto>>();
            return new(prospectsView, count);
        }
    }
}
