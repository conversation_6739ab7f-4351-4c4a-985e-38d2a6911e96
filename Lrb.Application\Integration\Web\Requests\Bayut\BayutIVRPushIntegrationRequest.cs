﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.LeadGenRequests;
using Lrb.Domain.Enums;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.WebUtilities;
using Newtonsoft.Json;

namespace Lrb.Application.Integration.Web.Requests.Bayut
{
    public class BayutIVRPushIntegrationRequest : IRequest<Response<bool>>
    {
        public string ApiKey { get; set; }
        public HttpRequest HttpRequest { get; set; }
        public string TenantId { get; set; }

        public BayutIVRPushIntegrationRequest(HttpRequest request, string tenant, string base64)
        {
            HttpRequest = request;
            TenantId = tenant;
            ApiKey = base64;
        }
        public class BayutIVRPushIntegrationRequestHandler : IRequestHandler<BayutIVRPushIntegrationRequest, Response<bool>>
        {
            private readonly IMediator _mediator;
            private readonly Serilog.ILogger _logger;
            private readonly IRepositoryWithEvents<IntegrationAccountInfo> _intAccRepo;
            public BayutIVRPushIntegrationRequestHandler(IMediator mediator, Serilog.ILogger logger, IRepositoryWithEvents<IntegrationAccountInfo> intAccRepo)
            {
                _mediator = mediator;
                _logger = logger;
                _intAccRepo = intAccRepo;
            }

            public async Task<Response<bool>> Handle(BayutIVRPushIntegrationRequest request, CancellationToken cancellationToken)
            {
                var accountId = request.ApiKey.GetAccountId();
                var integrationAccount = await _intAccRepo.GetByIdAsync(accountId, cancellationToken);
                if (integrationAccount == null)
                {
                    _logger.Error("BayutIVRPushIntegrationRequestHandler -> POST(Bayut) -> Integration Account not found for ApiKey: " + request.ApiKey);
                    return new(false, "Integration Account not found");
                }
                var httpRequest = request.HttpRequest;
                var bodyInString = "";
                BayutIVRDto? payload = null;
                if (request.HttpRequest.HasFormContentType)
                {
                    var form = await httpRequest.ReadFormAsync();
                    var formData = form.ToDictionary(x => x.Key, x => x.Value.ToString());
                    bodyInString = JsonConvert.SerializeObject(formData);
                    if (string.IsNullOrWhiteSpace(bodyInString))
                    {
                        throw new ArgumentNullException("Payload Cannot be empty");
                    }
                    payload = JsonConvert.DeserializeObject<BayutIVRDto>(bodyInString);
                }
                else if (httpRequest.QueryString.HasValue)
                {
                    var queryParamsData = httpRequest.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
                    bodyInString = JsonConvert.SerializeObject(queryParamsData);
                    if (string.IsNullOrWhiteSpace(bodyInString))
                    {
                        throw new ArgumentNullException("Payload Cannot be empty");
                    }
                    payload = JsonConvert.DeserializeObject<BayutIVRDto>(bodyInString);
                }
                else
                {
                    Stream stream = httpRequest.Body;
                    HttpRequestStreamReader reader = new(stream, System.Text.Encoding.UTF8);
                    bodyInString = await reader.ReadToEndAsync();
                    if (string.IsNullOrWhiteSpace(bodyInString))
                    {
                        throw new ArgumentNullException("Payload Cannot be empty");
                    }
                    payload = JsonConvert.DeserializeObject<BayutIVRDto>(bodyInString);
                }
                if (payload != null)
                {
                    _logger.Information("BayutIVRPushIntegrationRequestHandler -> POST(Bayut) -> called, Dto: " + payload);
                    CreateLeadGenRequest leadGenRequest = new(LeadSource.Bayut, payload);

                    await _mediator.Send(leadGenRequest);

                    var lead = new ServetelInboundIntegrationRequest
                    {
                        answered_agent_name = payload.agent?.name,
                        answered_agent_number = payload.agent?.phone,
                        answer_stamp = GetUtcDateTimeFromUnix(payload.call_details?.pickup_timestamp),
                        call_status = payload.call_details?.call_status,
                        duration = payload.call_details?.call_total_duration,
                        caller_id_number = payload.enquirer?.phone_number,
                        start_stamp = GetUtcDateTimeFromUnix(payload.call_details?.pickup_timestamp),
                        virtual_number = payload.call_details?.proxy_number,
                        ApiKey = request.ApiKey
                    };
                    await _mediator.Send(lead);
                }
                return new(true);
            }
        }
        public static string? GetUtcDateTimeFromUnix(string? unixTimestamp)
        {
            if (long.TryParse(unixTimestamp, out long seconds))
            {
                var dateTime = DateTimeOffset.FromUnixTimeSeconds(seconds).UtcDateTime;
                return dateTime.ToString("yyyy-MM-ddTHH:mm:ssZ"); 
            }
            return null;
        }
    }
}
